import 'package:flutter/material.dart';
import 'ToReceiveView.dart';
import 'CompletedOrderDetailsNewView.dart';
import 'CancelledOrderDetailsNewView.dart';
import 'ToShipNewView.dart';

/// Helper class to navigate to the appropriate order detail view based on order status
class OrderDetailNavigator {
  /// Navigate to the appropriate order detail view based on order state
  /// 
  /// Order states:
  /// -10: Refunded
  /// -1: Cancelled
  /// 0: Waiting for payment
  /// 10: Waiting for shipment (To Ship)
  /// 20: Waiting for receipt (To Receive)
  /// 100: Completed
  static void navigateToOrderDetail(
    BuildContext context, 
    int orderId, 
    int orderState,
  ) {
    Widget destinationView;

    switch (orderState) {
      case -1: // Cancelled
        destinationView = CancelledOrderDetailsNewView(orderId: orderId);
        break;
      case 0: // Waiting for payment
        // TODO: Create a payment pending view
        destinationView = ToShipNewView(orderId: orderId);
        break;
      case 10: // Waiting for shipment (To Ship)
        destinationView = ToShipNewView(orderId: orderId);
        break;
      case 20: // Waiting for receipt (To Receive)
        destinationView = ToReceiveView(orderId: orderId);
        break;
      case 100: // Completed
        destinationView = CompletedOrderDetailsNewView(orderId: orderId);
        break;
      case -10: // Refunded
        // TODO: Create a refund view
        destinationView = CancelledOrderDetailsNewView(orderId: orderId);
        break;
      default:
        // Default to completed view for unknown states
        destinationView = CompletedOrderDetailsNewView(orderId: orderId);
        break;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => destinationView,
      ),
    );
  }

  /// Get the appropriate status display name for an order state
  static String getStatusDisplayName(int orderState) {
    switch (orderState) {
      case -10:
        return 'Refunded';
      case -1:
        return 'Cancelled';
      case 0:
        return 'Waiting for Payment';
      case 10:
        return 'To Ship';
      case 20:
        return 'To Receive';
      case 100:
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  /// Get the appropriate status color for an order state
  static Color getStatusColor(int orderState) {
    switch (orderState) {
      case -10:
        return Colors.purple;
      case -1:
        return Colors.red;
      case 0:
        return Colors.orange;
      case 10:
        return Colors.blue;
      case 20:
        return Colors.amber;
      case 100:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}

/// Example usage widget showing how to use the new order detail views
class OrderListExample extends StatelessWidget {
  const OrderListExample({super.key});

  @override
  Widget build(BuildContext context) {
    // Mock order data - replace with actual data from your API
    final mockOrders = [
      {'id': 1, 'state': 20, 'productName': 'Silicone Folding Cup'},
      {'id': 2, 'state': 100, 'productName': 'Wireless Headphones'},
      {'id': 3, 'state': -1, 'productName': 'Phone Case'},
      {'id': 4, 'state': 10, 'productName': 'Bluetooth Speaker'},
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Orders'),
      ),
      body: ListView.builder(
        itemCount: mockOrders.length,
        itemBuilder: (context, index) {
          final order = mockOrders[index];
          final orderId = order['id'] as int;
          final orderState = order['state'] as int;
          final productName = order['productName'] as String;

          return Card(
            margin: const EdgeInsets.all(8.0),
            child: ListTile(
              title: Text(productName),
              subtitle: Text('Order #$orderId'),
              trailing: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: OrderDetailNavigator.getStatusColor(orderState).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: OrderDetailNavigator.getStatusColor(orderState),
                    width: 1,
                  ),
                ),
                child: Text(
                  OrderDetailNavigator.getStatusDisplayName(orderState),
                  style: TextStyle(
                    color: OrderDetailNavigator.getStatusColor(orderState),
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
              onTap: () {
                OrderDetailNavigator.navigateToOrderDetail(
                  context,
                  orderId,
                  orderState,
                );
              },
            ),
          );
        },
      ),
    );
  }
}

/// Example of how to integrate with existing MyOrderView
/// You can use this pattern in your existing order list views
class OrderItemWidget extends StatelessWidget {
  final int orderId;
  final int orderState;
  final String productName;
  final String productImage;
  final double totalPrice;

  const OrderItemWidget({
    super.key,
    required this.orderId,
    required this.orderState,
    required this.productName,
    required this.productImage,
    required this.totalPrice,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Use the new navigation system
        OrderDetailNavigator.navigateToOrderDetail(
          context,
          orderId,
          orderState,
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          children: [
            // Product image
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: 60,
                height: 60,
                color: Colors.grey.shade100,
                child: productImage.startsWith('http')
                    ? Image.network(
                        productImage,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.image,
                            color: Colors.grey.shade400,
                          );
                        },
                      )
                    : Icon(
                        Icons.image,
                        color: Colors.grey.shade400,
                      ),
              ),
            ),
            const SizedBox(width: 12),
            // Product info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    productName,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'RM${totalPrice.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            // Status badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: OrderDetailNavigator.getStatusColor(orderState).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: OrderDetailNavigator.getStatusColor(orderState),
                  width: 1,
                ),
              ),
              child: Text(
                OrderDetailNavigator.getStatusDisplayName(orderState),
                style: TextStyle(
                  color: OrderDetailNavigator.getStatusColor(orderState),
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
