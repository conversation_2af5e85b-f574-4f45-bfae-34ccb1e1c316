import 'package:flutter/material.dart';

class ReferralsFAQPage extends StatelessWidget {
  const ReferralsFAQPage({super.key});

  final List<Map<String, String>> faqs = const [
    {
      'question': 'How does the referral program work?',
      'answer':
          'You can invite your friends using your referral code. When they sign up and make a purchase, both of you will receive rewards.'
    },
    {
      'question': 'Where can I find my referral code?',
      'answer':
          'Go to the "Referrals" section in your profile. Your unique referral code and share options will be displayed there.'
    },
    {
      'question': 'What rewards do I get for referrals?',
      'answer':
          'You will receive points or voucher rewards depending on the current promotion. Check the Referrals section for details.'
    },
    {
      'question': 'How many friends can I refer?',
      'answer':
          'There is no limit to how many friends you can refer. The more friends you invite, the more rewards you can earn!'
    },
    {
      'question': 'When will I receive my referral reward?',
      'answer':
          'You will receive your reward once your referred friend completes their first qualifying transaction.'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        iconTheme: const IconThemeData(color: Colors.black),
        centerTitle: true,
        title: const Text(
          'Referrals FAQ',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: faqs.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final faq = faqs[index];
          return Card(
            elevation: 1,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ExpansionTile(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              title: Row(
                children: [
                  const Icon(Icons.group_outlined, color: Colors.black),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      faq['question']!,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
              children: [
                Text(
                  faq['answer']!,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
