import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import '../view-model/MySharingPostVM.dart';

class UserPostView extends StatefulWidget {
  const UserPostView({super.key, required this.post});
  final Map<String, dynamic> post;

  @override
  State<UserPostView> createState() => _PostViewState();
}

class _PostViewState extends State<UserPostView> {
  late bool isFavorite;
  late int likeCount;
  late PageController _pageController;
  int _currentPage = 0;

  // Helper method to extract images from post data (matching blindbox PostView)
  List<String> _extractImages(Map<String, dynamic> post) {
    return MySharingPostVM.extractImages(post);
  }

  @override
  void initState() {
    super.initState();
    isFavorite = widget.post['isFavorite'] ?? false;
    likeCount = widget.post['likeCount'] ?? 0;

    // Only initialize page controller if there are multiple images
    final images = _extractImages(widget.post);
    if (images.length > 1) {
      _pageController = PageController();
    }
  }

  @override
  void dispose() {
    if (_pageController.hasClients) {
      _pageController.dispose();
    }
    super.dispose();
  }

  void toggleFavorite() {
    setState(() {
      isFavorite = !isFavorite;
      if (isFavorite) {
        likeCount++;
      } else {
        likeCount = likeCount > 0 ? likeCount - 1 : 0;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final username = widget.post['username'] as String? ?? 'Angela';
    final title = widget.post['title'] as String? ?? '';

    final imageHeight = MySharingPostVM.getResponsiveImageHeight(context);
    final avatarRadius = MySharingPostVM.getResponsiveAvatarRadius(context);

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(
          'Details',
          style: TextStyle(
            color: Colors.black,
            fontSize: MySharingPostVM.getResponsiveFontSize(18, context),
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back_ios_new_rounded,
            color: Colors.black,
            size: MySharingPostVM.getResponsiveFontSize(24, context),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(top: BorderSide(color: Color(0xFFE8E8E8))),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // User info row
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: MySharingPostVM.getResponsivePadding(16, context),
                  vertical: MySharingPostVM.getResponsivePadding(12, context),
                ),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        // Navigate to profile like in blindbox PostView (but since this is already user's own post, we can keep it simple)
                      },
                      child: CircleAvatar(
                        radius: avatarRadius,
                        backgroundColor: Colors.grey,
                        child: Icon(
                          Icons.person,
                          size: MySharingPostVM.getResponsiveFontSize(
                            25,
                            context,
                          ),
                          color: Colors.white,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: MySharingPostVM.getResponsivePadding(10, context),
                    ),
                    Expanded(
                      child: Text(
                        username,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: MySharingPostVM.getResponsiveFontSize(
                            16,
                            context,
                          ),
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        // Handle delete action
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return AlertDialog(
                              backgroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              title: Text(
                                'Delete Post',
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize:
                                      MySharingPostVM.getResponsiveFontSize(
                                        18,
                                        context,
                                      ),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              content: Text(
                                'Are you sure you want to delete this post?',
                                style: TextStyle(
                                  color: Colors.black87,
                                  fontSize:
                                      MySharingPostVM.getResponsiveFontSize(
                                        14,
                                        context,
                                      ),
                                ),
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  style: TextButton.styleFrom(
                                    padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MySharingPostVM.getResponsivePadding(
                                            16,
                                            context,
                                          ),
                                      vertical:
                                          MySharingPostVM.getResponsivePadding(
                                            8,
                                            context,
                                          ),
                                    ),
                                  ),
                                  child: Text(
                                    'Cancel',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize:
                                          MySharingPostVM.getResponsiveFontSize(
                                            14,
                                            context,
                                          ),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                    Navigator.of(
                                      context,
                                    ).pop(); // Go back to previous screen
                                    // TODO: Implement actual delete functionality
                                  },
                                  style: TextButton.styleFrom(
                                    padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MySharingPostVM.getResponsivePadding(
                                            16,
                                            context,
                                          ),
                                      vertical:
                                          MySharingPostVM.getResponsivePadding(
                                            8,
                                            context,
                                          ),
                                    ),
                                  ),
                                  child: Text(
                                    'Delete',
                                    style: TextStyle(
                                      color: Colors.red,
                                      fontSize:
                                          MySharingPostVM.getResponsiveFontSize(
                                            14,
                                            context,
                                          ),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        );
                      },
                      child: Icon(
                        Icons.delete_outline,
                        size: MySharingPostVM.getResponsiveFontSize(
                          24,
                          context,
                        ),
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              // Main image section - show carousel only if multiple images (matching blindbox PostView)
              if (_extractImages(widget.post).length > 1) ...[
                // Multiple images - show carousel with page indicator
                Stack(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      height: imageHeight,
                      child: PageView.builder(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _currentPage = index;
                          });
                        },
                        itemCount: _extractImages(widget.post).length,
                        itemBuilder: (context, index) {
                          final imageUrl = _extractImages(widget.post)[index];
                          return CachedNetworkImage(
                            imageUrl: imageUrl,
                            width: double.infinity,
                            height: imageHeight,
                            fit: BoxFit.contain,
                            placeholder: (context, url) => Center(
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  width: double.infinity,
                                  height: imageHeight,
                                  color: Colors.grey[300]!,
                                ),
                              ),
                            ),
                            errorWidget: (context, url, error) => Center(
                              child: Icon(
                                Icons.broken_image,
                                size: MySharingPostVM.getResponsiveFontSize(
                                  60,
                                  context,
                                ),
                                color: Colors.grey,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    Positioned(
                      bottom: MySharingPostVM.getResponsivePadding(12, context),
                      left: 0,
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          _extractImages(widget.post).length,
                          (index) => Container(
                            width: MySharingPostVM.getResponsiveFontSize(
                              8,
                              context,
                            ),
                            height: MySharingPostVM.getResponsiveFontSize(
                              8,
                              context,
                            ),
                            margin: EdgeInsets.symmetric(
                              horizontal: MySharingPostVM.getResponsivePadding(
                                3,
                                context,
                              ),
                            ),
                            decoration: BoxDecoration(
                              color: _currentPage == index
                                  ? Colors.orange
                                  : Colors.grey[300],
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ] else if (_extractImages(widget.post).isNotEmpty) ...[
                // Single image - show without carousel
                SizedBox(
                  width: double.infinity,
                  height: imageHeight,
                  child: CachedNetworkImage(
                    imageUrl: _extractImages(widget.post).first,
                    width: double.infinity,
                    height: imageHeight,
                    fit: BoxFit.contain,
                    placeholder: (context, url) => Center(
                      child: Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: double.infinity,
                          height: imageHeight,
                          color: Colors.grey[300]!,
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Center(
                      child: Icon(
                        Icons.broken_image,
                        size: MySharingPostVM.getResponsiveFontSize(
                          60,
                          context,
                        ),
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
              ] else ...[
                // No images - show placeholder
                SizedBox(
                  width: double.infinity,
                  height: imageHeight,
                  child: Container(
                    color: Colors.grey[200],
                    child: Center(
                      child: Icon(
                        Icons.image_not_supported,
                        size: MySharingPostVM.getResponsiveFontSize(
                          60,
                          context,
                        ),
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
              ],
              // Details section at the bottom
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(bottom: BorderSide(color: Color(0xFFE8E8E8))),
                ),
                width: double.infinity,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: MySharingPostVM.getResponsivePadding(
                      16,
                      context,
                    ),
                    vertical: MySharingPostVM.getResponsivePadding(8, context),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              title,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: MySharingPostVM.getResponsiveFontSize(
                                  16,
                                  context,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: MySharingPostVM.getResponsivePadding(
                                2,
                                context,
                              ),
                            ),
                            Text(
                              '8 July 2025',
                              style: TextStyle(
                                color: Colors.black54,
                                fontSize: MySharingPostVM.getResponsiveFontSize(
                                  13,
                                  context,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          GestureDetector(
                            onTap: toggleFavorite,
                            child: Icon(
                              isFavorite
                                  ? Icons.thumb_up
                                  : Icons.thumb_up_alt_outlined,
                              color: isFavorite ? Colors.red : Colors.grey,
                              size: MySharingPostVM.getResponsiveFontSize(
                                20,
                                context,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: MySharingPostVM.getResponsivePadding(
                              4,
                              context,
                            ),
                          ),
                          Text(
                            likeCount.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: MySharingPostVM.getResponsiveFontSize(
                                15,
                                context,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              // Product Promotion Section
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(color: Color(0xFFE8E8E8), width: 0.5),
                  ),
                ),
                padding: EdgeInsets.symmetric(
                  vertical: MySharingPostVM.getResponsivePadding(12, context),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: MySharingPostVM.getResponsivePadding(
                          16,
                          context,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.shopping_bag_outlined,
                            size: MySharingPostVM.getResponsiveFontSize(
                              16,
                              context,
                            ),
                            color: Colors.orange,
                          ),
                          SizedBox(
                            width: MySharingPostVM.getResponsivePadding(
                              6,
                              context,
                            ),
                          ),
                          Text(
                            'Get the product from this post',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: MySharingPostVM.getResponsiveFontSize(
                                14,
                                context,
                              ),
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: MySharingPostVM.getResponsivePadding(8, context),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: MySharingPostVM.getResponsivePadding(
                          16,
                          context,
                        ),
                      ),
                      child: GestureDetector(
                        onTap: () => MySharingPostVM.onProductTap(
                          context,
                          MySharingPostVM.getSampleProduct(),
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              // Product Image
                              Padding(
                                padding: EdgeInsets.only(
                                  left: MySharingPostVM.getResponsivePadding(
                                    12,
                                    context,
                                  ),
                                  top: MySharingPostVM.getResponsivePadding(
                                    12,
                                    context,
                                  ),
                                  bottom: MySharingPostVM.getResponsivePadding(
                                    12,
                                    context,
                                  ),
                                ),
                                child: Stack(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: CachedNetworkImage(
                                        imageUrl:
                                            MySharingPostVM.getSampleProduct()['imageUrl'],
                                        width:
                                            MySharingPostVM.getResponsiveProductImageHeight(
                                              context,
                                            ),
                                        height:
                                            MySharingPostVM.getResponsiveProductImageHeight(
                                              context,
                                            ),
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) => Container(
                                          width:
                                              MySharingPostVM.getResponsiveProductImageHeight(
                                                context,
                                              ),
                                          height:
                                              MySharingPostVM.getResponsiveProductImageHeight(
                                                context,
                                              ),
                                          color: Colors.grey[200],
                                          child: Center(
                                            child: Icon(
                                              Icons.image,
                                              color: Colors.grey[400],
                                              size:
                                                  MySharingPostVM.getResponsiveFontSize(
                                                    24,
                                                    context,
                                                  ),
                                            ),
                                          ),
                                        ),
                                        errorWidget: (context, url, error) => Container(
                                          width:
                                              MySharingPostVM.getResponsiveProductImageHeight(
                                                context,
                                              ),
                                          height:
                                              MySharingPostVM.getResponsiveProductImageHeight(
                                                context,
                                              ),
                                          color: Colors.grey[200],
                                          child: Center(
                                            child: Icon(
                                              Icons.broken_image,
                                              color: Colors.grey[400],
                                              size:
                                                  MySharingPostVM.getResponsiveFontSize(
                                                    24,
                                                    context,
                                                  ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    if (!MySharingPostVM.getSampleProduct()['isAvailable'])
                                      Positioned.fill(
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Colors.black.withValues(
                                              alpha: 0.3,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                          child: Center(
                                            child: Text(
                                              'Sold Out',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                                fontSize:
                                                    MySharingPostVM.getResponsiveFontSize(
                                                      10,
                                                      context,
                                                    ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              // Product Details
                              Expanded(
                                child: Padding(
                                  padding: EdgeInsets.all(
                                    MySharingPostVM.getResponsivePadding(
                                      16,
                                      context,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        MySharingPostVM.getSampleProduct()['name'],
                                        style: TextStyle(
                                          fontSize:
                                              MySharingPostVM.getResponsiveFontSize(
                                                16,
                                                context,
                                              ),
                                          fontWeight: FontWeight.w600,
                                          color: Colors.black87,
                                        ),
                                      ),
                                      SizedBox(
                                        height:
                                            MySharingPostVM.getResponsivePadding(
                                              4,
                                              context,
                                            ),
                                      ),
                                      Text(
                                        MySharingPostVM.getSampleProduct()['price'],
                                        style: TextStyle(
                                          fontSize:
                                              MySharingPostVM.getResponsiveFontSize(
                                                18,
                                                context,
                                              ),
                                          fontWeight: FontWeight.bold,
                                          color:
                                              MySharingPostVM.getSampleProduct()['isAvailable']
                                              ? Colors.orange
                                              : Colors.grey,
                                        ),
                                      ),
                                      SizedBox(
                                        height:
                                            MySharingPostVM.getResponsivePadding(
                                              8,
                                              context,
                                            ),
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal:
                                              MySharingPostVM.getResponsivePadding(
                                                12,
                                                context,
                                              ),
                                          vertical:
                                              MySharingPostVM.getResponsivePadding(
                                                6,
                                                context,
                                              ),
                                        ),
                                        decoration: BoxDecoration(
                                          color:
                                              MySharingPostVM.getSampleProduct()['isAvailable']
                                              ? Colors.orange
                                              : Colors.grey,
                                          borderRadius: BorderRadius.circular(
                                            20,
                                          ),
                                        ),
                                        child: Text(
                                          MySharingPostVM.getSampleProduct()['isAvailable']
                                              ? 'Buy Now'
                                              : 'Sold Out',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize:
                                                MySharingPostVM.getResponsiveFontSize(
                                                  12,
                                                  context,
                                                ),
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Extra space after product description to prevent it from being at bottom of screen (matching blindbox PostView)
              SizedBox(
                height: MySharingPostVM.getResponsivePadding(100, context),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
