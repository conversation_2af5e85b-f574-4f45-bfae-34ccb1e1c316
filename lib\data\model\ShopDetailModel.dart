class ShopDetailModel {
  final int id;
  final String productName;
  final String productImage;
  final List<String> productImages;
  final String etails; // Note: API has "etails" not "details"
  final bool isCollect;
  final bool isSale;
  final int cardDeduction;
  final int sold;
  final List<ProductSku> productSkus;
  final List<ProductInfo> productInfoList;
  final int store;

  ShopDetailModel({
    required this.id,
    required this.productName,
    required this.productImage,
    required this.productImages,
    required this.etails,
    required this.isCollect,
    required this.isSale,
    required this.cardDeduction,
    required this.sold,
    required this.productSkus,
    required this.productInfoList,
    required this.store,
  });

  /// Factory for API JSON
  factory ShopDetailModel.fromApiJson(Map<String, dynamic> json) {
    // Helper functions for parsing
    int parseInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    bool parseBool(dynamic value) {
      if (value == null) return false;
      if (value is bool) return value;
      if (value is String) return value.toLowerCase() == 'true';
      if (value is int) return value != 0;
      return false;
    }

    List<String> parseStringList(dynamic value) {
      if (value == null) return [];
      if (value is List) return value.map((e) => e.toString()).toList();
      return [];
    }

    List<ProductSku> parseProductSkus(dynamic value) {
      if (value == null) return [];
      if (value is List) {
        return value.map((item) => ProductSku.fromApiJson(item)).toList();
      }
      return [];
    }

    List<ProductInfo> parseProductInfoList(dynamic value) {
      if (value == null) return [];
      if (value is List) {
        return value.map((item) => ProductInfo.fromApiJson(item)).toList();
      }
      return [];
    }

    return ShopDetailModel(
      id: parseInt(json["id"]),
      productName: json["productName"] ?? "",
      productImage: json["productImage"] ?? "",
      productImages: parseStringList(json["productImages"]),
      etails: json["etails"] ?? "", // Note: API uses "etails"
      isCollect: parseBool(json["isCollect"]),
      isSale: parseBool(json["isSale"]),
      cardDeduction: parseInt(json["cardDeduction"]),
      sold: parseInt(json["sold"]),
      productSkus: parseProductSkus(json["productSkus"]),
      productInfoList: parseProductInfoList(json["productInfoList"]),
      store: parseInt(json["store"]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "productName": productName,
      "productImage": productImage,
      "productImages": productImages,
      "etails": etails,
      "isCollect": isCollect,
      "isSale": isSale,
      "cardDeduction": cardDeduction,
      "sold": sold,
      "productSkus": productSkus.map((sku) => sku.toJson()).toList(),
      "productInfoList": productInfoList.map((info) => info.toJson()).toList(),
      "store": store,
    };
  }

  ShopDetailModel copyWith({
    int? id,
    String? productName,
    String? productImage,
    List<String>? productImages,
    String? etails,
    bool? isCollect,
    bool? isSale,
    int? cardDeduction,
    int? sold,
    List<ProductSku>? productSkus,
    List<ProductInfo>? productInfoList,
    int? store,
  }) {
    return ShopDetailModel(
      id: id ?? this.id,
      productName: productName ?? this.productName,
      productImage: productImage ?? this.productImage,
      productImages: productImages ?? this.productImages,
      etails: etails ?? this.etails,
      isCollect: isCollect ?? this.isCollect,
      isSale: isSale ?? this.isSale,
      cardDeduction: cardDeduction ?? this.cardDeduction,
      sold: sold ?? this.sold,
      productSkus: productSkus ?? this.productSkus,
      productInfoList: productInfoList ?? this.productInfoList,
      store: store ?? this.store,
    );
  }
}

class ProductSku {
  final int productSkuId;
  final String productSkuCode;
  final String productSkuImage;
  final String productSkuName;
  final double salePrice;
  final int stock;
  final List<SkuProperty> properties;

  ProductSku({
    required this.productSkuId,
    required this.productSkuCode,
    required this.productSkuImage,
    required this.productSkuName,
    required this.salePrice,
    required this.stock,
    required this.properties,
  });

  factory ProductSku.fromApiJson(Map<String, dynamic> json) {
    int parseInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    List<SkuProperty> parseProperties(dynamic value) {
      if (value == null) return [];
      if (value is List) {
        return value.map((item) => SkuProperty.fromApiJson(item)).toList();
      }
      return [];
    }

    return ProductSku(
      productSkuId: parseInt(json["productSkuId"]),
      productSkuCode: json["productSkuCode"] ?? "",
      productSkuImage: json["productSkuImage"] ?? "",
      productSkuName: json["productSkuName"] ?? "",
      salePrice: parseDouble(json["salePrice"]),
      stock: parseInt(json["stock"]),
      properties: parseProperties(json["properties"]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "productSkuId": productSkuId,
      "productSkuCode": productSkuCode,
      "productSkuImage": productSkuImage,
      "productSkuName": productSkuName,
      "salePrice": salePrice,
      "stock": stock,
      "properties": properties.map((prop) => prop.toJson()).toList(),
    };
  }
}

class SkuProperty {
  final String propertyKey;
  final String propertyValue;

  SkuProperty({
    required this.propertyKey,
    required this.propertyValue,
  });

  factory SkuProperty.fromApiJson(Map<String, dynamic> json) {
    return SkuProperty(
      propertyKey: json["propertyKey"] ?? "",
      propertyValue: json["propertyValue"] ?? "",
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "propertyKey": propertyKey,
      "propertyValue": propertyValue,
    };
  }
}

class ProductInfo {
  final String infoKey;
  final String infoValue;

  ProductInfo({
    required this.infoKey,
    required this.infoValue,
  });

  factory ProductInfo.fromApiJson(Map<String, dynamic> json) {
    return ProductInfo(
      infoKey: json["infoKey"] ?? "",
      infoValue: json["infoValue"] ?? "",
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "infoKey": infoKey,
      "infoValue": infoValue,
    };
  }
}

// Keep ProductReview for backwards compatibility if needed
class ProductReview {
  final int orderSource;
  final bool isAnonymous;
  final String avatar;
  final String nickname;
  final String content;
  final double score;
  final ReviewProduct product;
  final List<String> pictures;
  final String createdTime;

  ProductReview({
    required this.orderSource,
    required this.isAnonymous,
    required this.avatar,
    required this.nickname,
    required this.content,
    required this.score,
    required this.product,
    required this.pictures,
    required this.createdTime,
  });

  factory ProductReview.fromApiJson(Map<String, dynamic> json) {
    int parseInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    bool parseBool(dynamic value) {
      if (value == null) return false;
      if (value is bool) return value;
      if (value is String) return value.toLowerCase() == 'true';
      if (value is int) return value != 0;
      return false;
    }

    List<String> parseStringList(dynamic value) {
      if (value == null) return [];
      if (value is List) return value.map((e) => e.toString()).toList();
      return [];
    }

    return ProductReview(
      orderSource: parseInt(json["orderSource"]),
      isAnonymous: parseBool(json["isAnonymous"]),
      avatar: json["avatar"] ?? "",
      nickname: json["nickname"] ?? "",
      content: json["content"] ?? "",
      score: parseDouble(json["score"]),
      product: ReviewProduct.fromApiJson(json["product"] ?? {}),
      pictures: parseStringList(json["pictures"]),
      createdTime: json["createdTime"] ?? "",
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "orderSource": orderSource,
      "isAnonymous": isAnonymous,
      "avatar": avatar,
      "nickname": nickname,
      "content": content,
      "score": score,
      "product": product.toJson(),
      "pictures": pictures,
      "createdTime": createdTime,
    };
  }
}

class ReviewProduct {
  final int productSpuId;
  final int productSkuId;
  final int cartId;
  final int quantity;

  ReviewProduct({
    required this.productSpuId,
    required this.productSkuId,
    required this.cartId,
    required this.quantity,
  });

  factory ReviewProduct.fromApiJson(Map<String, dynamic> json) {
    int parseInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    return ReviewProduct(
      productSpuId: parseInt(json["productSpuId"]),
      productSkuId: parseInt(json["productSkuId"]),
      cartId: parseInt(json["cartId"]),
      quantity: parseInt(json["quantity"]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "productSpuId": productSpuId,
      "productSkuId": productSkuId,
      "cartId": cartId,
      "quantity": quantity,
    };
  }
}

// Add a response wrapper class for the reviews API
class ProductReviewResponse {
  final int status;
  final String message;
  final ProductReviewData data;
  final int timestamp;

  ProductReviewResponse({
    required this.status,
    required this.message,
    required this.data,
    required this.timestamp,
  });

  factory ProductReviewResponse.fromApiJson(Map<String, dynamic> json) {
    return ProductReviewResponse(
      status: json["status"] ?? 0,
      message: json["message"] ?? "",
      data: ProductReviewData.fromApiJson(json["data"] ?? {}),
      timestamp: json["timestamp"] ?? 0,
    );
  }

  // Add this factory constructor for empty responses
  factory ProductReviewResponse.empty() {
    return ProductReviewResponse(
      status: 200,
      message: "No reviews available",
      data: ProductReviewData(
        list: [],
        totalCount: 0,
        pageSize: 10,
        totalPage: 0,
        currentPage: 1,
      ),
      timestamp: 0
    );
  }
}

class ProductReviewData {
  final int totalCount;
  final int pageSize;
  final int totalPage;
  final int currentPage;
  final List<ProductReview> list;

  ProductReviewData({
    required this.totalCount,
    required this.pageSize,
    required this.totalPage,
    required this.currentPage,
    required this.list,
  });

  factory ProductReviewData.fromApiJson(Map<String, dynamic> json) {
    int parseInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    List<ProductReview> parseReviewList(dynamic value) {
      if (value == null) return [];
      if (value is List) {
        return value.map((item) => ProductReview.fromApiJson(item)).toList();
      }
      return [];
    }

    return ProductReviewData(
      totalCount: parseInt(json["totalCount"]),
      pageSize: parseInt(json["pageSize"]),
      totalPage: parseInt(json["totalPage"]),
      currentPage: parseInt(json["currentPage"]),
      list: parseReviewList(json["list"]),
    );
  }
}