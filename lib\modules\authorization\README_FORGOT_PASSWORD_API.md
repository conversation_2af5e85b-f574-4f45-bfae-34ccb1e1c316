# Forgot Password API Integration

This document describes the integration of the forgot password API endpoint for the Authorization module.

## API Endpoint

**POST** `/user/forgot/password`

**Base URL:** `https://api.luckymall.com.my`

**Request Body:**
```json
{
  "country": 0,
  "mobile": "",
  "code": "",
  "password": ""
}
```

**Response Structure:**
```json
{
  "status": 0,
  "message": "",
  "data": {},
  "timestamp": 0
}
```

## Implementation Files

### 1. Service
- **File:** `lib/modules/authorization/service/AuthHttpService.dart`
- **Method:** `forgotPassword()`
- **Purpose:** Handles API calls to the forgot password endpoint

### 2. View
- **File:** `lib/modules/authorization/view/ForgotPassword.dart`
- **Purpose:** UI for the forgot password flow
- **Features:** Phone number input, verification code, new password input, password confirmation

### 3. Test
- **File:** `test/forgot_password_api_test.dart`
- **Purpose:** Unit tests for the forgot password API integration

## Usage Examples

### 1. Basic API Call

```dart
import '../service/AuthHttpService.dart';

final service = AuthHttpService.instance;

try {
  final response = await service.forgotPassword(
    country: 60,
    mobile: '123456789',
    code: '123456',
    password: 'newPassword123',
  );
  
  if (response['status'] == 0) {
    print('Password changed successfully');
  } else {
    print('Error: ${response['message']}');
  }
} catch (e) {
  print('Exception: $e');
}
```

### 2. Using in ForgotPassword View

The `ForgotPassword` view automatically handles:
- Input validation (phone number, verification code, password, password confirmation)
- API calls to reset the password
- Success/error message display
- Navigation back to login page on success
- Loading states during API calls

## API Response Handling

### Success Response (status: 0)
- Shows success message
- Automatically navigates to login page
- Clears loading state

### Error Response (status: != 0)
- Shows error message from API
- Stays on the forgot password page
- Clears loading state

### Network Errors
- Gracefully handles network failures
- Shows appropriate error messages
- Maintains consistent response structure

## Validation

The implementation includes the following validations:
1. **Phone Number**: Must not be empty
2. **Verification Code**: Must not be empty
3. **Password**: Must not be empty
4. **Password Confirmation**: Must match the password field

## Error Handling

The service follows the same error handling pattern as other authentication methods:
- Catches `DioException` for network-related errors
- Catches general exceptions for unexpected errors
- Always returns a consistent response structure
- Includes proper error messages and status codes

## Testing

Run the tests with:
```bash
flutter test test/forgot_password_api_test.dart
```

The tests verify:
- Response structure consistency
- Error handling behavior
- API integration functionality
