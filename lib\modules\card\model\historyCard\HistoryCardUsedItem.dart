class HistoryCardUsedItem {
  final String id;
  final String name;
  final String imageUrl;
  final int quantity;

  HistoryCardUsedItem({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.quantity,
  });

  factory HistoryCardUsedItem.fromJson(Map<String, dynamic> json) {
    return HistoryCardUsedItem(
      id: json['id'],
      name: json['name'],
      imageUrl: json['imageUrl'],
      quantity: json['quantity'] ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'imageUrl': imageUrl,
      'quantity': quantity,
    };
  }
}
