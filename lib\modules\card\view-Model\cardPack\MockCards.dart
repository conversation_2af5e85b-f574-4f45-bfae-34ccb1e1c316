import '../../model/cardPack/CardItem.dart';

final List<CardItem> allCardCatalog = [
  // Royal - Fantasy Series
  CardItem(
    id: 'king',
    name: 'King',
    category: 'royal',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/royals/king.png',
    rank: 'UR',
    quantity: 1,
    isOwned: true,
  ),
  CardItem(
    id: 'queen',
    name: 'Queen',
    category: 'royal',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/royals/queen.png',
    rank: 'UR',
    quantity: 0,
    isOwned: false,
  ),
  // Knight - Fantasy Series
  CardItem(
    id: 'ColdSweatKnight',
    name: 'Cold Sweat Knight',
    category: 'knight',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/knight/ColdSweatKnight.png',
    rank: 'R',
    quantity: 2,
    isOwned: true,
  ),
  CardItem(
    id: 'CoolKnight',
    name: 'Cool Knight',
    category: 'knight',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/knight/CoolKnight.png',
    rank: 'SR',
    quantity: 1,
    isOwned: true,
  ),
  CardItem(
    id: 'CryingKnight',
    name: 'Crying Knight',
    category: 'knight',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/knight/CryingKnight.png',
    rank: 'SR',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'HappyKnight',
    name: 'Happy Knight',
    category: 'knight',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/knight/HappyKnight.png',
    rank: 'SR',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'DizzyKnight',
    name: 'Dizzy Knight',
    category: 'knight',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/knight/DizzyKnight.png',
    rank: 'R',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'LaughingKnight',
    name: 'Laughing Knight',
    category: 'knight',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/knight/LaughingKnight.png',
    rank: 'SSR',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'LovelyKnight',
    name: 'Lovely Knight',
    category: 'knight',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/knight/LovelyKnight.png',
    rank: 'SSR',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'PoutingKnight',
    name: 'Pouting Knight',
    category: 'knight',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/knight/PoutingKnight.png',
    rank: 'R',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'ScaredKnight',
    name: 'Scared Knight',
    category: 'knight',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/knight/ScaredKnight.png',
    rank: 'SR',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'ThinkingKnight',
    name: 'Thinking Knight',
    category: 'knight',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/knight/ThinkingKnight.png',
    rank: 'R',
    quantity: 0,
    isOwned: false,
  ),
  
  // Wizard - Fantasy Series
  CardItem(
    id: 'CoolWizard',
    name: 'Cool Wizard',
    category: 'wizard',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/wizard/CoolWizard.png',
    rank: 'SR',
    quantity: 0,
    isOwned: false,
  ),
   CardItem(
    id: 'CryingWizard',
    name: 'Crying Wizard',
    category: 'wizard',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/wizard/CryingWizard.png',
    rank: 'R',
    quantity: 0,
    isOwned: false,
  ),
   CardItem(
    id: 'DizzyWizard',
    name: 'Dizzy Wizard',
    category: 'wizard',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/wizard/DizzyWizard.png',
    rank: 'R',
    quantity: 0,
    isOwned: false,
  ),
   CardItem(
    id: 'LovelyWizard',
    name: 'Lovely Wizard',
    category: 'wizard',
    series: 'fantasy',
    imagePath: 'assets/images/card/cardType/fantasy/wizard/LovelyWizard.png',
    rank: 'SR',
    quantity: 0,
    isOwned: false,
  ),
];
