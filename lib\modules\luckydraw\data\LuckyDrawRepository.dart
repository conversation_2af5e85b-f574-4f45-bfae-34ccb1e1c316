import '../model/LuckyDrawProduct.dart';
import '../model/LuckyDrawRecord.dart';
import '../model/ParticipationRecord.dart';
import '../model/CharityFund.dart';
import '../model/CommunityPost.dart';
import '../model/PrizeRevealProduct.dart';
import '../model/BatchInfo.dart';
import '../model/LatestDrawSummary.dart';
import '../model/OpenSoonProduct.dart';
import '../model/LuckyDrawCategory.dart';
import 'LuckyDrawService.dart';
import 'LatestDrawRecordService.dart';
import 'OpenSoonProductService.dart';
import 'CharityFundService.dart';

abstract class LuckyDrawRepository {
  // Products
  Future<List<LuckyDrawProduct>> fetchProducts({
    String? category,
    int? minPoints,
    int? maxPoints,
    int? page,
    int? limit,
  });
  Future<List<LuckyDrawProduct>> searchProducts({
    required String query,
    String? category,
    String? sortBy,
    String? sortOrder,
    int? page,
    int? limit,
  });
  Future<LuckyDrawProduct> fetchProductDetails(String productId);

  // Group Purchase
  Future<List<LuckyDrawProduct>> fetchGroupPurchaseProducts({String? category});

  // Products by point range
  Future<List<LuckyDrawProduct>> fetchProductsByPointRange({
    required int minPoints,
    required int maxPoints,
    String? category,
    int? page,
    int? limit,
  });

  // Get products with pagination info
  Future<Map<String, dynamic>> fetchProductsWithPagination({
    String? category,
    int? minPoints,
    int? maxPoints,
    int? page,
    int? limit,
    String? sortBy,
    bool? ascending,
  });

  // Participation
  Future<Map<String, dynamic>> participateInLuckyDraw({
    required String productId,
    required int pointsToSpend,
    required int shares,
  });
  Future<List<ParticipationRecord>> fetchParticipationRecords({
    String? filter,
    String? status,
    int? page,
    int? limit,
  });
  Future<ParticipationRecord> fetchParticipationDetails(String participationId);

  // Prize Reveal
  Future<bool> hasPendingPrizeReveal();
  Future<List<PrizeRevealProduct>> fetchPrizeRevealProducts({
    String? userId,
    String? status,
  });
  Future<Map<String, dynamic>> joinNextBatch({
    required String productId,
    required int currentBatch,
    required int pointsToSpend,
    required int shares,
  });
  Future<Map<String, dynamic>> fetchCountdown(String participationId);

  // Prize Management
  Future<Map<String, dynamic>> checkPendingPrizes();
  Future<Map<String, dynamic>> claimPrize({
    required String participationId,
    required String claimType,
    Map<String, String>? deliveryAddress,
  });
  Future<List<Map<String, dynamic>>> fetchPrizeClaims({
    String? status,
    int? page,
    int? limit,
  });

  // Records and History
  Future<List<LuckyDrawRecord>> fetchLatestRecords({int? limit, String? type});
  Future<List<LatestDrawSummary>> fetchLatestDrawSummary({int? limit});
  Future<List<LuckyDrawRecord>> fetchWinningHistory({
    int? page,
    int? limit,
    String? period,
  });

  /// Product-specific winning history
  Future<List<LuckyDrawRecord>> fetchProductWinningHistory({
    required int goodsPointId,
    int? page,
    int? limit,
  });

  // Charity Fund
  Future<CharityFund> fetchCharityFund();

  // User Points
  Future<Map<String, dynamic>> fetchUserPoints();

  // Categories
  Future<List<LuckyDrawCategory>> fetchCategories();

  // Batches
  Future<List<BatchInfo>> fetchCurrentBatches();
  Future<Map<String, dynamic>> fetchBatchResults(String batchNumber);

  // Community
  Future<List<CommunityPost>> fetchCommunityPosts({
    int? page,
    int? limit,
    String? sortBy,
  });
  Future<CommunityPost> fetchCommunityPostDetails(String postId);
  Future<Map<String, dynamic>> createCommunityPost({
    required String caption,
    required String postImage,
    String? productName,
    String? prizeType,
  });
  Future<Map<String, dynamic>> likePost(String postId, bool isLike);
  Future<void> deleteCommunityPost(String postId);

  // Reviews
  Future<List<Map<String, dynamic>>> fetchReviewCategories();
  Future<List<Map<String, dynamic>>> fetchReviews({
    String? productId,
    String? category,
    int? rating,
    int? page,
    int? limit,
  });

  // Opening Soon
  Future<List<OpenSoonProduct>> fetchOpeningSoonProducts({
    String? category,
    int? minPoints,
    int? limit,
  });

  // Mega prizes
  Future<List<dynamic>> fetchMegaPrizes();
}

class MockLuckyDrawRepository implements LuckyDrawRepository {
  @override
  Future<List<LuckyDrawProduct>> fetchProducts({
    String? category,
    int? minPoints,
    int? maxPoints,
    int? page,
    int? limit,
  }) async {
    try {
      // Convert category name to category ID if provided
      int? categoryId;
      if (category != null && category != 'ALL') {
        categoryId = await luckyDrawService.getCategoryIdByName(category);
      }

      // Convert point range if provided
      List<int>? totalAmount;
      if (minPoints != null || maxPoints != null) {
        totalAmount = [minPoints ?? 0, maxPoints ?? 999999];
      }

      final result = await luckyDrawService.getLuckyDrawProducts(
        categoryId: categoryId,
        totalAmount: totalAmount,
        page: page,
        limit: limit,
      );

      return (result['products'] as List<LuckyDrawProduct>?) ?? [];
    } catch (e) {
      // Fallback to empty list if API fails
      return [];
    }
  }

  @override
  Future<List<LuckyDrawRecord>> fetchLatestRecords({
    int? limit,
    String? type,
  }) async {
    try {
      final result = await latestDrawRecordService.getLatestDrawRecords();
      final records = result['records'] as List<LuckyDrawRecord>;

      // Apply limit if specified
      if (limit != null && limit > 0) {
        return records.take(limit).toList();
      }

      return records;
    } catch (e) {
      print('Repository: Error fetching latest records: $e');
      // No fallback - let the error propagate to show real API issues
      rethrow;
    }
  }

  @override
  Future<List<LuckyDrawRecord>> fetchWinningHistory({
    int? page,
    int? limit,
    String? period,
  }) async {
    try {
      // Use the same API service for consistency
      return await fetchLatestRecords(limit: limit);
    } catch (e) {
      print('Repository: Error fetching winning history: $e');
      // No fallback - let the error propagate to show real API issues
      rethrow;
    }
  }

  @override
  Future<List<LuckyDrawRecord>> fetchProductWinningHistory({
    required int goodsPointId,
    int? page,
    int? limit,
  }) async {
    try {
      final result = await latestDrawRecordService.getProductWinningHistory(
        goodsPointId: goodsPointId,
        page: page,
        limit: limit,
      );
      return (result['records'] as List<LuckyDrawRecord>?) ?? [];
    } catch (e) {
      print('Repository: Error fetching product winning history: $e');
      rethrow;
    }
  }

  @override
  Future<List<ParticipationRecord>> fetchParticipationRecords({
    String? filter,
    String? status,
    int? page,
    int? limit,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));
    final List<Map<String, dynamic>> raw = [
      // Claim prize (normal)
      {
        'productName': 'Vacuum S8000',
        'imageUrl':
            'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=400&fit=crop',
        'date': '2025-06-27',
        'batchNumber': '004',
        'participationPoints': '120 points',
        'state': 'claimPrize',
        'prizeType': 'normal',
      },
      // Claim prize (TNG voucher)
      {
        'productName': 'TNG RM20 Voucher',
        'imageUrl':
            'https://images.getbats.com/item/ecget/20230613_9e4c1c2b6aff4a43f3c00c8619e7c6ef.png',
        'date': '2025-06-27',
        'batchNumber': '004',
        'participationPoints': '180 points',
        'state': 'claimPrize',
        'prizeType': 'tngVoucher',
      },
      // Waiting
      {
        'productName': 'Wireless Earbuds Pro',
        'imageUrl':
            'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=400&h=400&fit=crop',
        'date': '2025-06-29',
        'batchNumber': '005',
        'participationPoints': '75 points',
        'state': 'waiting',
        'prizeType': 'normal',
      },
      // Not win
      {
        'productName': 'Gaming Mouse Razer DeathAdder V3',
        'imageUrl':
            'https://hnsgsfp.imgix.net/9/images/detailed/109/Razer_Cobra_Pro_Compact_Wireless_Gaming_Mouse_with_Underglow_Lighting_-_Black_(RZ01-04660100-R3)_01.JPG?fit=fill&bg=0FFF&w=1536&h=901&auto=format,compress',
        'date': '2025-06-28',
        'batchNumber': '001',
        'participationPoints': '50 points',
        'state': 'notWin',
        'prizeType': 'normal',
      },
      // Already claimed
      {
        'productName': 'Premium Coffee Maker',
        'imageUrl':
            'https://www.plushieshop.co.uk/wp-content/uploads/2021/05/1111344994175-e1624151494656.jpg',
        'date': '2025-06-25',
        'batchNumber': '002',
        'participationPoints': '95 points',
        'state': 'alreadyClaimed',
        'prizeType': 'normal',
      },
      // ...existing items...
    ];
    final list = raw.map((e) => ParticipationRecord.fromJson(e)).toList();
    if (filter == 'My Prize') {
      return list
          .where(
            (r) =>
                r.state == ParticipationCardState.claimPrize ||
                r.state == ParticipationCardState.alreadyClaimed,
          )
          .toList();
    }
    return list;
  }

  @override
  Future<bool> hasPendingPrizeReveal() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return false;
  }

  // NEW METHODS - Stub implementations for demo/testing
  @override
  Future<List<LuckyDrawProduct>> searchProducts({
    required String query,
    String? category,
    String? sortBy,
    String? sortOrder,
    int? page,
    int? limit,
  }) async {
    try {
      // Convert category name to category ID if provided
      int? categoryId;
      if (category != null && category != 'ALL') {
        categoryId = await luckyDrawService.getCategoryIdByName(category);
      }

      // Convert sort order to boolean
      bool? ascending;
      if (sortOrder != null) {
        ascending = sortOrder.toLowerCase() == 'asc';
      }

      final result = await luckyDrawService.getLuckyDrawProducts(
        limit: limit,
        page: page,
        sortBy: sortBy,
        ascending: ascending,
        categoryId: categoryId,
        productSkuName: query,
      );

      return (result['products'] as List<LuckyDrawProduct>?) ?? [];
    } catch (e) {
      // Fallback to empty list if API fails
      return [];
    }
  }

  @override
  Future<LuckyDrawProduct> fetchProductDetails(String productId) async {
    try {
      final productIdInt = int.tryParse(productId);
      if (productIdInt != null) {
        final product = await luckyDrawService.getProductDetail(productIdInt);
        if (product != null) {
          return product;
        }
      }

      // Fallback to fetching from products list if detail fetch fails
      final products = await fetchProducts();
      return products.firstWhere(
        (p) => p.id.toString() == productId,
        orElse: () => products.first,
      );
    } catch (e) {
      // Fallback to first product if all else fails
      final products = await fetchProducts();
      return products.first;
    }
  }

  @override
  Future<List<LuckyDrawProduct>> fetchGroupPurchaseProducts({
    String? category,
  }) async {
    // For now, group purchase products are the same as regular products
    // This can be enhanced later with specific group purchase logic
    return fetchProducts(category: category);
  }

  @override
  Future<List<LuckyDrawProduct>> fetchProductsByPointRange({
    required int minPoints,
    required int maxPoints,
    String? category,
    int? page,
    int? limit,
  }) async {
    try {
      // Convert category name to category ID if provided
      int? categoryId;
      if (category != null && category != 'ALL') {
        categoryId = await luckyDrawService.getCategoryIdByName(category);
      }

      final result = await luckyDrawService.getLuckyDrawProductsByPointRange(
        minPoints: minPoints,
        maxPoints: maxPoints,
        categoryId: categoryId,
        page: page,
        limit: limit,
      );

      return (result['products'] as List<LuckyDrawProduct>?) ?? [];
    } catch (e) {
      // Fallback to empty list if API fails
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>> fetchProductsWithPagination({
    String? category,
    int? minPoints,
    int? maxPoints,
    int? page,
    int? limit,
    String? sortBy,
    bool? ascending,
  }) async {
    try {
      print(
        'Repository: fetchProductsWithPagination called with category: $category, minPoints: $minPoints, maxPoints: $maxPoints',
      );

      // Convert category name to category ID if provided
      int? categoryId;
      if (category != null && category != 'ALL') {
        categoryId = await luckyDrawService.getCategoryIdByName(category);
        print('Repository: Converted category "$category" to ID: $categoryId');
      }

      // Convert point range if provided
      List<int>? totalAmount;
      if (minPoints != null || maxPoints != null) {
        totalAmount = [minPoints ?? 0, maxPoints ?? 999999];
        print('Repository: Using point range: $totalAmount');
      }

      print(
        'Repository: Calling service with categoryId: $categoryId, totalAmount: $totalAmount, page: $page, limit: $limit',
      );

      final result = await luckyDrawService.getLuckyDrawProducts(
        categoryId: categoryId,
        totalAmount: totalAmount,
        page: page,
        limit: limit,
        sortBy: sortBy,
        ascending: ascending,
      );

      print('Repository: Service returned: $result');
      return result;
    } catch (e) {
      print('Repository: Error in fetchProductsWithPagination: $e');
      // Fallback to empty result if API fails
      return {
        'products': <LuckyDrawProduct>[],
        'totalCount': 0,
        'pageSize': 10,
        'totalPage': 1,
        'currentPage': 1,
      };
    }
  }

  @override
  Future<Map<String, dynamic>> participateInLuckyDraw({
    required String productId,
    required int pointsToSpend,
    required int shares,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return {
      'participationId': 'part_${DateTime.now().millisecondsSinceEpoch}',
      'productId': productId,
      'pointsSpent': pointsToSpend,
      'shares': shares,
      'status': 'waiting',
    };
  }

  @override
  Future<ParticipationRecord> fetchParticipationDetails(
    String participationId,
  ) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final records = await fetchParticipationRecords();
    return records.first;
  }

  @override
  Future<List<PrizeRevealProduct>> fetchPrizeRevealProducts({
    String? userId,
    String? status,
  }) async {
    try {
      // Use the real API service instead of mock data
      return await luckyDrawService.getPrizeRevealProducts();
    } catch (e) {
      print('Repository: Error fetching prize reveal products: $e');
      // Fallback to empty list if API fails
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>> joinNextBatch({
    required String productId,
    required int currentBatch,
    required int pointsToSpend,
    required int shares,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return {
      'participationId': 'part_${DateTime.now().millisecondsSinceEpoch}',
      'nextBatch': currentBatch + 1,
      'status': 'waiting',
    };
  }

  @override
  Future<Map<String, dynamic>> fetchCountdown(String participationId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return {'countdownSeconds': 3600, 'status': 'waiting'};
  }

  @override
  Future<Map<String, dynamic>> checkPendingPrizes() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return {'hasPendingReveals': false, 'pendingCount': 0};
  }

  @override
  Future<Map<String, dynamic>> claimPrize({
    required String participationId,
    required String claimType,
    Map<String, String>? deliveryAddress,
  }) async {
    await Future.delayed(const Duration(milliseconds: 800));
    return {
      'claimId': 'claim_${DateTime.now().millisecondsSinceEpoch}',
      'status': 'processing',
    };
  }

  @override
  Future<List<Map<String, dynamic>>> fetchPrizeClaims({
    String? status,
    int? page,
    int? limit,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return [];
  }

  @override
  Future<List<LatestDrawSummary>> fetchLatestDrawSummary({int? limit}) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return [
      LatestDrawSummary(
        itemName: 'Wireless Headphones',
        imageUrl:
            'https://images.unsplash.com/photo-1505740420928-5e560c06d30e',
        winningId: 'WIN001',
        dateTime: '2025-01-15T10:30:00Z',
        winnerName: 'John***',
        avatarUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
      ),
    ];
  }

  @override
  Future<CharityFund> fetchCharityFund() async {
    try {
      // Use the real charity fund service instead of mock data
      return await charityFundService.getCharityFund();
    } catch (e) {
      print('Repository: Error fetching charity fund: $e');
      // Fallback to empty charity fund if API fails
      return CharityFund(totalAmount: 0, lastUpdated: 0);
    }
  }

  @override
  Future<Map<String, dynamic>> fetchUserPoints() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {'currentBalance': 1500, 'totalEarned': 5000, 'totalSpent': 3500};
  }

  @override
  Future<List<LuckyDrawCategory>> fetchCategories() async {
    try {
      // Use the real API service instead of mock data
      return await luckyDrawService.getCategories();
    } catch (e) {
      print('Repository: Error fetching categories: $e');
      // Fallback to empty list if API fails
      return [];
    }
  }

  @override
  Future<List<BatchInfo>> fetchCurrentBatches() async {
    await Future.delayed(const Duration(milliseconds: 400));
    return [];
  }

  @override
  Future<Map<String, dynamic>> fetchBatchResults(String batchNumber) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {'batchNumber': batchNumber, 'status': 'completed', 'winners': []};
  }

  @override
  Future<List<CommunityPost>> fetchCommunityPosts({
    int? page,
    int? limit,
    String? sortBy,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return [
      CommunityPost(
        id: 'post1',
        userName: 'TestUser',
        avatarUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
        postImage:
            'https://images.unsplash.com/photo-1523275335684-37898b6baf30',
        caption: 'Just won this amazing prize!',
        timeAgo: '2h',
        likeCount: 15,
        isLiked: false,
        createdAt: DateTime.now().toIso8601String(),
        productName: 'Wireless Headphones',
        prizeType: 'normal',
      ),
    ];
  }

  @override
  Future<CommunityPost> fetchCommunityPostDetails(String postId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final posts = await fetchCommunityPosts();
    return posts.first;
  }

  @override
  Future<Map<String, dynamic>> createCommunityPost({
    required String caption,
    required String postImage,
    String? productName,
    String? prizeType,
  }) async {
    await Future.delayed(const Duration(milliseconds: 600));
    return {
      'postId': 'post_${DateTime.now().millisecondsSinceEpoch}',
      'message': 'Post created successfully',
    };
  }

  @override
  Future<Map<String, dynamic>> likePost(String postId, bool isLike) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return {'likeCount': isLike ? 16 : 14, 'isLiked': isLike};
  }

  @override
  Future<void> deleteCommunityPost(String postId) async {
    await Future.delayed(const Duration(milliseconds: 300));
  }

  @override
  Future<List<Map<String, dynamic>>> fetchReviewCategories() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return [
      {'id': 'all', 'name': 'All', 'displayName': 'All Reviews'},
      {
        'id': 'mainProduct',
        'name': 'Main Product',
        'displayName': 'Main Product',
      },
      {
        'id': 'pointProduct',
        'name': 'Point Product',
        'displayName': 'Point Product',
      },
    ];
  }

  @override
  Future<List<Map<String, dynamic>>> fetchReviews({
    String? productId,
    String? category,
    int? rating,
    int? page,
    int? limit,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return [];
  }

  @override
  Future<List<OpenSoonProduct>> fetchOpeningSoonProducts({
    String? category,
    int? minPoints,
    int? limit,
  }) async {
    try {
      // Use the real API service instead of mock data
      return await openSoonProductService.getOpeningSoonProducts();
    } catch (e) {
      print('Repository: Error fetching opening soon products: $e');
      // Fallback to empty list if API fails
      return [];
    }
  }

  @override
  Future<List<dynamic>> fetchMegaPrizes() async {
    try {
      return await luckyDrawService.getMegaPrizes();
    } catch (e) {
      print('Repository: Error fetching mega prizes: $e');
      return [];
    }
  }

  // All methods have been implemented above
}
