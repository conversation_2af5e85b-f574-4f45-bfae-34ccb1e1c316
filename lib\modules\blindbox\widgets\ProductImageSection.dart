import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

class ProductImageSection extends StatelessWidget {
  final List<String> imageUrls;
  final int currentImageIndex;
  final Function(int) onImageChanged;
  final bool isFavorite;
  final VoidCallback onFavoritePressed;

  const ProductImageSection({
    super.key,
    this.imageUrls = const [],
    this.currentImageIndex = 0,
    required this.onImageChanged,
    required this.isFavorite,
    required this.onFavoritePressed,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  @override
  Widget build(BuildContext context) {
    // Use picsum.photos as fallback images if imageUrls is empty
    final List<String> displayImages = imageUrls.isNotEmpty
        ? imageUrls
        : List.generate(3, (i) => 'https://picsum.photos/  {1000 + i * 333}');

    return LayoutBuilder(
      builder: (context, constraints) {
        final double maxWidth = 500;
        final double maxHeight = 400;
        final double width = constraints.maxWidth.clamp(0, maxWidth);
        final double height = width * 0.75;
        final double finalHeight = height > maxHeight ? maxHeight : height;
        return Center(
          child: SizedBox(
            width: width,
            height: finalHeight,
            child: Stack(
              children: [
                // Image PageView
                PageView.builder(
                  itemCount: displayImages.length,
                  onPageChanged: onImageChanged,
                  itemBuilder: (context, index) {
                    final imageUrl = displayImages[index];
                    return CachedNetworkImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      placeholder: (context, url) => Center(
                        child: Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: double.infinity,
                            height: double.infinity,
                            color: Colors.grey[300]!,
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) =>
                          _buildPlaceholderImage(context),
                    );
                  },
                ),
                // Page indicator
                if (displayImages.length > 1)
                  Positioned(
                    bottom: getResponsiveFontSize(16, context),
                    left: 0,
                    right: 0,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        displayImages.length,
                        (index) => Container(
                          width: getResponsiveFontSize(8, context),
                          height: getResponsiveFontSize(8, context),
                          margin: EdgeInsets.symmetric(
                            horizontal: getResponsiveFontSize(4, context),
                          ),
                          decoration: BoxDecoration(
                            color: index == currentImageIndex
                                ? Colors.white
                                : Colors.white.withValues(alpha: 0.5),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlaceholderImage(BuildContext context) {
    return Container(
      color: Colors.grey[200],
      child: Center(
        child: Icon(
          Icons.broken_image,
          size: getResponsiveFontSize(60, context),
          color: Colors.grey,
        ),
      ),
    );
  }
}
