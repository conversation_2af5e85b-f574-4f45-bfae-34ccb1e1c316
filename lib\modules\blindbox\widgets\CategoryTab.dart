import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CategoryTab extends StatelessWidget {
  final IconData? icon;
  final String? iconAsset;
  final String label;
  final bool isSelected;

  const CategoryTab({
    super.key,
    this.icon,
    this.iconAsset,
    required this.label,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final Color selectedColor = isSelected ? Colors.amber[700]! : Colors.white;
    final Color borderColor = Colors.amber;
    final Color iconColor = isSelected ? Colors.black : Colors.amber[700]!;
    final Color textColor = isSelected ? Colors.black87 : Colors.black;
    final FontWeight textWeight = isSelected
        ? FontWeight.w600
        : FontWeight.w500;

    return SizedBox(
      width: 72,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: selectedColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: borderColor, width: 2),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: iconAsset != null
                  ? SvgPicture.asset(
                      iconAsset!,
                      colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
                      width: 28,
                      height: 28,
                    )
                  : Icon(icon ?? Icons.category, color: iconColor, size: 28),
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 32,
            alignment: Alignment.topCenter,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 11,
                color: textColor,
                fontWeight: textWeight,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
