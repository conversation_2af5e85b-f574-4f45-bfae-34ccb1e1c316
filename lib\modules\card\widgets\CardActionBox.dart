import 'package:flutter/material.dart';

class CardActionBox extends StatefulWidget {
  final String iconAssetPath;
  final String label;
  final VoidCallback onTap;

  const CardActionBox({
    super.key,
    required this.iconAssetPath,
    required this.label,
    required this.onTap,
  });

  @override
  State<CardActionBox> createState() => _CardActionBoxState();
}

class _CardActionBoxState extends State<CardActionBox> {
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double baseWidth = 390;
    final double scale = screenWidth / baseWidth;

    final double iconWidth = 65 * scale;
    final double iconHeight = 60 * scale;
    final double fontSize = 14 * scale.clamp(0.85, 1.1);
    final double verticalPadding = 10 * scale.clamp(0.8, 1.2);
    final double horizontalPadding = 20 * scale.clamp(0.8, 1.2);

    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: widget.onTap,
          splashColor: Colors.amber.shade100,
          highlightColor: Colors.transparent,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            padding: EdgeInsets.symmetric(
              vertical: verticalPadding,
              horizontal: horizontalPadding,
            ),
            decoration: BoxDecoration(
              color: _isHovering
                  ? const Color.fromARGB(255, 252, 224, 124) // Light hover color
                  : Colors.white,           // Default white background
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFFFBF00),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: _isHovering ? 8 : 4,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  widget.iconAssetPath,
                  width: iconWidth,
                  height: iconHeight,
                ),
                const SizedBox(height: 8),
                Text(
                  widget.label,
                  style: TextStyle(
                    color: const Color(0xFF474747),
                    fontSize: fontSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
