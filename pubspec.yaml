name: luckymall
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  http: ^1.1.0

  # UI & Components
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.2.0
  shimmer: ^3.0.0
  lottie: ^3.3.1
  carousel_slider: ^5.1.1
  flutter_switch: ^0.3.2
  flutter_staggered_grid_view: ^0.7.0
  flutter_podium: ^1.0.5
  flutter_html: ^3.0.0

  # State Management & Routing
  flutter_riverpod: ^2.6.1
  provider: ^6.1.1
  go_router: ^16.0.0

  # Networking & JSON
  dio: ^5.8.0+1
  json_annotation: ^4.9.0
  freezed_annotation: ^3.1.0

  # Utilities
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2
  camera: ^0.11.0+1
  path_provider: ^2.1.5
  permission_handler: ^12.0.1
  connectivity_plus: ^6.1.4
  shared_preferences: ^2.5.3
  flutter_secure_storage: ^9.2.2
  country_flags: ^3.3.0
  intl: ^0.20.2
  video_player: ^2.10.0
  intl_phone_field: ^3.2.0
  share_plus:  ^7.2.1 
  mobile_scanner: ^4.0.1  
  jwt_decoder: ^2.0.1
  url_launcher: ^6.3.1
  pointycastle: ^3.9.1
  asn1lib: ^1.6.5
  background: ^1.3.1
  tencentcloud_cos_sdk_plugin: ^1.2.6
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  build_runner: ^2.5.4
  freezed: ^3.1.0
  json_serializable: ^6.9.5
  mocktail: ^1.0.4
 
flutter:
  uses-material-design: true

  assets:
    - assets/
    - assets/icons/
    - assets/icons/faq/
    - assets/icons/checkOutCard/
    - assets/icons/shop/
    - assets/icons/nav/
    - assets/icons/profiles/
    - assets/images/
    - assets/images/card/
    - assets/images/card/imageSlider/
    - assets/icons/cardCategory/
    - assets/images/card/cardType/fantasy/royals/
    - assets/images/card/cardType/fantasy/knight/
    - assets/images/card/cardType/fantasy/wizard/
    - assets/images/card/backgroundCard/
    - assets/images/card/icons/
    - assets/lottie/
    - assets/icons/other/
    - assets/fonts/
    - assets/videos/
