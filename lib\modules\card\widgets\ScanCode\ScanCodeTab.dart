// lib/modules/card/widgets/scanCode/ScanCodeTab.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../../view-model/scanCode/ScanCodeViewModel.dart';

class ScanCodeTab extends StatefulWidget {
  const ScanCodeTab({super.key});

  @override
  State<ScanCodeTab> createState() => _ScanCodeTabState();
}

class _ScanCodeTabState extends State<ScanCodeTab> {
  late final MobileScannerController controller;
  String? lastScannedCode;
  bool _isHandlingScan = false;

  @override
  void initState() {
    super.initState();
    controller = MobileScannerController(facing: CameraFacing.back, detectionSpeed: DetectionSpeed.noDuplicates);
    // start camera explicitly
    controller.start();
  }

  Future<void> _onDetect(BarcodeCapture capture) async {
    if (_isHandlingScan) return;
    _isHandlingScan = true;

    final vm = Provider.of<ScanCodeViewModel>(context, listen: false);
    final Barcode? barcode = capture.barcodes.isNotEmpty ? capture.barcodes.first : null;
    final String code = barcode?.rawValue ?? '';

    if (code.length == 8 && RegExp(r'^\d{8}$').hasMatch(code)) {
      if (code != lastScannedCode) {
        lastScannedCode = code;
        await controller.stop(); // stop camera while handling
        await vm.handleScannedCode(context, code);
        _resetScanner();
      }
    }

    _isHandlingScan = false;
  }

  void _resetScanner() {
    lastScannedCode = null;
    controller.start();
  }

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      return Column(
        children: [
          SizedBox(height: 16),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24),
            height: MediaQuery.of(context).size.width * 0.8,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(12),
            ),
            alignment: Alignment.center,
            child: const Text(
              'QR scanning is not supported on Web.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.black54),
            ),
          ),
          const SizedBox(height: 16),
        ],
      );
    }

    return Column(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 24),
          height: MediaQuery.of(context).size.width * 0.8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.black12),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Stack(
              children: [
                MobileScanner(
                  controller: controller,
                  onDetect: _onDetect,
                ),
                Positioned.fill(
                  child: IgnorePointer(
                    child: CustomPaint(
                      painter: ScannerOverlayPainter(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        ElevatedButton.icon(
          onPressed: () async {
            await controller.start();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.amber,
            foregroundColor: Colors.black,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          icon: const Icon(Icons.qr_code_scanner),
          label: const Text('Rescan', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
      ],
    );
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }
}

class ScannerOverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color.fromARGB(83, 255, 193, 7)
      ..strokeWidth = 4
      ..style = PaintingStyle.stroke;

    final double boxSize = size.width * 0.6;
    final Rect rect = Rect.fromCenter(center: size.center(Offset.zero), width: boxSize, height: boxSize);

    canvas.drawRect(rect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
