class ShopProduct {
  final int productSpuId;     // integer(int64)
  final int productSkuId;     // integer(int64) 
  final String productSkuImage;
  final String productSkuName;
  final double salePrice;     // number (double)
  final int givePoints;       // integer(int32)
  final int cardDeduction;    // integer(int32)
  final int sold;            // integer(int32)
  final int stock;           // integer(int32)
  final bool isCollect;      // boolean
  final bool isFavorite;     // Not in API, keep for UI
  final double productRating; // Not in API, keep for UI

  const ShopProduct({
    required this.productSpuId,
    required this.productSkuId,
    required this.productSkuImage,
    required this.productSkuName,
    required this.salePrice,
    required this.givePoints,
    required this.cardDeduction,
    required this.sold,
    required this.stock,
    required this.isCollect,
    required this.isFavorite,
    required this.productRating,
  });

  /// ✅ Factory for API JSON - Updated to match API spec
  factory ShopProduct.fromApiJson(Map<String, dynamic> json) {
    // Helper for int64 parsing
    int parseInt64(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    // Helper for int32 parsing  
    int parseInt32(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    // Helper for number (double) parsing
    double parseNumber(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    bool parseBool(dynamic value) {
      if (value == null) return false;
      if (value is bool) return value;
      if (value is String) return value.toLowerCase() == 'true';
      if (value is int) return value != 0;
      return false;
    }

    return ShopProduct(
      productSpuId: parseInt64(json["productSpuId"]),
      productSkuId: parseInt64(json["productSkuId"]),
      productSkuImage: json["productSkuImage"] ?? "",
      productSkuName: json["productSkuName"] ?? "",
      salePrice: parseNumber(json["salePrice"]),
      givePoints: parseInt32(json["givePoints"]),
      cardDeduction: parseInt32(json["cardDeduction"]),
      sold: parseInt32(json["sold"]),
      stock: parseInt32(json["stock"]),
      isCollect: parseBool(json["isCollect"]),
      isFavorite: false, // Not provided by API
      productRating: 0.0, // Not provided by API
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "productSpuId": productSpuId,
      "productSkuId": productSkuId,
      "productSkuImage": productSkuImage,
      "productSkuName": productSkuName,
      "salePrice": salePrice,
      "givePoints": givePoints,
      "cardDeduction": cardDeduction,
      "sold": sold,
      "stock": stock,
      "isCollect": isCollect,
      "isFavorite": isFavorite,
      "productRating": productRating,
    };
  }

  ShopProduct copyWith({
    int? productSpuId,
    int? productSkuId,
    String? productSkuImage,
    String? productSkuName,
    double? salePrice,
    int? givePoints,
    int? cardDeduction,
    int? sold,
    int? stock,
    bool? isCollect,
    bool? isFavorite,
    double? productRating,
  }) {
    return ShopProduct(
      productSpuId: productSpuId ?? this.productSpuId,
      productSkuId: productSkuId ?? this.productSkuId,
      productSkuImage: productSkuImage ?? this.productSkuImage,
      productSkuName: productSkuName ?? this.productSkuName,
      salePrice: salePrice ?? this.salePrice,
      givePoints: givePoints ?? this.givePoints,
      cardDeduction: cardDeduction ?? this.cardDeduction,
      sold: sold ?? this.sold,
      stock: stock ?? this.stock,
      isCollect: isCollect ?? this.isCollect,
      isFavorite: isFavorite ?? this.isFavorite,
      productRating: productRating ?? this.productRating,
    );
  }
}

class ShopProductDetail{

}

// Keep ShopCategory unchanged as it matches a different API endpoint
class ShopCategory {
  final int categoryId;
  final String categoryName;
  final bool isSelected;
  final String categoryIcon;
  final bool isAdult;

  const ShopCategory({
    required this.categoryId,
    required this.categoryName,
    required this.isSelected,
    required this.categoryIcon,
    required this.isAdult,
  });

  /// ✅ Factory for API JSON
  factory ShopCategory.fromApiJson(Map<String, dynamic> json) {
    int parseInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    bool parseBool(dynamic value) {
      if (value == null) return false;
      if (value is bool) return value;
      if (value is String) return value.toLowerCase() == 'true';
      if (value is int) return value != 0;
      return false;
    }

    return ShopCategory(
      categoryId: parseInt(json["categoryId"]),
      categoryName: json["categoryName"] ?? "",
      isSelected: false,
      categoryIcon: "",
      isAdult: parseBool(json["isAdult"]),
    );
  }
    
  Map<String, dynamic> toJson() {
    return {
      "categoryId": categoryId,
      "categoryName": categoryName,
      "isSelected": isSelected,
      "categoryIcon": categoryIcon,
      "isAdult": isAdult,
    };
  }

  ShopCategory copyWith({
    int? categoryId,
    String? categoryName,
    bool? isSelected,
    String? categoryIcon,
    bool? isAdult,
  }) {
    return ShopCategory(
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      isSelected: isSelected ?? this.isSelected,
      categoryIcon: categoryIcon ?? this.categoryIcon,
      isAdult: isAdult ?? this.isAdult,
    );
  }
}

