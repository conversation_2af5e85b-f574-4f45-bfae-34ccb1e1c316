import 'package:flutter/material.dart';
import '../data/LuckyDrawRepository.dart';
import '../model/LuckyDrawRecord.dart';

class WinningHistoryVM extends ChangeNotifier {
  final LuckyDrawRepository repository;

  // Optional: if provided, fetch history for a specific product
  final int? goodsPointId;

  WinningHistoryVM({LuckyDrawRepository? repository, this.goodsPointId})
    : repository = repository ?? MockLuckyDrawRepository();

  bool isLoading = true;
  List<LuckyDrawRecord> records = const [];

  Future<void> initialize() async {
    isLoading = true;
    notifyListeners();

    try {
      if (goodsPointId != null) {
        records = await repository.fetchProductWinningHistory(
          goodsPointId: goodsPointId!,
        );
      } else {
        records = await repository.fetchWinningHistory();
      }
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }
}
