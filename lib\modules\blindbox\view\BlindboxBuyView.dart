import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../widgets/ProductImageSection.dart';
import '../widgets/ProductInfoSection.dart';
import '../widgets/ProductReviewsSection.dart';
import '../widgets/ProductBottomActionBar.dart';
import '../providers/blindbox_providers.dart';
import '../data/model/BlindboxDetailModel.dart';
import '../../checkout/view-model/CheckoutVM.dart';
import '../widgets/BlindboxBuyShimmer.dart';

class BlindboxBuyView extends ConsumerStatefulWidget {
  final bool isGroupBuy;
  final Map<String, dynamic> product;
  final int? unboxId; // For deep linking support

  const BlindboxBuyView({
    super.key,
    this.isGroupBuy = false,
    required this.product,
    this.unboxId, // Optional parameter for deep linking
  });

  @override
  ConsumerState<BlindboxBuyView> createState() => _BlindboxBuyViewState();
}

class _BlindboxBuyViewState extends ConsumerState<BlindboxBuyView> {
  bool _descExpanded = false;
  final ScrollController _scrollController = ScrollController();
  double _appBarOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    double offset = _scrollController.offset;
    double opacity = (offset / 120).clamp(0, 1);
    if (opacity != _appBarOpacity) {
      setState(() {
        _appBarOpacity = opacity;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Priority: use unboxId parameter (for deep linking) or from product data
    final unboxId = widget.unboxId ?? widget.product['unboxId'] as int?;

    // If we have a unboxId, fetch detailed product information
    if (unboxId != null) {
      final productDetailAsync = ref.watch(
        blindboxProductDetailProvider(unboxId),
      );

      return productDetailAsync.when(
        data: (productDetail) =>
            _buildProductView(context, productDetail: productDetail),
        loading: () => const BlindboxBuyShimmer(),
        error: (error, stack) =>
            _buildProductView(context, error: error.toString()),
      );
    }

    // Fallback to basic product view if no unboxId
    return _buildProductView(context);
  }

  Widget _buildProductView(
    BuildContext context, {
    BlindboxDetailModel? productDetail,
    String? error,
  }) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          RefreshIndicator(
            onRefresh: () async {
              final unboxId =
                  widget.unboxId ?? widget.product['unboxId'] as int?;
              if (unboxId != null) {
                ref.invalidate(blindboxProductDetailProvider(unboxId));
              }
            },
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (error != null)
                    Container(
                      padding: const EdgeInsets.all(16),
                      color: Colors.red.withValues(alpha: 0.1),
                      child: Text(
                        'Error loading product details: $error\nFalling back to basic view.',
                        style: const TextStyle(color: Colors.red),
                      ),
                    ),
                  // Product Image Section
                  ProductImageSection(
                    imageUrls: _getImageUrls(productDetail),
                    currentImageIndex: 0,
                    onImageChanged: (index) {},
                    isFavorite: productDetail?.isCollect ?? false,
                    onFavoritePressed: () {},
                  ),
                  // Product Info Section
                  ProductInfoSection(
                    price: _getCurrentPrice(productDetail),
                    soldCount:
                        productDetail?.sold ?? widget.product['soldCount'] ?? 0,
                    pointsOffer: _getPointsOffer(productDetail),
                    title:
                        productDetail?.unboxTitle ??
                        widget.product['title']?.toString() ??
                        '',
                    variation:
                        'Random Selection', // Blindboxes don't have variants
                    isFavorite: productDetail?.isCollect ?? false,
                    onFavoritePressed: () {},
                  ),
                  const Divider(
                    height: 1,
                    thickness: 8,
                    color: Color(0xFFF5F5F5),
                  ),
                  // Product Description Section
                  _buildProductDescription(productDetail),
                  const Divider(
                    height: 1,
                    thickness: 8,
                    color: Color(0xFFF5F5F5),
                  ),
                  // Product Reviews Section
                  ProductReviewsSection(
                    rating: productDetail?.score ?? 4.5,
                    reviews: [],
                    isLoading: false,
                    onViewAllPressed: () {
                      context.push('/blind-box/reviews');
                    },
                    onHelpfulPressed: (reviewId, context) {
                      // Handle helpful pressed
                    },
                    isReviewHelpfulLoading: (reviewId) => false,
                  ),
                ],
              ),
            ),
          ),
          // Overlay AppBar
          Container(
            height: kToolbarHeight + MediaQuery.of(context).padding.top,
            color: Colors.white.withValues(alpha: _appBarOpacity),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: Padding(
                padding: const EdgeInsets.only(left: 12),
                child: IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios_new_rounded,
                    color: Colors.black,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              actions: [
                Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: IconButton(
                    icon: const Icon(
                      Icons.shopping_cart_outlined,
                      color: Colors.black,
                    ),
                    onPressed: () {
                      context.push('/shop/cart');
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: IconButton(
                    icon: const Icon(Icons.share, color: Colors.black),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Link copied')),
                      );
                    },
                  ),
                ),
              ],
              titleSpacing: 0,
            ),
          ),
        ],
      ),
      bottomNavigationBar: ProductBottomActionBar(
        type: ProductBottomActionBarType.chatAndBuy,
        onChatPressed: () {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('Redirect to Whatsapp')));
        },
        onBuyNowPressed: () {
          _navigateToCheckout(context, productDetail);
        },
      ),
    );
  }

  void _navigateToCheckout(
    BuildContext context, [
    BlindboxDetailModel? productDetail,
  ]) {
    // Use product detail data if available, otherwise fallback to basic product data
    final title =
        productDetail?.unboxTitle ??
        widget.product['title']?.toString() ??
        'Mystery Box';
    final price =
        productDetail?.salePrice ?? _parsePrice(widget.product['price']);
    final imageUrl = _getProductImageUrl(productDetail);

    // Get unboxId with priority: productDetail > widget.unboxId > product data
    final unboxId =
        productDetail?.unboxId ?? widget.unboxId ?? widget.product['unboxId'];

    // Format product data for checkout
    final productData = {
      'title': title,
      'price': price,
      'imageUrl': imageUrl,
      'quantity': 1,
      'deliveryFee': 5.0, // Standard delivery fee for blindbox
      'unboxId': unboxId, // Use unboxId for blindbox
    };

    // Use go_router navigation
    context.push(
      '/checkout',
      extra: {
        'checkoutType': CheckoutType.blindbox,
        'productData': productData,
        'onSuccess': () {
          // Navigate to reveal page instead of going back
          context.push(
            '/blind-box/reveal',
            extra: {
              'price': price,
              'productName': title,
              'orderId': DateTime.now().millisecondsSinceEpoch
                  .toString(), // Mock order ID
              'orderDate': DateTime.now().toIso8601String(),
            },
          );
        },
      },
    );
  }

  double _parsePrice(dynamic price) {
    if (price is String) {
      // Remove 'RM' prefix and any whitespace, then parse
      return double.tryParse(price.replaceAll('RM', '').trim()) ?? 0.0;
    }
    return (price as num?)?.toDouble() ?? 0.0;
  }

  String? _getProductImageUrl([BlindboxDetailModel? productDetail]) {
    if (productDetail != null && productDetail.unboxCover.isNotEmpty) {
      return productDetail.unboxCover.first;
    }
    // Try to get the first image from imageUrls array, fallback to imageUrl
    if (widget.product['imageUrls'] != null &&
        (widget.product['imageUrls'] as List).isNotEmpty) {
      return widget.product['imageUrls'][0];
    }
    return widget.product['imageUrl'];
  }

  Widget _buildProductDescription([BlindboxDetailModel? productDetail]) {
    final int maxLines = _descExpanded ? 100 : 3;
    final description =
        productDetail?.details ??
        widget.product['description']?.toString() ??
        'This is a mystery box containing various items. Each box is carefully curated to provide a unique unboxing experience. Perfect for gifts or personal enjoyment.';

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.description, color: Colors.orange[600]),
              const SizedBox(width: 8),
              const Text(
                'Product Description',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            description,
            style: const TextStyle(fontSize: 14),
            maxLines: maxLines,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => setState(() => _descExpanded = !_descExpanded),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(height: 1, width: 40, color: Colors.grey[300]),
                const SizedBox(width: 8),
                Text(
                  _descExpanded ? 'See Less' : 'See More',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
                const SizedBox(width: 4),
                Icon(
                  _descExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Colors.grey,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Container(height: 1, width: 40, color: Colors.grey[300]),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for handling product detail data
  List<String> _getImageUrls(BlindboxDetailModel? productDetail) {
    if (productDetail != null && productDetail.unboxCover.isNotEmpty) {
      return productDetail.unboxCover;
    }
    return widget.product['imageUrls'] ??
        (widget.product['imageUrl'] != null
            ? [widget.product['imageUrl']]
            : []);
  }

  String _getCurrentPrice(BlindboxDetailModel? productDetail) {
    if (productDetail != null) {
      return 'RM${productDetail.salePrice.toStringAsFixed(2)}';
    }
    return widget.product['price']?.toString() ?? 'RM0.00';
  }

  String _getPointsOffer(BlindboxDetailModel? productDetail) {
    if (productDetail != null && productDetail.cardDeduction > 0) {
      return 'Use ${productDetail.cardDeduction} card points';
    }
    return widget.product['pointsOffer']?.toString() ?? '';
  }
}
