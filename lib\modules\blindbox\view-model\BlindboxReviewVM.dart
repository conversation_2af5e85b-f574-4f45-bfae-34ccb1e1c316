import 'package:flutter/material.dart';

class BlindboxFullReview {
  final String userName;
  final double rating;
  final String date; // yyyy / mm / dd
  final String reviewText;
  final int helpfulCount;
  final bool hasMedia;

  const BlindboxFullReview({
    required this.userName,
    required this.rating,
    required this.date,
    required this.reviewText,
    required this.helpfulCount,
    this.hasMedia = false,
  });
}

enum ReviewTab { all, withMedia, stars }

class BlindboxReviewVM extends ChangeNotifier {
  ReviewTab selectedTab = ReviewTab.all;
  int? selectedStars; // 1..5, null = All

  final List<BlindboxFullReview> _allReviews = const [
    BlindboxFullReview(
      userName: '<PERSON> Wei',
      rating: 5,
      date: '2025 / 02 / 15',
      reviewText:
          'Received with perfect condition. Modern and square design make it more space for every dishes. RECOMMEND 👍',
      helpfulCount: 22,
      hasMedia: false,
    ),
    BlindboxFullReview(
      userName: '<PERSON>y <PERSON>',
      rating: 5,
      date: '2025 / 02 / 22',
      reviewText:
          'User friendly and make cooking much more easier. Safety with front mirror, can spot the the dishes while cooking 👍👍',
      helpfulCount: 23,
      hasMedia: true,
    ),
    BlindboxFullReview(
      userName: '<PERSON>',
      rating: 4,
      date: '2025 / 02 / 15',
      reviewText:
          'Received with perfect condition. Modern and square design make it more space for every dishes. RECOMMEND 👍',
      helpfulCount: 22,
      hasMedia: true,
    ),
    BlindboxFullReview(
      userName: 'Jacky Ma',
      rating: 5,
      date: '2025 / 02 / 22',
      reviewText:
          'User friendly and make cooking much more easier. Safety with front mirror, can spot the the dishes while cooking 👍👍',
      helpfulCount: 23,
      hasMedia: false,
    ),
  ];

  List<BlindboxFullReview> get allReviews => List.unmodifiable(_allReviews);

  void setTab(ReviewTab tab) {
    selectedTab = tab;
    notifyListeners();
  }

  void setStars(int? stars) {
    selectedStars = stars;
    notifyListeners();
  }

  int get allCount => _allReviews.length;
  int get withMediaCount =>
      _allReviews.where((review) => review.hasMedia).length;

  List<BlindboxFullReview> get filteredReviews {
    Iterable<BlindboxFullReview> result = _allReviews;

    if (selectedTab == ReviewTab.withMedia) {
      result = result.where((r) => r.hasMedia);
    }

    if (selectedStars != null) {
      result = result.where((r) => r.rating.floor() == selectedStars);
    }

    return result.toList();
  }
}
