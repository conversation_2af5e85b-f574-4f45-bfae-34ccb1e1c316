# LuckyMall Checkout Module Guide

## Overview

The LuckyMall Checkout Module is a comprehensive, reusable checkout system designed to handle multiple types of purchases including shop products, blindbox items, luckydraw prizes, and physical card delivery. It provides a unified interface with type-specific behavior and UI customization.

**Current Status**: Only shop product checkout is fully implemented with API integration. Other checkout types are available but require backend services to be implemented.

## Table of Contents

1. [Module Structure](#module-structure)
2. [Checkout Types](#checkout-types)
3. [Core Components](#core-components)
4. [API Integration](#api-integration)
5. [Form Validation](#form-validation)
6. [Address Management](#address-management)
7. [Usage Guide](#usage-guide)
8. [Customization](#customization)
9. [Examples](#examples)

## Module Structure

```
lib/modules/checkout/
├── view/
│   ├── CheckoutView.dart              # Main checkout view
│   ├── SelectVoucherView.dart         # Voucher selection screen
│   └── SelectAddressView.dart         # Address selection screen
├── view-model/
│   └── CheckoutVM.dart                # Business logic and state management
├── widgets/
│   ├── OrderHeaderSection.dart         # Order number and timer
│   ├── DeliveryTypeSelector.dart       # Delivery/pickup selector
│   ├── AddressSection.dart            # Shipping address display
│   ├── ProductDetailsSection.dart      # Product information and discounts
│   ├── PaymentDetailsSection.dart      # Price breakdown
│   ├── PaymentMethodsSection.dart      # Payment gateway selection
│   ├── BillingInfoSection.dart        # Customer information form
│   ├── RemarksSection.dart            # Order remarks input
│   └── CheckoutBottomBar.dart         # Total and submit button
└── data/
    ├── model/
    │   ├── CheckoutModel.dart          # Checkout data models
    │   └── PaymentModel.dart           # Payment data models
    └── service/
        └── CheckoutService.dart        # API service for checkout
```

## Checkout Types

### 1. Shop (`CheckoutType.shop`) ✅ **FULLY IMPLEMENTED**
- **Purpose**: Regular e-commerce product purchases
- **Features**: 
  - Delivery only (pickup not implemented yet)
  - Voucher and card discount support (voucher backend pending)
  - Payment methods selection (Billplz, Stripe, iPay88)
  - Billing information collection with validation
  - Address selection from user's saved addresses
  - Full API integration
- **App Bar Title**: "Order Details"

### 2. Blindbox (`CheckoutType.blindbox`) ⚠️ **PARTIAL**
- **Purpose**: Mystery box purchases
- **Features**:
  - Delivery only (no pickup)
  - No voucher/card discounts (backend pending)
  - Payment methods selection
  - Billing information collection
- **App Bar Title**: "Order Details"

### 3. Luckydraw (`CheckoutType.luckydraw`) ⚠️ **PARTIAL**
- **Purpose**: Prize claiming from lucky draws
- **Features**:
  - Delivery/pickup options
  - No voucher/card discounts
  - No payment methods (free prizes)
  - No billing information
- **App Bar Title**: "Claim Prize"

### 4. Physical Card (`CheckoutType.physicalCard`) ⚠️ **PARTIAL**
- **Purpose**: Physical card delivery orders
- **Features**:
  - Delivery only (no pickup)
  - No voucher/card discounts
  - Payment methods selection
  - Billing information collection
- **App Bar Title**: "Send Physical Card"

## Core Components

### CheckoutVM (View Model)

The central state management class that handles:
- Checkout type configuration
- Delivery type selection
- Payment gateway selection
- Form data management and validation
- Address selection
- API integration with backend services
- Error handling and loading states

```dart
class CheckoutVM extends ChangeNotifier {
  final CheckoutType checkoutType;
  DeliveryType _deliveryType;
  String? _selectedPaymentGateway;
  Addressmodel? _selectedAddress;
  
  // Form controllers with validation
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController remarksController = TextEditingController();
  
  // API integration methods
  Future<CheckoutReceiveModel?> getCheckoutData();
  Future<String?> createOrder();
  Future<bool> submitOrder();
}
```

### CheckoutView

The main UI component that orchestrates all checkout sections:

```dart
class CheckoutView extends StatefulWidget {
  final CheckoutType checkoutType;
  final Map<String, dynamic>? productData;
  final VoidCallback? onSuccess;
}
```

## API Integration

### 1. Checkout Service

The module integrates with backend services through `CheckoutService`:

```dart
class CheckoutService {
  // Get checkout data (prices, discounts, etc.)
  Future<CheckoutReceiveModel> getCheckoutData(CheckoutSendModel model);
  
  // Create order and get payment URL
  Future<String> createOrder(ToOrderModel model, String gateway, String name, String email, String mobile);
}
```

### 2. Supported Payment Gateways

Currently implemented payment gateways:
- **Billplz** ✅
- **Stripe** ✅  
- **iPay88** ✅

Pending implementation:
- Touch n Go
- Lucky Mall Wallet
- Cash/COD

### 3. Order Flow

1. **Checkout Data**: Fetch pricing, discounts, and delivery fees
2. **Order Creation**: Create order in backend and get payment URL
3. **Payment Processing**: Redirect user to payment gateway
4. **Order Confirmation**: Handle success/failure responses

## Form Validation

### 1. Required Fields

The checkout form includes comprehensive validation:

- **Name**: Required, non-empty
- **Phone**: Required, non-empty
- **Email**: Required, valid email format
- **Address**: Required for delivery (selected from saved addresses)
- **Payment Method**: Required for paid checkouts

### 2. Validation Features

- Real-time validation on field changes
- Visual error indicators (red borders, error messages)
- Form submission blocked until all validations pass
- Clear error messages displayed below each field

### 3. Validation Rules

```dart
// Email validation
bool _isValidEmail(String email) {
  return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
}

// Form validation
bool get isFormValid {
  bool isValid = true;
  
  if (nameController.text.trim().isEmpty) isValid = false;
  if (phoneController.text.trim().isEmpty) isValid = false;
  if (!_isValidEmail(emailController.text.trim())) isValid = false;
  if (_deliveryType == DeliveryType.delivery && _selectedAddress == null) isValid = false;
  if (requiresPayment && _selectedPaymentGateway == null) isValid = false;
  
  return isValid;
}
```

## Address Management

### 1. Address Selection Screen

The module includes a dedicated address selection screen (`SelectAddressView`) that:

- Fetches user's saved addresses from backend
- Displays addresses with recipient name, phone, and full address
- Shows default address indicator
- Allows selection via radio buttons
- Matches the design of the voucher selection screen

### 2. Address Integration

- Integrates with existing `AddressService` from profile module
- Auto-selects default address if available
- Shows validation errors if no address selected
- Updates checkout view with selected address details

### 3. Address Display

```dart
// In AddressSection
Text(
  viewModel.selectedAddress?.recipient ?? 'Select Address',
  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
),
Text(
  viewModel.selectedAddress?.detailedAddress ?? 'Tap to select delivery address',
  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
),
```

## Usage Guide

### Router Configuration

First, ensure your app router includes the checkout routes:

```dart
// In your app_router.dart
GoRoute(
  path: '/checkout',
  builder: (context, state) {
    final extra = state.extra as Map<String, dynamic>?;
    return CheckoutView(
      checkoutType: extra?['checkoutType'] ?? CheckoutType.shop,
      productData: extra?['productData'],
      onSuccess: extra?['onSuccess'],
    );
  },
  routes: [
    GoRoute(
      path: 'select-voucher',
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;
        return SelectVoucherView(
          orderAmount: extra?['orderAmount'] ?? 0.0,
          onVoucherSelected: extra?['onVoucherSelected'],
        );
      },
    ),
    GoRoute(
      path: 'select-address',
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;
        return SelectAddressView(
          onAddressSelected: extra?['onAddressSelected'],
        );
      },
    ),
  ],
),
```

### Basic Implementation

#### 1. Navigate to Shop Checkout

```dart
// Shop checkout with API integration
CheckoutHelper.navigateToShopCheckout(
  context,
  productData: {
    'title': 'Product Name',
    'price': 99.99,
    'deliveryFee': 5.00,
    'imageUrl': 'https://example.com/image.jpg',
    'productSpuId': 123,  // Required for API
    'productSkuId': 456,  // Required for API
    'quantity': 1,
  },
  onSuccess: () {
    // Handle success
  },
);
```

#### 2. Product Data Structure

For API integration, include these required fields:

```dart
Map<String, dynamic> productData = {
  'title': 'Product Name',           // Required: Product title
  'price': 99.99,                   // Required: Product price
  'productSpuId': 123,              // Required: Product SPU ID for API
  'productSkuId': 456,              // Required: Product SKU ID for API
  'imageUrl': 'https://...',        // Optional: Product image
  'quantity': 1,                    // Optional: Quantity (default: 1)
  'discount': 10.00,                // Optional: Discount amount
  'deliveryFee': 5.00,              // Optional: Delivery fee
  'variant': 'Size M',              // Optional: Product variant
};
```

## Customization

### 1. Adding New Payment Gateways

To add a new payment gateway:

1. **Update CheckoutService**:
```dart
case 'newGateway':
  try {
    final response = await authDio.post(
      '/pay/order/newGateway/$orderId',
      data: {'name': name, 'email': email, 'mobile': mobile},
    );
    if (response.statusCode == 200) {
      paymentUrl = response.data['data'];
    }
  } catch (e) {
    throw Exception('New gateway payment error: $e');
  }
  break;
```

2. **Update CheckoutVM**:
```dart
final List<Map<String, dynamic>> paymentGateways = [
  // ... existing gateways
  {
    'name': 'New Gateway',
    'iconSelected': 'assets/icons/new_gateway_selected.png',
    'iconUnselected': 'assets/icons/new_gateway_unselected.png',
  },
];
```

### 2. Custom Validation Rules

Override validation methods in `CheckoutVM`:

```dart
String? get phoneError {
  if (phoneController.text.trim().isEmpty) {
    return 'Phone number is required';
  }
  // Add custom phone format validation
  if (!RegExp(r'^\+?[\d\s-]+$').hasMatch(phoneController.text.trim())) {
    return 'Please enter a valid phone number';
  }
  return null;
}
```

## Error Handling

### 1. API Error Handling

The module includes comprehensive error handling:

- Network errors with retry options
- API response validation
- User-friendly error messages
- Error state management in UI

### 2. Form Validation Errors

- Real-time validation feedback
- Clear error messages below fields
- Visual error indicators
- Form submission prevention until errors resolved

### 3. Loading States

- Loading indicators during API calls
- Disabled buttons during processing
- Progress indicators for long operations

## Best Practices

### 1. Error Handling

Always implement proper error handling:

```dart
try {
  final success = await CheckoutHelper.navigateToShopCheckout(context, productData: data);
} catch (e) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(content: Text('Checkout failed: $e')),
  );
}
```

### 2. Form Validation

The checkout module automatically handles form validation. Use the validation getters:

```dart
Consumer<CheckoutVM>(
  builder: (context, viewModel, child) {
    if (viewModel.nameError != null) {
      // Show name error
    }
    if (viewModel.addressError != null) {
      // Show address error
    }
    return YourWidget();
  },
);
```

### 3. API Integration

For production use:

1. **Test all payment gateways** thoroughly
2. **Handle payment failures** gracefully
3. **Implement retry logic** for network issues
4. **Add logging** for debugging
5. **Test with real payment data** in staging environment

## Troubleshooting

### Common Issues

1. **API Integration Errors**
   - Verify `productSpuId` and `productSkuId` are provided
   - Check network connectivity
   - Verify API endpoints are correct

2. **Form Validation Fails**
   - Ensure all required fields are filled
   - Check email format is valid
   - Verify address is selected for delivery

3. **Payment Gateway Issues**
   - Test each gateway individually
   - Check gateway configuration in backend
   - Verify payment URLs are valid

4. **Address Selection Issues**
   - Ensure user has saved addresses
   - Check AddressService is working
   - Verify address data format

### Debug Mode

Enable debug prints in `CheckoutVM`:

```dart
// In API methods
debugPrint('API call started: ${methodName}');
debugPrint('Request data: $requestData');
debugPrint('Response received: $responseData');
```

## Future Enhancements

### 1. Planned Features

- **Voucher/Coupon Integration**: When backend service is ready
- **Pickup Location Selection**: For shop and luckydraw checkouts
- **Multiple Payment Methods**: Split payments across gateways
- **Order History**: Integration with order management system

### 2. Backend Services Required

- **Voucher Service**: For discount management
- **Pickup Location Service**: For store pickup options
- **Order Management**: For order tracking and history
- **Payment Webhook**: For payment status updates

## Conclusion

The LuckyMall Checkout Module provides a robust, flexible, and reusable checkout system with full API integration for shop products. The module includes comprehensive form validation, address management, and payment gateway integration.

**Current Status**: Ready for production use with shop products. Other checkout types require backend services to be implemented.

For additional support or customization requests, refer to the module's source code or contact the development team.
