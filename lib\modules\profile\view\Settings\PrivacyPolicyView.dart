import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class PrivacyPolicyview extends StatelessWidget {
  const PrivacyPolicyview({super.key});

  @override
  Widget build(BuildContext context) {
    // Randomly generated privacy policy sections
    final List<_TermsSection> sections = [
      _TermsSection(
        title: "Introduction",
        content:
            "This Privacy Policy describes how Lucky Mall collects, uses, and protects your personal information when you use our services. Please read this policy carefully.",
      ),
      _TermsSection(
        title: "1. Information We Collect",
        content:
            "We may collect information such as your name, email address, phone number, payment details, and usage data when you register or use our app.",
      ),
      _TermsSection(
        title: "2. How We Use Your Information",
        content:
            "Your information is used to provide and improve our services, process transactions, send notifications, and personalize your experience.",
      ),
      _TermsSection(
        title: "3. Sharing of Information",
        content:
            "We do not sell your personal information. We may share your data with trusted partners for service provision, legal compliance, or with your consent.",
      ),
      _TermsSection(
        title: "4. Data Security",
        content:
            "We implement security measures to protect your information from unauthorized access, alteration, or disclosure. However, no method is 100% secure.",
      ),
      _TermsSection(
        title: "5. Cookies and Tracking",
        content:
            "<PERSON> <PERSON> may use cookies and similar technologies to enhance your experience, analyze usage, and deliver personalized content.",
      ),
      _TermsSection(
        title: "6. Your Rights",
        content:
            "You have the right to access, update, or delete your personal information. Contact us if you wish to exercise these rights.",
      ),
      _TermsSection(
        title: "7. Children's Privacy",
        content:
            "Our services are not intended for children under 13. We do not knowingly collect personal information from children.",
      ),
      _TermsSection(
        title: "8. Changes to This Policy",
        content:
            "We may update this Privacy Policy from time to time. Continued use of Lucky Mall means you accept the updated policy.",
      ),
      _TermsSection(
        title: "9. Contact Us",
        content:
            "If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.",
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        centerTitle: true,
        title: const Text(
          'Privacy and Policy',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Lucky Mall Privacy Policy",
              style: TextStyle(fontWeight: FontWeight.w700, fontSize: 18),
            ),
            const SizedBox(height: 18),
            ...sections.map(
              (section) => Padding(
                padding: const EdgeInsets.only(bottom: 18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      section.title,
                      style: TextStyle(
                        fontWeight: section.title == "Introduction"
                            ? FontWeight.w700
                            : FontWeight.w600,
                        fontSize: section.title == "Introduction" ? 16 : 15,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      section.content,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TermsSection {
  final String title;
  final String content;
  _TermsSection({required this.title, required this.content});
}
