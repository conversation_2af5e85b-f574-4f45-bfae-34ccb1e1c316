import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/blindbox_providers.dart';
import '../widgets/SearchFilterTabs.dart';
import '../widgets/BlindboxShopCard.dart';
import '../data/model/BlindboxProductModel.dart';
import '../view-model/BlindboxSearchVM.dart';

class BlindboxSearchView extends ConsumerStatefulWidget {
  final String? initialSearchQuery;
  final bool triggerOnInit;

  const BlindboxSearchView({
    super.key,
    this.initialSearchQuery,
    this.triggerOnInit = false,
  });

  @override
  ConsumerState<BlindboxSearchView> createState() => _BlindboxSearchViewState();
}

class _BlindboxSearchViewState extends ConsumerState<BlindboxSearchView> {
  late TextEditingController _searchController;
  late ScrollController _scrollController;
  SearchFilter _selectedFilter = SearchFilter.relevance;

  // Pagination variables
  bool isLoadingMore = false;
  bool hasMoreData = true;
  int currentPage = 1;
  int pageSize = 20;
  List<BlindboxProductModel> allSearchResults = [];
  List<BlindboxProductModel> displayedSearchResults = [];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(
      text: widget.initialSearchQuery ?? '',
    );
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    // Prefill only; do not auto request until Search button is tapped
    final initialText = widget.initialSearchQuery?.trim() ?? '';
    if (initialText.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(blindboxSearchQueryProvider.notifier).state = initialText;
        if (widget.triggerOnInit) {
          _performSearch(initialText);
        }
      });
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreSearchResults();
    }
  }

  // Load more search results when scrolling near the bottom
  Future<void> _loadMoreSearchResults() async {
    if (isLoadingMore || !hasMoreData) return;

    setState(() {
      isLoadingMore = true;
    });

    try {
      // Check if we have more data to load
      if (displayedSearchResults.length < allSearchResults.length) {
        final nextBatch = allSearchResults
            .take(displayedSearchResults.length + pageSize)
            .toList();

        setState(() {
          displayedSearchResults = nextBatch;
          isLoadingMore = false;
        });
      } else {
        // Try to fetch more from API
        final nextPage = currentPage + 1;
        final searchParams = {
          'query': _searchController.text.trim(),
          'limit': pageSize,
          'page': nextPage,
          'sortBy': _currentSortParams().key,
          'ascending': _currentSortParams().value,
        };

        final results = await ref.read(
          blindboxSearchProductsProvider(searchParams).future,
        );
        final newProducts = results['products'] as List<BlindboxProductModel>;

        if (newProducts.isNotEmpty) {
          setState(() {
            allSearchResults.addAll(newProducts);
            displayedSearchResults = allSearchResults
                .take(displayedSearchResults.length + pageSize)
                .toList();
            currentPage = nextPage;
            isLoadingMore = false;
          });
        } else {
          setState(() {
            hasMoreData = false;
            isLoadingMore = false;
          });
        }
      }
    } catch (e) {
      setState(() {
        isLoadingMore = false;
      });
    }
  }

  // Refresh search results and reset pagination
  Future<void> _refreshSearchResults() async {
    setState(() {
      hasMoreData = true;
      currentPage = 1;
      allSearchResults.clear();
      displayedSearchResults.clear();
    });

    // Invalidate search-related providers
    ref.invalidate(blindboxSearchResultsProvider);
    ref.invalidate(blindboxLoadingProvider);

    // Re-perform the search with current query
    if (_searchController.text.trim().isNotEmpty) {
      await _performSearch(_searchController.text.trim());
    }

    // Wait a bit for the provider to refresh
    await Future.delayed(const Duration(milliseconds: 100));
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  // Helper method to get responsive crossAxisCount for grid
  int getResponsiveCrossAxisCount(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 2; // Small devices (phones)
    } else if (screenWidth < 900) {
      return 3; // Medium devices (tablets)
    } else if (screenWidth < 1200) {
      return 4; // Large tablets
    } else {
      return 5; // Desktop/large screens
    }
  }

  @override
  Widget build(BuildContext context) {
    final searchQuery = ref.watch(blindboxSearchQueryProvider);
    final searchResults = ref.watch(blindboxSearchResultsProvider);
    final isLoading = ref.watch(blindboxLoadingProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFF1F1F1),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 60,
        leading: GestureDetector(
          onTap: () => context.pop(),
          child: Icon(
            Icons.arrow_back_ios_rounded,
            size: getResponsiveFontSize(20, context),
            color: Colors.black,
          ),
        ),
        title: Container(
          height: getResponsivePadding(40, context),
          decoration: BoxDecoration(
            color: const Color(0xFFF1F1F1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: getResponsivePadding(12, context),
                ),
                child: Icon(
                  Icons.search,
                  color: Colors.black,
                  size: getResponsiveFontSize(20, context),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search',
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                      vertical: getResponsivePadding(10, context),
                    ),
                    hintStyle: TextStyle(
                      color: Colors.grey[500],
                      fontSize: getResponsiveFontSize(14, context),
                    ),
                  ),
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                  ),
                  onChanged: (value) {
                    // Update search query only; do not request until button click
                    ref.read(blindboxSearchQueryProvider.notifier).state =
                        value;
                  },
                  onSubmitted: (value) {
                    // Do nothing on submit to avoid unintended requests
                  },
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                  right: getResponsivePadding(4, context),
                ),
                child: Material(
                  color: Colors.red[700],
                  borderRadius: BorderRadius.circular(6),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(6),
                    onTap: () {
                      _performSearch(_searchController.text);
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: getResponsivePadding(16, context),
                        vertical: getResponsivePadding(8, context),
                      ),
                      child: Text(
                        'Search',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                          fontSize: getResponsiveFontSize(14, context),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshSearchResults,
        child: Column(
          children: [
            // Filter tabs
            SearchFilterTabs(
              selectedFilter: _selectedFilter,
              onFilterSelected: (filter) {
                setState(() {
                  _selectedFilter = filter;
                  // Reset pagination when filter changes
                  hasMoreData = true;
                  currentPage = 1;
                  allSearchResults.clear();
                  displayedSearchResults.clear();
                });
                // Invalidate search providers
                ref.invalidate(blindboxSearchResultsProvider);
                ref.invalidate(blindboxLoadingProvider);
                // Re-perform search with new filter
                if (searchQuery.isNotEmpty) {
                  _performSearch(searchQuery);
                }
              },
            ),

            // Divider
            Container(height: 1, color: Colors.grey[300]),

            // Search results
            Expanded(
              child: isLoading
                  ? _buildLoadingState(context)
                  : displayedSearchResults.isEmpty && searchQuery.isNotEmpty
                  ? _buildEmptyState(context)
                  : displayedSearchResults.isNotEmpty
                  ? _buildSearchResults(context, displayedSearchResults)
                  : _buildEmptyState(context),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _performSearch(
    String query, {
    String? sortBy,
    bool? ascending,
  }) async {
    if (query.trim().isEmpty) return;

    ref.read(blindboxLoadingProvider.notifier).state = true;

    try {
      // Use the new search provider with parameters
      final sort = sortBy != null && ascending != null
          ? MapEntry(sortBy, ascending)
          : _currentSortParams();

      final searchParams = {
        'query': query,
        'limit': pageSize,
        'page': 1,
        'sortBy': sort.key,
        'ascending': sort.value,
      };

      final results = await ref.read(
        blindboxSearchProductsProvider(searchParams).future,
      );
      final products = results['products'] as List<BlindboxProductModel>;

      setState(() {
        allSearchResults = List.from(products);
        displayedSearchResults = allSearchResults.take(pageSize).toList();
        currentPage = 1;
        hasMoreData = products.length >= pageSize;
      });

      // Update the search results provider
      ref.read(blindboxSearchResultsProvider.notifier).state = products;

      // Also update the loading state
      ref.read(blindboxLoadingProvider.notifier).state = false;
    } catch (e) {
      // Handle error
      ref.read(blindboxSearchResultsProvider.notifier).state = [];
    } finally {
      ref.read(blindboxLoadingProvider.notifier).state = false;
    }
  }

  // Map selected filter to API sort column and order
  MapEntry<String, bool> _currentSortParams() {
    switch (_selectedFilter) {
      case SearchFilter.latest:
        return const MapEntry('created_at', false); // newest first
      case SearchFilter.mostSold:
        return const MapEntry('sold', false); // high to low
      case SearchFilter.price:
        return const MapEntry('salePrice', true); // low to high
      case SearchFilter.relevance:
        return const MapEntry('relevance', false);
    }
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(getResponsivePadding(32, context)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: getResponsiveFontSize(80, context),
              color: Colors.grey[400],
            ),
            SizedBox(height: getResponsivePadding(16, context)),
            Text(
              'No results found',
              style: TextStyle(
                fontSize: getResponsiveFontSize(18, context),
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: getResponsivePadding(8, context)),
            Text(
              'Try searching for something else',
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults(
    BuildContext context,
    List<BlindboxProductModel> searchResults,
  ) {
    return Padding(
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      child: Column(
        children: [
          Expanded(
            child: GridView.builder(
              controller: _scrollController,
              gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                maxCrossAxisExtent: 250,
                mainAxisSpacing: getResponsivePadding(16, context),
                crossAxisSpacing: getResponsivePadding(16, context),
                childAspectRatio: 0.53,
              ),
              itemCount: searchResults.length,
              itemBuilder: (context, index) {
                final product = searchResults[index];

                return BlindboxShopCard(
                  title: product.unboxTitle,
                  price: 'RM${product.salePrice.toStringAsFixed(2)}',
                  rating: product.score, // Use actual score from model
                  soldCount: product.sold,
                  maxSavings: null, // No max savings in current model
                  imageUrl: product.unboxCover,
                  onTap: () {
                    context.push(
                      '/blind-box/buy',
                      extra: {
                        'product': _convertToMap(product),
                        'isGroupBuy': false,
                      },
                    );
                  },
                );
              },
            ),
          ),
          // Load more indicator
          if (hasMoreData)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: isLoadingMore
                    ? const CircularProgressIndicator()
                    : ElevatedButton(
                        onPressed: _loadMoreSearchResults,
                        child: const Text('Load More'),
                      ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
          maxCrossAxisExtent: 250,
          mainAxisSpacing: getResponsivePadding(16, context),
          crossAxisSpacing: getResponsivePadding(16, context),
          childAspectRatio: 0.53,
        ),
        itemCount: 6, // Show 6 shimmer cards
        itemBuilder: (context, index) => const BlindboxShopCardShimmer(),
      ),
    );
  }

  Map<String, dynamic> _convertToMap(BlindboxProductModel product) {
    return {
      'title': product.unboxTitle,
      'price': 'RM${product.salePrice.toStringAsFixed(2)}',
      'rating': product.score,
      'soldCount': product.sold,
      'maxSavings': null,
      'category': 'Mystery Box',
      'imageUrl': product.unboxCover,
      'imageUrls': [product.unboxCover],
      'pointsOffer': 'Use ${product.cardDeduction} card points',
      'variation': 'Random Selection',
      'description':
          'This is a mystery box containing various high-quality items. Each box is carefully curated to provide a unique unboxing experience.',
      'unboxId': product.unboxId,
      'salePrice': product.salePrice,
      'stock': 1, // Default stock for blindbox
      'cardDeduction': product.cardDeduction,
      'isCollect': product.isCollect,
    };
  }
}
