import 'package:flutter/material.dart';
import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import '../model/MyOrderModel.dart';
import 'OrderStatusBadge.dart';

class OrderCard extends StatefulWidget {
  final MyOrderModel order;
  final VoidCallback? onTap;
  final VoidCallback? onCancel;
  final VoidCallback? onPay;
  final VoidCallback? onBuyAgain;
  final VoidCallback? onHelpSupport;
  final VoidCallback? onRate;
  final VoidCallback? onShare;
  final VoidCallback? onReturnRefund;

  const OrderCard({
    super.key,
    required this.order,
    this.onTap,
    this.onCancel,
    this.onPay,
    this.onBuyAgain,
    this.onHelpSupport,
    this.onRate,
    this.onShare,
    this.onReturnRefund,
  });

  @override
  State<OrderCard> createState() => _OrderCardState();
}

class _OrderCardState extends State<OrderCard> {
  Timer? _countdownTimer;
  String _currentCountdown = '';

  @override
  void initState() {
    super.initState();
    _startCountdownTimer();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdownTimer() {
    if (widget.order.expiredTime != null && widget.order.state == 0) {
      // To Pay status
      _updateCountdown();
      _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (mounted) {
          _updateCountdown();
        } else {
          timer.cancel();
        }
      });
    }
  }

  void _updateCountdown() {
    setState(() {
      _currentCountdown = _formatCountdown(widget.order.expiredTime);
    });
  }

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  String _formatCountdown(String? expiredTime) {
    if (expiredTime == null) return '';

    try {
      final dueDate = DateTime.parse(expiredTime);
      final now = DateTime.now();
      final difference = dueDate.difference(now);

      if (difference.isNegative) return 'Overdue';

      final totalMinutes = difference.inMinutes;
      final minutes = totalMinutes % 60;
      final seconds = difference.inSeconds % 60;

      return '${totalMinutes.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }

  List<Widget> _buildActionButtons() {
    final List<Widget> buttons = [];

    // Status-specific actions
    switch (widget.order.state) {
      case 0: // To Pay
        if (widget.onCancel != null) {
          buttons.add(
            _buildActionButton(
              context: context,
              label: 'Cancel',
              onTap: widget.onCancel!,
              isOutlined: true,
            ),
          );
        }
        if (widget.onPay != null) {
          buttons.add(
            _buildActionButton(
              context: context,
              label: 'Pay Now $_currentCountdown',
              onTap: widget.onPay!,
              isPrimary: true,
            ),
          );
        }
        break;

      case 10: // To Ship
      case 20: // To Receive
        if (widget.onBuyAgain != null) {
          buttons.add(
            _buildActionButton(
              context: context,
              label: 'Buy Again',
              onTap: widget.onBuyAgain!,
              isPrimary: true,
            ),
          );
        }
        break;

      case 100: // Completed
        if (widget.onShare != null) {
          buttons.add(
            _buildActionButton(
              context: context,
              label: 'Share',
              onTap: widget.onShare!,
              isOutlined: true,
            ),
          );
        }
        if (widget.onRate != null && widget.order.isAppraise == true) {
          buttons.add(
            _buildActionButton(
              context: context,
              label: 'Rate',
              onTap: widget.onRate!,
              isOutlined: true,
            ),
          );
        }
        if (widget.onReturnRefund != null) {
          buttons.add(
            _buildActionButton(
              context: context,
              label: 'Refund',
              onTap: widget.onReturnRefund!,
              isOutlined: true,
            ),
          );
        }
        if (widget.onBuyAgain != null) {
          buttons.add(
            _buildActionButton(
              context: context,
              label: 'Buy Again',
              onTap: widget.onBuyAgain!,
              isPrimary: true,
            ),
          );
        }
        break;

      case -1: // Cancelled
        if (widget.onBuyAgain != null) {
          buttons.add(
            _buildActionButton(
              context: context,
              label: 'Buy Again',
              onTap: widget.onBuyAgain!,
              isPrimary: true,
            ),
          );
        }
        break;
    }

    return buttons;
  }

  Widget _buildActionButton({
    required BuildContext context,
    required String label,
    required VoidCallback onTap,
    bool isPrimary = false,
    bool isOutlined = false,
  }) {
    final double buttonHeight = getResponsivePadding(36, context);
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(left: getResponsivePadding(8, context)),
        child: SizedBox(
          height: buttonHeight,
          child: ElevatedButton(
            onPressed: onTap,
            style: ElevatedButton.styleFrom(
              backgroundColor: isPrimary
                  ? const Color(0xFFFCD255)
                  : isOutlined
                  ? Colors.white
                  : Colors.grey.shade200,
              foregroundColor: isPrimary
                  ? Colors.black87
                  : isOutlined
                  ? const Color(0xFFFCD255)
                  : Colors.grey.shade600,
              side: isOutlined
                  ? const BorderSide(color: Color(0xFFFCD255), width: 1)
                  : null,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            child: Text(
              label,
              style: TextStyle(
                fontSize: getResponsiveFontSize(12, context),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
        vertical: getResponsivePadding(8, context),
      ),
      elevation: 1,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200, width: 1),
      ),
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(getResponsivePadding(16, context)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with badges
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Order type badge
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: getResponsivePadding(8, context),
                      vertical: getResponsivePadding(4, context),
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _getOrderTypeDisplayName(),
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(12, context),
                        fontWeight: FontWeight.w600,
                        color: Colors.black54,
                      ),
                    ),
                  ),
                  // Status badge
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: getResponsivePadding(8, context),
                      vertical: getResponsivePadding(4, context),
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _getStatusDisplayName(),
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(12, context),
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: getResponsivePadding(12, context)),

              // Product section
              if (widget.order.orderItems != null &&
                  widget.order.orderItems!.isNotEmpty) ...[
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product image
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        width: getResponsivePadding(60, context),
                        height: getResponsivePadding(60, context),
                        color: Colors.grey.shade100,
                        child:
                            widget.order.orderItems!.first.productSkuImage !=
                                null
                            ? CachedNetworkImage(
                                imageUrl: widget
                                    .order
                                    .orderItems!
                                    .first
                                    .productSkuImage!,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => Container(
                                  color: Colors.grey.shade100,
                                  child: Center(
                                    child: SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.grey.shade400,
                                      ),
                                    ),
                                  ),
                                ),
                                errorWidget: (context, url, error) => Container(
                                  color: Colors.grey.shade200,
                                  child: Icon(
                                    Icons.image,
                                    color: Colors.grey.shade400,
                                  ),
                                ),
                              )
                            : Icon(Icons.image, color: Colors.grey.shade400),
                      ),
                    ),

                    SizedBox(width: getResponsivePadding(12, context)),

                    // Product details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.order.orderItems!.first.productSkuName ??
                                'Product',
                            style: TextStyle(
                              fontSize: getResponsiveFontSize(14, context),
                              fontWeight: FontWeight.w500,
                              color: Colors.black87,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: getResponsivePadding(4, context)),
                          Text(
                            'Quantity: ${widget.order.orderItems!.first.quantity ?? 1}',
                            style: TextStyle(
                              fontSize: getResponsiveFontSize(12, context),
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(height: getResponsivePadding(12, context)),
              ],

              // Total price
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total:',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(14, context),
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    'RM${widget.order.totalPrice ?? 0}',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(16, context),
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),

              // Action buttons
              if (_buildActionButtons().isNotEmpty) ...[
                SizedBox(height: getResponsivePadding(16, context)),
                Row(children: _buildActionButtons()),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _getOrderTypeDisplayName() {
    switch (widget.order.source) {
      case 0:
        return 'Mall Order';
      case 1:
        return 'Card Mailing';
      case 10:
        return 'Points Mall Order';
      case 30:
        return 'Group Purchase Order';
      default:
        return 'Order';
    }
  }

  String _getStatusDisplayName() {
    switch (widget.order.state) {
      case -1:
        return 'Cancelled';
      case 0:
        return 'To Pay';
      case 10:
        return 'To Ship';
      case 20:
        return 'To Receive';
      case 100:
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  Color _getStatusColor() {
    switch (widget.order.state) {
      case -1:
        return Colors.red;
      case 0:
        return Colors.orange;
      case 10:
        return Colors.blue;
      case 20:
        return Colors.purple;
      case 100:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
