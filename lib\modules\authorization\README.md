### Using JWT token in the request body

Some backend endpoints may require the JWT token to be sent in the body instead of the `Authorization` header. This guide shows how to retrieve the token and include it in request bodies (and query params) with Dio.

### Prerequisites
- Token is managed by `AuthService` in `lib/router/auth_guard.dart`.
  - Set during login: `await AuthService.setJwtToken(token)`
  - Read anywhere: `final token = AuthService.jwtToken;`
- A shared client exists in `lib/res/utility/network/api_client.dart`:
  - `authDio` injects the token as a header automatically
  - `publicDio` does not inject headers (use this when the API expects token in body)

### POST: Put token in the body
```dart
import 'package:dio/dio.dart';
import 'package:luckymall/router/auth_guard.dart';
import 'package:luckymall/res/utility/network/api_client.dart';

final Dio dio = ApiClient.instance.publicDio; // no auto Authorization header

Future<Response<dynamic>> submitWithTokenInBody() async {
  final String? token = AuthService.jwtToken;
  if (token == null || token.isEmpty) {
    throw StateError('Missing JWT token. User may not be logged in.');
  }

  // Use the key your backend expects: 'token', 'jwt', 'jwtToken', 'access_token', etc.
  final Map<String, dynamic> body = <String, dynamic>{
    'token': token,
    'someField': 'value',
  };

  return dio.post('https://api.example.com/endpoint', data: body);
}
```

### GET: Put token in query parameters
```dart
import 'package:dio/dio.dart';
import 'package:luckymall/router/auth_guard.dart';
import 'package:luckymall/res/utility/network/api_client.dart';

final Dio dio = ApiClient.instance.publicDio; // no auto Authorization header

Future<Response<dynamic>> fetchWithTokenInQuery() async {
  final String? token = AuthService.jwtToken;
  if (token == null || token.isEmpty) {
    throw StateError('Missing JWT token. User may not be logged in.');
  }

  return dio.get(
    'https://api.example.com/endpoint',
    queryParameters: <String, dynamic>{'token': token},
  );
}
```

### Helper: Merge token into any payload
```dart
import 'package:luckymall/router/auth_guard.dart';

Map<String, dynamic> withToken(
  Map<String, dynamic> payload, {
  String tokenKey = 'token',
}) {
  final String? token = AuthService.jwtToken;
  if (token == null || token.isEmpty) {
    throw StateError('Missing JWT token. User may not be logged in.');
  }
  return <String, dynamic>{
    ...payload,
    tokenKey: token,
  };
}
```
Usage:
```dart
final body = withToken(<String, dynamic>{'page': 1});
await ApiClient.instance.publicDio.post('https://api.example.com/list', data: body);
```

### Which Dio to use?
- Use `publicDio` when the endpoint expects the token in the body or query.
- Use `authDio` when the endpoint expects the token in the `Authorization` header. Avoid sending the token both in the header and body unless the backend requires it.

### Error handling and safety
- Always check for null/empty token and redirect users to login when missing.
- Never log the full token.
- Ensure all requests are over HTTPS.

### Common token keys
- Choose the key your backend expects and keep it consistent:
  - `token`
  - `jwt` / `jwtToken`
  - `access_token` / `accessToken`

If uncertain, inspect the backend contract or recent API responses to confirm the expected key.



