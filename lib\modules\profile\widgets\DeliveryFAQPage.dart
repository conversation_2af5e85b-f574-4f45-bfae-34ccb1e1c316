import 'package:flutter/material.dart';

class DeliveryFAQPage extends StatelessWidget {
  const DeliveryFAQPage({super.key});

  final List<Map<String, String>> faqs = const [
    {
      'question': 'How long does delivery take?',
      'answer': 'It usually takes 2-5 working days depending on your location.'
    },
    {
      'question': 'Can I track my order?',
      'answer': 'Yes, a tracking number will be sent to your email once shipped.'
    },
    {
      'question': 'Do you deliver on weekends?',
      'answer': 'Delivery is only available Monday to Friday (excluding public holidays).'
    },
    {
      'question': 'Can I change my delivery address?',
      'answer': 'Yes, please contact our support team before the order is shipped.'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        iconTheme: const IconThemeData(color: Colors.black),
        title: const Text(
          'Delivery FAQ',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        centerTitle: true,
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: faqs.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final faq = faqs[index];
          return Card(
            elevation: 1,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ExpansionTile(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              title: Row(
                children: [
                  const Icon(Icons.help_outline, color: Color.fromARGB(255, 13, 13, 13)),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      faq['question']!,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
              children: [
                Text(
                  faq['answer']!,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
