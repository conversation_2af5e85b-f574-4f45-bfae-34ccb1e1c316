import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../view-model/ToReceiveDetailVM.dart';

class ToShipNewView extends StatefulWidget {
  final int orderId;

  const ToShipNewView({super.key, required this.orderId});

  @override
  State<ToShipNewView> createState() => _ToShipNewViewState();
}

class _ToShipNewViewState extends State<ToShipNewView> {
  late ToReceiveDetailVM _orderDetailVM;

  @override
  void initState() {
    super.initState();
    _orderDetailVM = ToReceiveDetailVM(orderId: widget.orderId);
  }

  @override
  void dispose() {
    _orderDetailVM.dispose();
    super.dispose();
  }

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _orderDetailVM,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black87),
            onPressed: () => Navigator.of(context).pop(),
          ),
          centerTitle: true,
          title: Text(
            'To Ship',
            style: TextStyle(
              fontSize: getResponsiveFontSize(18, context),
              fontWeight: FontWeight.w700,
              color: Colors.black,
            ),
          ),
        ),
        body: Consumer<ToReceiveDetailVM>(
          builder: (context, vm, _) {
            if (vm.isLoading) {
              return const Center(
                child: CircularProgressIndicator(
                  color: Color(0xFFFCD255),
                ),
              );
            }

            if (vm.errorMessage != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      vm.errorMessage!,
                      style: const TextStyle(color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: vm.refreshTracking,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            if (vm.orderDetail == null) {
              return const Center(
                child: Text('No order details available'),
              );
            }

            return RefreshIndicator(
              color: const Color(0xFFFCD255),
              onRefresh: vm.refreshTracking,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Status Banner
                    _buildStatusBanner(context, vm),

                    SizedBox(height: getResponsivePadding(16, context)),

                    // Order Details
                    _buildOrderDetails(context, vm),

                    SizedBox(height: getResponsivePadding(16, context)),

                    // Address Section
                    if (vm.isDelivery) ...[
                      _buildDeliveryAddress(context, vm),
                      SizedBox(height: getResponsivePadding(16, context)),
                    ],

                    // Product Section
                    _buildProductSection(context, vm),

                    SizedBox(height: getResponsivePadding(16, context)),

                    // Pricing Section
                    _buildPricingSection(context, vm),

                    SizedBox(height: getResponsivePadding(16, context)),

                    // Remark Section
                    _buildRemarkSection(context, vm),

                    SizedBox(height: getResponsivePadding(16, context)),

                    // Payment Section
                    _buildPaymentSection(context, vm),

                    SizedBox(height: getResponsivePadding(32, context)),

                    // Action Buttons
                    _buildActionButtons(context, vm),

                    SizedBox(height: getResponsivePadding(32, context)),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatusBanner(BuildContext context, ToReceiveDetailVM vm) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(getResponsivePadding(12, context)),
      color: Colors.orange.shade100,
      child: Text(
        'Order is being prepared for shipment',
        style: TextStyle(
          fontSize: getResponsiveFontSize(14, context),
          fontWeight: FontWeight.w500,
          color: Colors.orange.shade800,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildOrderDetails(BuildContext context, ToReceiveDetailVM vm) {
    final orderDetail = vm.orderDetail!;
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        children: [
          _buildDetailRow('Order Number', orderDetail.id?.toString() ?? 'Unknown', hasIcon: true),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildDetailRow(
            'Order Time',
            orderDetail.createdTime != null 
                ? DateFormat('yyyy-MM-dd HH:mm').format(orderDetail.createdTime!)
                : 'Unknown',
          ),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildDetailRow(
            'Payment Time',
            orderDetail.payTime != null 
                ? DateFormat('yyyy-MM-dd HH:mm').format(orderDetail.payTime!)
                : 'Unknown',
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool hasIcon = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            color: Colors.black87,
          ),
        ),
        Row(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            if (hasIcon) ...[
              SizedBox(width: getResponsivePadding(4, context)),
              Icon(
                Icons.copy,
                size: getResponsivePadding(16, context),
                color: Colors.grey.shade600,
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildDeliveryAddress(BuildContext context, ToReceiveDetailVM vm) {
    final deliveryInfo = vm.deliveryInfo!;
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: getResponsivePadding(20, context),
                color: Colors.black87,
              ),
              SizedBox(width: getResponsivePadding(8, context)),
              Text(
                '${deliveryInfo.recipientName} ${deliveryInfo.phoneNumber}',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          SizedBox(height: getResponsivePadding(8, context)),
          Padding(
            padding: EdgeInsets.only(left: getResponsivePadding(28, context)),
            child: Text(
              deliveryInfo.address,
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: Colors.black87,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductSection(BuildContext context, ToReceiveDetailVM vm) {
    final orderDetail = vm.orderDetail!;
    final firstItem = orderDetail.orderItems?.first;
    
    if (firstItem == null) {
      return Container(
        margin: EdgeInsets.symmetric(
          horizontal: getResponsivePadding(16, context),
        ),
        padding: EdgeInsets.all(getResponsivePadding(16, context)),
        child: const Text('No product information available'),
      );
    }

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: getResponsivePadding(60, context),
              height: getResponsivePadding(60, context),
              color: Colors.grey.shade100,
              child: firstItem.productSkuImage != null && firstItem.productSkuImage!.startsWith('http')
                  ? Image.network(
                      firstItem.productSkuImage!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey.shade200,
                          child: Icon(
                            Icons.image,
                            color: Colors.grey.shade400,
                          ),
                        );
                      },
                    )
                  : Container(
                      color: Colors.grey.shade200,
                      child: Icon(
                        Icons.image,
                        color: Colors.grey.shade400,
                      ),
                    ),
            ),
          ),

          SizedBox(width: getResponsivePadding(12, context)),

          // Product info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  firstItem.productSkuName ?? 'Unknown Product',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: getResponsivePadding(4, context)),
                Text(
                  'RM${(firstItem.salePrice ?? 0.0).toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),

          // Quantity
          Text(
            'x${firstItem.quantity ?? 1}',
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPricingSection(BuildContext context, ToReceiveDetailVM vm) {
    final orderDetail = vm.orderDetail!;
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        children: [
          _buildPriceRow('Product Subtotal Price', orderDetail.totalPrice ?? 0.0),
          _buildPriceRow('Delivery Fees', orderDetail.deliveryFee ?? 0.0),
          _buildPriceRow('Card Discount Applied', -(orderDetail.cardDeduction ?? 0.0), isDiscount: true),
          SizedBox(height: getResponsivePadding(8, context)),
          Divider(color: Colors.grey.shade300),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildPriceRow('Total Payment', orderDetail.payableAmount ?? 0.0, isBold: true),
        ],
      ),
    );
  }

  Widget _buildPriceRow(
    String label,
    double amount, {
    bool isDiscount = false,
    bool isBold = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: getResponsivePadding(4, context)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: isBold ? FontWeight.w600 : FontWeight.normal,
              color: Colors.black87,
            ),
          ),
          Text(
            '${isDiscount ? '' : ''}RM${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: isBold ? FontWeight.w600 : FontWeight.normal,
              color: isDiscount ? Colors.red : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRemarkSection(BuildContext context, ToReceiveDetailVM vm) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Remark',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: getResponsivePadding(8, context)),
          Text(
            vm.orderDetail?.userNote ?? vm.remark,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.black87,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSection(BuildContext context, ToReceiveDetailVM vm) {
    final orderDetail = vm.orderDetail!;
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        children: [
          _buildPaymentRow('Payment Type', 'Billplz Pay - FPX'),
          _buildPaymentRow(
            'Payment Time',
            orderDetail.payTime != null
                ? DateFormat('yyyy-MM-dd HH:mm').format(orderDetail.payTime!)
                : 'Unknown'
          ),
          _buildPaymentRow(
            'Delivery Partner',
            vm.deliveryInfo?.deliveryPartner ?? 'J&T Express',
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: getResponsivePadding(8, context)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.black87,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, ToReceiveDetailVM vm) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: vm.contactSupport,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFFF59E0B),
                side: const BorderSide(color: Color(0xFFF59E0B), width: 1),
                elevation: 0,
                padding: EdgeInsets.symmetric(
                  vertical: getResponsivePadding(12, context),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Help Support',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(width: getResponsivePadding(12, context)),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                // TODO: Implement cancel order functionality
                debugPrint('Cancel order: ${widget.orderId}');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade50,
                foregroundColor: Colors.red.shade700,
                side: BorderSide(color: Colors.red.shade300, width: 1),
                elevation: 0,
                padding: EdgeInsets.symmetric(
                  vertical: getResponsivePadding(12, context),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Cancel Order',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
