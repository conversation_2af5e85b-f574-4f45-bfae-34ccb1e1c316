import 'package:flutter/material.dart';

class AccountFAQPage extends StatelessWidget {
  const AccountFAQPage({super.key});

  final List<Map<String, String>> faqs = const [
    {
      'question': 'How do I create an account?',
      'answer':
          'Tap on the "Sign Up" button on the login screen and fill in the required details such as your name, email, and password.'
    },
    {
      'question': 'I forgot my password. What should I do?',
      'answer':
          'Tap on "Forgot Password" on the login page. You will receive an email with instructions to reset your password.'
    },
    {
      'question': 'Can I change my registered email address?',
      'answer':
          'Unfortunately, changing your email is not allowed. You can create a new account or contact support for assistance.'
    },
    {
      'question': 'How do I update my profile information?',
      'answer':
          'Go to the "Profile" section in the app, tap the edit icon, and update your name, phone number, or other details.'
    },
    {
      'question': 'How can I delete my account?',
      'answer':
          'Please go to Settings > Account > Deactivate Account. Follow the steps to request account deletion.'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        iconTheme: const IconThemeData(color: Colors.black),
        centerTitle: true,
        title: const Text(
          'Account FAQ',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: faqs.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final faq = faqs[index];
          return Card(
            elevation: 1,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ExpansionTile(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              title: Row(
                children: [
                  const Icon(Icons.person_outline, color: Colors.black),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      faq['question']!,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
              children: [
                Text(
                  faq['answer']!,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
