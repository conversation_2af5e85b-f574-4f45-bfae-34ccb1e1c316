class UserModel {
  String userID; // Unique identifier for the user
  final String phoneNumber;
  String username;
  String? gender; // "Male","Female", or null
  String? birthDate; // Store as "YYYY-MM-DD" or null
  String profileImage;

  UserModel({
    required this.userID,
    required this.phoneNumber,
    required this.username,
    this.gender,
    this.birthDate,
    required this.profileImage,
  });

  factory UserModel.mock() {
    return UserModel(
      userID: "00121103651199",
      phoneNumber: "0138604691",
      username:
          "User${DateTime.now().millisecondsSinceEpoch % 10000}", // Random username
      gender: null,
      birthDate: null,
      profileImage: "",
    );
  }
}
