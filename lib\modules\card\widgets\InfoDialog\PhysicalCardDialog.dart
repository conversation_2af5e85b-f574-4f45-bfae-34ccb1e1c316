import 'package:flutter/material.dart';

class PhysicalCardDialog extends StatefulWidget {
  const PhysicalCardDialog({super.key});

  @override
  State<PhysicalCardDialog> createState() => _PhysicalCardDialogState();
}

class _PhysicalCardDialogState extends State<PhysicalCardDialog> {
  final PageController _controller = PageController();
  int _currentPage = 0;

  final List<List<String>> _pages = [
    [
      'We will convert the cards in your account into physical cards and deliver them to you.',
      'Cards withdrawn by users are not eligible for the grand prize worth RM8888. The prize is only for virtual cards remaining in the app.',
    ],
    [
      'Withdrawn cards are for personal collection only.',
      'Shipping fee for each withdrawal transaction is RM15.',
    ],
    [
      'Only cards that have been drawn by the user can be withdrawn.',
      'Locked cards cannot be withdrawn.',
      'Withdrawn cards will be deducted from your in-app inventory.',
    ],
  ];

  void _next() {
    if (_currentPage < _pages.length - 1) {
      _controller.nextPage(duration: const Duration(milliseconds: 300), curve: Curves.ease);
    } else {
      Navigator.pop(context);
    }
  }

  void _prev() {
    if (_currentPage > 0) {
      _controller.previousPage(duration: const Duration(milliseconds: 300), curve: Curves.ease);
    }
  }

  int _getStartNumberForPage(int index) {
    return _pages.take(index).fold(1, (sum, list) => sum + list.length);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            width: double.infinity,
            height: 400,
            child: Column(
              children: [
                if (_currentPage == 0)
                  const Padding(
                    padding: EdgeInsets.only(bottom: 12.0),
                    child: Text(
                      'Physical Card Info',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),

                // Content
                Expanded(
                  child: PageView.builder(
                    controller: _controller,
                    itemCount: _pages.length,
                    onPageChanged: (index) => setState(() => _currentPage = index),
                    itemBuilder: (context, pageIndex) {
                      final rules = _pages[pageIndex];
                      final startNumber = _getStartNumberForPage(pageIndex);

                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: rules.asMap().entries.map((entry) {
                              final i = entry.key;
                              final text = entry.value;
                              final number = startNumber + i;
                              return Padding(
                                padding: const EdgeInsets.symmetric(vertical: 6.0),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                   Text(
                                      '$number. ',
                                      style: const TextStyle(fontSize: 16, height: 1.4),
                                    ),
                                  Expanded(
                                    child :Text(
                                      text,
                                      style: const TextStyle(fontSize: 16, height: 1.4),
                                      textAlign: TextAlign.justify,
                                    ),
                                  ),
                                ],
                              ),
                              );
                            }).toList(),
                          ),
                        ),
                      );
                    },
                  ),
                ),

                const SizedBox(height: 16),

                // Dot indicators
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(_pages.length, (index) {
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _currentPage == index ? Colors.amber : Colors.grey.shade400,
                      ),
                    );
                  }),
                ),

                const SizedBox(height: 16),

                // Navigation buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: _currentPage > 0 ? _prev : null,
                      child: const Text("Back"),
                    ),
                    TextButton(
                      onPressed: _next,
                      child: Text(_currentPage == _pages.length - 1 ? "Close" : "Next"),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Left arrow
          if (_currentPage > 0)
            Positioned(
              left: -12,
              child: _AnimatedArrowButton(
                icon: Icons.arrow_back_ios_new,
                onTap: _prev,
              ),
            ),

          // Right arrow
          if (_currentPage < _pages.length - 1)
            Positioned(
              right: -12,
              child: _AnimatedArrowButton(
                icon: Icons.arrow_forward_ios,
                onTap: _next,
              ),
            ),
        ],
      ),
    );
  }
}

// Animated Arrow Button
class _AnimatedArrowButton extends StatefulWidget {
  final IconData icon;
  final VoidCallback onTap;

  const _AnimatedArrowButton({required this.icon, required this.onTap});

  @override
  State<_AnimatedArrowButton> createState() => _AnimatedArrowButtonState();
}

class _AnimatedArrowButtonState extends State<_AnimatedArrowButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scale;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
      lowerBound: 0.9,
      upperBound: 1.0,
    );
    _scale = CurvedAnimation(parent: _controller, curve: Curves.easeInOut);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTap() {
    _controller.reverse().then((_) {
      _controller.forward();
      widget.onTap();
    });
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scale,
      child: Material(
        color: Colors.amber.shade100,
        shape: const CircleBorder(),
        elevation: 4,
        child: InkWell(
          borderRadius: BorderRadius.circular(30),
          onTap: _handleTap,
          child: Container(
            padding: const EdgeInsets.all(10),
            child: Icon(widget.icon, size: 18, color: Colors.black87),
          ),
        ),
      ),
    );
  }
}
