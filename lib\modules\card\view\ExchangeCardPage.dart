import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/ExchangeCard/ExchangeCardItem.dart';
import '../model/exchangeCard/ExchangeCardModel.dart';
import '../view-model/cardPack/CardPackViewModel.dart';
import '../view-model/exchangeCard/ExchangeCardViewModel.dart';
import '../widgets/ExchangeCard/ExchangeCardAppBar.dart';
import '../widgets/ExchangeCard/EmptyExchangeState.dart';
import '../widgets/ExchangeCard/ExchangeCardListShimmer.dart';
import '../widgets/ExchangeCard/RankFilterBar.dart';
import '../widgets/ExchangeCard/SelectionTypeToggle.dart';

class ExchangeCardPage extends StatefulWidget {
  const ExchangeCardPage({super.key});

  @override
  State<ExchangeCardPage> createState() => _ExchangeCardPageState();
}

class _ExchangeCardPageState extends State<ExchangeCardPage> {
  final cardPackViewModel = CardPackViewModel();
  late final ExchangeCardViewModel exchangeViewModel;

  @override
  void initState() {
    super.initState();
    cardPackViewModel.loadOwnedCards();
    exchangeViewModel = ExchangeCardViewModel(
      cardPackViewModel: cardPackViewModel,
    );
    exchangeViewModel.loadExchangeItems();
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: const ExchangeCardAppBar(),
        body: SafeArea(
          child: Column(
            children: [
             // Listen to rank selection
              ValueListenableBuilder<CardRank?>(
                valueListenable: exchangeViewModel.selectedRankFilter,
                builder: (_, selectedRank, __) {
                  return RankFilterBar(
                    selectedRank: selectedRank,
                    onSelected: exchangeViewModel.setRankFilter, // pass directly
                  );
                },
              ),

              // Listen to selection type
              ValueListenableBuilder<SelectionType?>(
                valueListenable: exchangeViewModel.selectedTypeFilter,
                builder: (_, selectedType, __) {
                  return SelectionTypeToggle(
                    selectedType: selectedType,
                    onSelected: exchangeViewModel.setSelectionTypeFilter, // pass directly
                  );
                },
              ),
              Expanded(
                child: ValueListenableBuilder<bool>(
                  valueListenable: exchangeViewModel.isLoading,
                  builder: (context, isLoading, _) {
                    if (isLoading) {
                      return const ExchangeCardListShimmer();
                    }
                    return ValueListenableBuilder<List<ExchangeCardModel>>(
                      valueListenable: exchangeViewModel.exchangeCardsNotifier,
                      builder: (context, exchangeList, _) {
                        if (exchangeList.isEmpty) {
                          return const EmptyExchangeState();
                        }
                        return ScrollConfiguration(
                          behavior: ScrollConfiguration.of(context).copyWith(overscroll: false),
                          child: ListView.separated(
                            padding: const EdgeInsets.all(16),
                            itemCount: exchangeList.length,
                            separatorBuilder: (_, __) => const SizedBox(height: 12),
                            itemBuilder: (context, index) {
                              final item = exchangeList[index];
                              return ExchangeCardItem(
                                model: item,
                                isEligible: exchangeViewModel.checkEligibility(item),
                                onTap: () {
                                  if (exchangeViewModel.checkEligibility(item)) {
                                    exchangeViewModel.navigateToConfirmRedemption(context, item);
                                  } else {
                                    exchangeViewModel.showInsufficientDialog(context, item);
                                  }
                                },
                              );
                            },
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
