import 'package:dio/dio.dart';
import '../../model/LuckyDrawProductReview.dart';
import '../LuckyDrawProductDetailsService.dart';

final reviewService = ReviewService();

class ReviewService {
  final Dio dio = Dio();

  /// Fetch latest reviews for the main page
  ///
  /// [limit] - Number of reviews to fetch (default: 10)
  /// [page] - Page number for pagination (default: 1)
  /// [productSpuId] - Optional: Filter by specific product SPU ID
  /// [productSkuId] - Optional: Filter by specific product SKU ID
  /// [score] - Optional: Filter by rating score
  /// [orderSource] - Optional: Filter by order source (0: Store, 10: Point, 20: Blindbox)
  /// [targetId] - Optional: Filter by associated business ID
  /// [fetchVariations] - Whether to fetch actual variation properties (default: true)
  Future<Map<String, dynamic>> getLatestReviews({
    int? limit = 10,
    int? page = 1,
    int? productSpuId,
    int? productSkuId,
    int? score,
    int? orderSource,
    int? targetId,
    bool fetchVariations = true,
  }) async {
    try {
      // Build query parameters according to the API specification
      final Map<String, dynamic> queryParams = {'limit': limit, 'page': page};

      // Add vo parameters if provided
      final Map<String, dynamic> voParams = {};
      if (productSpuId != null) voParams['productSpuId'] = productSpuId;
      if (productSkuId != null) voParams['productSkuId'] = productSkuId;
      if (score != null) voParams['score'] = score;
      if (orderSource != null) voParams['orderSource'] = orderSource;
      if (targetId != null) voParams['targetId'] = targetId;

      // Only add vo if there are parameters
      if (voParams.isNotEmpty) {
        queryParams['vo'] = voParams;
      }

      final response = await dio.get(
        'https://api.luckymall.com.my/order/comment',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData['status'] == 0) {
          final List<dynamic> reviewList = responseData['data']['list'] ?? [];
          final reviews = reviewList
              .map((json) => _mapApiResponseToReview(json))
              .toList();

          // Fetch variation properties if requested
          if (fetchVariations) {
            await _enrichReviewsWithVariations(reviews);
          }

          return {
            'reviews': reviews,
            'totalCount': responseData['data']['totalCount'] ?? 0,
            'pageSize': responseData['data']['pageSize'] ?? 10,
            'totalPage': responseData['data']['totalPage'] ?? 1,
            'currentPage': responseData['data']['currentPage'] ?? 1,
          };
        } else {
          throw Exception('API Error: ${responseData['message']}');
        }
      } else {
        throw Exception('Failed to load reviews: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        throw Exception(
          'Network Error: ${e.response?.statusCode} - ${e.response?.statusMessage}',
        );
      } else {
        throw Exception('Network Error: ${e.message}');
      }
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  /// Map API response to LuckyDrawProductReview model
  LuckyDrawProductReview _mapApiResponseToReview(Map<String, dynamic> json) {
    // Handle anonymous reviews
    final isAnonymous = json['isAnonymous'] ?? false;
    final reviewerName = isAnonymous
        ? 'Anonymous User'
        : (json['nickname'] ?? 'Unknown User');
    final avatarUrl = isAnonymous ? '' : (json['avatar'] ?? '');

    // Extract product information
    final product = json['product'] ?? {};
    final productSpuId = product['productSpuId']?.toString() ?? '';
    final productSkuId = product['productSkuId']?.toString() ?? '';

    // Convert pictures array to List<String>
    final pictures = json['pictures'] ?? [];
    final imageUrls = pictures
        .map((pic) => pic.toString())
        .toList()
        .cast<String>();

    // Convert score to double for rating
    final score = (json['score'] as num?)?.toDouble() ?? 0.0;

    // Convert createdTime to formatted date
    final createdTime = json['createdTime'] ?? '';
    final formattedDate = _formatDate(createdTime);

    return LuckyDrawProductReview(
      reviewId: DateTime.now().millisecondsSinceEpoch
          .toString(), // Generate unique ID since API doesn't provide one
      reviewerName: reviewerName,
      avatarUrl: avatarUrl,
      productId: productSpuId,
      productName: '', // Will be populated later if needed from product service
      rating: score,
      variation: productSkuId, // Using SKU ID as variation identifier
      reviewText: json['content'] ?? '',
      imageUrls: imageUrls,
      helpfulCount: 0, // API doesn't provide this, defaulting to 0
      date: formattedDate,
      category:
          '', // Not provided by API, can be derived from orderSource if needed
      verified: false, // Not provided by API, defaulting to false
      orderType: json['orderSource'] ?? 0,
    );
  }

  /// Format date string from API response
  String _formatDate(String dateString) {
    if (dateString.isEmpty) return '';

    try {
      final date = DateTime.parse(dateString);
      return '${date.year} / ${date.month.toString().padLeft(2, '0')} / ${date.day.toString().padLeft(2, '0')}';
    } catch (e) {
      // If parsing fails, return the original string
      return dateString;
    }
  }

  /// Enrich reviews with actual variation properties by fetching product details
  Future<void> _enrichReviewsWithVariations(
    List<LuckyDrawProductReview> reviews,
  ) async {
    try {
      // Collect unique productSkuIds from reviews
      final Set<int> uniqueSkuIds = {};
      for (final review in reviews) {
        final skuId = int.tryParse(review.variation);
        if (skuId != null && skuId > 0) {
          uniqueSkuIds.add(skuId);
        }
      }

      if (uniqueSkuIds.isEmpty) return;

      // Fetch product details for each unique SKU ID
      final Map<int, String> skuVariations = {};
      final productDetailsService = luckyDrawProductDetailsService;

      for (final skuId in uniqueSkuIds) {
        try {
          final response = await productDetailsService.getProductDetails(skuId);
          if (response.isSuccess && response.hasData && response.data != null) {
            final productDetails = response.data!;
            skuVariations[skuId] = productDetails.productSkuPropertys;
          }
        } catch (e) {
          // If fetching fails for a specific SKU, continue with others
          print('Failed to fetch variation for SKU $skuId: $e');
        }
      }

      // Update reviews with actual variation properties
      for (final review in reviews) {
        final skuId = int.tryParse(review.variation);
        if (skuId != null && skuVariations.containsKey(skuId)) {
          // Create a new review with updated variation
          final updatedReview = review.copyWith(
            variation: skuVariations[skuId] ?? review.variation,
          );
          // Replace the review in the list
          final index = reviews.indexOf(review);
          if (index != -1) {
            reviews[index] = updatedReview;
          }
        }
      }
    } catch (e) {
      // If enrichment fails, reviews will still work with SKU IDs
      print('Failed to enrich reviews with variations: $e');
    }
  }

  /// Get reviews for a specific product
  Future<List<LuckyDrawProductReview>> getProductReviews({
    required int productSpuId,
    int? limit = 10,
    int? page = 1,
    bool fetchVariations = true,
  }) async {
    final result = await getLatestReviews(
      productSpuId: productSpuId,
      limit: limit,
      page: page,
      fetchVariations: fetchVariations,
    );

    return List<LuckyDrawProductReview>.from(result['reviews']);
  }

  /// Get reviews by order source (Store, Point, Blindbox)
  Future<List<LuckyDrawProductReview>> getReviewsByOrderSource({
    required int orderSource,
    int? limit = 10,
    int? page = 1,
    bool fetchVariations = true,
  }) async {
    final result = await getLatestReviews(
      orderSource: orderSource,
      limit: limit,
      page: page,
      fetchVariations: fetchVariations,
    );

    return List<LuckyDrawProductReview>.from(result['reviews']);
  }

  /// Get reviews with specific rating
  Future<List<LuckyDrawProductReview>> getReviewsByRating({
    required int rating,
    int? limit = 10,
    int? page = 1,
    bool fetchVariations = true,
  }) async {
    final result = await getLatestReviews(
      score: rating,
      limit: limit,
      page: page,
      fetchVariations: fetchVariations,
    );

    return List<LuckyDrawProductReview>.from(result['reviews']);
  }

  /// Get reviews without fetching variation properties (faster performance)
  Future<List<LuckyDrawProductReview>> getReviewsWithoutVariations({
    int? limit = 10,
    int? page = 1,
    int? productSpuId,
    int? productSkuId,
    int? score,
    int? orderSource,
    int? targetId,
  }) async {
    final result = await getLatestReviews(
      limit: limit,
      page: page,
      productSpuId: productSpuId,
      productSkuId: productSkuId,
      score: score,
      orderSource: orderSource,
      targetId: targetId,
      fetchVariations: false,
    );

    return List<LuckyDrawProductReview>.from(result['reviews']);
  }
}
