import 'HistoryCardUsedItem.dart';
class HistoryCardItem {
  final DateTime timestamp;
  final String type;
  final String detail;
  final String status;
  final double amount;

  // Optional details
  final String? paymentMethod;
  final String? paymentUsed;
  final String? orderMethod;
  final String? transferType;
  final String? giftSource;
  final String? productName;

  // 🔄 Replace cardsUsed with this
  final List<HistoryCardUsedItem>? cardsUsed;

  HistoryCardItem({
    required this.timestamp,
    required this.type,
    required this.detail,
    required this.status,
    required this.amount,
    this.paymentMethod,
    this.paymentUsed,
    this.orderMethod,
    this.transferType,
    this.giftSource,
    this.productName,
    this.cardsUsed, // 🆕
  });
}

