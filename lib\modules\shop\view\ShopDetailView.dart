import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_html/flutter_html.dart'; // Add this import
import '../view-model/ShopDetailViewModel.dart';
import '../widget/ShopDetailReviewCard.dart';
import '../widget/ShopDetailReviewCardSkeleton.dart';
import 'package:flutter_svg/flutter_svg.dart'; // Add this import
import 'ShopCartView.dart';

class ShopDetailView extends StatelessWidget {
  final String productId;

  const ShopDetailView({super.key, required this.productId});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ShopDetailViewModel()..loadProductDetail(productId),
      child: const _ShopDetailViewBody(),
    );
  }
}

class _ShopDetailViewBody extends StatefulWidget {
  const _ShopDetailViewBody();

  @override
  State<_ShopDetailViewBody> createState() => _ShopDetailViewBodyState();
}

class _ShopDetailViewBodyState extends State<_ShopDetailViewBody> {
  bool _descExpanded = false;
  final ScrollController _scrollController = ScrollController();
  double _appBarOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    double offset = _scrollController.offset;
    double opacity = (offset / 120).clamp(0, 1);
    if (opacity != _appBarOpacity) {
      setState(() {
        _appBarOpacity = opacity;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ShopDetailViewModel>(
      builder: (context, vm, child) {
        if (vm.isLoading) {
          // Show skeletons for the whole product detail page while loading
          return Scaffold(
            backgroundColor: Colors.white,
            body: Column(
              children: [
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.zero,
                    children: [
                      // Product image skeleton
                      Container(
                        width: double.infinity,
                        height: MediaQuery.of(context).size.width * 0.75,
                        color: Colors.grey.shade300,
                        child: const Center(
                          child: Icon(Icons.image, color: Colors.white54, size: 64),
                        ),
                      ),
                      // Product info skeleton
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 20, 16, 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Price skeleton
                            Container(
                              width: 120,
                              height: 28,
                              color: Colors.grey.shade300,
                              margin: const EdgeInsets.only(bottom: 12),
                            ),
                            // Discount badges skeleton
                            Row(
                              children: [
                                Container(
                                  width: 100,
                                  height: 20,
                                  color: Colors.grey.shade300,
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  width: 120,
                                  height: 20,
                                  color: Colors.grey.shade300,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            // Title skeleton
                            Container(
                              width: double.infinity,
                              height: 22,
                              color: Colors.grey.shade300,
                              margin: const EdgeInsets.only(bottom: 8),
                            ),
                            // Variation skeleton
                            Container(
                              width: 160,
                              height: 16,
                              color: Colors.grey.shade300,
                            ),
                          ],
                        ),
                      ),
                      // Product description skeleton
                      Container(
                        color: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.description, color: Colors.orange[600]),
                                const SizedBox(width: 8),
                                Container(
                                  width: 140,
                                  height: 18,
                                  color: Colors.grey.shade300,
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Container(
                              width: double.infinity,
                              height: 14,
                              color: Colors.grey.shade300,
                              margin: const EdgeInsets.only(bottom: 6),
                            ),
                            Container(
                              width: double.infinity,
                              height: 14,
                              color: Colors.grey.shade300,
                              margin: const EdgeInsets.only(bottom: 6),
                            ),
                            Container(
                              width: MediaQuery.of(context).size.width * 0.7,
                              height: 14,
                              color: Colors.grey.shade300,
                            ),
                            const SizedBox(height: 16),
                            Container(
                              width: 80,
                              height: 16,
                              color: Colors.grey.shade200,
                              alignment: Alignment.center,
                            ),
                          ],
                        ),
                      ),
                      // Reviews skeleton section
                      Container(
                        color: const Color(0xFFFFF8E1),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Header skeleton
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                              child: Row(
                                children: [
                                  Container(
                                    width: 32,
                                    height: 20,
                                    color: Colors.grey.shade300,
                                  ),
                                  const SizedBox(width: 8),
                                  Container(
                                    width: 120,
                                    height: 20,
                                    color: Colors.grey.shade300,
                                  ),
                                  const Spacer(),
                                  Container(
                                    width: 60,
                                    height: 20,
                                    color: Colors.grey.shade300,
                                  ),
                                ],
                              ),
                            ),
                            // Skeleton review cards
                            Container(
                              color: Colors.white,
                              padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
                              child: Column(
                                children: List.generate(
                                  3,
                                  (index) => const ShopDetailReviewCardSkeleton(),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            // Optionally: add a skeleton for the bottom action bar
          );
        }
        if (vm.error != null) {
          return Center(child: Text('Error: ${vm.error}'));
        }
        if (vm.product == null) {
          return const Center(child: Text('Product not found'));
        }
        final product = vm.product!;
        return Scaffold(
          backgroundColor: Colors.white,
          body: Stack(
            children: [
              SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Update the image section
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.width * 0.75,
                      child: vm.productImages.isNotEmpty 
                          ? PageView.builder(
                              itemCount: vm.productImages.length,
                              onPageChanged: vm.onImageChanged,
                              controller: PageController(
                                initialPage: vm.currentImageIndex,
                              ),
                              itemBuilder: (context, index) {
                                final imageUrl = vm.productImages[index];
                                
                                return Image.network(
                                  imageUrl,
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: Colors.grey[200],
                                      child: const Icon(
                                        Icons.image_not_supported,
                                        color: Colors.grey,
                                        size: 60,
                                      ),
                                    );
                                  },
                                );
                              },
                            )
                          : 
                          // Single product image fallback
                          Image.network(
                            vm.productImage,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[200],
                                child: const Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey,
                                  size: 60,
                                ),
                              );
                            },
                          ),
                    ),
                    _buildProductInfo(vm, context),
                    const Divider(height: 1, thickness: 8, color: Color(0xFFF5F5F5)),
                    _buildProductDescription(vm), // ✅ Pass ViewModel, not Model
                    const Divider(height: 1, thickness: 8, color: Color(0xFFF5F5F5)),
                    _buildReviews(vm), // ✅ Pass ViewModel, not Model
                  ],
                ),
              ),
              // Overlay AppBar
              Container(
                height: kToolbarHeight + MediaQuery.of(context).padding.top,
                color: Colors.white.withOpacity(_appBarOpacity),
                child: AppBar(
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: Padding(
                    padding: const EdgeInsets.only(left: 12),
                    child: IconButton(
                      icon: SvgPicture.asset(
                        'assets/icons/back.svg',
                        width: 20,
                        height: 20,
                        colorFilter: const ColorFilter.mode(Colors.black, BlendMode.srcIn),
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                  actions: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: IconButton(
                        icon: Stack(
                          children: [
                            const Icon(Icons.shopping_cart_outlined, color: Colors.black),
                            Positioned(
                              right: 0,
                              top: 0,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 12,
                                  minHeight: 12,
                                ),
                                child: const Text(
                                  '1',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 8,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                        onPressed: () {
                          context.push('/shop/cart');
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(right: 12), // Adjust right spacing here
                      child: IconButton(
                        icon: const Icon(Icons.share, color: Colors.black),
                        onPressed: () {},
                      ),
                    ),
                  ],
                  titleSpacing: 0, // You can adjust this value as needed
                ),
              ),
            ],
          ),
          bottomNavigationBar: _buildBottomActionBar(context, vm),
        );
      },
    );
  }

  // Update _buildProductInfo method
Widget _buildProductInfo(ShopDetailViewModel vm, BuildContext context) {
  return Padding(
    padding: const EdgeInsets.fromLTRB(16, 20, 16, 12),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Price section
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              vm.displayPrice,
              style: TextStyle(
                fontSize: MediaQuery.of(context).size.width * 0.07,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(width: 8),
            // Show discount if on sale
            if (vm.isSale)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'SALE',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        
        // Discount badges
        Row(
          children: [
            // Flexible(
            //   flex: 1,
            //   child: FittedBox(
            //     fit: BoxFit.scaleDown,
            //     child: Container(
            //       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            //       decoration: BoxDecoration(
            //         color: Colors.red,
            //         borderRadius: BorderRadius.circular(4),
            //       ),
            //       child: const Text(
            //         "New User's 20% Discount",
            //         style: TextStyle(
            //           color: Colors.white,
            //           fontWeight: FontWeight.bold,
            //           fontSize: 12,
            //         ),
            //       ),
            //     ),
            //   ),
            // ),
            // const SizedBox(width: 8),
            if (vm.hasCardDeduction)
              Flexible(
                flex: 1,
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      vm.cardDeductionText,
                      style: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),
        
        // Title - use productName
        Text(
          vm.productName,
          style: TextStyle(
            fontSize: MediaQuery.of(context).size.width * 0.05,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        
        // Only show sold count (removed stock)
        Text(
          vm.soldText,
          style: TextStyle(
            fontSize: MediaQuery.of(context).size.width * 0.035,
            color: Colors.grey[600],
          ),
        ),
        
        // Add variation text display
        if (vm.hasProductSkus) ...[
          const SizedBox(height: 12),
          Text(
            'Variation: ${_getVariationText(vm)}',
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width * 0.035,
              color: Colors.grey[700],
            ),
          ),
        ],
      ],
    ));
  }

  // Add this helper method to get variation text
String _getVariationText(ShopDetailViewModel vm) {
  final variations = <String>[];
  final variationGroups = vm.getVariationGroups();
  
  for (var entry in variationGroups.entries) {
    final propertyKey = entry.key;
    final propertyValues = entry.value;
    
    if (propertyKey.isNotEmpty && propertyValues.isNotEmpty) {
      // Group variations by property type
      variations.add('${propertyValues.join(', ')}');
    } else if (propertyValues.isNotEmpty) {
      // If no property key, just show the values
      variations.add(propertyValues.join(', '));
    }
  }
  
  return variations.isNotEmpty ? variations.join(', ') : 'No variations';
}

  Widget _buildProductDescription(ShopDetailViewModel vm) {
  final int maxLines = _descExpanded ? 100 : 3;
  return Container(
    color: Colors.white,
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.description, color: Colors.orange[600]),
            const SizedBox(width: 8),
            const Text(
              'Product Description',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // Rich text HTML rendering
        _descExpanded 
            ? Html(
                data: vm.etails.isNotEmpty ? vm.etails : '<p>No description available</p>',
                style: {
                  "body": Style(
                    margin: Margins.zero,
                    padding: HtmlPaddings.zero,
                    fontSize: FontSize(14),
                    lineHeight: LineHeight(1.4),
                  ),
                  "p": Style(
                    margin: Margins.only(bottom: 8),
                    fontSize: FontSize(14),
                  ),
                  "strong": Style(
                    fontWeight: FontWeight.bold,
                  ),
                  "b": Style(
                    fontWeight: FontWeight.bold,
                  ),
                  "em": Style(
                    fontStyle: FontStyle.italic,
                  ),
                  "i": Style(
                    fontStyle: FontStyle.italic,
                  ),
                  "ul": Style(
                    margin: Margins.only(left: 16, bottom: 8),
                  ),
                  "ol": Style(
                    margin: Margins.only(left: 16, bottom: 8),
                  ),
                  "li": Style(
                    margin: Margins.only(bottom: 4),
                  ),
                  "h1": Style(
                    fontSize: FontSize(18),
                    fontWeight: FontWeight.bold,
                    margin: Margins.only(bottom: 8),
                  ),
                  "h2": Style(
                    fontSize: FontSize(16),
                    fontWeight: FontWeight.bold,
                    margin: Margins.only(bottom: 6),
                  ),
                  "h3": Style(
                    fontSize: FontSize(15),
                    fontWeight: FontWeight.w600,
                    margin: Margins.only(bottom: 6),
                  ),
                },
                onLinkTap: (url, _, __) {
                  // Handle link taps if needed
                  print('Link tapped: $url');
                },
              )
            : Container(
                constraints: BoxConstraints(
                  maxHeight: maxLines * 20.0, // Approximate line height
                ),
                child: Stack(
                  children: [
                    Html(
                      data: vm.etails.isNotEmpty ? vm.etails : '<p>No description available</p>',
                      style: {
                        "body": Style(
                          margin: Margins.zero,
                          padding: HtmlPaddings.zero,
                          fontSize: FontSize(14),
                          lineHeight: LineHeight(1.4),
                        ),
                        "p": Style(
                          margin: Margins.only(bottom: 8),
                          fontSize: FontSize(14),
                        ),
                        "strong": Style(
                          fontWeight: FontWeight.bold,
                        ),
                        "b": Style(
                          fontWeight: FontWeight.bold,
                        ),
                        "em": Style(
                          fontStyle: FontStyle.italic,
                        ),
                        "i": Style(
                          fontStyle: FontStyle.italic,
                        ),
                        "ul": Style(
                          margin: Margins.only(left: 16, bottom: 8),
                        ),
                        "ol": Style(
                          margin: Margins.only(left: 16, bottom: 8),
                        ),
                        "li": Style(
                          margin: Margins.only(bottom: 4),
                        ),
                      },
                    ),
                    // Gradient overlay for collapsed state
                    if (!_descExpanded)
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        height: 20,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.white.withOpacity(0.0),
                                Colors.white.withOpacity(0.8),
                                Colors.white,
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
        
        // Show product info list if available
        if (vm.hasProductInfo) ...[
          const SizedBox(height: 16),
          const Text(
            'Product Information:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...vm.productInfoList.map((info) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 100,
                  child: Text(
                    '${info.infoKey}:',
                    style: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    info.infoValue,
                    style: const TextStyle(fontSize: 13),
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
        
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () => setState(() => _descExpanded = !_descExpanded),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 1,
                width: 40,
                color: Colors.grey[300],
              ),
              const SizedBox(width: 8),
              Text(
                _descExpanded ? 'See Less' : 'See More',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(width: 4),
              Icon(
                _descExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: Colors.grey,
                size: 18,
              ),
              const SizedBox(width: 8),
              Container(
                height: 1,
                width: 40,
                color: Colors.grey[300],
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

  Widget _buildReviews(ShopDetailViewModel vm) {
  return Container(
    color: const Color(0xFFFFF8E1),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Text(
                vm.averageRating.toStringAsFixed(1),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(width: 4),
              const Icon(Icons.star, color: Colors.amber, size: 20),
              const SizedBox(width: 8),
              Text(
                'Product Ratings & Reviews (${vm.reviewCount})',
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              const Spacer(),
              if (vm.hasReviews)
                GestureDetector(
                  onTap: () {
                    // Navigate to full reviews page
                    _showAllReviews(context, vm);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'View All',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        // Reviews content
        Container(
          color: Colors.white,
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
          child: _buildReviewsContent(vm),
        ),
      ],
    ),
  );
}

Widget _buildReviewsContent(ShopDetailViewModel vm) {
  if (vm.isLoadingReviews && !vm.hasReviews) {
    // Show loading skeletons
    return Column(
      children: List.generate(
        3,
        (index) => const ShopDetailReviewCardSkeleton(),
      ),
    );
  }

  if (vm.reviewsError != null && !vm.hasReviews) {
    return Text(
      'Error loading reviews: ${vm.reviewsError}',
      style: const TextStyle(
        fontSize: 14,
        color: Colors.red,
      ),
    );
  }

  if (!vm.hasReviews) {
    return const Text(
      'No reviews available yet. Be the first to review this product!',
      style: TextStyle(
        fontSize: 14,
        color: Colors.grey,
        fontStyle: FontStyle.italic,
      ),
    );
  }

  // Show first 3 reviews
  final displayReviews = vm.reviews.take(3).toList();
  
  return Column(
    children: [
      ...displayReviews.map((review) => ShopDetailReviewCard(review: review)),
      if (vm.reviews.length > 3)
        Padding(
          padding: const EdgeInsets.only(top: 8),
          child: GestureDetector(
            onTap: () => _showAllReviews(context, vm),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'View ${vm.reviews.length - 3} more reviews',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  const Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey),
                ],
              ),
            ),
          ),
        ),
    ],
  );
}

// Add method to show all reviews
void _showAllReviews(BuildContext context, ShopDetailViewModel vm) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (context) => DraggableScrollableSheet(
      initialChildSize: 0.9,
      maxChildSize: 0.95,
      minChildSize: 0.5,
      expand: false,
      builder: (context, scrollController) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
              ),
              child: Row(
                children: [
                  Text(
                    'Reviews (${vm.reviewCount})',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            // Reviews list
            Expanded(
              child: ListView.builder(
                controller: scrollController,
                padding: const EdgeInsets.all(16),
                itemCount: vm.reviews.length + (vm.hasMoreReviews ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index >= vm.reviews.length) {
                    // Load more indicator
                    if (vm.isLoadingReviews) {
                      return const Padding(
                        padding: EdgeInsets.all(16),
                        child: Center(child: CircularProgressIndicator()),
                      );
                    } else {
                      // Load more button
                      return Padding(
                        padding: const EdgeInsets.all(16),
                        child: ElevatedButton(
                          onPressed: () => vm.loadMoreReviews(),
                          child: const Text('Load More Reviews'),
                        ),
                      );
                    }
                  }
                  
                  return ShopDetailReviewCard(review: vm.reviews[index]);
                },
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

  Widget _buildBottomActionBar(BuildContext context, ShopDetailViewModel vm) {
    return SizedBox(
      height: 60, // Taller for a more prominent bottom action bar
      child: Row(
        children: [
          // Chat Now
          Expanded(
            child: SizedBox.expand(
              child: OutlinedButton(
                onPressed: () => vm.onChatNow(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.grey),
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: const TextStyle(fontSize: 14),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(Icons.chat_outlined, size: 24),
                    SizedBox(height: 4),
                    Text('Chat Now'),
                  ],
                ),
              ),
            ),
          ),
          // Add to Cart
          Expanded(
            child: SizedBox.expand(
              child: OutlinedButton(
                onPressed: () => vm.onAddToCart(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.grey),
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: const TextStyle(fontSize: 14),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(Icons.shopping_cart_outlined, size: 24),
                    SizedBox(height: 4),
                    Text('Add to Cart'),
                  ],
                ),
              ),
            ),
          ),
          // Buy Now
          Expanded(
            child: SizedBox.expand(
              child: ElevatedButton(
                onPressed: () => vm.onBuyNow(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber[600],
                  foregroundColor: Colors.black,
                  padding: EdgeInsets.zero,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                child: const Text('Buy Now'),
              ),
            ),
          ),
        ],
      ),
    );
  }
}