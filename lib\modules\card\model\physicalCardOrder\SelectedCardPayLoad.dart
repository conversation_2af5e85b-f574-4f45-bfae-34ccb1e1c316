import '../cardPack/CardItem.dart';

class SelectedCardPayload {
  final String id;
  final String name;
  final String category;
  final int quantity;

  SelectedCardPayload({
    required this.id,
    required this.name,
    required this.category,
    required this.quantity,
  });

  factory SelectedCardPayload.fromCardItem(CardItem card, int quantity) {
    return SelectedCardPayload(
      id: card.id,
      name: card.name,
      category: card.category,
      quantity: quantity,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'category': category,
    'quantity': quantity,
  };
}
