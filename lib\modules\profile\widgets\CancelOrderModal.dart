import 'package:flutter/material.dart';
import '../model/MyOrderModel.dart';

class CancelOrderModal extends StatelessWidget {
  final MyOrderModel order;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  const CancelOrderModal({
    super.key,
    required this.order,
    this.onConfirm,
    this.onCancel,
  });

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  static Future<bool?> show({
    required BuildContext context,
    required MyOrderModel order,
  }) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CancelOrderModal(
          order: order,
          onConfirm: () => Navigator.of(context).pop(true),
          onCancel: () => Navigator.of(context).pop(false),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: EdgeInsets.all(getResponsivePadding(24, context)),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Warning icon
            Container(
              width: getResponsivePadding(60, context),
              height: getResponsivePadding(60, context),
              decoration: BoxDecoration(
                color: const Color(0xFFEF4444).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.warning_rounded,
                size: getResponsivePadding(32, context),
                color: const Color(0xFFEF4444),
              ),
            ),

            SizedBox(height: getResponsivePadding(20, context)),

            // Title
            Text(
              'Cancel Order',
              style: TextStyle(
                fontSize: getResponsiveFontSize(20, context),
                fontWeight: FontWeight.w700,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: getResponsivePadding(12, context)),

            // Order info
            Container(
              padding: EdgeInsets.all(getResponsivePadding(12, context)),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200, width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Order ID: ${order.id}',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(12, context),
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  SizedBox(height: getResponsivePadding(4, context)),
                  Text(
                    order.orderItems?.first.productSkuName ?? 'Product',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(14, context),
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: getResponsivePadding(4, context)),
                  Text(
                    'Total: RM${order.totalPrice ?? 0}',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(14, context),
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFFEF4444),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: getResponsivePadding(16, context)),

            // Warning message
            Text(
              'Are you sure you want to cancel this order?',
              style: TextStyle(
                fontSize: getResponsiveFontSize(16, context),
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: getResponsivePadding(8, context)),

            Text(
              order.state == 0
                  ? 'You can cancel this order without any charges.'
                  : 'Cancelling this order may incur additional fees. Please check our cancellation policy.',
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: Colors.grey.shade600,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: getResponsivePadding(24, context)),

            // Action buttons
            Row(
              children: [
                // Cancel button
                Expanded(
                  child: ElevatedButton(
                    onPressed: onCancel,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade100,
                      foregroundColor: Colors.black54,
                      elevation: 0,
                      padding: EdgeInsets.symmetric(
                        vertical: getResponsivePadding(12, context),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(color: Colors.grey.shade300, width: 1),
                      ),
                    ),
                    child: Text(
                      'Keep Order',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),

                SizedBox(width: getResponsivePadding(12, context)),

                // Confirm button
                Expanded(
                  child: ElevatedButton(
                    onPressed: onConfirm,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFEF4444),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      padding: EdgeInsets.symmetric(
                        vertical: getResponsivePadding(12, context),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Cancel Order',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
