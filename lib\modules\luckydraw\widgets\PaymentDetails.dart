import 'package:flutter/material.dart';

class PaymentDetails extends StatelessWidget {
  const PaymentDetails({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375; // base width 375
    double scaleH(double value) =>
        value * screenHeight / 812; // base height 812
    double scaleText(double value) => value * screenWidth / 375;

    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(scaleW(5)),
      ),
      color: Colors.white,
      child: Padding(
        padding: EdgeInsets.all(scaleW(12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Details',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: scaleText(15),
              ),
            ),
            SizedBox(height: scaleH(15)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Product Price',
                  style: TextStyle(fontSize: scaleText(13)),
                ),
                Text('RM10.00', style: TextStyle(fontSize: scaleText(13))),
              ],
            ),
            SizedBox(height: scaleH(4)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Delivery Fees',
                  style: TextStyle(fontSize: scaleText(13)),
                ),
                Text('-', style: TextStyle(fontSize: scaleText(13))),
              ],
            ),
            Divider(height: scaleH(20)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Payment',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: scaleText(13),
                  ),
                ),
                Text(
                  'RM0.00',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: scaleText(13),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
