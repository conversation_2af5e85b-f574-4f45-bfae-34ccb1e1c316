import 'package:flutter/material.dart';

class LuckyPointsFAQPage extends StatelessWidget {
  const LuckyPointsFAQPage({super.key});

  final List<Map<String, String>> faqs = const [
    {
      'question': 'What are Lucky Points?',
      'answer': 'Lucky Points are reward points you earn through purchases, promotions, or challenges within the app.'
    },
    {
      'question': 'How do I earn Lucky Points?',
      'answer': 'You can earn Lucky Points by completing eligible purchases, joining promotions, or referring friends.'
    },
    {
      'question': 'Where can I check my Lucky Points balance?',
      'answer': 'You can view your current Lucky Points balance in the “Rewards” section of your profile.'
    },
    {
      'question': 'Do Lucky Points expire?',
      'answer': 'Yes, Lucky Points typically expire after 12 months unless stated otherwise in a promotion.'
    },
    {
      'question': 'Can I transfer my Lucky Points to another account?',
      'answer': 'No, Lucky Points are non-transferable and tied to your registered account only.'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        iconTheme: const IconThemeData(color: Colors.black),
        title: const Text(
          'Lucky Points FAQ',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        centerTitle: true,
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: faqs.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final faq = faqs[index];
          return Card(
            elevation: 1,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ExpansionTile(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              title: Row(
                children: [
                  const Icon(Icons.help_outline, color: Color.fromARGB(255, 13, 13, 13)),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      faq['question']!,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
              children: [
                Text(
                  faq['answer']!,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
