import 'package:flutter/material.dart';

class RedemptionBottomBar extends StatelessWidget {
  final int selectedCount;
  final int totalRequired;
  final VoidCallback onConfirm;
  final bool isEligible; // ✅ NEW

  const RedemptionBottomBar({
    super.key,
    required this.selectedCount,
    required this.totalRequired,
    required this.onConfirm,
    required this.isEligible, // ✅ NEW
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final fontSize = screenWidth < 360 ? 13.0 : 14.0;
    final fontSizeBtn = screenWidth < 360 ? 14.0 : 15.0;
    final buttonWidth = screenWidth < 360 ? 90.0 : screenWidth * 0.3;

    return Container(
      height: screenHeight * 0.1,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            offset: const Offset(0, -3),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'Total Select: $selectedCount / $totalRequired',
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          SizedBox(
            width: buttonWidth,
            height: 42,
            child: DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: isEligible ? const Color.fromARGB(78, 244, 67, 54) : const Color.fromARGB(29, 0, 0, 0) ,
                    offset: const Offset(0, 4),
                    blurRadius: 6,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: isEligible ? Colors.red : Colors.grey,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: () {
                  if (isEligible) {
                    onConfirm();
                  } else {
                    Navigator.pop(context); // ✅ Back to exchange page
                  }
                },
                child: Text(
                  isEligible ? 'Confirm' : 'Back',
                  style: TextStyle(
                    fontSize: fontSizeBtn,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

