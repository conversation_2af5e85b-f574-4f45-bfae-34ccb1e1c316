{"version": "0.2.0", "configurations": [{"name": "Flutter", "request": "launch", "type": "dart", "program": "lib/main.dart"}, {"name": "Flutter (Profile Mode)", "request": "launch", "type": "dart", "flutterMode": "profile", "program": "lib/main.dart"}, {"name": "Flutter (Release Mode)", "request": "launch", "type": "dart", "flutterMode": "release", "program": "lib/main.dart"}, {"name": "Flutter (Android Emulator)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "android"}, {"name": "Flutter (iOS Simulator)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "ios"}, {"name": "Flutter (Chrome)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "chrome"}, {"name": "Flutter (Edge)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "edge"}, {"name": "Flutter (Windows)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "windows"}, {"name": "Flutter (macOS)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "macos"}, {"name": "Flutter (Linux)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "linux"}, {"name": "Flutter (Connected Device)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "connected"}]}