import '../../model/historyCard/HistoryCardItem.dart';

class MockHistoryCardData {
  final List<HistoryCardItem> allRecords;

  MockHistoryCardData(this.allRecords);

  List<HistoryCardItem> getFilteredRecords({
    required String mode, // today / month / year
    required String sortOrder,
    required String selectedFilter,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    List<HistoryCardItem> filtered = [...allRecords];

    // Filter by mode
    final now = DateTime.now();
    filtered = filtered.where((item) {
      if (mode == 'today') {
        return item.timestamp.day == now.day &&
            item.timestamp.month == now.month &&
            item.timestamp.year == now.year;
      } else if (mode == 'month') {
        if (startDate == null || endDate == null) return false;
        return item.timestamp.isAfter(startDate.subtract(const Duration(days: 1))) &&
               item.timestamp.isBefore(endDate.add(const Duration(days: 1)));
      } else if (mode == 'year') {
        if (startDate == null || endDate == null) return false;
        return item.timestamp.year >= startDate.year &&
               item.timestamp.year <= endDate.year;
      }
      return false;
    }).toList();

    // Filter by type
    if (selectedFilter != 'All Record') {
      filtered = filtered.where((item) {
        if (selectedFilter == 'Gift') {
          return item.type == 'Gift' && item.amount > 0;
        }
        return item.type == selectedFilter;
      }).toList();
    }

    // Sort
    filtered.sort((a, b) =>
      sortOrder == 'Latest'
          ? b.timestamp.compareTo(a.timestamp)
          : a.timestamp.compareTo(b.timestamp));
    return filtered;
  }
}
