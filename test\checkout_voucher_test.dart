import 'package:flutter_test/flutter_test.dart';
import 'package:luckymall/modules/checkout/view-model/CheckoutVM.dart';

void main() {
  group('CheckoutVM Voucher Tests', () {
    late CheckoutVM checkoutVM;

    setUp(() {
      checkoutVM = CheckoutVM(checkoutType: CheckoutType.shop);
    });

    test('should initialize with no voucher selected', () {
      expect(checkoutVM.selectedVoucher, isNull);
      expect(checkoutVM.voucherDiscount, equals(0.0));
      expect(checkoutVM.voucherDescription, equals(''));
    });

    test('should select voucher correctly', () {
      final voucher = {
        'id': '1',
        'amount': 10.0,
        'minSpend': 50.0,
        'description': 'RM10 Off',
      };

      checkoutVM.selectVoucher(voucher);

      expect(checkoutVM.selectedVoucher, equals(voucher));
      expect(checkoutVM.voucherDiscount, equals(10.0));
      expect(checkoutVM.voucherDescription, equals('RM10 Off'));
    });

    test('should clear voucher correctly', () {
      final voucher = {
        'id': '1',
        'amount': 10.0,
        'minSpend': 50.0,
        'description': 'RM10 Off',
      };

      checkoutVM.selectVoucher(voucher);
      expect(checkoutVM.selectedVoucher, isNotNull);

      checkoutVM.clearVoucher();
      expect(checkoutVM.selectedVoucher, isNull);
      expect(checkoutVM.voucherDiscount, equals(0.0));
      expect(checkoutVM.voucherDescription, equals(''));
    });

    test('should check if voucher can be applied', () {
      final productData = {'price': 100.0, 'discount': 0.0};
      checkoutVM.setProductData(productData);

      final validVoucher = {
        'id': '1',
        'amount': 10.0,
        'minSpend': 50.0,
        'description': 'RM10 Off',
      };

      final invalidVoucher = {
        'id': '2',
        'amount': 10.0,
        'minSpend': 150.0,
        'description': 'RM10 Off',
      };

      expect(checkoutVM.canApplyVoucher(validVoucher), isTrue);
      expect(checkoutVM.canApplyVoucher(invalidVoucher), isFalse);
    });

    test('should filter available vouchers by minimum spend', () {
      final productData = {'price': 100.0, 'discount': 0.0};
      checkoutVM.setProductData(productData);

      final allVouchers = [
        {
          'id': '1',
          'amount': 10.0,
          'minSpend': 50.0,
          'description': 'RM10 Off',
        },
        {
          'id': '2',
          'amount': 15.0,
          'minSpend': 150.0,
          'description': 'RM15 Off',
        },
        {'id': '3', 'amount': 8.0, 'minSpend': 80.0, 'description': 'RM8 Off'},
      ];

      final availableVouchers = checkoutVM.getAvailableVouchers(allVouchers);

      expect(availableVouchers.length, equals(2));
      expect(availableVouchers[0]['id'], equals('1'));
      expect(availableVouchers[1]['id'], equals('3'));
    });

    test('should show voucher option only for shop checkout', () {
      final shopVM = CheckoutVM(checkoutType: CheckoutType.shop);
      final blindboxVM = CheckoutVM(checkoutType: CheckoutType.blindbox);
      final luckydrawVM = CheckoutVM(checkoutType: CheckoutType.luckydraw);
      final physicalCardVM = CheckoutVM(
        checkoutType: CheckoutType.physicalCard,
      );

      expect(shopVM.showVoucherOption, isTrue);
      expect(blindboxVM.showVoucherOption, isFalse);
      expect(luckydrawVM.showVoucherOption, isFalse);
      expect(physicalCardVM.showVoucherOption, isFalse);
    });
  });
}
