import 'package:flutter/material.dart';

// Singleton class to manage background state with reactive updates
class BackgroundStateManager {
  static final BackgroundStateManager _instance = BackgroundStateManager._internal();
  factory BackgroundStateManager() => _instance;
  BackgroundStateManager._internal();

  // Using ValueNotifier to make the background reactive
  final ValueNotifier<Color> _backgroundNotifier = ValueNotifier<Color>(const Color(0xFFFFEAAB));
  
  // Getters
  Color get currentBackground => _backgroundNotifier.value;
  ValueNotifier<Color> get backgroundNotifier => _backgroundNotifier;
  
  // Method to update background
  void updateBackground(Color newBackground) {
    _backgroundNotifier.value = newBackground;
  }
  
  // Method to get background based on selection data
  Color getBackgroundFromSelection({
    required int tabIndex,
    required int backgroundIndex,
    int fantasyCategory = 0,
    int zodiacCategory = 0,
  }) {
    switch (tabIndex) {
      case 0: // Default
        final defaultColors = [
          const Color(0xFFFFE082),
          const Color(0xFF81C784),
          const Color(0xFF64B5F6),
        ];
        return defaultColors[backgroundIndex % defaultColors.length];
        
      case 1: // Fantasy
        if (fantasyCategory == 0) {
          // Royal backgrounds - Fixed to handle King/Queen selection
          final royalColors = [
            const Color(0xFF8D6E63), // King
            const Color(0xFF8D6E63), // Queen (same color in your UI)
          ];
          return royalColors[backgroundIndex % royalColors.length];
        } else {
          // Generic fantasy backgrounds
          return _getFantasyColor(backgroundIndex);
        }
        
      case 2: // Zodiac
        return _getZodiacColor(zodiacCategory, backgroundIndex);
        
      default:
        return const Color(0xFFFFEAAB);
    }
  }
  
  // Dispose method to clean up resources
  void dispose() {
    _backgroundNotifier.dispose();
  }
  
  static Color _getFantasyColor(int index) {
    final colors = [
      Color(0xFFFFE082), Color(0xFF81C784), Color(0xFF64B5F6), Color(0xFFFF8A65),
      Color(0xFFBA68C8), Color(0xFFFFAB91), Color(0xFF80CBC4), Color(0xFFFF7043),
      Color(0xFFF48FB1), Color(0xFF90CAF9), Color(0xFFA5D6A7), Color(0xFFFFCC02),
      Color(0xFFCE93D8),
    ];
    return colors[index % colors.length];
  }

  static Color _getZodiacColor(int zodiacIndex, int styleIndex) {
    final baseColors = [
      Color(0xFFE57373), Color(0xFF81C784), Color(0xFF64B5F6), Color(0xFFFFB74D),
      Color(0xFFBA68C8), Color(0xFF4DB6AC), Color(0xFFFF8A65), Color(0xFFF06292),
      Color(0xFF9575CD), Color(0xFF4FC3F7), Color(0xFFAED581), Color(0xFFFFD54F),
    ];
    
    final base = baseColors[zodiacIndex % baseColors.length];
    final opacity = 0.3 + (styleIndex % 8) * 0.1;
    return base.withOpacity(opacity.clamp(0.3, 1.0));
  }
}