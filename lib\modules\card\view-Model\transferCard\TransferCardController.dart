// TransferCardController.dart
import 'package:flutter/material.dart';
import '../../model/cardPack/CardItem.dart';
import '../cardPack/CardCategoryViewModel.dart';

class TransferCardController extends ChangeNotifier {
  final CardCategoryViewModel categoryVM;
  final List<CardItem> allCards;

  Map<String, int> selectedQuantities = {};
  String _currentCategory = '';
  String _currentSeries = 'fantasy';
  bool _isFetching = true;
  bool groupSelected = false;
  bool isTransferring = false;

  List<CardItem> ownedCards = [];

  TransferCardController({required this.categoryVM, required this.allCards}) {
    _initialize();
  }

  String get currentSeries => _currentSeries;

  // ----------------------------
  // Init & Loading
  // ----------------------------
  Future<void> _initialize() async {
    _isFetching = true;
    notifyListeners();

    try {
      await categoryVM.loadSeriesData();
      categoryVM.selectSeries(_currentSeries);
      await _loadCards();
    } catch (e) {
      debugPrint('Error initializing transfer card data: $e');
    }

    _isFetching = false;
    notifyListeners();
  }

  Future<void> loadCards() async {
    _isFetching = true;
    notifyListeners();

    await Future.delayed(const Duration(seconds: 2)); // simulate fetch
    _isFetching = false;
    notifyListeners();
  }

  void _loadCardsForCategory(String category) {
    final filtered = allCards
        .where((card) => card.category == category && card.isOwned)
        .toList();
    _currentCategory = category;
    ownedCards = filtered;
    groupSelected = _isAllCategoryCardsSelected(filtered);
    notifyListeners();
  }

  void onSeriesAndCategoryChanged(String series, String category) {
    _currentSeries = series;
    _currentCategory = category;

    // Update CategoryViewModel
    categoryVM.selectSeries(series);
    categoryVM.selectCategory(category);

    // Reload cards with new series and category
    _loadCards();
    notifyListeners();
  }

  Future<void> _loadCards() async {
    _isFetching = true;
    notifyListeners();

    try {
      // Filter cards based on both series and category
      final filtered = allCards.where((card) =>
          card.series.toLowerCase() == _currentSeries.toLowerCase() &&
          card.category.toLowerCase() == _currentCategory.toLowerCase() &&
          card.isOwned)
          .toList();

      ownedCards = filtered;
      groupSelected = _isAllCategoryCardsSelected(filtered);
    } catch (e) {
      debugPrint('Error loading cards: $e');
      ownedCards = [];
      groupSelected = false;
    }

    _isFetching = false;
    notifyListeners();
  }

  // ----------------------------
  // Category & Selection
  // ----------------------------
  void onCategoryChanged(String category) {
    _currentCategory = category;
    _loadCards();
    notifyListeners();
  }

  void onToggleCardSelection(String id, bool selected) {
    if (selected) {
      selectedQuantities[id] = 1;
    } else {
      selectedQuantities.remove(id);
    }
    groupSelected = _isAllCategoryCardsSelected(ownedCards);
    notifyListeners();
  }

  void onUpdateQuantity(String id, int newQuantity, BuildContext context) {
    final currentTotal = selectedQuantities.entries.fold<int>(0, (sum, entry) {
      return entry.key == id ? sum : sum + entry.value;
    });
    final newTotal = currentTotal + newQuantity;
    if (newTotal > 10) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Maximum of 10 cards allowed."),
          backgroundColor: Colors.redAccent,
        ),
      );
      return;
    }
    selectedQuantities[id] = newQuantity;
    groupSelected = _isAllCategoryCardsSelected(ownedCards);
    notifyListeners();
  }

  void onToggleGroup() {
    groupSelected = !groupSelected;
    if (groupSelected) {
      for (var card in ownedCards) {
        selectedQuantities[card.id] = 1;
      }
    } else {
      selectedQuantities.clear();
    }
    notifyListeners();
  }

  bool _isAllCategoryCardsSelected(List<CardItem> categoryCards) {
    for (var card in categoryCards) {
      if (!selectedQuantities.containsKey(card.id)) return false;
    }
    return true;
  }

  // ----------------------------
  // Transfer Logic
  // ----------------------------
  Future<bool> transferCards(String accountId) async {
    // ✅ Validate accountId
    if (!_isValidAccountId(accountId)) {
      return false;
    }

    // 🔄 Start transfer
    isTransferring = true;
    notifyListeners();

    try {
      // TODO: Replace with API call
      await Future.delayed(const Duration(seconds: 1));

      // For demo: assume "************" is valid
      final isSuccess = accountId == '************';

      if (isSuccess) {
        _resetSelection();
      }

      return isSuccess;
    } finally {
      // 🔄 End transfer (whether success or fail)
      isTransferring = false;
      notifyListeners();
    }
  }

  bool _isValidAccountId(String accountId) {
    return RegExp(r'^\d{1,12}$').hasMatch(accountId);
  }

  void _resetSelection() {
    selectedQuantities.clear();
    groupSelected = false;
    notifyListeners();
  }

  // ----------------------------
  // Getters
  // ----------------------------
  int get totalSelected =>
      selectedQuantities.values.fold(0, (sum, qty) => sum + qty);

  bool get hasSelected => selectedQuantities.isNotEmpty;

  List<CardItem> get currentVisibleCards => ownedCards;

  Map<String, int> get selectedMap => selectedQuantities;

  String get currentCategory => _currentCategory;

  bool get isFetching => _isFetching;
}
