import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PointRangeBottomSheet extends StatefulWidget {
  final Function(int min, int max)? onConfirm;
  final VoidCallback? onReset;

  const PointRangeBottomSheet({super.key, this.onConfirm, this.onReset});

  @override
  State<PointRangeBottomSheet> createState() => _PointRangeBottomSheetState();
}

class _PointRangeBottomSheetState extends State<PointRangeBottomSheet> {
  String? selectedRange;
  final TextEditingController minController = TextEditingController(text: '0');
  final TextEditingController maxController = TextEditingController(
    text: '999999',
  );

  final List<Map<String, dynamic>> predefinedRanges = [
    {'label': '0 - 50', 'min': 0, 'max': 50},
    {'label': '51 - 100', 'min': 51, 'max': 100},
    {'label': '101 - 300', 'min': 101, 'max': 300},
    {'label': '301 - 500', 'min': 301, 'max': 500},
    {'label': '501 - 1000', 'min': 501, 'max': 1000},
    {'label': '1000 - 1500', 'min': 1000, 'max': 1500},
  ];

  @override
  void dispose() {
    minController.dispose();
    maxController.dispose();
    super.dispose();
  }

  void _selectRange(String range, int min, int max) {
    setState(() {
      selectedRange = range;
      minController.text = min.toString();
      maxController.text = max.toString();
    });
  }

  void _resetRange() {
    setState(() {
      selectedRange = null;
      minController.text = '0';
      maxController.text = '999999';
    });
    widget.onReset?.call();
  }

  void _confirmRange() {
    final min = int.tryParse(minController.text) ?? 0;
    final max = int.tryParse(maxController.text) ?? 999999;
    widget.onConfirm?.call(min, max);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Point Range (Pts)',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: const Icon(Icons.close, size: 24, color: Colors.black),
                ),
              ],
            ),
          ),

          // Predefined range buttons
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                // First row
                Row(
                  children: [
                    for (int i = 0; i < 3; i++)
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: i < 2 ? 12 : 0),
                          child: _buildRangeButton(predefinedRanges[i]),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                // Second row
                Row(
                  children: [
                    for (int i = 3; i < 6; i++)
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: i < 5 ? 12 : 0),
                          child: _buildRangeButton(predefinedRanges[i]),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 10),

          // Custom range input
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                // Min input
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: TextField(
                      controller: minController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                      onChanged: (value) {
                        setState(() {
                          selectedRange = null;
                        });
                      },
                    ),
                  ),
                ),

                // Separator line
                Container(
                  width: 40,
                  height: 2,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  color: Colors.grey.shade600,
                ),

                // Max input
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: TextField(
                      controller: maxController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                      onChanged: (value) {
                        setState(() {
                          selectedRange = null;
                        });
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Action buttons
          Padding(
            padding: const EdgeInsets.all(10),
            child: Row(
              children: [
                // Reset button
                Expanded(
                  child: GestureDetector(
                    onTap: _resetRange,
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade400,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Center(
                        child: Text(
                          'Reset',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Confirm button
                Expanded(
                  child: GestureDetector(
                    onTap: _confirmRange,
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color: const Color(0xFFffbf00),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Center(
                        child: Text(
                          'Confirm',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Bottom safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildRangeButton(Map<String, dynamic> range) {
    final isSelected = selectedRange == range['label'];

    return GestureDetector(
      onTap: () => _selectRange(range['label'], range['min'], range['max']),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFffbf00) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? const Color(0xFFffbf00) : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            range['label'],
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: isSelected ? Colors.black : Colors.black,
            ),
          ),
        ),
      ),
    );
  }
}
