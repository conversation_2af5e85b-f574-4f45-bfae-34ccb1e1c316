import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../model/exchangeCard/ExchangeCardModel.dart' as model;
import '../../widgets/ExchangeCard/InsufficientCardDialog.dart';
import '../cardPack/CardPackViewModel.dart';

class ExchangeCardViewModel {
  final CardPackViewModel cardPackViewModel;

  ExchangeCardViewModel({required this.cardPackViewModel});

  // Raw data
  final List<model.ExchangeCardModel> _allExchangeCards = [];

  // State
  final ValueNotifier<List<model.ExchangeCardModel>> exchangeCardsNotifier = ValueNotifier([]);
  final ValueNotifier<bool> isLoading = ValueNotifier(false);
  final ValueNotifier<model.CardRank?> selectedRankFilter = ValueNotifier(null); // null = all
  final ValueNotifier<model.SelectionType?> selectedTypeFilter = ValueNotifier(null); // null = all

  Future<void> loadExchangeItems() async {
    isLoading.value = true;
    await Future.delayed(const Duration(seconds: 1));

    final mockList = [
      model.ExchangeCardModel(
        id: '1',
        title: 'TNG Reload RM10',
        type: 'TNG',
        imageUrl: 'assets/images/card/icons/tngLogo.png',
        bwImageUrl: 'assets/images/card/icons/greyTng.png',
        isNetworkImage: false,
        requirementDetail: 'Require all Knight Series',
        requiredCardIds: [
          'ColdSweatKnight',
          'CoolKnight',
          'CryingKnight',
          'HappyKnight',
          'DizzyKnight',
          'LaughingKnight',
          'LovelyKnight',
          'PoutingKnight',
          'ScaredKnight',
          'ThinkingKnight',
        ],
        rank: model.CardRank.ssr,
        selectionType: model.SelectionType.auto,
      ),
      model.ExchangeCardModel(
        id: '2',
        title: 'Voucher RM20',
        type: 'Voucher',
        imageUrl: 'assets/images/card/icons/tngLogo.png',
        bwImageUrl: 'assets/images/card/icons/greyTng.png',
        isNetworkImage: false,
        requirementDetail: 'Require 2 Royal Cards',
        requiredCardIds: ['king', 'queen'],
        rank: model.CardRank.ur,
        selectionType: model.SelectionType.selfSelect,
      ),
    ];

    _allExchangeCards.clear();
    _allExchangeCards.addAll(mockList);
    _applyFilters();

    isLoading.value = false;
  }

  void setRankFilter(model.CardRank? rank) {
    selectedRankFilter.value = rank;
    _applyFilters();
  }

  void setSelectionTypeFilter(model.SelectionType? type) {
    selectedTypeFilter.value = type;
    _applyFilters();
  }

  void _applyFilters() {
    var filtered = _allExchangeCards;

    if (selectedRankFilter.value != null) {
      filtered = filtered.where((c) => c.rank == selectedRankFilter.value).toList();
    }

    if (selectedTypeFilter.value != null) {
      filtered = filtered.where((c) => c.selectionType == selectedTypeFilter.value).toList();
    }

    exchangeCardsNotifier.value = filtered;
  }

  bool checkEligibility(model.ExchangeCardModel model) {
    final ownedIds = cardPackViewModel.getOwnedCardIds();
    return model.requiredCardIds.every((id) => ownedIds.contains(id));
  }

  void navigateToConfirmRedemption(BuildContext context, model.ExchangeCardModel model) {
    final ownedIds = cardPackViewModel.getOwnedCardIds();
    final ownedCardsToUse = model.requiredCardIds.where((id) => ownedIds.contains(id)).toList();

    context.push(
      '/confirm-redemption',
      extra: {
        'model': model,
        'selectedCardIds': ownedCardsToUse,
        'totalRequired': model.requiredCardIds.length,
        'selectionType': model.selectionType // or SelectionType.auto
      },
    );
  }

  void showInsufficientDialog(BuildContext context, model.ExchangeCardModel model) {
    showDialog(
      context: context,
      builder: (_) => InsufficientCardDialog(
        model: model,
        onConfirm: () => navigateToConfirmRedemption(context, model),
      ),
    );
  }
}

