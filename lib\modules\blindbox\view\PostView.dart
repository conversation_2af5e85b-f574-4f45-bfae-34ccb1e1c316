import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:go_router/go_router.dart';
import '../view-model/PostVM.dart';

class PostView extends StatefulWidget {
  const PostView({super.key, required this.post});
  final Map<String, dynamic> post;

  @override
  State<PostView> createState() => _PostViewState();
}

class _PostViewState extends State<PostView> {
  late bool isFavorite;
  late int likeCount;
  late PageController _pageController;
  int _currentPage = 0;

  // Helper method to extract images from post data
  List<String> _extractImages(Map<String, dynamic> post) {
    final List<String> images = [];

    if (post['images'] != null) {
      final dynamicImages = post['images'] as List<dynamic>;

      for (int i = 0; i < dynamicImages.length; i++) {
        final item = dynamicImages[i];

        if (item != null && item is String && item.isNotEmpty) {
          images.add(item);
        }
      }
    }

    return images;
  }

  @override
  void initState() {
    super.initState();
    isFavorite = widget.post['isFavorite'] ?? false;
    likeCount = widget.post['likeCount'] ?? 0;

    // Only initialize page controller if there are multiple images
    final images = _extractImages(widget.post);
    if (images.length > 1) {
      _pageController = PageController();
    }
  }

  @override
  void dispose() {
    if (_pageController.hasClients) {
      _pageController.dispose();
    }
    super.dispose();
  }

  void toggleFavorite() {
    setState(() {
      isFavorite = !isFavorite;
      if (isFavorite) {
        likeCount++;
      } else {
        likeCount = likeCount > 0 ? likeCount - 1 : 0;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final username = widget.post['username'] as String? ?? 'Unknown';
    final title = widget.post['title'] as String? ?? '';

    // Use helper method to extract images
    final images = _extractImages(widget.post);

    final imageHeight = PostVM.getResponsiveImageHeight(context);
    final avatarRadius = PostVM.getResponsiveAvatarRadius(context);

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(
          'Details',
          style: TextStyle(
            color: Colors.black,
            fontSize: PostVM.getResponsiveFontSize(18, context),
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back_ios_new_rounded,
            color: Colors.black,
            size: PostVM.getResponsiveFontSize(24, context),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(top: BorderSide(color: Color(0xFFE8E8E8))),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // User info row
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: PostVM.getResponsivePadding(16, context),
                  vertical: PostVM.getResponsivePadding(12, context),
                ),
                child: GestureDetector(
                  onTap: () {
                    context.push('/blind-box/profile/$username');
                  },
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: avatarRadius,
                        backgroundColor: Colors.grey,
                        child: Icon(
                          Icons.person,
                          size: PostVM.getResponsiveFontSize(25, context),
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(width: PostVM.getResponsivePadding(10, context)),
                      Text(
                        username,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: PostVM.getResponsiveFontSize(16, context),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Main image section - show carousel only if multiple images
              if (images.length > 1) ...[
                // Multiple images - show carousel with page indicator
                Stack(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      height: imageHeight,
                      child: PageView.builder(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _currentPage = index;
                          });
                        },
                        itemCount: images.length,
                        itemBuilder: (context, index) {
                          final imageUrl = images[index];
                          return CachedNetworkImage(
                            imageUrl: imageUrl,
                            width: double.infinity,
                            height: imageHeight,
                            fit: BoxFit.contain,
                            placeholder: (context, url) => Center(
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  width: double.infinity,
                                  height: imageHeight,
                                  color: Colors.grey[300]!,
                                ),
                              ),
                            ),
                            errorWidget: (context, url, error) => Center(
                              child: Icon(
                                Icons.broken_image,
                                size: PostVM.getResponsiveFontSize(60, context),
                                color: Colors.grey,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    Positioned(
                      bottom: PostVM.getResponsivePadding(12, context),
                      left: 0,
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          images.length,
                          (index) => Container(
                            width: PostVM.getResponsiveFontSize(8, context),
                            height: PostVM.getResponsiveFontSize(8, context),
                            margin: EdgeInsets.symmetric(
                              horizontal: PostVM.getResponsivePadding(
                                3,
                                context,
                              ),
                            ),
                            decoration: BoxDecoration(
                              color: _currentPage == index
                                  ? Colors.orange
                                  : Colors.grey[300],
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ] else if (images.isNotEmpty) ...[
                // Single image - show without carousel
                SizedBox(
                  width: double.infinity,
                  height: imageHeight,
                  child: CachedNetworkImage(
                    imageUrl: images.first,
                    width: double.infinity,
                    height: imageHeight,
                    fit: BoxFit.contain,
                    placeholder: (context, url) => Center(
                      child: Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: double.infinity,
                          height: imageHeight,
                          color: Colors.grey[300]!,
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Center(
                      child: Icon(
                        Icons.broken_image,
                        size: PostVM.getResponsiveFontSize(60, context),
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
              ] else ...[
                // No images - show placeholder
                SizedBox(
                  width: double.infinity,
                  height: imageHeight,
                  child: Container(
                    color: Colors.grey[200],
                    child: Center(
                      child: Icon(
                        Icons.image_not_supported,
                        size: PostVM.getResponsiveFontSize(60, context),
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
              ],
              // Details section at the bottom
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(bottom: BorderSide(color: Color(0xFFE8E8E8))),
                ),
                width: double.infinity,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: PostVM.getResponsivePadding(16, context),
                    vertical: PostVM.getResponsivePadding(8, context),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              title,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: PostVM.getResponsiveFontSize(
                                  16,
                                  context,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: PostVM.getResponsivePadding(2, context),
                            ),
                            Text(
                              '8 July 2025',
                              style: TextStyle(
                                color: Colors.black54,
                                fontSize: PostVM.getResponsiveFontSize(
                                  13,
                                  context,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          GestureDetector(
                            onTap: toggleFavorite,
                            child: Icon(
                              isFavorite
                                  ? Icons.thumb_up
                                  : Icons.thumb_up_alt_outlined,
                              color: isFavorite ? Colors.red : Colors.grey,
                              size: PostVM.getResponsiveFontSize(20, context),
                            ),
                          ),
                          SizedBox(
                            width: PostVM.getResponsivePadding(4, context),
                          ),
                          Text(
                            likeCount.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: PostVM.getResponsiveFontSize(
                                15,
                                context,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              // Product Promotion Section
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(color: Color(0xFFE8E8E8), width: 0.5),
                  ),
                ),
                padding: EdgeInsets.symmetric(
                  vertical: PostVM.getResponsivePadding(12, context),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: PostVM.getResponsivePadding(16, context),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.shopping_bag_outlined,
                            size: PostVM.getResponsiveFontSize(16, context),
                            color: Colors.orange,
                          ),
                          SizedBox(
                            width: PostVM.getResponsivePadding(6, context),
                          ),
                          Text(
                            'Get the product from this post',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: PostVM.getResponsiveFontSize(
                                14,
                                context,
                              ),
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: PostVM.getResponsivePadding(8, context)),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: PostVM.getResponsivePadding(16, context),
                      ),
                      child: GestureDetector(
                        onTap: () => PostVM.onProductTap(
                          context,
                          PostVM.getSampleProduct(),
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              // Product Image
                              Padding(
                                padding: EdgeInsets.only(
                                  left: PostVM.getResponsivePadding(
                                    12,
                                    context,
                                  ),
                                  top: PostVM.getResponsivePadding(12, context),
                                  bottom: PostVM.getResponsivePadding(
                                    12,
                                    context,
                                  ),
                                ),
                                child: Stack(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: CachedNetworkImage(
                                        imageUrl:
                                            PostVM.getSampleProduct()['imageUrl'],
                                        width:
                                            PostVM.getResponsiveProductImageHeight(
                                              context,
                                            ),
                                        placeholder: (context, url) => Container(
                                          width:
                                              PostVM.getResponsiveProductImageHeight(
                                                context,
                                              ),
                                          height:
                                              PostVM.getResponsiveProductImageHeight(
                                                context,
                                              ),
                                          color: Colors.grey[200],
                                          child: Center(
                                            child: Icon(
                                              Icons.image,
                                              color: Colors.grey[400],
                                              size:
                                                  PostVM.getResponsiveFontSize(
                                                    24,
                                                    context,
                                                  ),
                                            ),
                                          ),
                                        ),
                                        errorWidget: (context, url, error) => Container(
                                          width:
                                              PostVM.getResponsiveProductImageHeight(
                                                context,
                                              ),
                                          height:
                                              PostVM.getResponsiveProductImageHeight(
                                                context,
                                              ),
                                          color: Colors.grey[200],
                                          child: Center(
                                            child: Icon(
                                              Icons.broken_image,
                                              color: Colors.grey[400],
                                              size:
                                                  PostVM.getResponsiveFontSize(
                                                    24,
                                                    context,
                                                  ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    if (!PostVM.getSampleProduct()['isAvailable'])
                                      Positioned.fill(
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Colors.black.withValues(
                                              alpha: 0.3,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                          child: Center(
                                            child: Text(
                                              'Sold Out',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                                fontSize:
                                                    PostVM.getResponsiveFontSize(
                                                      10,
                                                      context,
                                                    ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              // Product Details
                              Expanded(
                                child: Padding(
                                  padding: EdgeInsets.all(
                                    PostVM.getResponsivePadding(16, context),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        PostVM.getSampleProduct()['title'],
                                        style: TextStyle(
                                          fontSize:
                                              PostVM.getResponsiveFontSize(
                                                16,
                                                context,
                                              ),
                                          fontWeight: FontWeight.w600,
                                          color: Colors.black87,
                                        ),
                                      ),
                                      SizedBox(
                                        height: PostVM.getResponsivePadding(
                                          4,
                                          context,
                                        ),
                                      ),
                                      Text(
                                        PostVM.getSampleProduct()['price'],
                                        style: TextStyle(
                                          fontSize:
                                              PostVM.getResponsiveFontSize(
                                                18,
                                                context,
                                              ),
                                          fontWeight: FontWeight.bold,
                                          color:
                                              PostVM.getSampleProduct()['isAvailable']
                                              ? Colors.orange
                                              : Colors.grey,
                                        ),
                                      ),
                                      SizedBox(
                                        height: PostVM.getResponsivePadding(
                                          8,
                                          context,
                                        ),
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal:
                                              PostVM.getResponsivePadding(
                                                12,
                                                context,
                                              ),
                                          vertical: PostVM.getResponsivePadding(
                                            6,
                                            context,
                                          ),
                                        ),
                                        decoration: BoxDecoration(
                                          color:
                                              PostVM.getSampleProduct()['isAvailable']
                                              ? Colors.orange
                                              : Colors.grey,
                                          borderRadius: BorderRadius.circular(
                                            20,
                                          ),
                                        ),
                                        child: Text(
                                          PostVM.getSampleProduct()['isAvailable']
                                              ? 'Buy Now'
                                              : 'Sold Out',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize:
                                                PostVM.getResponsiveFontSize(
                                                  12,
                                                  context,
                                                ),
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Extra space after product description to prevent it from being at bottom of screen
              SizedBox(height: PostVM.getResponsivePadding(100, context)),
            ],
          ),
        ),
      ),
    );
  }
}
