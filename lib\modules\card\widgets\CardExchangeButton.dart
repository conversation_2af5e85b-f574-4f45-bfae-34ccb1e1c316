import 'package:flutter/material.dart';

class CardExchangeButton extends StatefulWidget {
  final VoidCallback? onTap;
  final bool isEnabled;
  final bool showBadge;
  final bool showClaim;

  const CardExchangeButton({
    super.key,
    this.onTap,
    this.isEnabled = true,
    this.showBadge = true,
    this.showClaim = false,
  });

  @override
  State<CardExchangeButton> createState() => _CardExchangeButtonState();
}

class _CardExchangeButtonState extends State<CardExchangeButton>
    with TickerProviderStateMixin {
  late AnimationController _claimController;
  late AnimationController _waveController;
  late Animation<Color?> _colorAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();

    _claimController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat(reverse: true);

    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);

    _colorAnimation = ColorTween(
      begin: const Color(0xFFFFBF00),
      end: const Color(0xFFFCDC85),
    ).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _claimController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scale = (screenWidth / 390.0).clamp(0.85, 1.2); // base on iPhone 12 width

    double buttonWidth = 90 * scale;
    double buttonHeight = 60 * scale;
    double iconSize = 30 * scale;
    double fontSize = 11.5 * scale;

    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: widget.isEnabled ? widget.onTap : null,
      child: Opacity(
        opacity: widget.isEnabled ? 1.0 : 0.5,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            AnimatedBuilder(
              animation: _colorAnimation,
              builder: (context, child) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 150),
                  width: buttonWidth,
                  height: buttonHeight,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12 * scale),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: _isPressed
                          ? [Colors.orange.shade600, Colors.white]
                          : [_colorAnimation.value!, Colors.white],
                    ),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 8,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 4.0 * scale),
                        child: Image.asset(
                          'assets/images/card/icons/tngLogo.png',
                          width: iconSize,
                          height: iconSize,
                          fit: BoxFit.contain,
                        ),
                      ),
                      SizedBox(height: 3 * scale),
                      Text(
                        'Card Exchange',
                        style: TextStyle(
                          fontSize: fontSize,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF474747),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            //  RM Badge
            if (widget.showBadge)
              Positioned(
                top: -12 * scale,
                left: -12 * scale,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 8 * scale,
                    vertical: 4 * scale,
                  ),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFFFFBF00), Color(0xFFE97348)],
                    ),
                    borderRadius: BorderRadius.circular(20 * scale),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 4,
                        offset: Offset(1, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    'RM8,888',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10 * scale,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

            // Claim bubble
            if (widget.showClaim)
              Positioned(
                top: -6 * scale,
                right: -6 * scale,
                child: ScaleTransition(
                  scale: Tween(begin: 1.0, end: 1.3).animate(
                    CurvedAnimation(parent: _claimController, curve: Curves.easeInOut),
                  ),
                  child: Container(
                    width: 18 * scale,
                    height: 18 * scale,
                    alignment: Alignment.center,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color.fromRGBO(234, 11, 15, 1),
                          Color.fromRGBO(246, 201, 201, 1),
                        ],
                      ),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black26,
                          blurRadius: 3,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Text(
                      '!',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
