import 'package:flutter/material.dart';
import '../../model/cardPack/CardItem.dart';
import 'MockCards.dart'; // your allCardCatalog

class CardPackViewModel {
  final ValueNotifier<List<CardItem>> ownedCardsNotifier = ValueNotifier([]);

  Future<void> loadOwnedCards() async {
    // Simulate API fetch with delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Filter only owned cards with quantity > 0
    final owned = allCardCatalog.where((card) => card.quantity > 0).toList();

    ownedCardsNotifier.value = owned;
  }

  // Helper: Get owned card IDs
  List<String> getOwnedCardIds() {
    return ownedCardsNotifier.value.map((card) => card.id).toList();
  }

  // Optional: filter by category
  List<CardItem> getCardsByCategory(String category) {
    return ownedCardsNotifier.value.where((card) => card.category == category).toList();
  }
}
