import 'package:flutter/material.dart';
import '../../model/cardPack/CategoryItem.dart';
import '../../model/SeriesContent.dart';
import '../../services/SeriesService.dart';

class CardCategoryViewModel extends ChangeNotifier {
  String _selectedCategory = '';
  String get selectedCategory => _selectedCategory;

  List<CategoryItem> _categories = [];
  List<CategoryItem> get categories => List.unmodifiable(_categories);

  List<SeriesContent> _seriesList = [];
  List<SeriesContent> get seriesList => List.unmodifiable(_seriesList);

  SeriesContent? _selectedSeries;
  SeriesContent? get selectedSeries => _selectedSeries;

  final SeriesService _service = SeriesService();

  Future<void> loadSeriesData() async {
    _seriesList = [
      await _service.getSeriesContent('fantasy'),
      await _service.getSeriesContent('zodiac'),
    ];
    if (_seriesList.isNotEmpty) {
      selectSeries(_seriesList.first.name);
    }
    notifyListeners();
  }

  void selectSeries(String seriesName) {
    _selectedSeries = _seriesList.firstWhere(
      (s) => s.name.toLowerCase() == seriesName.toLowerCase(),
      orElse: () => _seriesList.first,
    );
    _categories = _selectedSeries?.categories ?? [];
    if (_categories.isNotEmpty) {
      _selectedCategory = _categories.first.key;
    }
    notifyListeners();
  }

  void selectCategory(String key) {
    _selectedCategory = key;
    notifyListeners();
  }

  String getCurrentSeriesName() {
    return _selectedSeries?.name.toLowerCase() ?? 'fantasy';
  }
}
