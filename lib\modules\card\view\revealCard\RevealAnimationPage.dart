import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:provider/provider.dart';
import '../../model/purchaseCard/PurchaseCardOrder.dart';
import '../../view-model/reveal/RevealCardViewModel.dart';
import '../../model/reveal/RevealedCard.dart';

class RevealAnimationPage extends StatefulWidget {
  final PurchaseCardOrder order;

  const RevealAnimationPage({super.key, required this.order});

  @override
  State<RevealAnimationPage> createState() => _RevealAnimationPageState();
}

class _RevealAnimationPageState extends State<RevealAnimationPage>
    with SingleTickerProviderStateMixin {
  late VideoPlayerController _controller;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.asset('assets/videos/animation.mp4')
      ..initialize().then((_) {
        setState(() {});
        _controller.play();
      });

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _goToCardReveal() {
    final viewModel = context.read<RevealCardViewModel>();

    final revealedCards = <RevealedCard>[
      RevealedCard(
        id: 'LovelyWizard',
        name: 'Lovely Wizard',
        category: 'wizard',
        series: 'fantasy',
        imagePath:
            'assets/images/card/cardType/fantasy/wizard/LovelyWizard.png',
      ),
      RevealedCard(
        id: 'LovelyKnight',
        name: 'Lovely Knight',
        category: 'knight',
        series: 'fantasy',
        imagePath:
            'assets/images/card/cardType/fantasy/knight/LovelyKnight.png',
      ),
      RevealedCard(
        id: 'LovelyKnight',
        name: 'Lovely Knight',
        category: 'knight',
        series: 'fantasy',
        imagePath:
            'assets/images/card/cardType/fantasy/knight/LovelyKnight.png',
      ),
      RevealedCard(
        id: 'LovelyKnight',
        name: 'Lovely Knight',
        category: 'knight',
        series: 'fantasy',
        imagePath:
            'assets/images/card/cardType/fantasy/knight/LovelyKnight.png',
      ),
      RevealedCard(
        id: 'LovelyKnight',
        name: 'Lovely Knight',
        category: 'knight',
        series: 'fantasy',
        imagePath:
            'assets/images/card/cardType/fantasy/knight/LovelyKnight.png',
      ),
    ];

    viewModel.loadCards(revealedCards);
    _controller.pause();
    _controller.dispose();

    context.findAncestorStateOfType<NavigatorState>()?.pushNamed('/reveal-view');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Fullscreen responsive video
          Positioned.fill(
            child: _controller.value.isInitialized
                ? FittedBox(
                    fit: BoxFit.cover,
                    child: SizedBox(
                      width: _controller.value.size.width,
                      height: _controller.value.size.height,
                      child: VideoPlayer(_controller),
                    ),
                  )
                : const Center(child: CircularProgressIndicator()),
          ),

          // Skip button (top right)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: ElevatedButton(
              onPressed: _goToCardReveal,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber.withOpacity(0.9),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                elevation: 6,
              ),
              child: const Text(
                'Skip Animation',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),

          // Center "CLICK TO REVEAL" button with glowing animation
          Center(
            child: FadeTransition(
              opacity: Tween(begin: 0.5, end: 1.0).animate(_animationController),
              child: GestureDetector(
                onTap: _goToCardReveal,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100),
                    gradient: const LinearGradient(
                      colors: [Color(0xFFFFF176), Color(0xFFFFC107)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.amberAccent.withOpacity(0.6),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: const Text(
                    "CLICK TO REVEAL",
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                      letterSpacing: 2,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
