import 'package:flutter/material.dart';
import '../data/LuckyDrawRepository.dart';
import '../model/ParticipationRecord.dart';

class ParticipationRecordsVM extends ChangeNotifier {
  final LuckyDrawRepository repository;

  ParticipationRecordsVM({LuckyDrawRepository? repository})
    : repository = repository ?? MockLuckyDrawRepository();

  bool isLoading = true;
  String selectedCategory = 'My Participation';
  List<ParticipationRecord> _all = const [];

  List<ParticipationRecord> get records {
    if (selectedCategory == 'My Prize') {
      return _all
          .where(
            (r) =>
                r.state == ParticipationCardState.claimPrize ||
                r.state == ParticipationCardState.alreadyClaimed,
          )
          .toList();
    }
    return _all;
  }

  Future<void> initialize() async {
    await fetchRecords();
  }

  Future<void> fetchRecords() async {
    isLoading = true;
    notifyListeners();
    _all = await repository.fetchParticipationRecords(filter: selectedCategory);
    isLoading = false;
    notifyListeners();
  }

  void setCategory(String category) {
    selectedCategory = category;
    notifyListeners();
  }
}
