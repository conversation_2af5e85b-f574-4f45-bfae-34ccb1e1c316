import 'package:flutter/material.dart';

class TransferResultDialog extends StatelessWidget {
  final bool isSuccess;
  final String accountId; // ✅ Changed from phone → accountId

  const TransferResultDialog({
    super.key,
    required this.isSuccess,
    required this.accountId,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scale = (screenWidth / 390.0).clamp(0.9, 1.2);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(20 * scale),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Top Icon
            Image.asset(
              isSuccess
                  ? 'assets/images/card/new-success-1.png'
                  : 'assets/images/card/new-didnt-win.png',
              width: 100 * scale,
              height: 100 * scale,
            ),
            const SizedBox(height: 12),

            // Title
            Text(
              isSuccess ? 'Transfer Successful!' : 'Transfer Failed!',
              style: TextStyle(
                fontSize: 18 * scale,
                fontWeight: FontWeight.bold,
                color: isSuccess ? Colors.green[700] : Colors.redAccent,
              ),
            ),
            const SizedBox(height: 10),

            // Content
            Text(
              isSuccess
                  ? 'Your cards have been transferred to Account ID:\n$accountId'
                  : 'Oops! Account not found or something went wrong.\nPlease try again.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14 * scale,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 20),

            // OK Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isSuccess ? Colors.green : Colors.redAccent,
                  padding: EdgeInsets.symmetric(vertical: 12 * scale),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10 * scale),
                  ),
                ),
                child: Text(
                  'OK',
                  style: TextStyle(
                    fontSize: 14 * scale,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
