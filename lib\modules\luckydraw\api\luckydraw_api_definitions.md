# Lucky Draw API Definitions

## Overview
This document defines the API endpoints for the Lucky Draw module functionality, including product browsing, participation management, winning records, and prize claiming.

## Base URL
```
https://api.luckymall.com.my/
```

## Authentication
All endpoints require authentication via Bearer token in the Authorization header:
```
Authorization: Bearer <access_token>
```

---

## Products Endpoints

### GET `/pointproducts`
Fetch available lucky draw products with optional filtering.

**Query Parameters:**
- `category` (optional): Filter by product category (e.g., "Hot Goods", "TNG Vouchers", "ALL")
- `minPoints` (optional): Minimum point value filter
- `maxPoints` (optional): Maximum point value filter
- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of items per page (default: 20)

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "string",
        "productName": "string",
        "imageUrl": "string",
        "variation": "string|null",
        "pointValue": "integer",
        "rating": "number",
        "claimsRemaining": "integer",
        "currentParticipants": "integer",
        "maxParticipants": "integer",
        "category": "string",
        "description": "string|null"
      }
    ],
    "pagination": {
      "currentPage": "integer",
      "totalPages": "integer",
      "totalItems": "integer",
      "hasNext": "boolean",
      "hasPrevious": "boolean"
    }
  },
  "message": "Products fetched successfully"
}
```

### GET `/pointproducts/search`
Search for lucky draw products by name or description.

**Query Parameters:**
- `q`: Search query string
- `category` (optional): Filter by category
- `sortBy` (optional): Sort criteria ("relevance", "latest", "points")
- `sortOrder` (optional): Sort order ("asc", "desc")
- `page` (optional): Page number for pagination
- `limit` (optional): Number of items per page

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "string",
        "productName": "string",
        "imageUrl": "string",
        "variation": "string|null",
        "pointValue": "integer",
        "rating": "number",
        "claimsRemaining": "integer",
        "currentParticipants": "integer",
        "maxParticipants": "integer",
        "category": "string",
        "description": "string|null"
      }
    ],
    "searchQuery": "string",
    "totalResults": "integer"
  },
  "message": "Search completed successfully"
}
```

### GET `/pointproducts/{productId}`
Get detailed information about a specific lucky draw product.

**Path Parameters:**
- `productId`: Unique identifier of the product

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "string",
    "productName": "string",
    "imageUrl": "string",
    "variation": "string|null",
    "pointValue": "integer",
    "rating": "number",
    "claimsRemaining": "integer",
    "currentParticipants": "integer",
    "maxParticipants": "integer",
    "category": "string",
    "description": "string|null",
    "reviews": [
      {
        "reviewerName": "string",
        "avatarUrl": "string",
        "rating": "number",
        "variation": "string",
        "reviewText": "string",
        "imageUrls": ["string"],
        "date": "string"
      }
    ]
  },
  "message": "Product details fetched successfully"
}
```

---

## Participation Endpoints

### POST `/pointproducts/{productId}/participate`
Join a lucky draw by spending points.

**Path Parameters:**
- `productId`: Unique identifier of the product

**Request Body:**
```json
{
  "pointsToSpend": "integer",
  "shares": "integer"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "participationId": "string",
    "productId": "string",
    "productName": "string",
    "pointsSpent": "integer",
    "shares": "integer",
    "batchNumber": "string",
    "participationDate": "string",
    "drawDate": "string",
    "status": "waiting"
  },
  "message": "Successfully joined lucky draw"
}
```

**Error Responses:**
```json
{
  "success": false,
  "error": "INSUFFICIENT_POINTS",
  "message": "Not enough points to participate"
}
```

```json
{
  "success": false,
  "error": "DRAW_FULL",
  "message": "Lucky draw is full, no more participants allowed"
}
```

### GET `/participation`
Get user's participation records with optional filtering.

**Query Parameters:**
- `filter` (optional): Filter type ("My Participation", "My Prize")
- `status` (optional): Filter by participation status ("waiting", "claimPrize", "notWin", "alreadyClaimed")
- `page` (optional): Page number for pagination
- `limit` (optional): Number of items per page

**Response:**
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "participationId": "string",
        "productId": "string",
        "productName": "string",
        "imageUrl": "string",
        "date": "string",
        "batchNumber": "string",
        "participationPoints": "string",
        "state": "claimPrize|waiting|notWin|alreadyClaimed",
        "prizeType": "normal|tngVoucher"
      }
    ],
    "pagination": {
      "currentPage": "integer",
      "totalPages": "integer",
      "totalItems": "integer"
    }
  },
  "message": "Participation records fetched successfully"
}
```

### GET `/participation/{participationId}`
Get details of a specific participation record.

**Path Parameters:**
- `participationId`: Unique identifier of the participation record

**Response:**
```json
{
  "success": true,
  "data": {
    "participationId": "string",
    "productId": "string",
    "productName": "string",
    "imageUrl": "string",
    "date": "string",
    "batchNumber": "string",
    "participationPoints": "string",
    "state": "claimPrize|waiting|notWin|alreadyClaimed",
    "prizeType": "normal|tngVoucher",
    "drawResult": {
      "isWinner": "boolean",
      "revealDate": "string|null",
      "prizeDetails": "object|null"
    }
  },
  "message": "Participation details fetched successfully"
}
```

---

## Prize Reveal Endpoints

### GET `/prize-reveal/products`
Get products that user is waiting for prize reveal with countdown timers.

**Query Parameters:**
- `userId` (optional): User ID (if admin/system access)
- `status` (optional): Filter by status ("waiting", "revealing", "completed")

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "productId": "string",
        "productName": "string",
        "imageUrl": "string",
        "countdownSeconds": "integer",
        "currentBatch": "integer",
        "nextBatch": "integer",
        "participationDate": "string",
        "revealDate": "string",
        "status": "waiting|revealing|completed"
      }
    ],
    "totalWaitingReveals": "integer"
  },
  "message": "Prize reveal products fetched successfully"
}
```

### POST `/prize-reveal/join-next-batch`
Join the next batch for a specific product.

**Request Body:**
```json
{
  "productId": "string",
  "currentBatch": "integer",
  "pointsToSpend": "integer",
  "shares": "integer"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "participationId": "string",
    "productId": "string",
    "productName": "string",
    "nextBatch": "integer",
    "pointsSpent": "integer",
    "shares": "integer",
    "participationDate": "string",
    "drawDate": "string",
    "status": "waiting"
  },
  "message": "Successfully joined next batch"
}
```

**Error Responses:**
```json
{
  "success": false,
  "error": "BATCH_FULL",
  "message": "Next batch is already full"
}
```

```json
{
  "success": false,
  "error": "INVALID_BATCH_NUMBER",
  "message": "Invalid batch number provided"
}
```

### GET `/opening-soon/products`
Get products that will be opening soon for participation.

**Query Parameters:**
- `category` (optional): Filter by product category
- `minPoints` (optional): Minimum point threshold
- `limit` (optional): Number of items to return (default: 5)

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "productId": "string",
        "productName": "string",
        "imageUrl": "string",
        "pointValue": "integer",
        "currentPoints": "integer",
        "progressPercentage": "number",
        "estimatedOpenDate": "string",
        "category": "string",
        "priority": "integer"
      }
    ]
  },
  "message": "Opening soon products fetched successfully"
}
```

### GET `/prize-reveal/countdown/{participationId}`
Get real-time countdown information for a specific participation.

**Path Parameters:**
- `participationId`: Unique identifier of the participation record

**Response:**
```json
{
  "success": true,
  "data": {
    "participationId": "string",
    "productId": "string",
    "productName": "string",
    "countdownSeconds": "integer",
    "revealDate": "string",
    "currentBatch": "integer",
    "status": "waiting|revealing|revealed",
    "lastUpdated": "string"
  },
  "message": "Countdown information fetched successfully"
}
```

---

## Prize Management Endpoints

### GET `/prize/pending`
Check if user has any pending prize reveals.

**Response:**
```json
{
  "success": true,
  "data": {
    "hasPendingReveals": "boolean",
    "pendingCount": "integer",
    "nextRevealDate": "string|null"
  },
  "message": "Pending prize status checked successfully"
}
```

### POST `/prize/{participationId}/claim`
Claim a won prize.

**Path Parameters:**
- `participationId`: Unique identifier of the participation record

**Request Body:**
```json
{
  "claimType": "normal|tngVoucher",
  "deliveryAddress": {
    "fullName": "string",
    "phoneNumber": "string",
    "address": "string",
    "city": "string",
    "state": "string",
    "postalCode": "string",
    "country": "string"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "claimId": "string",
    "participationId": "string",
    "claimStatus": "processing",
    "estimatedDelivery": "string",
    "trackingNumber": "string|null"
  },
  "message": "Prize claim submitted successfully"
}
```

### GET `/prize/claims`
Get user's prize claim history.

**Query Parameters:**
- `status` (optional): Filter by claim status ("processing", "shipped", "delivered", "cancelled")
- `page` (optional): Page number for pagination
- `limit` (optional): Number of items per page

**Response:**
```json
{
  "success": true,
  "data": {
    "claims": [
      {
        "claimId": "string",
        "productName": "string",
        "imageUrl": "string",
        "claimDate": "string",
        "status": "processing|shipped|delivered|cancelled",
        "trackingNumber": "string|null",
        "estimatedDelivery": "string|null"
      }
    ],
    "pagination": {
      "currentPage": "integer",
      "totalPages": "integer",
      "totalItems": "integer"
    }
  },
  "message": "Prize claims fetched successfully"
}
```

---

## Records and History Endpoints

### GET `/product/point/latest`
Get the latest lucky draw records from the point products API.

**Query Parameters:**
- `limit` (optional): Number of records to return (default: 10)
- `page` (optional): Page number for pagination (default: 1)

**Response:**
```json
{
  "status": 200,
  "message": "success",
  "data": {
    "totalCount": 2,
    "pageSize": 10,
    "totalPage": 1,
    "currentPage": 1,
    "list": [
      {
        "id": 1,
        "period": 1,
        "user": {
          "avatar": "",
          "nickname": "128****47"
        },
        "productName": "TNG 15 Cash Voucher",
        "productImage": "https://files.luckymall.my/TNG/15.jpg",
        "amount": 24,
        "totalAmount": 24,
        "createdTime": "2025-07-17 16:26:49"
      }
    ]
  },
  "timestamp": 1755658032598
}
```

### GET `/records/latest`
Get the latest lucky draw records and winners for main page display.

**Query Parameters:**
- `limit` (optional): Number of records to return (default: 3)
- `type` (optional): Display type ("summary" for main page, "detailed" for full view)

**Response for Summary Type (Main Page):**
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "itemName": "string",
        "imageUrl": "string",
        "winningId": "string",
        "dateTime": "string",
        "winnerName": "string",
        "avatarUrl": "string"
      }
    ]
  },
  "message": "Latest draw records fetched successfully"
}
```

**Response for Detailed Type (Full View):**
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "username": "string",
        "productTitle": "string",
        "date": "string",
        "time": "string",
        "period": "string",
        "totalShares": "string",
        "batchNumber": "string",
        "participationPoint": "string",
        "winningName": "string",
        "imageUrl": "string"
      }
    ]
  },
  "message": "Latest draw records fetched successfully"
}
```

### GET `/records/winning-history`
Get comprehensive winning history for all lucky draws.

**Query Parameters:**
- `page` (optional): Page number for pagination
- `limit` (optional): Number of items per page
- `period` (optional): Filter by time period ("daily", "weekly", "monthly")

**Response:**
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "username": "string",
        "productTitle": "string",
        "date": "string",
        "time": "string",
        "period": "string",
        "totalShares": "string",
        "batchNumber": "string",
        "participationPoint": "string",
        "winningName": "string",
        "imageUrl": "string"
      }
    ],
    "pagination": {
      "currentPage": "integer",
      "totalPages": "integer",
      "totalItems": "integer"
    }
  },
  "message": "Winning history fetched successfully"
}
```

---

## Charity Fund Endpoints

### GET `/charity/total`
Get the current total charity fund amount.

**Response:**
```json
{
  "success": true,
  "data": {
    "totalAmount": "integer",
    "lastUpdated": "string"
  },
  "message": "Charity fund total fetched successfully"
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "totalAmount": 123,
    "lastUpdated": "2025-01-15T10:30:00Z"
  },
  "message": "Charity fund total fetched successfully"
}
```

---

## User Points Endpoints

### GET `/user/points`
Get user's current point balance and recent transactions.

**Response:**
```json
{
  "success": true,
  "data": {
    "currentBalance": "integer",
    "totalEarned": "integer",
    "totalSpent": "integer",
    "recentTransactions": [
      {
        "transactionId": "string",
        "type": "earned|spent",
        "amount": "integer",
        "description": "string",
        "date": "string",
        "relatedActivity": "string|null"
      }
    ]
  },
  "message": "Points information fetched successfully"
}
```

---

## Categories Endpoints

### GET `/categories`
Get all available product categories for lucky draws.

**Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": "string",
        "name": "string",
        "displayName": "string",
        "productCount": "integer",
        "isActive": "boolean"
      }
    ]
  },
  "message": "Categories fetched successfully"
}
```

---

## Batch Management Endpoints

### GET `/batches/current`
Get information about current active draw batches.

**Response:**
```json
{
  "success": true,
  "data": {
    "batches": [
      {
        "batchNumber": "integer",
        "productId": "string",
        "productName": "string",
        "imageUrl": "string",
        "drawDate": "string",
        "drawTime": "string",
        "countdownSeconds": "integer",
        "status": "active|closed|completed|waiting",
        "totalParticipants": "integer",
        "maxParticipants": "integer",
        "totalShares": "integer",
        "pointValue": "integer",
        "category": "string"
      }
    ]
  },
  "message": "Current batches fetched successfully"
}
```

### GET `/batches/{batchNumber}/results`
Get results for a specific draw batch.

**Path Parameters:**
- `batchNumber`: Unique identifier of the batch

**Response:**
```json
{
  "success": true,
  "data": {
    "batchNumber": "string",
    "productName": "string",
    "drawDate": "string",
    "drawTime": "string",
    "totalParticipants": "integer",
    "totalShares": "integer",
    "winners": [
      {
        "winningName": "string",
        "username": "string",
        "participationPoints": "string",
        "shares": "string",
        "prizeType": "normal|tngVoucher"
      }
    ],
    "status": "completed"
  },
  "message": "Batch results fetched successfully"
}
```

---

## Community and Social Endpoints

### GET `/community/posts`
Get community sharing posts with pagination.

**Query Parameters:**
- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of posts per page (default: 20)
- `sortBy` (optional): Sort criteria ("latest", "popular", "trending")

**Response:**
```json
{
  "success": true,
  "data": {
    "posts": [
      {
        "id": "string",
        "userName": "string",
        "avatarUrl": "string",
        "postImage": "string",
        "caption": "string",
        "timeAgo": "string",
        "likeCount": "integer",
        "isLiked": "boolean",
        "createdAt": "string",
        "productName": "string|null",
        "prizeType": "string|null"
      }
    ],
    "pagination": {
      "currentPage": "integer",
      "totalPages": "integer",
      "totalItems": "integer"
    }
  },
  "message": "Community posts fetched successfully"
}
```

### GET `/community/posts/{postId}`
Get detailed information about a specific community post.

**Path Parameters:**
- `postId`: Unique identifier of the post

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "string",
    "userName": "string",
    "avatarUrl": "string",
    "postImage": "string",
    "caption": "string",
    "timeAgo": "string",
    "likeCount": "integer",
    "isLiked": "boolean",
    "createdAt": "string",
    "productName": "string|null",
    "prizeType": "string|null",
    "comments": [
      {
        "id": "string",
        "userName": "string",
        "avatarUrl": "string",
        "comment": "string",
        "timeAgo": "string",
        "likeCount": "integer"
      }
    ]
  },
  "message": "Post details fetched successfully"
}
```

### POST `/community/posts`
Create a new community sharing post.

**Request Body:**
```json
{
  "caption": "string",
  "postImage": "string",
  "productName": "string|null",
  "prizeType": "string|null"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "postId": "string",
    "message": "Post created successfully"
  }
}
```

### PUT `/community/posts/{postId}/like`
Like or unlike a community post.

**Path Parameters:**
- `postId`: Unique identifier of the post

**Request Body:**
```json
{
  "action": "like|unlike"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "likeCount": "integer",
    "isLiked": "boolean",
    "message": "Post liked successfully"
  }
}
```

### DELETE `/community/posts/{postId}`
Delete a user's own community post.

**Path Parameters:**
- `postId`: Unique identifier of the post

**Response:**
```json
{
  "success": true,
  "message": "Post deleted successfully"
}
```

### POST `/community/posts/{postId}/comments`
Add a comment to a community post.

**Path Parameters:**
- `postId`: Unique identifier of the post

**Request Body:**
```json
{
  "comment": "string"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "commentId": "string",
    "comment": "string",
    "userName": "string",
    "avatarUrl": "string",
    "timeAgo": "string",
    "likeCount": "integer"
  },
  "message": "Comment added successfully"
}
```

### GET `/community/posts/{postId}/comments`
Get comments for a specific community post.

**Path Parameters:**
- `postId`: Unique identifier of the post

**Query Parameters:**
- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of comments per page (default: 10)

**Response:**
```json
{
  "success": true,
  "data": {
    "comments": [
      {
        "id": "string",
        "userName": "string",
        "avatarUrl": "string",
        "comment": "string",
        "timeAgo": "string",
        "likeCount": "integer"
      }
    ],
    "pagination": {
      "currentPage": "integer",
      "totalPages": "integer",
      "totalItems": "integer"
    }
  },
  "message": "Comments fetched successfully"
}
```

### PUT `/community/comments/{commentId}/like`
Like or unlike a community comment.

**Path Parameters:**
- `commentId`: Unique identifier of the comment

**Request Body:**
```json
{
  "action": "like|unlike"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "likeCount": "integer",
    "isLiked": "boolean"
  },
  "message": "Comment liked successfully"
}
```

### GET `/community/sharing`
Get community sharing summary data (legacy endpoint for backward compatibility).

**Response:**
```json
{
  "success": true,
  "data": {
    "recentActivity": [
      {
        "username": "string",
        "action": "string",
        "productName": "string",
        "timestamp": "string"
      }
    ],
    "topParticipants": [
      {
        "username": "string",
        "totalParticipations": "integer",
        "totalWins": "integer"
      }
    ]
  },
  "message": "Community data fetched successfully"
}
```

### GET `/reviews/categories`
Get all available review categories.

**Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": "all",
        "name": "All",
        "displayName": "All Reviews"
      },
      {
        "id": "mainProduct",
        "name": "Main Product",
        "displayName": "Main Product"
      },
      {
        "id": "pointProduct",
        "name": "Point Product",
        "displayName": "Point Product"
      },
      {
        "id": "blindBox",
        "name": "Blind Box",
        "displayName": "Blind Box"
      },
      {
        "id": "luckyGroup",
        "name": "Lucky Group",
        "displayName": "Lucky Group"
      }
    ]
  },
  "message": "Review categories fetched successfully"
}
```

### GET `/reviews`
Get product reviews for lucky draw items.

**Query Parameters:**
- `productId` (optional): Filter reviews for specific product
- `category` (optional): Filter by review category ("All", "Main Product", "Point Product", "Blind Box", "Lucky Group")
- `rating` (optional): Filter by rating (1-5)
- `page` (optional): Page number for pagination
- `limit` (optional): Number of items per page

**Response:**
```json
{
  "success": true,
  "data": {
    "reviews": [
      {
        "reviewId": "string",
        "reviewerName": "string",
        "avatarUrl": "string",
        "productId": "string",
        "productName": "string",
        "rating": "number",
        "variation": "string",
        "reviewText": "string",
        "imageUrls": ["string"],
        "helpfulCount": "integer",
        "date": "string",
        "category": "string",
        "verified": "boolean"
      }
    ],
    "averageRating": "number",
    "totalReviews": "integer",
    "pagination": {
      "currentPage": "integer",
      "totalPages": "integer",
      "totalItems": "integer"
    }
  },
  "message": "Reviews fetched successfully"
}
```

---

## Error Response Format

All endpoints follow a consistent error response format:

```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "details": "object|null"
}
```

### Common Error Codes:
- `INVALID_TOKEN`: Authentication token is invalid or expired
- `INSUFFICIENT_POINTS`: User doesn't have enough points
- `PRODUCT_NOT_FOUND`: Requested product doesn't exist
- `DRAW_NOT_ACTIVE`: Lucky draw is not currently active
- `PARTICIPATION_LIMIT_REACHED`: Maximum participants reached
- `ALREADY_PARTICIPATED`: User has already participated in this draw
- `INVALID_BATCH`: Batch number is invalid or expired
- `BATCH_FULL`: Batch has reached maximum participants
- `INVALID_BATCH_NUMBER`: Invalid batch number provided
- `PRIZE_ALREADY_CLAIMED`: Prize has already been claimed
- `NO_PENDING_REVEALS`: User has no pending prize reveals
- `COUNTDOWN_EXPIRED`: Countdown timer has expired
- `PRODUCT_NOT_OPENING_SOON`: Product is not in opening soon status
- `VALIDATION_ERROR`: Request data validation failed

---

## Data Models

### LuckyDrawProduct
```typescript
interface LuckyDrawProduct {
  id: string;
  productName: string;
  imageUrl: string;
  variation?: string;
  pointValue: number;
  rating: number;
  claimsRemaining: number;
  currentParticipants: number;
  maxParticipants: number;
  category: string;
  description?: string;
}
```

### LuckyDrawRecord
```typescript
interface LuckyDrawRecord {
  username: string;
  productTitle: string;
  date: string;
  time: string;
  period: string;
  totalShares: string;
  batchNumber: string;
  participationPoint: string;
  winningName: string;
  imageUrl: string;
}
```

### LatestDrawSummary
```typescript
interface LatestDrawSummary {
  itemName: string;
  imageUrl: string;
  winningId: string;
  dateTime: string;
  winnerName: string;
  avatarUrl: string;
}
```

### ParticipationRecord
```typescript
interface ParticipationRecord {
  participationId: string;
  productId: string;
  productName: string;
  imageUrl: string;
  date: string;
  batchNumber: string;
  participationPoints: string;
  state: 'claimPrize' | 'waiting' | 'notWin' | 'alreadyClaimed';
  prizeType: 'normal' | 'tngVoucher';
}
```

### ParticipationCardState
```typescript
enum ParticipationCardState {
  claimPrize = 'claimPrize',
  waiting = 'waiting',
  notWin = 'notWin',
  alreadyClaimed = 'alreadyClaimed'
}
```

### PrizeType
```typescript
enum PrizeType {
  normal = 'normal',
  tngVoucher = 'tngVoucher'
}
```

### LuckyDrawProductReview
```typescript
interface LuckyDrawProductReview {
  reviewId: string;
  reviewerName: string;
  avatarUrl: string;
  productId: string;
  productName: string;
  rating: number;
  variation: string;
  reviewText: string;
  imageUrls: string[];
  helpfulCount: number;
  date: string;
  category: 'Main Product' | 'Point Product' | 'Blind Box' | 'Lucky Group';
  verified: boolean;
}
```

### ReviewCategory
```typescript
enum ReviewCategory {
  all = 'All',
  mainProduct = 'Main Product',
  pointProduct = 'Point Product',
  blindBox = 'Blind Box',
  luckyGroup = 'Lucky Group'
}
```

### CharityFund
```typescript
interface CharityFund {
  totalAmount: number;
  lastUpdated: string;
}
```

### CommunityPost
```typescript
interface CommunityPost {
  id: string;
  userName: string;
  avatarUrl: string;
  postImage: string;
  caption: string;
  timeAgo: string;
  likeCount: number;
  isLiked: boolean;
  createdAt: string;
  productName?: string;
  prizeType?: string;
}
```

### CommunityComment
```typescript
interface CommunityComment {
  id: string;
  userName: string;
  avatarUrl: string;
  comment: string;
  timeAgo: string;
  likeCount: number;
}
```

### GroupPurchaseProduct
```typescript
interface GroupPurchaseProduct extends LuckyDrawProduct {
  currentBatch: number;
  nextBatch?: number;
  batchStatus: 'active' | 'closed' | 'completed' | 'waiting';
  groupSize?: number;
  maxGroupSize?: number;
}
```

### PrizeRevealProduct
```typescript
interface PrizeRevealProduct {
  productId: string;
  productName: string;
  imageUrl: string;
  countdownSeconds: number;
  currentBatch: number;
  nextBatch?: number;
  participationDate: string;
  revealDate: string;
  status: 'waiting' | 'revealing' | 'completed';
}
```

### OpenSoonProduct
```typescript
interface OpenSoonProduct {
  productId: string;
  productName: string;
  imageUrl: string;
  pointValue: number;
  currentPoints: number;
  progressPercentage: number;
  estimatedOpenDate: string;
  category: string;
  priority: number;
}
```

### BatchInfo
```typescript
interface BatchInfo {
  batchNumber: number;
  productId: string;
  productName: string;
  imageUrl: string;
  drawDate: string;
  drawTime: string;
  countdownSeconds: number;
  status: 'active' | 'closed' | 'completed' | 'waiting';
  totalParticipants: number;
  maxParticipants: number;
  totalShares: number;
  pointValue: number;
  category: string;
}
```

---

## Integration Notes

### Point System Integration
- All point transactions should integrate with the main wallet/points system
- Point deductions for participation should be atomic operations
- Failed transactions should roll back point deductions

### Checkout Integration
- Prize claiming integrates with the existing checkout module
- Use `CheckoutType.luckydraw` for prize claims
- Follow existing checkout flow for delivery address collection

### Authentication Integration
- All endpoints require valid user authentication
- User context should be available for filtering personal records
- Rate limiting should be applied based on user tier

### Real-time Updates
Consider implementing WebSocket connections for:
- Real-time participant count updates
- Live draw results
- Prize reveal notifications with countdown timers
- Real-time charity fund total updates
- Countdown timer synchronization for prize reveals
- Batch status changes (active, closed, completed)
- Opening soon product progress updates

### Charity Fund Integration
- The charity fund counter should reflect accumulated contributions from lucky draw activities
- Consider implementing automatic updates when users participate in draws
- May integrate with external charity tracking systems

### Prize Reveal Integration
- Prize reveal countdown timers should be synchronized across all client devices
- Implement batch validation to prevent joining invalid or full batches
- Consider caching countdown data to reduce server load
- Prize reveal notifications should trigger real-time updates to the UI
- Opening soon products should automatically transition to active when thresholds are met

---

## Implementation Priority

1. **Core Functionality (High Priority)**
   - GET `/pointproducts` - Product listing and filtering
   - GET `/pointproducts/{productId}` - Product details with reviews
   - POST `/pointproducts/{productId}/participate` - Join lucky draws
   - GET `/participation` - User participation history
   - GET `/charity/total` - Get total charity fund counter
   - GET `/prize-reveal/products` - Prize reveal products with countdown
   - GET `/opening-soon/products` - Products opening soon

2. **User Experience (Medium Priority)**
   - GET `/pointproducts/search` - Product search
   - GET `/records/latest` - Latest draw results
   - GET `/prize/pending` - Pending prize status
   - POST `/prize-reveal/join-next-batch` - Join next batch functionality
   - GET `/prize-reveal/countdown/{participationId}` - Real-time countdown
   - GET `/batches/current` - Current batch information with countdown

3. **Advanced Features (Low Priority)**
   - POST `/prize/{participationId}/claim` - Prize claiming
   - GET `/community/posts` - Community posts listing
   - POST `/community/posts` - Create community posts
   - POST `/community/posts/{postId}/comments` - Add comments to posts
   - GET `/community/posts/{postId}/comments` - Get post comments
   - PUT `/community/comments/{commentId}/like` - Like/unlike comments
   - GET `/reviews` - Product reviews
   - GET `/reviews/categories` - Review categories
