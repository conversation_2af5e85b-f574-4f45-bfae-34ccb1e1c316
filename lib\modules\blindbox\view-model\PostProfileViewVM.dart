import 'package:flutter/material.dart';

class PostProfileViewVM extends ChangeNotifier {
  bool _isLoading = true;
  bool get isLoading => _isLoading;

  // User information
  String _userName = '';
  String get userName => _userName;

  final String _userAvatar = '';
  String get userAvatar => _userAvatar;

  int _blindBoxCount = 0;
  int get blindBoxCount => _blindBoxCount;

  int _cardCount = 0;
  int get cardCount => _cardCount;

  // User posts
  List<Map<String, dynamic>> _userPosts = [];
  List<Map<String, dynamic>> get userPosts => _userPosts;

  // Constructor - accepts user data
  PostProfileViewVM({String? userName}) {
    if (userName != null) {
      _userName = userName;
      _loadUserProfile();
    }
  }

  // Initialize with user data
  void initializeUserData(String userName) {
    _userName = userName;
    _isLoading = true;
    notifyListeners();
    _loadUserProfile();
  }

  // Load user profile and posts
  Future<void> _loadUserProfile() async {
    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 1500));

      // Mock user data based on username
      _loadMockUserData();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load mock user data and posts
  void _loadMockUserData() {
    // Set user stats based on username
    switch (_userName) {
      case 'Niki':
        _blindBoxCount = 520;
        _cardCount = 15;
        _userPosts = [
          {
            "imageUrl":
                "https://down-my.img.susercontent.com/file/sg-11134201-7rd4j-lvmr60yl4xybd5",
            "title": "Cute dolls!!!",
            "likeCount": 230,
            "username": "Niki",
            "id": "niki_1",
            "isFavorite": true,
          },
          {
            "imageUrl":
                "https://cdn1.npcdn.net/images/1631432215824d6f0aab2c15672038e6499f69a644.webp?md5id=6ff2b69d6d2ebf159937c9ec2784ad7e&new_width=1000&new_height=1000&w=1669687395&from=jpg",
            "title": "My collection is growing! 🥰",
            "likeCount": 180,
            "username": "Niki",
            "id": "niki_2",
            "isFavorite": false,
          },
          {
            "imageUrl":
                "https://i.ebayimg.com/images/g/RzwAAOSwwbRk35w8/s-l1200.jpg",
            "title": "Look what I got today!",
            "likeCount": 312,
            "username": "Niki",
            "id": "niki_3",
            "isFavorite": false,
          },
        ];
        break;

      case 'Leo':
        _blindBoxCount = 850;
        _cardCount = 22;
        _userPosts = [
          {
            "imageUrl":
                "https://preview.redd.it/skintific-water-gel-ceramide-toner-first-impression-v0-r8u75rmvvs3c1.jpeg?auto=webp&s=16435fbc1a0c6db0a41b8547ffa5a129a6881130",
            "title": "I've been waiting for this",
            "likeCount": 180,
            "username": "Leo",
            "id": "leo_1",
            "isFavorite": false,
          },
          {
            "imageUrl":
                "https://community.sephora.com/t5/image/serverpage/image-id/2486286i7AD6EB1905A3DDB8/image-size/large?v=v2&px=999",
            "title": "Finally completed my skincare routine!",
            "likeCount": 145,
            "username": "Leo",
            "id": "leo_2",
            "isFavorite": true,
          },
        ];
        break;

      case 'Maya':
        _blindBoxCount = 1200;
        _cardCount = 35;
        _userPosts = [
          {
            "imageUrl":
                "https://img4.dhresource.com/webp/m/0x0/f3/albu/km/y/15/7816b33c-fca1-42fe-a411-c4c1b46b50a9.jpg",
            "title": "VENTIIII!!!",
            "likeCount": 312,
            "username": "Maya",
            "id": "maya_1",
            "isFavorite": false,
          },
          {
            "imageUrl":
                "https://suntalapress.com/cdn/shop/files/871687_b1609ccbc3754bf382bb00e4f50e98d5_mv2.webp?v=1693048015",
            "title": "Genshin merch haul! 💫",
            "likeCount": 420,
            "username": "Maya",
            "id": "maya_2",
            "isFavorite": true,
          },
          {
            "imageUrl":
                "https://i.ebayimg.com/images/g/GeMAAOSwFjtnWXKb/s-l400.jpg",
            "title": "Can't wait to open these!",
            "likeCount": 267,
            "username": "Maya",
            "id": "maya_3",
            "isFavorite": false,
          },
          {
            "imageUrl":
                "https://preview.redd.it/my-little-shrine-v0-y0ctxwvxvpyb1.jpg?width=640&crop=smart&auto=webp&s=630de4468640143144aaba0f621ad359b23bf02a",
            "title": "My shrine is complete ✨",
            "likeCount": 390,
            "username": "Maya",
            "id": "maya_4",
            "isFavorite": true,
          },
        ];
        break;

      case 'Zane':
        _blindBoxCount = 300;
        _cardCount = 8;
        _userPosts = [
          {
            "imageUrl":
                "https://cdn.shopify.com/s/files/1/0537/1177/files/NaPacks_Sling_Bag_Gear_Review_Ultralight_Fanny_Pack_Made_of_Challenge_Ultra_Backpacking_Thru-Hiking_GGG_Garage_Grown_Gear.jpg?v=1681326192",
            "title": "Comftyyy",
            "likeCount": 90,
            "username": "Zane",
            "id": "zane_1",
            "isFavorite": false,
          },
          {
            "imageUrl":
                "https://i.ebayimg.com/images/g/ONoAAOSwoEJhMnGG/s-l1200.jpg",
            "title": "Perfect for my outdoor adventures",
            "likeCount": 75,
            "username": "Zane",
            "id": "zane_2",
            "isFavorite": false,
          },
        ];
        break;

      case 'Alina':
        _blindBoxCount = 2000;
        _cardCount = 48;
        _userPosts = [
          {
            "imageUrl":
                "https://i.ebayimg.com/images/g/upIAAOSw3sJak0DU/s-l1200.jpg",
            "title": "WORTH IT",
            "likeCount": 450,
            "username": "Alina",
            "id": "alina_1",
            "isFavorite": true,
          },
          {
            "imageUrl": "https://i.redd.it/nbfbms374brd1.jpeg",
            "title": "Designer bags collection 👜",
            "likeCount": 580,
            "username": "Alina",
            "id": "alina_2",
            "isFavorite": false,
          },
          {
            "imageUrl":
                "https://i.pinimg.com/736x/f1/76/05/f176051e0fe108b890c46373da7e50a1.jpg",
            "title": "Luxury haul! Can't believe the quality",
            "likeCount": 320,
            "username": "Alina",
            "id": "alina_3",
            "isFavorite": true,
          },
        ];
        break;

      case 'Chris':
        _blindBoxCount = 750;
        _cardCount = 18;
        _userPosts = [
          {
            "imageUrl":
                "https://xcdn.next.co.uk/common/items/default/default/itemimages/3_4Ratio/product/lge/732297s.jpg",
            "title": "Nice vase for my flower",
            "likeCount": 207,
            "username": "Chris",
            "id": "chris_1",
            "isFavorite": false,
          },
          {
            "imageUrl":
                "https://www.bhg.com/thmb/sq8cqhphc0TdtATgElc6N9snT6E=/1792x0/filters:no_upscale():strip_icc()/living-room-rug-shelves-7b5d7a52-dcb3e3a7b7e04df99893aeaa76f57d08.jpg",
            "title": "Home decor upgrade complete! 🏠",
            "likeCount": 165,
            "username": "Chris",
            "id": "chris_2",
            "isFavorite": false,
          },
        ];
        break;

      default:
        // Default user data
        _blindBoxCount = 450;
        _cardCount = 12;
        _userPosts = [
          {
            "imageUrl": "https://via.placeholder.com/300x200",
            "title": "My first post!",
            "likeCount": 45,
            "username": _userName,
            "id": "default_1",
            "isFavorite": false,
          },
        ];
    }
  }

  // Refresh user profile
  Future<void> refreshProfile() async {
    _isLoading = true;
    notifyListeners();
    await _loadUserProfile();
  }

  // Get responsive values for UI
  static double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  static double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  static int getResponsiveCrossAxisCount(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 2;
    } else if (screenWidth < 900) {
      return 3;
    } else {
      return 4;
    }
  }
}
