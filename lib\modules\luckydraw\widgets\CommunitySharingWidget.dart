import 'package:flutter/material.dart';
import 'package:luckymall/modules/luckydraw/view/CommunitySharingView.dart';

class CommunitySharingWidget extends StatelessWidget {
  const CommunitySharingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // Sample community sharing data
    final List<Map<String, dynamic>> communityPosts = [
      {
        'userName': '<PERSON>u <PERSON>',
        'avatarUrl':
            'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWL7US3TICICzxpQG0xaDUHQR5ajebo7Jj7A&s',
        'postImage':
            'https://down-my.img.susercontent.com/file/sg-11134201-7rd74-lxb3gtas096y53',
        'caption':
            "A pleasant surprise from today's lucky draw! How i wish this items for the long time... see more",
        'timeAgo': '2d',
      },
      {
        'userName': '<PERSON><PERSON><PERSON>',
        'avatarUrl': 'https://i.mydramalist.com/0nQBN_5f.jpg',
        'postImage':
            'https://image.made-in-china.com/202f0j00yktzrcGIORbv/Manufacturer-Wholesale-Custom-Super-Soft-Velvet-Plush-Toys.webp',
        'caption':
            "A pleasant surprise from today's lucky draw! How i wish this items for the long time... see more",
        'timeAgo': '2d',
      },
      {
        'userName': 'Im Nayeon',
        'avatarUrl': 'https://i.mydramalist.com/d0zr2d_5c.jpg',
        'postImage':
            'https://cdn1.sgliteasset.com/EasyWearhouse/images/product/product-4486273/Z4Hakcoe671215e339b5a_1729238499.png',
        'caption':
            "A pleasant surprise from today's lucky draw! How i wish this items for the long time... see more",
        'timeAgo': '2d',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with yellow background
        Container(
          width: double.infinity,
          decoration: const BoxDecoration(
            color: Color(0xFFFFC107), // More vibrant yellow to match image
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(18, 5, 18, 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Community Sharing',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => CommunitySharingView(),
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 5,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xfffffeeb9),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: const [
                        Text(
                          'See more',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                        SizedBox(width: 6),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: Colors.black,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // Off-white background container for cards
        Container(
          decoration: const BoxDecoration(
            color: Color(0xfffffeeb9), // Off-white background
          ),
          child: Container(
            height: 300,
            padding: const EdgeInsets.only(top: 16, bottom: 10),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 18),
              itemCount: communityPosts.length,
              itemBuilder: (context, index) {
                final post = communityPosts[index];
                return Container(
                  width: 220,
                  margin: const EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.08),
                        spreadRadius: 0,
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Post image
                      Container(
                        height: 140,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(16),
                          ),
                          image: DecorationImage(
                            image: NetworkImage(post['postImage']),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),

                      // Post content
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Caption
                              Expanded(
                                child: Text(
                                  post['caption'],
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.black87,
                                    height: 1.3,
                                  ),
                                  maxLines: 3,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),

                              const SizedBox(height: 12),

                              // User info
                              Row(
                                children: [
                                  CircleAvatar(
                                    radius: 14,
                                    backgroundImage: NetworkImage(
                                      post['avatarUrl'],
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          post['userName'],
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black87,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 2),
                                        Text(
                                          post['timeAgo'],
                                          style: const TextStyle(
                                            fontSize: 11,
                                            color: Colors.grey,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
