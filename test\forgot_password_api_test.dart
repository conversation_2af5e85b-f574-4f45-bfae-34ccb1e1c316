import 'package:flutter_test/flutter_test.dart';
import 'package:luckymall/modules/authorization/service/AuthHttpService.dart';

void main() {
  group('Forgot Password API Tests', () {
    test(
      'forgotPassword method should return proper response structure',
      () async {
        final service = AuthHttpService.instance;

        // Test with valid parameters
        final response = await service.forgotPassword(
          country: 60,
          mobile: '123456789',
          code: '123456',
          password: 'newPassword123',
        );

        // Verify response structure
        expect(response, isA<Map<String, dynamic>>);
        expect(response.containsKey('status'), isTrue);
        expect(response.containsKey('message'), isTrue);
        expect(response.containsKey('data'), isTrue);
        expect(response.containsKey('timestamp'), isTrue);

        // Verify status is an integer
        expect(response['status'], isA<int>);

        // Verify message is a string
        expect(response['message'], isA<String>);

        // Verify data is a map
        expect(response['data'], isA<Map<String, dynamic>>);

        // Verify timestamp is an integer
        expect(response['timestamp'], isA<int>);
      },
    );

    test('forgotPassword should handle network errors gracefully', () async {
      final service = AuthHttpService.instance;

      // Test with invalid endpoint (this will likely fail)
      // Note: In a real test environment, you might want to mock the Dio client
      final response = await service.forgotPassword(
        country: 60,
        mobile: '123456789',
        code: '123456',
        password: 'newPassword123',
      );

      // Even if the API call fails, it should return a proper error response structure
      expect(response, isA<Map<String, dynamic>>);
      expect(response.containsKey('status'), isTrue);
      expect(response.containsKey('message'), isTrue);
      expect(response.containsKey('data'), isTrue);
      expect(response.containsKey('timestamp'), isTrue);
    });
  });
}
