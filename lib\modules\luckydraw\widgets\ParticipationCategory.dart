import 'package:flutter/material.dart';

class ParticipationCategory extends StatefulWidget {
  final Function(String)? onCategorySelected;
  final String? initialCategory;

  const ParticipationCategory({
    super.key,
    this.onCategorySelected,
    this.initialCategory,
  });

  @override
  State<ParticipationCategory> createState() => _ParticipationCategoryState();
}

class _ParticipationCategoryState extends State<ParticipationCategory> {
  String selectedCategory = 'My Participation';

  final List<String> categories = ['My Participation', 'My Prize'];

  @override
  Widget build(BuildContext context) {
    final int initialIndex = widget.initialCategory != null
        ? categories.indexOf(widget.initialCategory!)
        : 0;

    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375; // base width 375
    double scaleH(double value) =>
        value * screenHeight / 812; // base height 812
    double scaleText(double value) => value * screenWidth / 375;

    return DefaultTabController(
      length: categories.length,
      initialIndex: initialIndex >= 0 ? initialIndex : 0,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Material(
            color: Colors.white,
            elevation: 2,
            child: SizedBox(
              height: scaleH(60),
              width: double.infinity,
              child: TabBar(
                isScrollable: true,
                indicatorColor: const Color(0xFFFBBF00),
                labelColor: Colors.black,
                unselectedLabelColor: Colors.grey,
                labelStyle: TextStyle(
                  fontSize: scaleText(14),
                  fontWeight: FontWeight.w600,
                ),
                onTap: (index) {
                  widget.onCategorySelected?.call(categories[index]);
                },
                tabs: categories
                    .map((category) => Tab(text: category))
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
