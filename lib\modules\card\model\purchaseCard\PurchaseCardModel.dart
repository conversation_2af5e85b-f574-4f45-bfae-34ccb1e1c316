class PurchaseCardModel {
  final List<String> imageUrls;
  final String category;      // selected category key (royal, knight, etc.)
  final String description;   // description (series-level or category-level)
  final String seriesName;
  final String? iconAsset;    // ✅ support icon assets

  // ✅ quantity rules
  final int minQuantity;
  final int stepQuantity;
  final int? maxQuantity;

  const PurchaseCardModel({
    required this.imageUrls,
    required this.category,
    required this.description,
    required this.seriesName,
    this.iconAsset,
    this.minQuantity = 1,
    this.stepQuantity = 1,
    this.maxQuantity,
  });

  factory PurchaseCardModel.fromJson(Map<String, dynamic> json) {
    return PurchaseCardModel(
      imageUrls: List<String>.from(json['imageUrls']),
      category: json['category'],
      description: json['description'],
      seriesName: json['seriesName'],
      iconAsset: json['iconAsset'],
      minQuantity: json['minQuantity'] ?? 1,
      stepQuantity: json['stepQuantity'] ?? 1,
      maxQuantity: json['maxQuantity'],
    );
  }

  PurchaseCardModel copyWith({
    List<String>? imageUrls,
    String? category,
    String? description,
    String? seriesName,
    String? iconAsset,
    int? minQuantity,
    int? stepQuantity,
    int? maxQuantity,
  }) {
    return PurchaseCardModel(
      imageUrls: imageUrls ?? this.imageUrls,
      category: category ?? this.category,
      description: description ?? this.description,
      seriesName: seriesName ?? this.seriesName,
      iconAsset: iconAsset ?? this.iconAsset,
      minQuantity: minQuantity ?? this.minQuantity,
      stepQuantity: stepQuantity ?? this.stepQuantity,
      maxQuantity: maxQuantity ?? this.maxQuantity,
    );
  }
}
