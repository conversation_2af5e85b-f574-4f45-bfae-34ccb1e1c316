class OpenSoonProduct {
  final int id;
  final int productSpuId;
  final int productSkuId;
  final String productSkuName;
  final String productSkuImage;
  final int usedAmount;
  final int totalAmount;
  final double score;
  final int sold;

  const OpenSoonProduct({
    required this.id,
    required this.productSpuId,
    required this.productSkuId,
    required this.productSkuName,
    required this.productSkuImage,
    required this.usedAmount,
    required this.totalAmount,
    required this.score,
    required this.sold,
  });

  factory OpenSoonProduct.fromJson(Map<String, dynamic> json) {
    return OpenSoonProduct(
      id: (json['id'] as num?)?.toInt() ?? 0,
      productSpuId: (json['productSpuId'] as num?)?.toInt() ?? 0,
      productSkuId: (json['productSkuId'] as num?)?.toInt() ?? 0,
      productSkuName: json['productSkuName']?.toString() ?? '',
      productSkuImage: json['productSkuImage']?.toString() ?? '',
      usedAmount: (json['usedAmount'] as num?)?.toInt() ?? 0,
      totalAmount: (json['totalAmount'] as num?)?.toInt() ?? 0,
      score: (json['score'] as num?)?.toDouble() ?? 0.0,
      sold: (json['sold'] as num?)?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productSpuId': productSpuId,
      'productSkuId': productSkuId,
      'productSkuName': productSkuName,
      'productSkuImage': productSkuImage,
      'usedAmount': usedAmount,
      'totalAmount': totalAmount,
      'score': score,
      'sold': sold,
    };
  }

  OpenSoonProduct copyWith({
    int? id,
    int? productSpuId,
    int? productSkuId,
    String? productSkuName,
    String? productSkuImage,
    int? usedAmount,
    int? totalAmount,
    double? score,
    int? sold,
  }) {
    return OpenSoonProduct(
      id: id ?? this.id,
      productSpuId: productSpuId ?? this.productSpuId,
      productSkuId: productSkuId ?? this.productSkuId,
      productSkuName: productSkuName ?? this.productSkuName,
      productSkuImage: productSkuImage ?? this.productSkuImage,
      usedAmount: usedAmount ?? this.usedAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      score: score ?? this.score,
      sold: sold ?? this.sold,
    );
  }

  // Helper getter to calculate progress percentage from current/target points
  double get calculatedProgressPercentage {
    if (totalAmount == 0) return 0.0;
    return (usedAmount / totalAmount * 100).clamp(0.0, 100.0);
  }

  // Helper getter to get remaining points needed
  int get remainingPoints => (totalAmount - usedAmount).clamp(0, totalAmount);

  // Helper getter to check if product is ready to open
  bool get isReadyToOpen => usedAmount >= totalAmount;

  // Legacy getters for backward compatibility
  String get productId => id.toString();
  String get productName => productSkuName;
  String get imageUrl => productSkuImage;
  int get pointValue => totalAmount;
  int get currentPoints => usedAmount;
  double get progressPercentage => calculatedProgressPercentage;
  String get category => 'Open Soon'; // Default category for open soon products

  @override
  String toString() {
    return 'OpenSoonProduct(id: $id, productSkuName: $productSkuName, progress: ${calculatedProgressPercentage.toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OpenSoonProduct && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
