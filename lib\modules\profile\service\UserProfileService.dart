import 'package:luckymall/modules/profile/model/UserInfoModel.dart';
import 'package:dio/dio.dart';
import 'package:luckymall/res/utility/network/api_client.dart';
import 'package:flutter/foundation.dart';
import 'package:luckymall/router/auth_guard.dart';

final profileService = ProfileService();

class ProfileService {
  final Dio authDio = ApiClient.instance.authDio;

  Future<UserInfoModel> getUserInfo() async {
    try {
      debugPrint('[ProfileService] Fetching user info from API...');

      // Debug print headers before sending request
      final requestOptions = Options(headers: <String, dynamic>{});

      // Get the current headers that will be sent (including those from interceptors)
      final currentHeaders = authDio.options.headers;
      debugPrint('[ProfileService] Request headers: $currentHeaders');

      // Debug: Check current auth status
      final isLoggedIn = AuthService.isLoggedIn;
      final token = await AuthService.getJwtToken();
      debugPrint(
        '[ProfileService] Auth status - isLoggedIn: $isLoggedIn, hasToken: ${token != null}',
      );

      final response = await authDio.get('/user/info', options: requestOptions);

      debugPrint(
        '[ProfileService] API response status: ${response.statusCode}',
      );
      debugPrint('[ProfileService] API response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData is Map<String, dynamic>) {
          // API shape: { status, message, data: { avatar, nickname, ... }, timestamp }
          final payload = responseData['data'];
          if (payload is Map<String, dynamic>) {
            final userInfo = UserInfoModel.fromJson(payload);
            debugPrint(
              '[ProfileService] User info parsed successfully: ${userInfo.nickname}',
            );
            return userInfo;
          }
          throw Exception(
            'Invalid response format: missing data payload, got ${payload.runtimeType}',
          );
        } else {
          throw Exception(
            'Invalid response format: expected Map but got ${responseData.runtimeType}',
          );
        }
      } else {
        throw Exception(
          'Failed to load user info: HTTP ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      debugPrint('[ProfileService] DioException: ${e.message}');
      debugPrint('[ProfileService] DioException type: ${e.type}');
      debugPrint('[ProfileService] DioException response: ${e.response?.data}');

      if (e.response?.statusCode == 401) {
        throw Exception('Authentication failed. Please log in again.');
      } else if (e.response?.statusCode == 403) {
        throw Exception(
          'Access denied. You do not have permission to view this information.',
        );
      } else if (e.response?.statusCode == 404) {
        throw Exception('User information not found.');
      } else if (e.response?.statusCode == 500) {
        throw Exception('Server error. Please try again later.');
      } else if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception(
          'Connection timeout. Please check your internet connection.',
        );
      } else if (e.type == DioExceptionType.receiveTimeout) {
        throw Exception('Request timeout. Please try again.');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    } catch (e) {
      debugPrint('[ProfileService] Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  Future<bool> updateUserInfo(UserInfoModel userInfo) async {
    try {
      debugPrint('[ProfileService] Updating user info...');

      // Debug print headers before sending request
      final currentHeaders = authDio.options.headers;
      debugPrint('[ProfileService] Update request headers: $currentHeaders');

      final response = await authDio.post(
        '/user/edit',
        data: userInfo.toJson(),
      );

      if (response.statusCode == 200) {
        debugPrint('[ProfileService] User info updated successfully');
        return true;
      } else {
        throw Exception(
          'Failed to update user info: HTTP ${response.statusCode}',
        );
      }
    } catch (e) {
      debugPrint('[ProfileService] Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }
}
