import 'package:flutter/material.dart';

class VerificationCode<PERSON>ield extends StatefulWidget {
  final TextEditingController? controller;
  final String? hintText;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final VoidCallback? onSendCode;
  final bool isRequired;

  const VerificationCodeField({
    super.key,
    this.controller,
    this.hintText,
    this.onChanged,
    this.validator,
    this.onSendCode,
    this.isRequired = true,
  });

  @override
  State<VerificationCodeField> createState() => _VerificationCodeFieldState();
}

class _VerificationCodeFieldState extends State<VerificationCodeField> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Verification Code Label
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                Text(
                  'Verification Code',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 13,
                  ),
                ),
                if (widget.isRequired)
                  const Text(
                    ' *',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                      fontSize: 13,
                    ),
                  ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),
        // Verification Code Field
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Container(
            width: double.infinity, // Ensure full width
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.black),
            ),
            child: TextField(
              controller: widget.controller,
              keyboardType: TextInputType.number,
              maxLength: 6,
              onChanged: widget.onChanged,
              style: const TextStyle(fontSize: 12),
              decoration: InputDecoration(
                border: InputBorder.none,
                counterText: '', // Hide character counter
                hintText: widget.hintText ?? 'Please enter your OTP code',
                hintStyle: const TextStyle(color: Colors.grey),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 16,
                ),
                suffixIcon: TextButton(
                  onPressed: widget.onSendCode,
                  child: const Text(
                    'Send Code',
                    style: TextStyle(color: Color(0xFFD90019)),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
