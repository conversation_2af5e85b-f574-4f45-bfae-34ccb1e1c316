import 'package:luckymall/modules/profile/model/AddressModel.dart';

// this is for shop product only
class CheckoutSendModel {
  // for sending to backend
  final int? couponId;
  int? addressId;
  final List<Cards>? cards;
  final List<Products> products;

  CheckoutSendModel({
    this.couponId,
    this.addressId,
    this.cards,
    required this.products,
  });

  Map<String, dynamic> toJson(int? addressId) {
    return {
      'couponId': couponId,
      'addressId': addressId ?? this.addressId,
      'cards': cards?.map((card) => card.toJson()).toList(),
      'products': products.map((product) => product.toJson()).toList(),
    };
  }
}

class CheckoutReceiveModel {
  final Addressmodel address;
  final double totalPrice;
  final double deliveryFee; // Changed from deliveryfee to deliveryFee
  final double cardDeduction;
  final double payableAmount;
  final List<Products> list; // Changed from products to list to match API

  CheckoutReceiveModel({
    required this.address,
    required this.totalPrice,
    required this.deliveryFee,
    required this.cardDeduction,
    required this.payableAmount,
    required this.list,
  });

  factory CheckoutReceiveModel.fromJson(Map<String, dynamic> json) {
    final addressJson = json['address'];
    return CheckoutReceiveModel(
      address: addressJson is Map<String, dynamic>
          ? Addressmodel.fromJson(_normalizeAddress(addressJson))
          : Addressmodel(
              id: 0,
              country: 60,
              recipient: '',
              mobile: '',
              postcode: 0,
              detailedAddress: '',
              isDefault: true,
            ),
      totalPrice: (json['totalPrice'] ?? 0).toDouble(),
      deliveryFee: (json['deliveryFee'] ?? 0).toDouble(),
      cardDeduction: (json['cardDeduction'] ?? 0).toDouble(),
      payableAmount: (json['payableAmount'] ?? 0).toDouble(),
      list: json['list'] is List
          ? (json['list'] as List)
                .map((product) => Products.fromJson(product))
                .toList()
          : [],
    );
  }

  static Map<String, dynamic> _normalizeAddress(Map<String, dynamic> json) {
    // Backend may return 'addressId' and omit booleans; coerce types
    return {
      'id': json['id'] ?? json['addressId'] ?? 0,
      'country': _toInt(json['country'], defaultValue: 60),
      'recipient': json['recipient']?.toString() ?? '',
      'mobile': json['mobile']?.toString() ?? '',
      'postcode': _toInt(json['postcode'], defaultValue: 0),
      'detailedAddress': json['detailedAddress']?.toString() ?? '',
      'isDefault': json['isDefault'] ?? true,
    };
  }

  static int _toInt(dynamic v, {int defaultValue = 0}) {
    if (v is int) return v;
    if (v is String) return int.tryParse(v) ?? defaultValue;
    return defaultValue;
  }
}

class Cards {
  final int cardId;
  final int quantity;

  Cards({required this.cardId, required this.quantity});

  Map<String, dynamic> toJson() {
    return {'cardId': cardId, 'quantity': quantity};
  }
}

class Products {
  final int productSpuId;
  final int productSkuId;
  final String productSkuImage;
  final String productSkuName;
  final String productSkuPropertys;
  final int quantity;
  final double salePrice;
  final double totalSalePrice;
  final double cardDeduction;
  final double totalCardDeduction;
  final double useCardDeduction;
  final double payableAmount;
  final int stock;

  Products({
    required this.productSpuId,
    required this.productSkuId,
    required this.productSkuImage,
    required this.productSkuName,
    required this.productSkuPropertys,
    required this.quantity,
    required this.salePrice,
    required this.totalSalePrice,
    required this.cardDeduction,
    required this.totalCardDeduction,
    required this.useCardDeduction,
    required this.payableAmount,
    required this.stock,
  });

  factory Products.fromJson(Map<String, dynamic> json) {
    return Products(
      productSpuId: json['productSpuId'],
      productSkuId: json['productSkuId'],
      productSkuImage: json['productSkuImage'],
      productSkuName: json['productSkuName'],
      productSkuPropertys: json['productSkuPropertys'],
      quantity: json['quantity'],
      salePrice: (json['salePrice'] ?? 0).toDouble(),
      totalSalePrice: (json['totalSalePrice'] ?? 0).toDouble(),
      cardDeduction: (json['cardDeduction'] ?? 0).toDouble(),
      totalCardDeduction: (json['totalCardDeduction'] ?? 0).toDouble(),
      useCardDeduction: (json['useCardDeduction'] ?? 0).toDouble(),
      payableAmount: (json['payableAmount'] ?? 0).toDouble(),
      stock: json['stock'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productSpuId': productSpuId,
      'productSkuId': productSkuId,
      'quantity': quantity,
    };
  }
}
