import 'package:flutter/material.dart';
import '../data/LuckyDrawRepository.dart';
import '../model/LuckyDrawProduct.dart';
import '../model/MegaPrize.dart';
import '../model/PrizeRevealProduct.dart';

class PrizeRevealVM extends ChangeNotifier {
  final LuckyDrawRepository repository;

  PrizeRevealVM({LuckyDrawRepository? repository})
    : repository = repository ?? MockLuckyDrawRepository();

  bool isPointProductsLoading = true;
  bool hasParticipatedInLuckyDraw = false;

  // Product management
  List<LuckyDrawProduct> _allProducts = [];
  List<LuckyDrawProduct> get allProducts => _allProducts;

  // Mega prizes management
  List<MegaPrize> _megaPrizes = [];
  List<MegaPrize> get megaPrizes => _megaPrizes;
  bool _isMegaPrizesLoading = true;
  bool get isMegaPrizesLoading => _isMegaPrizesLoading;

  // Prize reveal products management
  List<PrizeRevealProduct> _prizeRevealProducts = [];
  List<PrizeRevealProduct> get prizeRevealProducts => _prizeRevealProducts;
  bool _isPrizeRevealLoading = true;
  bool get isPrizeRevealLoading => _isPrizeRevealLoading;

  // Filter state
  String selectedCategory = 'ALL';
  int minPointFilter = 0;
  int maxPointFilter = 1000;

  Future<void> initialize() async {
    await _checkUserParticipationStatus();
    await Future.wait([
      fetchProducts(notify: false), // Don't notify during initialization
      fetchMegaPrizes(notify: false), // Don't notify during initialization
      fetchPrizeRevealProducts(
        notify: false,
      ), // Don't notify during initialization
    ]);
    await Future.delayed(const Duration(milliseconds: 1200));
    isPointProductsLoading = false;
    notifyListeners(); // Only notify once at the end
  }

  Future<void> _checkUserParticipationStatus() async {
    hasParticipatedInLuckyDraw = await repository.hasPendingPrizeReveal();
  }

  /// Fetch products from the API
  Future<void> fetchProducts({bool notify = true}) async {
    isPointProductsLoading = true;
    if (notify) notifyListeners();

    try {
      print(
        'PrizeRevealVM: Fetching products with category: $selectedCategory, minPoints: $minPointFilter, maxPoints: $maxPointFilter',
      );

      // Use the pagination method with filters
      final result = await repository.fetchProductsWithPagination(
        category: selectedCategory == 'ALL' ? null : selectedCategory,
        minPoints: minPointFilter,
        maxPoints: maxPointFilter,
        page: 1,
        limit: 50,
      );

      print('PrizeRevealVM: API result: $result');

      final productsList = result['products'];
      print('PrizeRevealVM: Products list from result: $productsList');

      _allProducts = (productsList as List<LuckyDrawProduct>?) ?? [];
      print('PrizeRevealVM: Products fetched: ${_allProducts.length}');
    } catch (e) {
      print('PrizeRevealVM: Error fetching products: $e');
      // Fallback to basic fetch if pagination fails
      try {
        _allProducts = await repository.fetchProducts();
        print(
          'PrizeRevealVM: Fallback products fetched: ${_allProducts.length}',
        );
      } catch (fallbackError) {
        print('PrizeRevealVM: Fallback also failed: $fallbackError');
        _allProducts = [];
      }
    }

    isPointProductsLoading = false;
    if (notify) notifyListeners();
  }

  /// Fetch mega prizes from the API
  Future<void> fetchMegaPrizes({bool notify = true}) async {
    _isMegaPrizesLoading = true;
    if (notify) notifyListeners();

    try {
      print('PrizeRevealVM: Fetching mega prizes...');

      final megaPrizesData = await repository.fetchMegaPrizes();
      print('PrizeRevealVM: Mega prizes data from API: $megaPrizesData');

      // Convert the API response to MegaPrize objects
      _megaPrizes = megaPrizesData
          .map((data) => MegaPrize.fromJson(data))
          .toList();

      print('PrizeRevealVM: Mega prizes fetched: ${_megaPrizes.length}');
      print('PrizeRevealVM: Mega prizes: $_megaPrizes');
    } catch (e) {
      print('PrizeRevealVM: Error fetching mega prizes: $e');
      _megaPrizes = [];
    }

    _isMegaPrizesLoading = false;
    if (notify) notifyListeners();
  }

  /// Fetch prize reveal products from the API
  Future<void> fetchPrizeRevealProducts({bool notify = true}) async {
    _isPrizeRevealLoading = true;
    if (notify) notifyListeners();

    try {
      print('PrizeRevealVM: Fetching prize reveal products...');

      final prizeRevealData = await repository.fetchPrizeRevealProducts();
      print('PrizeRevealVM: Prize reveal data from API: $prizeRevealData');

      _prizeRevealProducts = prizeRevealData;
      print(
        'PrizeRevealVM: Prize reveal products fetched: ${_prizeRevealProducts.length}',
      );
    } catch (e) {
      print('PrizeRevealVM: Error fetching prize reveal products: $e');
      _prizeRevealProducts = [];
    }

    _isPrizeRevealLoading = false;
    if (notify) notifyListeners();
  }

  void setCategory(String category) {
    selectedCategory = category;
    fetchProducts(); // Fetch new data with category filter
  }

  void setPointRange(int min, int max) {
    minPointFilter = min;
    maxPointFilter = max;
    fetchProducts(); // Fetch new data with point range filter
  }

  void simulateParticipate() {
    hasParticipatedInLuckyDraw = true;
    notifyListeners();
  }

  /// Refresh all data
  Future<void> refreshAll() async {
    isPointProductsLoading = true;
    notifyListeners();
    await Future.wait([
      fetchProducts(),
      fetchMegaPrizes(),
      fetchPrizeRevealProducts(),
      _checkUserParticipationStatus(),
    ]);
  }

  /// Manually refresh mega prizes
  Future<void> refreshMegaPrizes() async {
    await fetchMegaPrizes();
  }

  /// Manually refresh prize reveal products
  Future<void> refreshPrizeRevealProducts() async {
    await fetchPrizeRevealProducts();
  }
}
