import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:luckymall/main.dart';
import 'package:luckymall/router/auth_guard.dart';

void main() {
  group('Login Page Tests', () {
    testWidgets('App should start with login page', (
      WidgetTester tester,
    ) async {
      // Reset auth state to ensure we start logged out
      AuthService.logout();

      // Build the app
      await tester.pumpWidget(const MyApp());

      // Wait for the app to settle
      await tester.pumpAndSettle();

      // Verify that we're on the login page by checking for login-specific elements
      expect(find.text('Login'), findsOneWidget);
      expect(
        find.byType(TextFormField),
        findsAtLeast(2),
      ); // Phone and password fields
    });

    test('AuthService should start with user logged out', () {
      // Reset auth state
      AuthService.logout();

      // Verify user is not logged in
      expect(AuthService.isLoggedIn, false);
    });

    test('AuthService login should authenticate user', () {
      // Reset auth state
      AuthService.logout();

      // Verify user starts logged out
      expect(AuthService.isLoggedIn, false);

      // Call login
      AuthService.loginSimple();

      // Verify user is now logged in
      expect(AuthService.isLoggedIn, true);
    });
  });
}
