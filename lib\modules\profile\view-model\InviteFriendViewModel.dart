import 'package:flutter/material.dart';
import '../model/InvitedFriends.dart';

class InviteFriendViewModel extends ChangeNotifier {
  final List<InvitedFriend> _invitedFriends = [];
  int _page = 0;
  bool _hasMore = true;
  bool _isLoading = false;

  List<InvitedFriend> get invitedFriends => _invitedFriends;
  bool get hasMore => _hasMore;
  String get referralCode => "123456789077"; // Mock code
  String get profileImageUrl => ""; // Empty means show default
  String get qrCodeImageUrl => "assets/images/mockQR.png"; // Mock asset
  int get totalInvitedCount =>_invitedFriends.length;

  Future<void> fetchFriends() async {
    if (_isLoading || !_hasMore) return;
    _isLoading = true;

    await Future.delayed(Duration(milliseconds: 800));
    final mockData = List.generate(10, (index) => InvitedFriend(
      name: "Friend \${_page * 10 + index + 1}",
      invitedAt: DateTime.now().subtract(Duration(minutes: index * 5)),
    ));

    _invitedFriends.addAll(mockData);
    _page++;
    _hasMore = _page < 3; // Only 3 pages
    _isLoading = false;
    notifyListeners();
  }
}
