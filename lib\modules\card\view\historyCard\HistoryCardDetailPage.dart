import 'package:flutter/material.dart';
import '../../model/historyCard/HistoryCardItem.dart';
import '../../widgets/HistoryCardDetail/HistoryItemGrid.dart';
import '../../widgets/HistoryCardDetail/HistoryRecordInfoRows.dart';
import '../../widgets/loadings/HistoryCard/HistoryCardDetailShimmer.dart';

class HistoryCardDetailPage extends StatefulWidget {
  final HistoryCardItem record;

  const HistoryCardDetailPage({super.key, required this.record});

  @override
  State<HistoryCardDetailPage> createState() => _HistoryCardDetailPageState();
}

class _HistoryCardDetailPageState extends State<HistoryCardDetailPage> {
  bool isLoading = true;

  @override
  void initState() {
    super.initState();

    // Simulate loading delay
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() => isLoading = false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final scale = MediaQuery.of(context).size.width / 390;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.amber, Color(0xFFFFF9C4)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                offset: Offset(0, 4),
                blurRadius: 8,
              ),
            ],
          ),
          child: SafeArea(
            child: Stack(
              children: [
                Center(
                  child: Text(
                    'Record Detail',
                    style: TextStyle(
                      fontSize: 18 * scale,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Positioned(
                  left: 0,
                  top: 0,
                  bottom: 0,
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black87),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body: isLoading
          ? const HistoryCardDetailShimmer()
          : SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: 12 * scale),
                  Center(
                    child: Text(
                      "${widget.record.amount >= 0 ? '+' : '-'} RM${widget.record.amount.abs().toStringAsFixed(2)}",
                      style: TextStyle(
                        fontSize: 25 * scale,
                        fontWeight: FontWeight.bold,
                        color: widget.record.amount >= 0 ? Colors.green : Colors.red,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Divider(),

                  HistoryRecordInfoRows(item: widget.record),

                  if (widget.record.cardsUsed != null &&
                      widget.record.cardsUsed!.isNotEmpty) ...[
                    const SizedBox(height: 20),
                    Center(
                      child: Text(
                        'Items',
                        style: TextStyle(
                          fontSize: 15 * scale,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    const Divider(),
                    HistoryItemGrid(cardsUsed: widget.record.cardsUsed!),
                  ],
                ],
              ),
            ),
    );
  }
}
