class CommunityComment {
  final String id;
  final String userName;
  final String avatarUrl;
  final String comment;
  final String timeAgo;
  final int likeCount;

  const CommunityComment({
    required this.id,
    required this.userName,
    required this.avatarUrl,
    required this.comment,
    required this.timeAgo,
    required this.likeCount,
  });

  factory CommunityComment.fromJson(Map<String, dynamic> json) {
    return CommunityComment(
      id: json['id']?.toString() ?? '',
      userName: json['userName']?.toString() ?? '',
      avatarUrl: json['avatarUrl']?.toString() ?? '',
      comment: json['comment']?.toString() ?? '',
      timeAgo: json['timeAgo']?.toString() ?? '',
      likeCount: (json['likeCount'] as num?)?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userName': userName,
      'avatarUrl': avatarUrl,
      'comment': comment,
      'timeAgo': timeAgo,
      'likeCount': likeCount,
    };
  }

  CommunityComment copyWith({
    String? id,
    String? userName,
    String? avatarUrl,
    String? comment,
    String? timeAgo,
    int? likeCount,
  }) {
    return CommunityComment(
      id: id ?? this.id,
      userName: userName ?? this.userName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      comment: comment ?? this.comment,
      timeAgo: timeAgo ?? this.timeAgo,
      likeCount: likeCount ?? this.likeCount,
    );
  }

  @override
  String toString() {
    return 'CommunityComment(id: $id, userName: $userName, comment: $comment, likeCount: $likeCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommunityComment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
