import 'package:flutter/material.dart';

class PasskeyInputDialog extends StatefulWidget {
  const PasskeyInputDialog({super.key});

  @override
  State<PasskeyInputDialog> createState() => _PasskeyInputDialogState();
}

class _PasskeyInputDialogState extends State<PasskeyInputDialog>
    with SingleTickerProviderStateMixin {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  String? _errorText;
  late AnimationController _shakeController;
  late Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _shakeAnimation = Tween<double>(begin: 0, end: 12).chain(CurveTween(curve: Curves.elasticIn)).animate(_shakeController);
  }

  void _submit() {
    final value = _controller.text.trim();

    if (value.isEmpty) {
      _triggerError('Passkey cannot be empty');
      return;
    }
    if (!RegExp(r'^\d{8}$').hasMatch(value)) {
      _triggerError('Enter a valid 8-digit number');
      return;
    }

    Navigator.pop(context, value);
  }

  void _triggerError(String message) {
    setState(() {
      _errorText = message;
    });

    _shakeController.forward(from: 0);
    _focusNode.requestFocus();
    _controller.selection = TextSelection(baseOffset: 0, extentOffset: _controller.text.length);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _shakeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 340),
        child: Material(
          borderRadius: BorderRadius.circular(20),
          elevation: 10,
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
            child: IntrinsicHeight(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.lock_outline, size: 36, color: Colors.amber),
                  const SizedBox(height: 10),
                  const Text(
                    'Enter Passkey',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 6),
                  const Text(
                    'Input your 8-digit passkey below.',
                    style: TextStyle(fontSize: 13, color: Colors.black54),
                  ),
                  const SizedBox(height: 18),

                  // Animated shake when error
                  AnimatedBuilder(
                    animation: _shakeController,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(_shakeAnimation.value * (_shakeController.status == AnimationStatus.forward ? 1 : 0), 0),
                        child: child,
                      );
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(14),
                        boxShadow: _focusNode.hasFocus
                            ? [
                                BoxShadow(
                                  color: const Color.fromARGB(97, 255, 193, 7),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ]
                            : [],
                      ),
                      child: TextField(
                        focusNode: _focusNode,
                        controller: _controller,
                        keyboardType: TextInputType.number,
                        maxLength: 8,
                        textAlign: TextAlign.left,
                        decoration: InputDecoration(
                          counterText: '',
                          prefixIcon: const Icon(Icons.vpn_key_rounded, color: Colors.black54),
                          hintText: 'Enter passkey',
                          errorText: _errorText,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(14),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(14),
                            borderSide: const BorderSide(color: Colors.amber, width: 2),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                          filled: true,
                          fillColor: Colors.transparent,
                        ),
                        onChanged: (_) {
                          if (_errorText != null) {
                            setState(() => _errorText = null);
                          }
                        },
                        onTap: () => setState(() {}),
                      ),
                    ),
                  ),

                  const SizedBox(height: 18),

                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _submit,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.amber,
                            foregroundColor: Colors.black,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(14),
                            ),
                          ),
                          child: const Text('Submit'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
