import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';
import 'package:luckymall/modules/luckydraw/widgets/ParticipationCategory.dart';
import 'package:luckymall/modules/luckydraw/widgets/ParticipationCard.dart'
    as card;
import 'package:luckymall/modules/luckydraw/view-model/ParticipationRecordsVM.dart';
import 'package:luckymall/modules/luckydraw/model/ParticipationRecord.dart';
import 'package:shimmer/shimmer.dart';

class ParticipationRecordsView extends StatelessWidget {
  const ParticipationRecordsView({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ParticipationRecordsVM()..initialize(),
      child: Consumer<ParticipationRecordsVM>(
        builder: (context, vm, _) {
          return Scaffold(
            appBar: const CustomAppBar(title: 'Participation Records'),
            body: Column(
              children: [
                // Category Filter
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: ParticipationCategory(
                    onCategorySelected: (category) {
                      vm.setCategory(category);
                    },
                    initialCategory: vm.selectedCategory,
                  ),
                ),

                // Participation Cards List
                Expanded(
                  child: vm.isLoading
                      ? ListView.builder(
                          padding: const EdgeInsets.only(top: 8, bottom: 16),
                          itemCount: 4,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: EdgeInsets.symmetric(
                                vertical: 8.0,
                                horizontal: 16.0,
                              ),
                              child: _ParticipationCardShimmer(),
                            );
                          },
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.only(top: 8, bottom: 16),
                          itemCount: vm.records.length,
                          itemBuilder: (context, index) {
                            final ParticipationRecord participation =
                                vm.records[index];
                            return card.ParticipationCard(
                              productName: participation.productName,
                              imageUrl: participation.imageUrl,
                              date: participation.date,
                              batchNumber: participation.batchNumber,
                              participationPoints:
                                  participation.participationPoints,
                              state: _toWidgetState(participation.state),
                              prizeType: _toWidgetPrizeType(
                                participation.prizeType,
                              ),
                              productData: participation.toJson(),
                              onClaimTngVoucher:
                                  participation.prizeType ==
                                      PrizeType.tngVoucher
                                  ? () {
                                      context.push('/lucky-draw/claim-voucher');
                                    }
                                  : null,
                              onViewClaimed: () {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Viewing claimed prize details...',
                                    ),
                                    backgroundColor: Color(0xFF0EA5E9),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

card.ParticipationCardState _toWidgetState(ParticipationCardState state) {
  switch (state) {
    case ParticipationCardState.claimPrize:
      return card.ParticipationCardState.claimPrize;
    case ParticipationCardState.waiting:
      return card.ParticipationCardState.waiting;
    case ParticipationCardState.notWin:
      return card.ParticipationCardState.notWin;
    case ParticipationCardState.alreadyClaimed:
      return card.ParticipationCardState.alreadyClaimed;
  }
}

card.PrizeType _toWidgetPrizeType(PrizeType type) {
  switch (type) {
    case PrizeType.normal:
      return card.PrizeType.normal;
    case PrizeType.tngVoucher:
      return card.PrizeType.tngVoucher;
  }
}

// Shimmer skeleton for ParticipationCard
class _ParticipationCardShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    double scaleW(double value) => value * screenWidth / 375;

    Color baseColor = Colors.grey[300]!;
    Color highlightColor = Colors.grey[100]!;

    Widget shimmerBox({
      double? width,
      double? height,
      BorderRadius? borderRadius,
    }) {
      return Shimmer.fromColors(
        baseColor: baseColor,
        highlightColor: highlightColor,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: baseColor,
            borderRadius: borderRadius ?? BorderRadius.circular(4),
          ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(scaleW(12)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(scaleW(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image placeholder
          shimmerBox(
            width: scaleW(64),
            height: scaleW(64),
            borderRadius: BorderRadius.circular(scaleW(8)),
          ),
          SizedBox(width: scaleW(12)),
          // Text placeholders
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                shimmerBox(width: scaleW(180), height: scaleW(14)),
                SizedBox(height: scaleW(8)),
                shimmerBox(width: scaleW(100), height: scaleW(12)),
                SizedBox(height: scaleW(8)),
                shimmerBox(width: scaleW(80), height: scaleW(12)),
                SizedBox(height: scaleW(8)),
                Row(
                  children: [
                    shimmerBox(width: scaleW(60), height: scaleW(12)),
                    SizedBox(width: scaleW(8)),
                    shimmerBox(width: scaleW(40), height: scaleW(12)),
                  ],
                ),
                SizedBox(height: scaleW(12)),
                Align(
                  alignment: Alignment.centerRight,
                  child: shimmerBox(
                    width: scaleW(80),
                    height: scaleW(24),
                    borderRadius: BorderRadius.circular(scaleW(6)),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
