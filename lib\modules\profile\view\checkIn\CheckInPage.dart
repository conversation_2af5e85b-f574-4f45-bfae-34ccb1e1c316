// lib/modules/profile/checkin/view/checkin_page.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../view-model/CheckInViewModel.dart';
import '../../widgets/checkIn/CheckInAppBar.dart';
import '../../widgets/checkIn/CheckInBanner.dart';
import '../../widgets/checkIn/CheckInCalender.dart';
import '../../widgets/checkIn/CheckInSummaryText.dart';

class CheckInPage extends StatelessWidget {
  const CheckInPage({super.key});

  @override
  Widget build(BuildContext context) {
    final vm = Provider.of<CheckInViewModel>(context);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const PreferredSize(
        preferredSize: Size.fromHeight(70),
        child: CheckInAppBar(),
      ),
      body: Column(
        children: [
          Stack(
            children: [
              const CheckInBanner(),
              Padding(
                padding: const EdgeInsets.only(top: 80), // Slight overlap
                child: CheckInCalendar(),
              ),
            ],
          ),
          const SizedBox(height: 12),
          CheckInSummaryText(consecutiveDays: vm.consecutiveDays),
        ],
      ),
    );
  }
}
