// lib/modules/profile/checkin/widget/total_points_display.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../view-model/CheckInViewModel.dart';

class TotalPointsDisplay extends StatelessWidget {
  const TotalPointsDisplay({super.key});

  @override
  Widget build(BuildContext context) {
    final totalPoints = context.watch<CheckInViewModel>().totalPoints;

    return GestureDetector(
      onTap: () {
        // Optional: Implement dialog or toast to explain points
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: const LinearGradient(
            colors: [Colors.orange, Colors.deepOrange],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          boxShadow: const [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 4,
              offset: Offset(2, 3),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.star, color: Colors.white),
            const SizedBox(width: 8),
            Text(
              '$totalPoints pts',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
