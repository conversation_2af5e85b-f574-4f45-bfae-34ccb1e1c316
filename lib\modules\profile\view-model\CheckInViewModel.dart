// lib/modules/profile/checkin/view_model/checkin_view_model.dart
import 'package:flutter/material.dart';
import '../model/CheckingModel.dart';

class CheckInViewModel extends ChangeNotifier {
  DateTime _currentMonth = DateTime.now();
  final Map<String, CheckInDay> _checkInDays = {}; // Key: 'yyyy-MM-dd'
  int _totalPoints = 0;

  DateTime get currentMonth => _currentMonth;

  List<CheckInDay?> get daysInMonth {
    final firstDay = DateTime(_currentMonth.year, _currentMonth.month, 1);
    final lastDay = DateTime(_currentMonth.year, _currentMonth.month + 1, 0);

    final int startWeekday = (firstDay.weekday + 6) % 7; // Monday = 0
    List<CheckInDay?> days = [];

    // Add empty slots for alignment (e.g. if first day is Wednesday)
    for (int i = 0; i < startWeekday; i++) {
      days.add(null);
    }

    for (int i = 0; i < lastDay.day; i++) {
      final day = firstDay.add(Duration(days: i));
      final key = _keyFromDate(day);
      days.add(_checkInDays[key] ?? CheckInDay(date: day));
    }

    return days;
  }


  int get totalPoints => _totalPoints;

  int get consecutiveDays {
    final today = DateTime.now();
    int streak = 0;
    for (int i = 0; i < 31; i++) {
      final date = today.subtract(Duration(days: i));
      final key = _keyFromDate(date);
      if (_checkInDays[key]?.isClaimed == true) {
        streak++;
      } else {
        break;
      }
    }
    return streak;
  }

  void goToNextMonth() {
    _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
    notifyListeners();
  }

  void goToPreviousMonth() {
    _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
    notifyListeners();
  }

  void claimDay(DateTime date) {
    final key = _keyFromDate(date);
    if (_checkInDays[key]?.isClaimed == true) return;

    _checkInDays[key] = CheckInDay(date: date, isClaimed: true);
    _totalPoints += 1;
    notifyListeners();
  }

  bool isDayClaimed(DateTime date) {
    return _checkInDays[_keyFromDate(date)]?.isClaimed ?? false;
  }

  String _keyFromDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }
}
