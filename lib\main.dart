import 'package:flutter/material.dart';
import 'router/app_router.dart';
import 'package:provider/provider.dart';
import 'modules/card/services/SeriesService.dart';
import 'modules/card/view-model/reveal/RevealCardViewModel.dart';
import 'modules/profile/view-model/CheckInViewModel.dart';
import 'modules/profile/view-model/UserProfileVM.dart';
import 'modules/profile/view-model/InviteFriendViewModel.dart';
import 'modules/card/view-Model/purchaseCard/PurchaseCardViewModel.dart';
import 'router/auth_guard.dart';
import 'modules/card/view-Model/cardPack/CardCategoryViewModel.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize AuthService before running the app
  await AuthService.initialize();

  final seriesService = SeriesService();
  
  runApp(
    MultiProvider(
      providers: [
        Provider<SeriesService>.value(value: seriesService),
        ChangeNotifierProvider(create: (_) => RevealCardViewModel()),
        ChangeNotifierProvider(create: (_) => CheckInViewModel()),
        ChangeNotifierProvider(
          create: (_) => UserProfileVM()..fetchUserProfile(),
        ),
        ChangeNotifierProvider(create: (_) => InviteFriendViewModel()),
        // provide CardCategoryViewModel first
        ChangeNotifierProvider(
          create: (_) => CardCategoryViewModel()..loadSeriesData(),
        ),

        // PurchaseCardViewModel depends on CardCategoryViewModel
        ChangeNotifierProxyProvider<CardCategoryViewModel, PurchaseCardViewModel>(
          create: (context) => PurchaseCardViewModel(
            cardCategoryViewModel: context.read<CardCategoryViewModel>(),
            seriesService: seriesService,
          ),
          update: (context, categoryVM, previous) => previous ?? 
            PurchaseCardViewModel(
              cardCategoryViewModel: categoryVM,
              seriesService: seriesService,
            ),
        ),
        
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'MY Lucky Mall',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      routerConfig: AppRouter.router,
      debugShowCheckedModeBanner: false,
    );
  }
}
