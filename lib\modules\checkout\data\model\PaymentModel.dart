import 'CheckoutModel.dart';

class ToOrderModel {
  final int? couponId;
  int addressId;
  final List<Cards>? cards;
  final List<Products> products;
  final String? userNote; // Basically remark

  ToOrderModel({
    this.couponId,
    required this.addressId,
    this.cards,
    required this.products,
    this.userNote,
  });

  Map<String, dynamic> toJson(int? addressId) {
    return {
      'addressId': addressId,
      'products': products.map((product) => product.toJson()).toList(),
      'userNote': userNote,
    };
  }
}

class ToOrderReceiveModel {
  final int orderId;
  final int payOrderId;

  ToOrderReceiveModel({required this.orderId, required this.payOrderId});

  factory ToOrderReceiveModel.fromJson(Map<String, dynamic> json) {
    return ToOrderReceiveModel(
      orderId: json['orderId'],
      payOrderId: json['payOrderId'],
    );
  }
}