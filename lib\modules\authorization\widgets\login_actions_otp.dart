import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class LoginActions extends StatelessWidget {
  final VoidCallback? onForgotPassword;
  final VoidCallback? onOtpLogin;

  const LoginActions({super.key, this.onForgotPassword, this.onOtpLogin});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          GestureDetector(
            onTap:
                onOtpLogin ??
                () {
                  context.go('/login');
                },
            child: const Text(
              'Password Login',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
