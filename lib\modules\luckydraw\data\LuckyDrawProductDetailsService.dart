import 'package:dio/dio.dart';
import '../model/LuckyDrawProductDetails.dart';

final luckyDrawProductDetailsService = LuckyDrawProductDetailsService();

class LuckyDrawProductDetailsService {
  final Dio dio = Dio();

  /// Get detailed information for a specific lucky draw product by ID
  /// Endpoint: GET /product/point/{id}
  ///
  /// Parameters:
  /// - id: The product ID (required path parameter, type: integer)
  ///
  /// Returns: LuckyDrawProductDetailsResponse with product details
  Future<LuckyDrawProductDetailsResponse> getProductDetails(int id) async {
    try {
      print('ProductDetailsService: getProductDetails called with id: $id');

      final response = await dio.get(
        'https://api.luckymall.com.my/product/point/$id',
      );

      print(
        'ProductDetailsService: API response status: ${response.statusCode}',
      );
      print('ProductDetailsService: API response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        final result = LuckyDrawProductDetailsResponse.fromJson(responseData);

        print(
          'ProductDetailsService: Parsed response - status: ${result.status}, hasData: ${result.hasData}',
        );

        return result;
      } else {
        throw Exception(
          'Failed to load product details: HTTP ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      print('ProductDetailsService: DioException: ${e.message}');
      print('ProductDetailsService: DioException type: ${e.type}');
      print(
        'ProductDetailsService: DioException response: ${e.response?.data}',
      );

      String errorMessage = 'Network error';
      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = 'Connection timeout';
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Response timeout';
      } else if (e.type == DioExceptionType.badResponse) {
        errorMessage = 'Server error: ${e.response?.statusCode}';
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'Connection failed';
      }

      throw Exception('$errorMessage: ${e.message}');
    } catch (e) {
      print('ProductDetailsService: Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  /// Get product details with error handling and retry logic
  Future<LuckyDrawProductDetailsResponse> getProductDetailsWithRetry(
    int id, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      try {
        attempts++;
        print('ProductDetailsService: Attempt $attempts of $maxRetries');

        return await getProductDetails(id);
      } catch (e) {
        print('ProductDetailsService: Attempt $attempts failed: $e');

        if (attempts >= maxRetries) {
          throw Exception('Failed after $maxRetries attempts: $e');
        }

        // Wait before retrying
        await Future.delayed(retryDelay * attempts); // Exponential backoff
      }
    }

    throw Exception('Unexpected error in retry logic');
  }

  /// Validate if a product ID is valid (basic validation)
  bool isValidProductId(int id) {
    return id > 0;
  }

  /// Test the product details API endpoint
  Future<bool> testProductDetailsAPI(int testId) async {
    try {
      print(
        'ProductDetailsService: Testing product details API with ID: $testId',
      );

      final response = await getProductDetails(testId);

      print('ProductDetailsService: API test successful');
      print('ProductDetailsService: Response status: ${response.status}');
      print('ProductDetailsService: Has data: ${response.hasData}');

      return response.isSuccess;
    } catch (e) {
      print('ProductDetailsService: API test failed: $e');
      return false;
    }
  }
}
