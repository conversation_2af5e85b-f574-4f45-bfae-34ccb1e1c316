import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../widgets/LuckyAppBar.dart';
import '../widgets/CharityCounter.dart';
import '../widgets/ReviewCard.dart';
import '../widgets/LatestDrawsWidget.dart';
import '../widgets/CommunitySharingWidget.dart';
import '../widgets/CategorySelectorWidget.dart';
import '../widgets/PointProductCard.dart';
import '../widgets/SkeletonLoader.dart';
import '../view-model/MainpageVM.dart';
import '../view-model/CategoryVM.dart';
import '../data/service/ReviewService.dart';
import '../model/LuckyDrawProductReview.dart';

class MainpageView extends StatefulWidget {
  const MainpageView({super.key});

  @override
  State<MainpageView> createState() => _MainpageViewState();
}

class _MainpageViewState extends State<MainpageView> {
  String selectedCategory = 'ALL';
  int minPointFilter = 0;
  int maxPointFilter = 999999;

  // Loading states for different components
  bool isReviewsLoading = true;
  bool isLatestDrawsLoading = true;
  bool isCommunitySharingLoading = true;
  bool isPointProductsLoading = true;

  // Pagination state
  bool _isLoadingMore = false;
  bool _hasMoreProducts = true;
  int _currentPage = 1;
  static const int _pageSize = 10;

  // Carousel controller and state
  final PageController _reviewPageController = PageController(
    viewportFraction: 0.92,
  );
  int _currentReviewPage = 0;
  Timer? _reviewAutoScrollTimer;

  // Cache filtered products to avoid recalculation
  List<Map<String, dynamic>> _cachedFilteredProducts = [];
  String _lastFilterKey = '';

  // ViewModel for managing lucky draw data
  late final MainpageVM _viewModel;

  // Fetch data from ViewModel
  Future<void> _fetchData() async {
    await Future.wait([
      _viewModel.fetchProducts(),
      _viewModel.fetchLatestDraws(),
      _fetchReviews(),
    ]);
    // Update filtered products after fetching data
    if (mounted) {
      _updateFilteredProducts();
    }
  }

  // Fetch reviews from API
  Future<void> _fetchReviews() async {
    try {
      setState(() {
        isReviewsLoading = true;
      });

      final result = await reviewService.getLatestReviews(
        limit: 10,
        page: 1,
        fetchVariations: true, // This will fetch actual variation names
      );

      if (mounted) {
        setState(() {
          reviewData = List<LuckyDrawProductReview>.from(result['reviews']);
          isReviewsLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isReviewsLoading = false;
        });
        // Show error message or fallback to empty reviews
        print('Error fetching reviews: $e');
      }
    }
  }

  // Build no reviews message
  Widget _buildNoReviewsMessage() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.rate_review_outlined, size: 48, color: Colors.grey[400]),
          SizedBox(height: 16),
          Text(
            'No reviews yet',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Be the first to share your experience!',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Handle ViewModel changes
  void _onViewModelChanged() {
    if (mounted) {
      print('MainpageView: ViewModel changed, updating state...');
      setState(() {
        // Update local state based on ViewModel changes
        isPointProductsLoading = _viewModel.isPointProductsLoading;
        isLatestDrawsLoading = _viewModel.isLatestDrawsLoading;
        selectedCategory = _viewModel.selectedCategory;
        minPointFilter = _viewModel.minPointFilter;
        maxPointFilter = _viewModel.maxPointFilter;
      });
      // Update filtered products after state changes
      print(
        'MainpageView: Calling _updateFilteredProducts from _onViewModelChanged',
      );
      _updateFilteredProducts();
    }
  }

  @override
  void initState() {
    super.initState();
    _viewModel = MainpageVM();
    _viewModel.addListener(_onViewModelChanged);
    _fetchData();
    _simulateLoading();

    // Delay the start of auto-scroll to ensure PageView is built first
    Future.delayed(const Duration(milliseconds: 1600), () {
      if (mounted) {
        _startReviewAutoScroll();
      }
    });
  }

  // Load more products when user scrolls near the bottom
  Future<void> _loadMoreProducts() async {
    if (_isLoadingMore || !_hasMoreProducts) {
      print(
        'MainpageView: Skipping load more - isLoadingMore: $_isLoadingMore, hasMoreProducts: $_hasMoreProducts',
      );
      return;
    }

    print('MainpageView: Loading more products... Current page: $_currentPage');
    setState(() {
      _isLoadingMore = true;
    });

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Get all filtered products from ViewModel
      final int minPts = minPointFilter;
      final int maxPts = maxPointFilter;

      final allFilteredProducts = _viewModel.allProducts
          .where((product) {
            final pts = product.totalAmount;
            final withinMin = minPts <= 0 ? true : pts >= minPts;
            final withinMax = (maxPts >= 999999) ? true : pts <= maxPts;
            return withinMin && withinMax;
          })
          .map((product) {
            final map = {
              'id': product.id,
              'productSpuId': product.productSpuId,
              'productSkuId': product.productSkuId,
              'productSkuName': product.productSkuName,
              'productSkuImage': product.productSkuImage,
              'usedAmount': product.usedAmount,
              'totalAmount': product.totalAmount,
              'score': product.score,
              'sold': product.sold,
            };
            return map;
          })
          .toList();

      print(
        'MainpageView: Total filtered products available: ${allFilteredProducts.length}',
      );

      // Load next page of products
      _currentPage++;
      final endIndex = (_currentPage * _pageSize).clamp(
        0,
        allFilteredProducts.length,
      );
      _cachedFilteredProducts = allFilteredProducts.take(endIndex).toList();

      // Update hasMoreProducts flag
      _hasMoreProducts =
          _cachedFilteredProducts.length < allFilteredProducts.length;

      print(
        'MainpageView: Loaded page $_currentPage, total products: ${_cachedFilteredProducts.length}, hasMore: $_hasMoreProducts',
      );
    } catch (e) {
      print('Error loading more products: $e');
      // Reset loading state on error
      _isLoadingMore = false;
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  // Reset pagination when filters change
  void _resetPagination() {
    _currentPage = 1;
    _hasMoreProducts = true;
    _cachedFilteredProducts.clear();
  }

  // Debug scroll controller status
  void _debugScrollController() {
    print('MainpageView: Debug scroll controller:');
    print('  - Using NotificationListener for pagination');
    print('  - Current page: $_currentPage');
    print('  - Has more products: $_hasMoreProducts');
    print('  - Cached products: ${_cachedFilteredProducts.length}');
  }

  void _startReviewAutoScroll() {
    _reviewAutoScrollTimer?.cancel();
    _reviewAutoScrollTimer = Timer.periodic(const Duration(seconds: 4), (
      timer,
    ) {
      if (!mounted || isReviewsLoading || reviewData.isEmpty) return;

      // Check if the PageController is attached to a PageView
      if (!_reviewPageController.hasClients) return;

      int nextPage = _currentReviewPage + 1;
      if (nextPage >= reviewData.length) nextPage = 0;
      _reviewPageController.animateToPage(
        nextPage,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  void dispose() {
    _reviewAutoScrollTimer?.cancel();
    _reviewPageController.dispose();
    _viewModel.removeListener(_onViewModelChanged);
    super.dispose();
  }

  // Optimized loading simulation with single timer
  void _simulateLoading() {
    final loadingStages = [
      {'delay': 2500, 'update': () => isCommunitySharingLoading = false},
      {'delay': 3000, 'update': () => isPointProductsLoading = false},
    ];

    for (var stage in loadingStages) {
      Future.delayed(Duration(milliseconds: stage['delay'] as int), () {
        if (mounted) {
          setState(() {
            (stage['update'] as Function)();
          });
        }
      });
    }
  }

  // Handle filter changes and reset pagination
  void _handleFilterChange() {
    print('MainpageView: Filter changed, resetting pagination');
    _resetPagination();
    _updateFilteredProducts();
  }

  // Update cached filtered products only when filters change
  void _updateFilteredProducts() {
    print('MainpageView: _updateFilteredProducts called');
    final filterKey = '$selectedCategory-$minPointFilter-$maxPointFilter';
    print(
      'MainpageView: Current filter key: $filterKey, last filter key: $_lastFilterKey',
    );

    // Always update if we have products, regardless of filter key
    if (_lastFilterKey != filterKey || _cachedFilteredProducts.isEmpty) {
      print(
        'MainpageView: Updating filtered products. ViewModel has ${_viewModel.allProducts.length} products',
      );
      print('MainpageView: ViewModel products: ${_viewModel.allProducts}');

      // Apply client-side point range filtering similar to Blindbox price filter
      // If max is effectively unlimited, don't filter max side; same for min
      final int minPts = minPointFilter;
      final int maxPts = maxPointFilter;

      final allFilteredProducts = _viewModel.allProducts
          .where((product) {
            final pts = product.totalAmount;
            final withinMin = minPts <= 0 ? true : pts >= minPts;
            final withinMax = (maxPts >= 999999) ? true : pts <= maxPts;
            return withinMin && withinMax;
          })
          .map((product) {
            final map = {
              'id': product.id,
              'productSpuId': product.productSpuId,
              'productSkuId': product.productSkuId,
              'productSkuName': product.productSkuName,
              'productSkuImage': product.productSkuImage,
              'usedAmount': product.usedAmount,
              'totalAmount': product.totalAmount,
              'score': product.score,
              'sold': product.sold,
            };
            print('MainpageView: Converting product to map: $map');
            return map;
          })
          .toList();

      // Load first page of products
      final endIndex = (_currentPage * _pageSize).clamp(
        0,
        allFilteredProducts.length,
      );
      _cachedFilteredProducts = allFilteredProducts.take(endIndex).toList();

      // Update hasMoreProducts flag
      _hasMoreProducts =
          _cachedFilteredProducts.length < allFilteredProducts.length;

      print(
        'MainpageView: Converted ${_cachedFilteredProducts.length} products to Map format (page $_currentPage)',
      );
      print('MainpageView: Final cached products: $_cachedFilteredProducts');
      _lastFilterKey = filterKey;
    } else {
      print(
        'MainpageView: Skipping update - same filter key and products already cached',
      );
    }
  }

  // Handle refresh functionality
  Future<void> _handleRefresh() async {
    // Cancel existing auto-scroll timer
    _reviewAutoScrollTimer?.cancel();

    // Reset pagination
    _resetPagination();

    // Reset all loading states to true in single setState
    setState(() {
      isReviewsLoading = true;
      isLatestDrawsLoading = true;
      isCommunitySharingLoading = true;
      isPointProductsLoading = true;
    });

    // Start loading simulation again
    _simulateLoading();

    // Wait for at least 1 second to show the refresh indicator
    await Future.delayed(const Duration(milliseconds: 1000));

    // Refresh data from ViewModel
    await Future.wait([
      _viewModel.fetchProducts(),
      _viewModel.fetchLatestDraws(),
      _fetchReviews(),
    ]);

    // Restart auto-scroll timer after refresh
    Future.delayed(const Duration(milliseconds: 1600), () {
      if (mounted) {
        _startReviewAutoScroll();
      }
    });

    // Show completion feedback
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.refresh, color: Colors.white, size: 20),
              SizedBox(width: 8),
              Text(
                'Content refreshed successfully!',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
            ],
          ),
          backgroundColor: const Color(0xFFFFBF00),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  // Dynamic review data from API
  List<LuckyDrawProductReview> reviewData = [];

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [ChangeNotifierProvider(create: (context) => CategoryVM())],
      child: _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375;
    double scaleH(double value) => value * screenHeight / 812;
    double scaleText(double value) => value * screenWidth / 375;

    return Scaffold(
      appBar: LuckyAppBar(),
      backgroundColor: Colors.white,
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        color: const Color(0xFFFFBF00),
        backgroundColor: Colors.white,
        strokeWidth: 3.0,
        displacement: scaleH(40),
        child: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            if (scrollInfo is ScrollEndNotification) {
              // Check if we're near the bottom
              if (scrollInfo.metrics.pixels >=
                  scrollInfo.metrics.maxScrollExtent - 300) {
                print(
                  'MainpageView: Scroll notification - near bottom, triggering pagination',
                );
                _loadMoreProducts();
              }
            }
            return false;
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Simplified background section from charity counter to review cards
                Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFFFFD54F), // Light cream at the top
                        Color(0xFFFFFFFF), // White at the bottom
                      ],
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Charity Counter
                      SizedBox(height: scaleH(8)),
                      CharityCounter(),
                      SizedBox(height: scaleH(12)),
                      // Latest Reviews
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: scaleW(18)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Latest Reviews',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: scaleText(15),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                context.push('/lucky-draw/review-details');
                              },
                              child: Text(
                                'See more',
                                style: TextStyle(
                                  fontSize: scaleText(13),
                                  color: Colors.black87,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: scaleH(8)),
                      // Horizontal scrollable review cards
                      SizedBox(
                        height: scaleH(160),
                        child: isReviewsLoading
                            ? ListView.builder(
                                scrollDirection: Axis.horizontal,
                                padding: EdgeInsets.symmetric(
                                  horizontal: scaleW(10),
                                ),
                                itemCount: 3,
                                itemBuilder: (context, index) {
                                  return const Padding(
                                    padding: EdgeInsets.only(right: 6.0),
                                    child: ReviewCardSkeleton(),
                                  );
                                },
                              )
                            : reviewData.isEmpty
                            ? _buildNoReviewsMessage()
                            : Column(
                                children: [
                                  Expanded(
                                    child: PageView.builder(
                                      controller: _reviewPageController,
                                      itemCount: reviewData.length,
                                      onPageChanged: (index) {
                                        setState(() {
                                          _currentReviewPage = index;
                                        });
                                      },
                                      itemBuilder: (context, index) {
                                        final review = reviewData[index];
                                        return Padding(
                                          padding: EdgeInsets.only(
                                            right: index < reviewData.length - 1
                                                ? scaleW(6.0)
                                                : 0,
                                          ),
                                          child: ReviewCard.fromReview(
                                            review: review,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                      ),
                      SizedBox(height: scaleH(20)),
                    ],
                  ),
                ),

                // Quick Actions Section
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: scaleW(18.0)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Section Title
                      Padding(
                        padding: EdgeInsets.only(bottom: scaleH(8)),
                        child: Row(
                          children: [
                            Container(
                              width: scaleW(4),
                              height: scaleH(20),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFFBF00),
                                borderRadius: BorderRadius.circular(scaleW(2)),
                              ),
                            ),
                            SizedBox(width: scaleW(8)),
                            Text(
                              'Quick Actions',
                              style: TextStyle(
                                fontSize: scaleText(15),
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Three Feature Buttons
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                context.go('/blind-box/shop');
                              },
                              child: _buildFeatureButton(
                                title: 'Blind Box \nStore',
                                image: 'assets/images/wizard-blindbox.png',
                                backgroundColor: const Color(0xFFd90019),
                                textColor: Colors.white,
                              ),
                            ),
                          ),
                          SizedBox(width: scaleW(8)),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                context.push('/lucky-draw/prize-reveal');
                              },
                              child: _buildFeatureButton(
                                title: 'Prize \nRevealing',
                                image: 'assets/images/knight-prize-reveal.png',
                                backgroundColor: const Color(0xFFffbf00),
                                textColor: Colors.black,
                              ),
                            ),
                          ),
                          SizedBox(width: scaleW(8)),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                context.push(
                                  '/lucky-draw/participation-records',
                                );
                              },
                              child: _buildFeatureButton(
                                title: 'Participate \nRecords',
                                image: 'assets/images/participation-record.png',
                                backgroundColor: const Color(0xFFd90019),
                                textColor: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                SizedBox(height: scaleH(20)),

                // Latest Draws
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: scaleW(18)),
                  child: isLatestDrawsLoading
                      ? Container(
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFBF00),
                            borderRadius: BorderRadius.circular(scaleW(12)),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Header
                              Padding(
                                padding: EdgeInsets.fromLTRB(
                                  scaleW(16),
                                  scaleH(16),
                                  scaleW(16),
                                  0,
                                ),
                                child: Row(
                                  children: [
                                    Text(
                                      'Latest Draws',
                                      style: TextStyle(
                                        fontSize: scaleText(13),
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const Spacer(),
                                    Text(
                                      'See more',
                                      style: TextStyle(
                                        fontSize: scaleText(13),
                                        color: Colors.black87,
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // Skeleton content
                              Container(
                                margin: EdgeInsets.all(scaleW(16)),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF9F6EE),
                                  borderRadius: BorderRadius.circular(
                                    scaleW(12),
                                  ),
                                ),
                                child: SizedBox(
                                  height: scaleH(200),
                                  child: ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    padding: EdgeInsets.all(scaleW(16)),
                                    itemCount: 3,
                                    itemBuilder: (context, index) {
                                      return const LatestDrawCardSkeleton();
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      : LatestDrawsWidget(latestDraws: _viewModel.latestDraws),
                ),
                SizedBox(height: scaleH(20)),

                // Community Sharing
                isCommunitySharingLoading
                    ? Padding(
                        padding: EdgeInsets.symmetric(horizontal: scaleW(18)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Community Sharing',
                                  style: TextStyle(
                                    fontSize: scaleText(15),
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                Text(
                                  'See more',
                                  style: TextStyle(
                                    fontSize: scaleText(13),
                                    color: Colors.black87,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: scaleH(12)),
                            SizedBox(
                              height: scaleH(200),
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: 3,
                                itemBuilder: (context, index) {
                                  return const CommunitySharingCardSkeleton();
                                },
                              ),
                            ),
                          ],
                        ),
                      )
                    : CommunitySharingWidget(),
                SizedBox(height: scaleH(30)),

                Divider(
                  color: Color(0xFFffbf00),
                  thickness: scaleH(3),
                  indent: scaleW(20),
                  endIndent: scaleW(20),
                ),
                SizedBox(height: scaleH(30)),

                Center(
                  child: Text(
                    'Point Product',
                    style: TextStyle(
                      fontSize: scaleText(16),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                SizedBox(height: scaleH(20)),

                // Category Selector Widget
                CategorySelectorWidget(
                  initialCategory: selectedCategory,
                  onCategoryChanged: (category) {
                    setState(() {
                      selectedCategory = category;
                    });
                    _viewModel.setCategory(category);
                    // Reset pagination when category changes
                    _handleFilterChange();
                    print('Selected category: $category');
                  },
                  onPointRangeChanged: (min, max) {
                    setState(() {
                      minPointFilter = min;
                      maxPointFilter = max;
                    });
                    _viewModel.setPointRange(min, max);
                    // Reset pagination when point range changes
                    _handleFilterChange();
                    print('Point range changed: $min - $max');
                  },
                ),

                SizedBox(height: scaleH(16)),

                // Filter Results Info
                if (!isPointProductsLoading)
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: scaleW(16)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Showing ${_cachedFilteredProducts.length} product${_cachedFilteredProducts.length != 1 ? 's' : ''}',
                          style: TextStyle(
                            fontSize: scaleText(14),
                            fontWeight: FontWeight.w500,
                            color: Colors.black54,
                          ),
                        ),
                        Row(
                          children: [
                            if (_hasMoreProducts)
                              Text(
                                'Page $_currentPage',
                                style: TextStyle(
                                  fontSize: scaleText(12),
                                  fontWeight: FontWeight.w400,
                                  color: Colors.grey[600],
                                ),
                              ),
                            if (_hasMoreProducts && !_isLoadingMore)
                              TextButton(
                                onPressed: _loadMoreProducts,
                                child: Text(
                                  'Load More',
                                  style: TextStyle(
                                    fontSize: scaleText(12),
                                    color: const Color(0xFFFFBF00),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),

                SizedBox(height: scaleH(16)),

                // Point Products Grid
                Column(
                  children: [
                    // Simple no products found message
                    if (_cachedFilteredProducts.isEmpty)
                      Container(
                        padding: EdgeInsets.all(scaleW(24)),
                        margin: EdgeInsets.symmetric(horizontal: scaleW(16)),
                        child: Column(
                          children: [
                            Icon(
                              Icons.inbox_outlined,
                              color: Color(0xFFffbf00),
                              size: 64,
                            ),
                            SizedBox(height: scaleH(16)),
                            Text(
                              'No products found',
                              style: TextStyle(
                                color: Color(0xFF808080),
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),

                    // Products Grid
                    isPointProductsLoading
                        ? const PointProductGridSkeleton()
                        : Builder(
                            builder: (context) {
                              print(
                                'MainpageView: Rendering PointProductGrid with ${_cachedFilteredProducts.length} products',
                              );
                              print(
                                'MainpageView: Products data: $_cachedFilteredProducts',
                              );
                              return PointProductGrid(
                                products: _cachedFilteredProducts,
                                isLoadingMore: _isLoadingMore,
                                hasMoreProducts: _hasMoreProducts,
                                onProductTap: (product) {
                                  // For now, we'll use a simple navigation - in a real app, you'd pass product data
                                  context.push(
                                    '/lucky-draw/product-detail',
                                    extra: product,
                                  );
                                },
                              );
                            },
                          ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureButton({
    required String title,
    required String image,
    required Color backgroundColor,
    required Color textColor,
  }) {
    return Container(
      height: 160,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(image, height: 90, width: 90, fit: BoxFit.fitWidth),
          const SizedBox(height: 4),
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }
}
