import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:luckymall/modules/authorization/service/AuthHttpService.dart';
import 'package:luckymall/modules/authorization/widgets/login_header.dart';
import '../widgets/phone_number_field.dart';
import '../widgets/verification_code_field.dart';
import '../widgets/password_field.dart';
import '../widgets/login_button.dart';

class ForgotPasswordView extends StatefulWidget {
  const ForgotPasswordView({super.key});

  @override
  State<ForgotPasswordView> createState() => _ForgotPasswordViewState();
}

class _ForgotPasswordViewState extends State<ForgotPasswordView> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _verificationController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  bool _isLoading = false;
  String _phoneComplete = '';
  int _countryCode =
      60; // Default to MY as per IntlPhoneField initialCountryCode

  @override
  void dispose() {
    _phoneController.dispose();
    _verificationController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _onSendCode() {
    if (_phoneComplete.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your phone number')),
      );
      return;
    }
    AuthHttpService.instance
        .sendCode(country: _countryCode, mobile: _phoneComplete.trim())
        .then((response) {
          print(
            '[ForgotPasswordView] Sending code => country: ' +
                _countryCode.toString() +
                ', mobile: ' +
                _phoneComplete.trim(),
          );
          final int status = (response['status'] is int)
              ? response['status'] as int
              : -1;
          final String message = response['message']?.toString() ?? '';
          if (status == 0) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Verification code sent')),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  message.isEmpty ? 'Failed to send code' : message,
                ),
              ),
            );
          }
        });
  }

  void _onChangePassword() {
    // Validate inputs
    if (_phoneComplete.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your phone number')),
      );
      return;
    }

    if (_verificationController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter the verification code')),
      );
      return;
    }

    if (_passwordController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your new password')),
      );
      return;
    }

    if (_passwordController.text != _confirmPasswordController.text) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Passwords do not match')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Call the forgot password API
    AuthHttpService.instance
        .forgotPassword(
          country: _countryCode,
          mobile: _phoneComplete.trim(),
          code: _verificationController.text.trim(),
          password: _passwordController.text.trim(),
        )
        .then((response) {
          print(
            '[ForgotPasswordView] Forgot password response => status: ' +
                response['status'].toString() +
                ', message: ' +
                response['message'].toString(),
          );

          final int status = (response['status'] is int)
              ? response['status'] as int
              : -1;
          final String message = response['message']?.toString() ?? '';

          setState(() {
            _isLoading = false;
          });

          if (status == 0) {
            // Success - show success message and navigate to login
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Password changed successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            // Navigate back to login page after a short delay
            Future.delayed(const Duration(seconds: 1), () {
              context.go('/login');
            });
          } else {
            // Error - show error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  message.isEmpty ? 'Failed to change password' : message,
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        })
        .catchError((error) {
          print('[ForgotPasswordView] Error: $error');
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${error.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFFFFFFF), Color(0xFFFCD255)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top,
              ),
              child: IntrinsicHeight(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const LoginHeader(),
                    const SizedBox(height: 20),
                    // Phone Number Field
                    PhoneNumberField(
                      controller: _phoneController,
                      hintText: 'Please enter your phone number',
                      onChanged: (value) {
                        _phoneComplete = value;
                      },
                      onCountryCodeChanged: (dial) {
                        final normalized = dial.replaceAll('+', '');
                        final parsed = int.tryParse(normalized);
                        if (parsed != null) {
                          _countryCode = parsed;
                        }
                      },
                    ),
                    const SizedBox(height: 10),
                    // Verification Code Field
                    VerificationCodeField(
                      controller: _verificationController,
                      hintText: 'Please enter your OTP code',
                      onSendCode: _onSendCode,
                    ),
                    const SizedBox(height: 10),
                    // Password Field
                    PasswordField(
                      controller: _passwordController,
                      label: 'Password',
                      hintText: 'Please enter your password',
                    ),
                    const SizedBox(height: 10),
                    // Confirm Password Field
                    PasswordField(
                      controller: _confirmPasswordController,
                      label: 'Confirm Password',
                      hintText: 'Please confirm your password',
                    ),
                    const SizedBox(height: 20),
                    // Change Password Button
                    LoginButton(
                      text: 'Change Password',
                      onPressed: _onChangePassword,
                      isLoading: _isLoading,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
