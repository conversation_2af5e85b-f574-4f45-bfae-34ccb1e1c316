import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SelectVoucherView extends StatefulWidget {
  final double orderAmount;
  final Function(Map<String, dynamic>?)? onVoucherSelected;

  const SelectVoucherView({
    super.key,
    required this.orderAmount,
    this.onVoucherSelected,
  });

  @override
  State<SelectVoucherView> createState() => _SelectVoucherViewState();
}

class _SelectVoucherViewState extends State<SelectVoucherView> {
  Map<String, dynamic>? selectedVoucher;
  List<Map<String, dynamic>> availableVouchers = [];

  @override
  void initState() {
    super.initState();
    debugPrint(
      'SelectVoucherView initialized with order amount: ${widget.orderAmount}',
    );
    _loadAvailableVouchers();
  }

  void _loadAvailableVouchers() {
    // Mock data - in real app, this would come from API
    availableVouchers = [
      {
        'id': '1',
        'amount': 8.0,
        'minSpend': 20.0,
        'validDate': '20.07.2025',
        'description': 'RM8 Off',
        'isActive': true,
      },
      {
        'id': '2',
        'amount': 8.0,
        'minSpend': 20.0,
        'validDate': '20.07.2025',
        'description': 'RM8 Off',
        'isActive': true,
      },
      {
        'id': '3',
        'amount': 10.0,
        'minSpend': 30.0,
        'validDate': '25.12.2025',
        'description': 'RM10 Off',
        'isActive': true,
      },
      {
        'id': '4',
        'amount': 15.0,
        'minSpend': 50.0,
        'validDate': '30.12.2025',
        'description': 'RM15 Off',
        'isActive': true,
      },
    ];

    // Filter vouchers that meet minimum spend requirement
    availableVouchers = availableVouchers
        .where((voucher) => voucher['minSpend'] <= widget.orderAmount)
        .toList();
  }

  void _selectVoucher(Map<String, dynamic> voucher) {
    setState(() {
      selectedVoucher = voucher;
    });
  }

  void _confirmSelection() {
    if (selectedVoucher != null) {
      widget.onVoucherSelected?.call(selectedVoucher);
    }
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('SelectVoucherView build method called');
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 70,
        elevation: 1,
        shadowColor: Colors.black26,
        backgroundColor: Colors.white,
        title: const Text(
          'Select Voucher',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
          onPressed: () => context.pop(),
        ),
      ),
      body: Column(
        children: [
          // Available vouchers section
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      'Available vouchers',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  ...availableVouchers.map(
                    (voucher) => _buildVoucherCard(voucher),
                  ),
                  if (availableVouchers.isEmpty) _buildEmptyState(),
                ],
              ),
            ),
          ),

          // Bottom section with selection summary and OK button
          _buildBottomSection(),
        ],
      ),
    );
  }

  Widget _buildVoucherCard(Map<String, dynamic> voucher) {
    final isSelected = selectedVoucher?['id'] == voucher['id'];
    final isValid = voucher['minSpend'] <= widget.orderAmount;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected ? const Color(0xFFFFB800) : Colors.grey.shade300,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          // Voucher icon and details
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Voucher icon
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFB800),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: SvgPicture.asset(
                      'assets/icons/Voucher.svg',
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                      width: 30,
                      height: 30,
                    ),
                  ),
                  const SizedBox(width: 16),

                  // Voucher details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          voucher['description'],
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Min. Spend RM${voucher['minSpend'].toStringAsFixed(0)}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'Valid Until ${voucher['validDate']}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Selected Products',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Radio button
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Radio<String>(
              value: voucher['id'],
              groupValue: selectedVoucher?['id'],
              onChanged: isValid ? (value) => _selectVoucher(voucher) : null,
              activeColor: const Color(0xFFFFB800),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      margin: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(Icons.local_offer_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No vouchers available',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You don\'t have any vouchers that meet the minimum spend requirement.',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Selection summary
            if (selectedVoucher != null) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '1 Voucher Selected.',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                  Text(
                    'discount: -RM${selectedVoucher!['amount'].toStringAsFixed(0)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // OK button
            SizedBox(
              width: double.infinity,
              height: 52,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFFB800),
                  foregroundColor: Colors.black,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                onPressed: () => _confirmSelection(),
                child: const Text(
                  'OK',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
