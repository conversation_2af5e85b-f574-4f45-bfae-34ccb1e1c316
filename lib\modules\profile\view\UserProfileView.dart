//import 'package:luckymall/component/Background.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../view-model/BackgroundStateManager.dart';
import '../view-model/FaceVerificationState.dart';
import '../view-model/UserProfileVM.dart';

class UserProfileView extends StatefulWidget {
  const UserProfileView({super.key});

  @override
  State<UserProfileView> createState() => _UserProfileViewState();
}

class _UserProfileViewState extends State<UserProfileView> {
  @override
  void initState() {
    super.initState();
    // Fetch user profile when the view is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final vm = Provider.of<UserProfileVM>(context, listen: false);
      vm.fetchUserProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final responsive = _ResponsiveHelper(mediaQuery);

    return Scaffold(
      backgroundColor: Colors.white,
      body: Consumer<UserProfileVM>(
        builder: (context, vm, child) {
          return RefreshIndicator(
            onRefresh: vm.refreshProfile,
            color: const Color(0xFFFFBF00),
            child: SingleChildScrollView(
              child: Stack(
                children: [
                  // Dynamic background color with ValueListenableBuilder
                  ValueListenableBuilder<Color>(
                    valueListenable:
                        BackgroundStateManager().backgroundNotifier,
                    builder: (context, backgroundColor, child) {
                      return GestureDetector(
                        onTap: () => context.push('/profile/choose-background'),
                        child: Container(
                          width: double.infinity,
                          height: _getHeaderHeight(context),
                          color: backgroundColor,
                        ),
                      );
                    },
                  ),

                  // Background(
                  //   path: 'assets/videos/Background.mp4',
                  //   child: SizedBox(
                  //     width: double.infinity,
                  //     height: _getHeaderHeight(context),
                  //     // color: backgroundColor,
                  //   ),
                  // ),

                  // Top-right icons
                  Positioned(
                    top: mediaQuery.padding.top + 16,
                    right: 16,
                    child: Row(
                      children: [
                        _topIcon(
                          'assets/icons/Customer Support.svg',
                          iconSize: responsive.topIconSize,
                        ),
                        _topIcon(
                          'assets/icons/Notification.svg',
                          iconSize: responsive.topIconSize,
                          onTap: () => context.push('/profile/notification'),
                        ),
                        _topIcon(
                          'assets/icons/Settings 02.svg',
                          iconSize: responsive.topIconSize,
                          onTap: () => context.push('/profile/settings'),
                        ),
                      ],
                    ),
                  ),

                  // Main content
                  Column(
                    children: [
                      SizedBox(height: mediaQuery.padding.top + 40),

                      // Profile section - show different content based on auth state
                      if (vm.isLoggedIn && !vm.isLoading && vm.userInfo != null)
                        _ProfileSection(
                          username: vm.displayName,
                          userId: vm.userId,
                          points: vm.points,
                          cardCount: vm.cardCount,
                          profileImage: vm.profileImage,
                          responsive: responsive,
                        )
                      else if (vm.isLoggedIn && vm.isLoading)
                        _LoadingProfileSection(responsive: responsive)
                      else if (!vm.isLoggedIn)
                        _NotLoggedInSection(responsive: responsive)
                      else if (vm.errorMessage != null)
                        _ErrorProfileSection(
                          errorMessage: vm.errorMessage!,
                          responsive: responsive,
                          onRetry: () => vm.fetchUserProfile(),
                        )
                      else
                        _LoadingProfileSection(responsive: responsive),

                      // Action buttons and info cards
                      _buildActionContainer(context, responsive),

                      const SizedBox(height: 16),

                      // Updated invite friends banner
                      Padding(
                        padding: responsive.horizontalPadding,
                        child: _UserProfileBanner(responsive: responsive),
                      ),

                      const SizedBox(height: 24),

                      // Feature grid
                      _buildFeatureGrid(context, responsive),

                      const SizedBox(height: 32),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Helper method to get header height
  double _getHeaderHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final isTablet = screenWidth > 600;
    return isTablet ? 380 : 346;
  }

  // Extracted method for action container
  Widget _buildActionContainer(
    BuildContext context,
    _ResponsiveHelper responsive,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: responsive.horizontalPadding.horizontal / 2,
        vertical: 12,
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade400,
              blurRadius: 12,
              spreadRadius: 1,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _ActionIcon(
                  title: 'My Orders',
                  imagePath: 'assets/icons/My Order.svg',
                  responsive: responsive,
                  onTap: () => context.push('/profile/my-order'),
                ),
                _ActionIcon(
                  title: 'Check-in',
                  imagePath: 'assets/icons/Check In.svg',
                  responsive: responsive,
                  onTap: () {
                    context.pushNamed('checkin');
                  },
                ),
                _ActionIcon(
                  title: 'Exchange',
                  imagePath: 'assets/icons/Exchange.svg',
                  responsive: responsive,
                  onTap: () => context.push('/profile/exchange'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _InfoCard(
                    title: 'Balance',
                    value: '300 Credits',
                    imagePath: 'assets/icons/Balance.svg',
                    responsive: responsive,
                    onTap: () => context.push('/profile/balance'),
                  ),
                ),
                Container(
                  width: 1,
                  height: 48,
                  color: Colors.grey.shade300,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                ),
                Expanded(
                  child: _InfoCard(
                    title: 'Friends Invited',
                    value: '42069',
                    imagePath: 'assets/icons/Invite Friends.svg',
                    responsive: responsive,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Extracted method for responsive feature grid
  Widget _buildFeatureGrid(BuildContext context, _ResponsiveHelper responsive) {
    return Padding(
      padding: responsive.horizontalPadding,
      child: ValueListenableBuilder<FaceVerificationStatus>(
        valueListenable: FaceVerificationStateManager.instance.statusNotifier,
        builder: (context, status, _) {
          return GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: responsive.gridColumns,
              childAspectRatio: responsive.gridChildAspectRatio,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: _FeatureData.features.length,
            itemBuilder: (context, index) {
              final bool hasRoute = _FeatureData.featureRoutes.containsKey(
                index,
              );
              final bool isFaceVerificationItem = index == 8;
              final bool isPending =
                  status == FaceVerificationStatus.pendingApproval;
              final bool isActive =
                  hasRoute && (!isFaceVerificationItem || !isPending);

              VoidCallback onTap;
              VoidCallback? onLongPress;
              if (isActive && hasRoute) {
                onTap = () => context.push(_FeatureData.featureRoutes[index]!);
              } else {
                onTap = () {};
              }

              // Demo shortcut: long-press Face Verification item while pending to mark verified
              if (isFaceVerificationItem && isPending) {
                onLongPress = () {
                  FaceVerificationStateManager.instance.setVerified();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Marked as Verified (demo)'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                };
              }

              return _FeatureItem(
                imagePath: _FeatureData.featureIcons[index],
                label: _FeatureData.features[index],
                responsive: responsive,
                onTap: onTap,
                onLongPress: onLongPress,
                isActive: isActive,
              );
            },
          );
        },
      ),
    );
  }

  // Helper widget for top icons - responsive sizing
  Widget _topIcon(
    String assetPath, {
    required double iconSize,
    VoidCallback? onTap,
  }) {
    Widget iconWidget = Padding(
      padding: const EdgeInsets.only(left: 8),
      child: SvgPicture.asset(
        assetPath,
        width: iconSize,
        height: iconSize,
        colorFilter: const ColorFilter.mode(Colors.black, BlendMode.srcIn),
      ),
    );

    if (onTap != null) {
      return GestureDetector(onTap: onTap, child: iconWidget);
    }

    return iconWidget;
  }
}

// Loading Profile Section Widget
class _LoadingProfileSection extends StatelessWidget {
  final _ResponsiveHelper responsive;

  const _LoadingProfileSection({required this.responsive});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Loading profile picture
        Container(
          width: responsive.profileIconSize,
          height: responsive.profileIconSize,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            shape: BoxShape.circle,
          ),
          child: const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
            ),
          ),
        ),
        const SizedBox(height: 8),
        // Loading username
        Container(
          width: 120,
          height: responsive.profileUsernameSize,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: 4),
        // Loading user ID
        Container(
          width: 100,
          height: 14,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: 12),
        // Loading stats
        Container(
          padding: responsive.profileStatsContainerPadding,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(12),
          ),
          child: SizedBox(
            width: responsive.profileStatsWidth,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _LoadingStatBox(responsive: responsive),
                SizedBox(width: responsive.profileStatsSpacing),
                _LoadingStatBox(responsive: responsive),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

// Loading Stat Box Widget
class _LoadingStatBox extends StatelessWidget {
  final _ResponsiveHelper responsive;

  const _LoadingStatBox({required this.responsive});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 60,
          height: responsive.profileStatsTitleSize,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          width: 40,
          height: responsive.profileStatsSubtitleSize,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ],
    );
  }
}

// Not Logged In Section Widget
class _NotLoggedInSection extends StatelessWidget {
  final _ResponsiveHelper responsive;

  const _NotLoggedInSection({required this.responsive});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Default profile picture
        Container(
          width: responsive.profileIconSize,
          height: responsive.profileIconSize,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            shape: BoxShape.circle,
          ),
          child: SvgPicture.asset(
            'assets/icons/profiles/EmptyProfile.svg',
            width: responsive.profileIconSize * 0.6,
            height: responsive.profileIconSize * 0.6,
            colorFilter: const ColorFilter.mode(Colors.grey, BlendMode.srcIn),
          ),
        ),
        const SizedBox(height: 8),
        // Not logged in message
        Text(
          'Not Logged In',
          style: TextStyle(
            fontSize: responsive.profileUsernameSize,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Please log in to view your profile',
          style: TextStyle(
            fontSize: responsive.isTablet ? 16 : 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade500,
          ),
        ),
        const SizedBox(height: 12),
        // Login button
        ElevatedButton(
          onPressed: () => context.go('/login'),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFFBF00),
            foregroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text('Login'),
        ),
      ],
    );
  }
}

// Error Profile Section Widget
class _ErrorProfileSection extends StatelessWidget {
  final String errorMessage;
  final _ResponsiveHelper responsive;
  final VoidCallback onRetry;

  const _ErrorProfileSection({
    required this.errorMessage,
    required this.responsive,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Error icon
        Container(
          width: responsive.profileIconSize,
          height: responsive.profileIconSize,
          decoration: BoxDecoration(
            color: Colors.red.shade100,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.error_outline,
            size: responsive.profileIconSize * 0.5,
            color: Colors.red.shade600,
          ),
        ),
        const SizedBox(height: 8),
        // Error message
        Text(
          'Error Loading Profile',
          style: TextStyle(
            fontSize: responsive.profileUsernameSize,
            fontWeight: FontWeight.bold,
            color: Colors.red.shade600,
          ),
        ),
        const SizedBox(height: 4),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            errorMessage,
            style: TextStyle(
              fontSize: responsive.isTablet ? 16 : 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 12),
        // Retry button
        ElevatedButton(
          onPressed: onRetry,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFFBF00),
            foregroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text('Retry'),
        ),
      ],
    );
  }
}

// Responsive helper class to cache calculations and improve performance
class _ResponsiveHelper {
  final MediaQueryData mediaQuery;

  _ResponsiveHelper(this.mediaQuery);

  late final double screenWidth = mediaQuery.size.width;
  late final bool isTablet = screenWidth > 600;
  late final bool isLargeTablet = screenWidth > 900;

  // Header
  late final double headerHeight = isTablet ? 380 : 346;

  // Padding
  late final EdgeInsets horizontalPadding = isTablet
      ? EdgeInsets.symmetric(horizontal: screenWidth * 0.1)
      : const EdgeInsets.symmetric(horizontal: 16.0);

  // Icons
  late final double topIconSize = isTablet ? 32.0 : 28.0;
  late final double actionIconSize = isTablet ? 40.0 : 32.0;
  late final double featureIconSize = isTablet ? 56.0 : 48.0;
  late final double infoIconSize = isTablet ? 30.0 : 25.0;

  // Text sizes
  late final double actionTextSize = isTablet ? 14.0 : 12.0;
  late final double featureTextSize = isTablet ? 12.0 : 11.0;
  late final double infoValueSize = isTablet ? 18.0 : 16.0;
  late final double infoTitleSize = isTablet ? 16.0 : 14.0;

  // Grid
  late final int gridColumns = isLargeTablet ? 6 : (isTablet ? 5 : 4);
  late final double gridChildAspectRatio = isTablet ? 0.9 : 0.8;

  // Profile
  late final double profileRadius = isTablet ? 50.0 : 40.0;
  late final double profileIconSize = isTablet ? 100.0 : 80.0;
  late final double profileEditIconSize = isTablet ? 20.0 : 16.0;
  late final int profileEditPadding = isTablet ? 6 : 4;
  late final double profileUsernameSize = isTablet ? 22.0 : 18.0;
  late final double profileStatsTitleSize = isTablet ? 24.0 : 20.0;
  late final double profileStatsSubtitleSize = isTablet ? 16.0 : 14.0;
  late final double profileStatsWidth = isTablet ? 280.0 : 220.0;
  late final double profileStatsSpacing = isTablet ? 32.0 : 24.0;
  late final EdgeInsets profileStatsContainerPadding = EdgeInsets.symmetric(
    horizontal: isTablet ? 24 : 16,
    vertical: isTablet ? 12 : 6,
  );

  // Banner
  late final double bannerHeight = isTablet ? 150.0 : 130.0;
  late final double bannerImageSize = isTablet ? 140.0 : 120.0;
  late final EdgeInsets bannerPadding = EdgeInsets.symmetric(
    horizontal: isTablet ? 24 : 15,
    vertical: isTablet ? 16 : 4,
  );
  late final double bannerTitleSize = isTablet ? 24.0 : 20.0;
  late final double bannerSubtitleSize = isTablet ? 16.0 : 14.0;
  late final double bannerTitleSpacing = isTablet ? 8.0 : 4.0;
  late final double bannerSubtitleSpacing = isTablet ? 16.0 : 8.0;
  late final double bannerButtonSize = isTablet ? 15.0 : 13.0;
  late final EdgeInsets bannerButtonPadding = EdgeInsets.symmetric(
    horizontal: isTablet ? 16 : 10,
    vertical: isTablet ? 8 : 5,
  );
  late final double bannerButtonIconSize = isTablet ? 24.0 : 20.0;
}

// Static data class for feature grid
class _FeatureData {
  static const List<String> features = [
    'Tutorial',
    'My Favourite',
    'Voucher',
    'Participation Record',
    'My Sharing',
    'My Review',
    'My Address',
    'Lucky Group Record',
    'Face Verification',
  ];

  static const List<String> featureIcons = [
    'assets/icons/Tutorial.svg',
    'assets/icons/My Favorite.svg',
    'assets/icons/Voucher.svg',
    'assets/icons/Participant Record.svg',
    'assets/icons/My Sharing.svg',
    'assets/icons/My Review.svg',
    'assets/icons/Address.svg',
    'assets/icons/Lucky Draw Group.svg',
    'assets/icons/Face Verification.svg',
  ];

  static const Map<int, String> featureRoutes = {
    0: '/profile/tutorial', // Tutorial
    1: '/shop/favourites', // My Favourite
    2: '/profile/voucher', // Voucher
    3: '/lucky-draw/draw-records', // Lucky Draw Record
    4: '/profile/my-sharing', // My Sharing
    5: '/profile/my-review', // My Review
    6: '/profile/my-address', // My Address
    7: '/profile/lucky-group-buy', // Lucky Group Record
    8: '/profile/face-verification', // Face Verification
  };
}

// Updated Gradient Icon Widget with responsive sizing
class _GradientIcon extends StatelessWidget {
  final String assetPath;
  final double width;
  final double height;
  final List<Color> gradientColors;
  final bool showStroke;
  final Color? strokeColor;
  final double strokeWidth;

  const _GradientIcon({
    required this.assetPath,
    required this.width,
    required this.height,
    required this.gradientColors,
    this.showStroke = false,
    this.strokeColor,
    this.strokeWidth = 1.5,
  });

  @override
  Widget build(BuildContext context) {
    if (showStroke && strokeColor != null) {
      return Stack(
        alignment: Alignment.center,
        children: [
          SvgPicture.asset(
            assetPath,
            width: width,
            height: height,
            colorFilter: ColorFilter.mode(strokeColor!, BlendMode.srcIn),
          ),
          ShaderMask(
            shaderCallback: (bounds) {
              return LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: gradientColors,
              ).createShader(bounds);
            },
            child: SvgPicture.asset(
              assetPath,
              width: width - (strokeWidth * 2),
              height: height - (strokeWidth * 2),
              colorFilter: const ColorFilter.mode(
                Colors.white,
                BlendMode.srcIn,
              ), // This will be masked by the gradient
            ),
          ),
        ],
      );
    }

    return ShaderMask(
      shaderCallback: (bounds) {
        return LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: gradientColors,
        ).createShader(bounds);
      },
      child: SvgPicture.asset(
        assetPath,
        width: width,
        height: height,
        colorFilter: const ColorFilter.mode(
          Colors.white,
          BlendMode.srcIn,
        ), // This will be masked by the gradient
      ),
    );
  }
}

// Updated UserProfileBanner Widget - Fixed overflow issues
class _UserProfileBanner extends StatelessWidget {
  final _ResponsiveHelper responsive;

  const _UserProfileBanner({required this.responsive});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          padding: responsive.bannerPadding,
          height: responsive.bannerHeight,
          width: constraints.maxWidth, // Use available width
          decoration: BoxDecoration(
            color: const Color(0xFFD90019),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Row(
            children: [
              Expanded(
                flex: responsive.isTablet ? 2 : 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          'Invite Friends,',
                          style: TextStyle(
                            fontSize: responsive.bannerTitleSize,
                            fontWeight: FontWeight.w900,
                            fontFamily: 'Solitreo',
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: responsive.bannerTitleSpacing),
                    Flexible(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          'There Are Rewards',
                          style: TextStyle(
                            fontSize: responsive.bannerSubtitleSize,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Solitreo',
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: responsive.bannerSubtitleSpacing),
                    Flexible(
                      child: _InviteNowButton(responsive: responsive),
                    ), // Make button flexible
                  ],
                ),
              ),
              const SizedBox(width: 15),
              SizedBox(
                width: responsive.bannerImageSize,
                height: responsive.bannerImageSize,
                child: SvgPicture.asset(
                  'assets/images/invite-friend-banner.svg',
                  fit: BoxFit.contain,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// Updated InviteNowButton Widget with responsive sizing
class _InviteNowButton extends StatelessWidget {
  final _ResponsiveHelper responsive;

  const _InviteNowButton({required this.responsive});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFFFBF00),
        borderRadius: BorderRadius.circular(15),
      ),
      padding: responsive.bannerButtonPadding,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () => context.push('/profile/invite-friends'),
            child: Text(
              'Invite Now!',
              style: TextStyle(
                fontSize: responsive.bannerButtonSize,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          const SizedBox(width: 8),
          SvgPicture.asset(
            'assets/icons/Click.svg',
            height: responsive.bannerButtonIconSize,
            width: responsive.bannerButtonIconSize,
          ),
        ],
      ),
    );
  }
}

// Profile Section Widget with responsive sizing
class _ProfileSection extends StatelessWidget {
  final String username;
  final String userId;
  final String points;
  final String cardCount;
  final String? profileImage;
  final _ResponsiveHelper responsive;

  const _ProfileSection({
    required this.username,
    required this.userId,
    required this.points,
    required this.cardCount,
    this.profileImage,
    required this.responsive,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Profile picture with edit button
        _buildProfilePicture(context),
        const SizedBox(height: 8),
        // Username with face verification badge
        ValueListenableBuilder<FaceVerificationStatus>(
          valueListenable: FaceVerificationStateManager.instance.statusNotifier,
          builder: (context, status, _) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  username,
                  style: TextStyle(
                    fontSize: responsive.profileUsernameSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 6),
                _FaceVerificationBadge(status: status),
              ],
            );
          },
        ),
        const SizedBox(height: 4),
        Text(
          'ID: $userId',
          style: TextStyle(
            fontSize: responsive.isTablet ? 16 : 14,
            fontWeight: FontWeight.w500,
            color: Colors.black.withValues(alpha: 0.7),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Container(
            padding: responsive.profileStatsContainerPadding,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: SizedBox(
              width: responsive.profileStatsWidth,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _StatBox(
                    title: points,
                    subtitle: 'Points',
                    responsive: responsive,
                  ),
                  SizedBox(width: responsive.profileStatsSpacing),
                  _StatBox(
                    title: cardCount,
                    subtitle: 'Card Count',
                    responsive: responsive,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfilePicture(BuildContext context) {
    Widget profileWidget;

    if (profileImage != null &&
        profileImage!.isNotEmpty &&
        profileImage!.startsWith('http')) {
      // Show user's profile image if available and valid URL
      profileWidget = ClipOval(
        child: Image.network(
          profileImage!,
          width: responsive.profileIconSize,
          height: responsive.profileIconSize,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              width: responsive.profileIconSize,
              height: responsive.profileIconSize,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                            loadingProgress.expectedTotalBytes!
                      : null,
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.grey),
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            // Fallback to default profile picture if network image fails
            return SvgPicture.asset(
              'assets/icons/profiles/EmptyProfile.svg',
              width: responsive.profileIconSize,
              height: responsive.profileIconSize,
              colorFilter: const ColorFilter.mode(
                Colors.black,
                BlendMode.srcIn,
              ),
            );
          },
        ),
      );
    } else {
      // Show default profile picture if no image is available
      profileWidget = SvgPicture.asset(
        'assets/icons/profiles/EmptyProfile.svg',
        width: responsive.profileIconSize,
        height: responsive.profileIconSize,
        colorFilter: const ColorFilter.mode(Colors.black, BlendMode.srcIn),
      );
    }

    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        profileWidget,
        Positioned(
          bottom: 0,
          right: 0,
          child: GestureDetector(
            onTap: () => context.push('/profile/view-profile'),
            child: Container(
              padding: EdgeInsets.all(responsive.profileEditPadding.toDouble()),
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
              ),
              child: Icon(
                Icons.create,
                size: responsive.profileEditIconSize,
                color: Colors.black,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _StatBox extends StatelessWidget {
  final String title, subtitle;
  final _ResponsiveHelper responsive;

  const _StatBox({
    required this.title,
    required this.subtitle,
    required this.responsive,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: responsive.profileStatsTitleSize,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        Text(
          subtitle,
          style: TextStyle(
            fontSize: responsive.profileStatsSubtitleSize,
            color: Colors.black,
          ),
        ),
      ],
    );
  }
}

class _ActionIcon extends StatelessWidget {
  final String title;
  final String imagePath;
  final _ResponsiveHelper responsive;
  final VoidCallback? onTap;

  const _ActionIcon({
    required this.title,
    required this.imagePath,
    required this.responsive,
    this.onTap,
  });

  // Changed to solid color instead of gradient
  static const List<Color> gradientColors = [
    Color(0xFFFFBF00),
    Color(0xFFFFBF00),
  ];

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Column(
        children: [
          _GradientIcon(
            assetPath: imagePath,
            width: responsive.actionIconSize,
            height: responsive.actionIconSize,
            gradientColors: gradientColors,
            showStroke: true,
            strokeColor: const Color(0xFFFFBF00),
            strokeWidth: 1.5,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: responsive.actionTextSize),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _InfoCard extends StatelessWidget {
  final String title, value, imagePath;
  final _ResponsiveHelper responsive;
  final VoidCallback? onTap;

  const _InfoCard({
    required this.title,
    required this.value,
    required this.imagePath,
    required this.responsive,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    Widget content = Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: responsive.infoTitleSize,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                imagePath,
                width: responsive.infoIconSize,
                height: responsive.infoIconSize,
                colorFilter: const ColorFilter.mode(
                  Color(0xFFD90019),
                  BlendMode.srcIn,
                ), // Changed to solid red color
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: responsive.infoValueSize,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );

    // Wrap with GestureDetector if onTap is provided
    if (onTap != null) {
      return GestureDetector(onTap: onTap, child: content);
    }

    return content;
  }
}

// Updated Feature Item Widget with solid color
class _FeatureItem extends StatelessWidget {
  final String imagePath;
  final String label;
  final _ResponsiveHelper responsive;
  final VoidCallback onTap;
  final VoidCallback? onLongPress;
  final bool isActive;

  const _FeatureItem({
    required this.imagePath,
    required this.label,
    required this.responsive,
    required this.onTap,
    this.onLongPress,
    this.isActive = true,
  });

  static const List<Color> gradientColors = [
    Color(0xFFFFBF00),
    Color(0xFFFFBF00),
  ];

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Opacity(
        opacity: isActive ? 1.0 : 0.6,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _GradientIcon(
              assetPath: imagePath,
              width: responsive.featureIconSize,
              height: responsive.featureIconSize,
              gradientColors: gradientColors,
              showStroke: true,
              strokeColor: isActive ? const Color(0xFFFFBF00) : Colors.grey,
              strokeWidth: 1.5,
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Text(
                label,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: responsive.featureTextSize,
                  color: isActive ? Colors.black : Colors.grey,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FaceVerificationBadge extends StatefulWidget {
  final FaceVerificationStatus status;

  const _FaceVerificationBadge({required this.status});

  @override
  State<_FaceVerificationBadge> createState() => _FaceVerificationBadgeState();
}

class _FaceVerificationBadgeState extends State<_FaceVerificationBadge> {
  bool _showLabel = false;

  @override
  Widget build(BuildContext context) {
    Icon icon;
    Text label;
    switch (widget.status) {
      case FaceVerificationStatus.notVerified:
        icon = const Icon(Icons.verified_user, color: Colors.grey, size: 18);
        label = const Text(
          'Not verified',
          style: TextStyle(color: Colors.grey, fontSize: 12),
        );
        break;
      case FaceVerificationStatus.pendingApproval:
        icon = const Icon(
          Icons.hourglass_empty,
          color: Color.fromARGB(255, 81, 7, 255),
          size: 18,
        );
        label = const Text(
          'Pending',
          style: TextStyle(
            color: Color.fromARGB(255, 81, 7, 255),
            fontSize: 12,
          ),
        );
        break;
      case FaceVerificationStatus.verified:
        icon = const Icon(
          Icons.verified,
          color: Color.fromARGB(255, 7, 224, 72),
          size: 18,
        );
        label = const Text(
          'Verified',
          style: TextStyle(
            color: Color.fromARGB(255, 7, 224, 72),
            fontSize: 12,
          ),
        );
        break;
    }

    return GestureDetector(
      onTap: () {
        setState(() {
          _showLabel = !_showLabel;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.white.withOpacity(0.95), width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            icon,
            if (_showLabel) const SizedBox(width: 3),
            if (_showLabel) label,
          ],
        ),
      ),
    );
  }
}
