import 'package:flutter/material.dart';
import '../../../data/model/ShopCartModel.dart';

class CartItemWidget extends StatelessWidget {
  final ShopCartItem item;
  final VoidCallback? onIncrease;
  final VoidCallback? onDecrease;
  final ValueChanged<bool?>? onSelected;

  const CartItemWidget({
    super.key,
    required this.item,
    this.onIncrease,
    this.onDecrease,
    this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    const double imageSize = 60;
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE0E0E0)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Checkbox vertically centered with image
          SizedBox(
            height: imageSize,
            child: Center(
              child: Transform.scale(
                scale: 0.8,
                child: Checkbox(
                  value: item.selected,
                  onChanged: onSelected,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(3)),
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // Product Image
          ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Image.network(
              item.image,
              width: imageSize,
              height: imageSize,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(width: 12),
          // Product Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product Title
                Text(
                  item.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    height: 1.3,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                // Variation
                Text(
                  "Variation: ${item.variation}",
                  style: const TextStyle(
                    color: Color(0xFF666666),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 8),
                // Price and Quantity Controls Row
                Row(
                  children: [
                    Text(
                      "RM${item.price.toStringAsFixed(2)}",
                      style: const TextStyle(
                        color: Color(0xFFE53E3E),
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const Spacer(),
                    // Quantity Controls
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Minus button
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            border: Border.all(color: const Color(0xFFD0D0D0)),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: InkWell(
                            onTap: item.quantity > 1 ? onDecrease : null,
                            child: const Icon(
                              Icons.remove,
                              size: 14,
                              color: Colors.black54,
                            ),
                          ),
                        ),
                        // Quantity display
                        Container(
                          width: 32,
                          height: 24,
                          alignment: Alignment.center,
                          margin: const EdgeInsets.symmetric(horizontal: 2),
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFF3CD),
                            border: Border.all(color: const Color(0xFFFFE082)),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            "${item.quantity}",
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        // Plus button
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            border: Border.all(color: const Color(0xFFD0D0D0)),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: InkWell(
                            onTap: onIncrease,
                            child: const Icon(
                              Icons.add,
                              size: 14,
                              color: Colors.black54,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}