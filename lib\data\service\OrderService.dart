import '../model/OrderModel.dart';

class OrderService {
  // Mock data for different order statuses
  static List<OrderModel> getMockOrders() {
    return [
      // To Pay Orders
      OrderModel(
        id: 'LM001',
        type: OrderType.physicalCard,
        status: OrderStatus.toPay,
        deliveryMethod: DeliveryMethod.delivery,
        productName: 'Laughing Elf',
        productImage:
            'https://oss.luckymymall.com/3238aef0019fb92e208adb997431f2ac2337a7baab70f472d0a49a314b29fde9.jpg',
        subtotal: 0.00,
        total: 15.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(minutes: 7)),
        paymentDueDate: DateTime.now().add(const Duration(minutes: 23)),
      ),
      OrderModel(
        id: 'LM002',
        type: OrderType.storeOrder,
        status: OrderStatus.toPay,
        deliveryMethod: DeliveryMethod.delivery,
        productName: 'ZenyumSonic Go Black',
        productImage:
            'https://down-my.img.susercontent.com/file/sg-11134201-7rd4j-lvmr60yl4xybd5',
        subtotal: 149.00,
        total: 169.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
        paymentDueDate: DateTime.now().add(const Duration(minutes: 25)),
      ),

      // To Ship Orders
      OrderModel(
        id: 'LM003',
        type: OrderType.luckyGroupBuy,
        status: OrderStatus.toShip,
        deliveryMethod: DeliveryMethod.delivery,
        productName:
            'Russell Taylor\'s 3D Visible Window Air Fryer White Large',
        productImage:
            'https://preview.redd.it/skintific-water-gel-ceramide-toner-first-impression-v0-r8u75rmvvs3c1.jpeg',
        subtotal: 301.98,
        total: 321.98,
        quantity: 2,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      OrderModel(
        id: 'LM004',
        type: OrderType.storeOrder,
        status: OrderStatus.toShip,
        deliveryMethod: DeliveryMethod.delivery,
        productName:
            'Russell Taylor\'s 3D Visible Window Air Fryer White Large, White Medium',
        productImage: 'assets/images/card/imageSlider/example3.png',
        subtotal: 301.98,
        total: 355.98,
        quantity: 2,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),

      // To Receive Orders
      OrderModel(
        id: 'LM005',
        type: OrderType.prize,
        status: OrderStatus.toReceive,
        deliveryMethod: DeliveryMethod.pickup,
        productName:
            'Mouse Razer Pulsar X120 With Ergonomic Functional (PAW3360 Sensor) White',
        productImage: 'assets/images/card/imageSlider/example1.jpg',
        subtotal: 0.00,
        total: 0.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        trackingNumber: 'TRK123456789',
      ),
      OrderModel(
        id: 'LM006',
        type: OrderType.storeOrder,
        status: OrderStatus.toReceive,
        deliveryMethod: DeliveryMethod.delivery,
        productName: 'ZenyumSonic Go Black',
        productImage: 'assets/images/card/imageSlider/example2.jpeg',
        subtotal: 149.00,
        total: 153.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        trackingNumber: 'TRK987654321',
      ),
      OrderModel(
        id: 'LM007',
        type: OrderType.storeOrder,
        status: OrderStatus.toReceive,
        deliveryMethod: DeliveryMethod.delivery,
        productName:
            'Russell Taylor\'s 3D Visible Window Air Fryer White Large',
        productImage: 'assets/images/card/imageSlider/example3.png',
        subtotal: 301.98,
        total: 200.88,
        quantity: 3,
        createdAt: DateTime.now().subtract(const Duration(days: 4)),
        trackingNumber: 'TRK111222333',
      ),

      // Completed Orders
      OrderModel(
        id: 'LM008',
        type: OrderType.blindBox,
        status: OrderStatus.completed,
        purchaseType: PurchaseType.appPurchase,
        deliveryMethod: DeliveryMethod.delivery,
        productName: 'My Lucky Mall Exclusive Snacks Mystery Box Default',
        productImage:
            'https://oss.luckymymall.com/3238aef0019fb92e208adb997431f2ac2337a7baab70f472d0a49a314b29fde9.jpg',
        subtotal: 30.00,
        total: 45.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
      OrderModel(
        id: 'LM009',
        type: OrderType.prize,
        status: OrderStatus.completed,
        deliveryMethod: DeliveryMethod.pickup,
        productName:
            'Mouse Razer Pulsar X120 With Ergonomic Functional (PAW3360 Sensor) White',
        productImage: 'assets/images/card/imageSlider/example1.jpg',
        subtotal: 0.00,
        total: 0.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 6)),
      ),
      OrderModel(
        id: 'LM010',
        type: OrderType.storeOrder,
        status: OrderStatus.completed,
        deliveryMethod: DeliveryMethod.delivery,
        productName:
            'Russell Taylor\'s 3D Visible Window Air Fryer White Large',
        productImage: 'assets/images/card/imageSlider/example3.png',
        subtotal: 301.98,
        total: 355.98,
        quantity: 2,
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
      ),

      // Cancelled Orders
      OrderModel(
        id: 'LM011',
        type: OrderType.storeOrder,
        status: OrderStatus.cancelled,
        deliveryMethod: DeliveryMethod.pickup,
        productName:
            'Diamond Frying Pan 30cm Non-stick pan Kitchen Frying for Gas Stove induction Cooker 30cm',
        productImage:
            'https://down-my.img.susercontent.com/file/sg-11134201-7rd4j-lvmr60yl4xybd5',
        subtotal: 54.00,
        total: 54.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 8)),
      ),

      // Refund Orders with different statuses
      OrderModel(
        id: 'LM012',
        type: OrderType.storeOrder,
        status: OrderStatus.returnRefund,
        refundStatus: RefundStatus.refundRequested,
        deliveryMethod: DeliveryMethod.delivery,
        productName:
            'Mouse Razer Pulsar X120 With Ergonomic Functional (PAW3360 Sensor) White',
        productImage:
            'https://preview.redd.it/skintific-water-gel-ceramide-toner-first-impression-v0-r8u75rmvvs3c1.jpeg',
        subtotal: 150.00,
        total: 150.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        refundRequestId: 'ndjops82djis',
        refundReason: 'Item is defective or broken',
        refundEvidence: ['https://completed_order_details.jpg'],
      ),
      // Completed prize order for testing (Pickup)
      OrderModel(
        id: 'MM12100267391',
        type: OrderType.prize,
        status: OrderStatus.completed,
        deliveryMethod: DeliveryMethod.pickup,
        productName:
            'Mouse Razer Pulsar X120 With Ergonomic Functional (PAW3360 Sensor)',
        productImage:
            'https://oss.luckymymall.com/3238aef0019fb92e208adb997431f2ac2337a7baab70f472d0a49a314b29fde9.jpg',
        subtotal: 0.00,
        total: 0.00,
        quantity: 1,
        createdAt: DateTime(2025, 2, 20, 12, 0),
      ),

      // Completed blindbox order for testing (Delivery)
      OrderModel(
        id: 'MM12100267393',
        type: OrderType.blindBox,
        status: OrderStatus.completed,
        deliveryMethod: DeliveryMethod.delivery,
        productName: 'My Lucky Mall Exclusive Snacks Mystery Box',
        productImage:
            'https://oss.luckymymall.com/3238aef0019fb92e208adb997431f2ac2337a7baab70f472d0a49a314b29fde9.jpg',
        subtotal: 30.00,
        total: 45.00,
        quantity: 1,
        createdAt: DateTime(2025, 2, 20, 12, 0),
        trackingNumber: 'IM29202920292',
      ),

      // Cancelled shop order for testing
      OrderModel(
        id: 'MM12100267392',
        type: OrderType.storeOrder,
        status: OrderStatus.cancelled,
        deliveryMethod: DeliveryMethod.delivery,
        productName: 'Russell Taylor\'s 3D Visible Window Air Fryer',
        productImage: 'https://oss.luckymymall.com/air-fryer-example.jpg',
        subtotal: 242.98,
        total: 200.88,
        quantity: 2,
        createdAt: DateTime(2025, 2, 20, 12, 0),
      ),

      OrderModel(
        id: 'LM013',
        type: OrderType.blindBox,
        status: OrderStatus.returnRefund,
        refundStatus: RefundStatus.refundApproved,
        purchaseType: PurchaseType.appPurchase,
        deliveryMethod: DeliveryMethod.delivery,
        productName: 'Premium Gaming Headset Mystery Box',
        productImage:
            'https://img4.dhresource.com/webp/m/0x0/f3/albu/km/y/15/7816b33c-fca1-42fe-a411-c4c1b46b50a9.jpg',
        subtotal: 45.00,
        total: 50.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        refundRequestId: 'ref789xyz',
        refundReason: 'Wrong colour',
        trackingNumber: 'KDDH121002067391',
      ),

      OrderModel(
        id: 'LM014',
        type: OrderType.storeOrder,
        status: OrderStatus.returnRefund,
        refundStatus: RefundStatus.returnInProgress,
        deliveryMethod: DeliveryMethod.delivery,
        productName: 'ZenyumSonic Go Black Electric Toothbrush',
        productImage:
            'https://cdn.shopify.com/s/files/1/0537/1177/files/NaPacks_Sling_Bag_Gear_Review_Ultralight_Fanny_Pack_Made_of_Challenge_Ultra_Backpacking_Thru-Hiking_GGG_Garage_Grown_Gear.jpg',
        subtotal: 149.00,
        total: 169.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        trackingNumber: 'KDDH121002067391',
        refundRequestId: 'ref456abc',
      ),

      OrderModel(
        id: 'LM015',
        type: OrderType.blindBox,
        status: OrderStatus.returnRefund,
        refundStatus: RefundStatus.refundCompleted,
        purchaseType: PurchaseType.qrRedemption,
        deliveryMethod: DeliveryMethod.delivery,
        productName: 'QR Exclusive Mystery Box',
        productImage:
            'https://i.ebayimg.com/images/g/upIAAOSw3sJak0DU/s-l1200.jpg',
        subtotal: 0.00,
        total: 30.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        refundRequestId: 'ref123qr',
        metadata: {'qrCode': 'QR123456789', 'pointsRefunded': 30},
      ),

      OrderModel(
        id: 'LM016',
        type: OrderType.storeOrder,
        status: OrderStatus.returnRefund,
        refundStatus: RefundStatus.refundRejected,
        deliveryMethod: DeliveryMethod.delivery,
        productName: 'Wireless Bluetooth Earbuds',
        productImage:
            'https://xcdn.next.co.uk/common/items/default/default/itemimages/3_4Ratio/product/lge/732297s.jpg',
        subtotal: 89.00,
        total: 99.00,
        quantity: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 12)),
        refundRequestId: 'ref789rej',
        refundReason: 'Rejected reason...',
      ),
    ];
  }

  static List<OrderModel> getOrdersByStatus(OrderStatus status) {
    return getMockOrders().where((order) => order.status == status).toList();
  }

  static OrderModel? getOrderById(String id) {
    try {
      return getMockOrders().firstWhere((order) => order.id == id);
    } catch (e) {
      return null;
    }
  }

  // API-ready methods (currently using mock data)
  static Future<List<OrderModel>> fetchOrders() async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 800));
    return getMockOrders();
  }

  static Future<List<OrderModel>> fetchOrdersByStatus(
    OrderStatus status,
  ) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return getOrdersByStatus(status);
  }

  static Future<OrderModel?> fetchOrderById(String id) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return getOrderById(id);
  }

  static Future<bool> cancelOrder(String orderId) async {
    await Future.delayed(const Duration(milliseconds: 1000));
    // Simulate API call success
    return true;
  }

  static Future<bool> requestRefund(String orderId, String reason) async {
    await Future.delayed(const Duration(milliseconds: 1500));
    // Simulate API call success
    return true;
  }

  // New methods for enhanced refund functionality
  static Future<bool> updateRefundStatus(
    String orderId,
    RefundStatus newStatus,
  ) async {
    await Future.delayed(const Duration(milliseconds: 800));
    // Simulate API call success
    return true;
  }

  static Future<List<OrderModel>> fetchRefundOrders() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return getMockOrders()
        .where((order) => order.status == OrderStatus.returnRefund)
        .toList();
  }

  static Future<List<OrderModel>> fetchOrdersByRefundStatus(
    RefundStatus refundStatus,
  ) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return getMockOrders()
        .where(
          (order) =>
              order.status == OrderStatus.returnRefund &&
              order.refundStatus == refundStatus,
        )
        .toList();
  }

  static Future<bool> submitRefundEvidence(
    String orderId,
    List<String> evidenceUrls,
  ) async {
    await Future.delayed(const Duration(milliseconds: 1200));
    // Simulate API call success
    return true;
  }

  static Future<bool> approveRefund(String orderId) async {
    await Future.delayed(const Duration(milliseconds: 1000));
    // Simulate API call success
    return true;
  }

  static Future<bool> rejectRefund(String orderId, String reason) async {
    await Future.delayed(const Duration(milliseconds: 1000));
    // Simulate API call success
    return true;
  }
}
