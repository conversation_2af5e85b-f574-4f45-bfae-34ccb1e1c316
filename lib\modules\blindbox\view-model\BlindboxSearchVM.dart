import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../data/model/BlindboxProductModel.dart';
import '../providers/blindbox_providers.dart';

enum SearchFilter { relevance, latest, mostSold, price }

class BlindboxSearchViewVM extends ConsumerStatefulWidget {
  final Widget child;
  final String? initialSearchQuery;
  final bool triggerOnInit;

  const BlindboxSearchViewVM({
    super.key,
    required this.child,
    this.initialSearchQuery,
    this.triggerOnInit = false,
  });

  @override
  ConsumerState<BlindboxSearchViewVM> createState() =>
      BlindboxSearchViewVMState();
}

class BlindboxSearchViewVMState extends ConsumerState<BlindboxSearchViewVM> {
  // Search controller
  late TextEditingController searchController;
  late ScrollController scrollController;
  SearchFilter selectedFilter = SearchFilter.relevance;

  // Pagination variables
  bool isLoadingMore = false;
  bool hasMoreData = true;
  int currentPage = 1;
  int pageSize = 20;
  List<BlindboxProductModel> allSearchResults = [];
  List<BlindboxProductModel> displayedSearchResults = [];
  String currentSearchQuery = '';

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController(
      text: widget.initialSearchQuery ?? '',
    );
    scrollController = ScrollController();
    scrollController.addListener(_onScroll);

    // Prefill search query
    final initialText = widget.initialSearchQuery?.trim() ?? '';
    if (initialText.isNotEmpty) {
      currentSearchQuery = initialText;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(blindboxSearchQueryProvider.notifier).state = initialText;
        if (widget.triggerOnInit) {
          performSearch(initialText);
        }
      });
    }
  }

  @override
  void dispose() {
    searchController.dispose();
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    super.dispose();
  }

  void navigateToSearch(BuildContext context) {
    context.push('/blind-box/search');
  }

  // Scroll listener for pagination
  void _onScroll() {
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200) {
      _loadMoreData();
    }
  }

  // Load more data when scrolling near the bottom
  Future<void> _loadMoreData() async {
    if (isLoadingMore || !hasMoreData) return;

    setState(() {
      isLoadingMore = true;
    });

    try {
      // Check if we have more data to load
      if (displayedSearchResults.length < allSearchResults.length) {
        final nextBatch = allSearchResults
            .take(displayedSearchResults.length + pageSize)
            .toList();

        setState(() {
          displayedSearchResults = nextBatch;
          isLoadingMore = false;
        });
      } else {
        // Try to fetch more from API
        final nextPage = currentPage + 1;
        final searchParams = {
          'query': currentSearchQuery,
          'limit': pageSize,
          'page': nextPage,
          'sortBy': _currentSortParams().key,
          'ascending': _currentSortParams().value,
        };

        final results = await ref.read(
          blindboxSearchProductsProvider(searchParams).future,
        );
        final newProducts = results['products'] as List<BlindboxProductModel>;

        if (newProducts.isNotEmpty) {
          setState(() {
            allSearchResults.addAll(newProducts);
            displayedSearchResults = allSearchResults
                .take(displayedSearchResults.length + pageSize)
                .toList();
            currentPage = nextPage;
            isLoadingMore = false;
          });
        } else {
          setState(() {
            hasMoreData = false;
            isLoadingMore = false;
          });
        }
      }
    } catch (e) {
      setState(() {
        isLoadingMore = false;
      });
    }
  }

  // Refresh data and reset pagination
  Future<void> refreshData() async {
    setState(() {
      hasMoreData = true;
      currentPage = 1;
      allSearchResults.clear();
      displayedSearchResults.clear();
    });

    // Invalidate search-related providers
    ref.invalidate(blindboxSearchResultsProvider);
    ref.invalidate(blindboxLoadingProvider);

    // Re-perform the search with current query
    if (currentSearchQuery.isNotEmpty) {
      await performSearch(currentSearchQuery);
    }

    // Wait a bit for the provider to refresh
    await Future.delayed(const Duration(milliseconds: 100));
  }

  void updateSearchQuery(String query) {
    setState(() {
      currentSearchQuery = query;
      searchController.text = query;
    });
    ref.read(blindboxSearchQueryProvider.notifier).state = query;
  }

  void clearSearch() {
    setState(() {
      currentSearchQuery = '';
      searchController.clear();
      allSearchResults.clear();
      displayedSearchResults.clear();
      hasMoreData = true;
      currentPage = 1;
    });
    ref.read(blindboxSearchQueryProvider.notifier).state = '';
    ref.read(blindboxSearchResultsProvider.notifier).state = [];
  }

  void selectFilter(SearchFilter filter) {
    setState(() {
      selectedFilter = filter;
      // Reset pagination when filter changes
      hasMoreData = true;
      currentPage = 1;
      allSearchResults.clear();
      displayedSearchResults.clear();
    });

    // Invalidate search providers
    ref.invalidate(blindboxSearchResultsProvider);
    ref.invalidate(blindboxLoadingProvider);

    // Re-perform search with new filter
    if (currentSearchQuery.isNotEmpty) {
      performSearch(currentSearchQuery);
    }
  }

  Future<void> performSearch(String query) async {
    if (query.trim().isEmpty) return;

    ref.read(blindboxLoadingProvider.notifier).state = true;

    try {
      // Use the search provider with parameters
      final sort = _currentSortParams();
      final searchParams = {
        'query': query,
        'limit': pageSize,
        'page': 1,
        'sortBy': sort.key,
        'ascending': sort.value,
      };

      final results = await ref.read(
        blindboxSearchProductsProvider(searchParams).future,
      );
      final products = results['products'] as List<BlindboxProductModel>;

      setState(() {
        allSearchResults = List.from(products);
        displayedSearchResults = allSearchResults.take(pageSize).toList();
        currentPage = 1;
        hasMoreData = products.length >= pageSize;
      });

      // Update the search results provider
      ref.read(blindboxSearchResultsProvider.notifier).state = products;

      // Also update the loading state
      ref.read(blindboxLoadingProvider.notifier).state = false;
    } catch (e) {
      // Handle error
      ref.read(blindboxSearchResultsProvider.notifier).state = [];
      ref.read(blindboxLoadingProvider.notifier).state = false;
    }
  }

  // Map selected filter to API sort column and order
  MapEntry<String, bool> _currentSortParams() {
    switch (selectedFilter) {
      case SearchFilter.latest:
        return const MapEntry('created_at', false); // newest first
      case SearchFilter.mostSold:
        return const MapEntry('sold', false); // high to low
      case SearchFilter.price:
        return const MapEntry('salePrice', true); // low to high
      case SearchFilter.relevance:
        return const MapEntry('relevance', false);
    }
  }

  List<BlindboxProductModel> filterProducts(
    List<BlindboxProductModel> products,
    Map<String, dynamic> filters,
  ) {
    List<BlindboxProductModel> filtered = List.from(products);

    // Filter by price range
    if (filters['minPrice'] != null || filters['maxPrice'] != null) {
      filtered = filtered.where((product) {
        double price = product.salePrice;
        bool minCheck =
            filters['minPrice'] == null || price >= filters['minPrice'];
        bool maxCheck =
            filters['maxPrice'] == null || price <= filters['maxPrice'];
        return minCheck && maxCheck;
      }).toList();
    }

    // Filter by category
    if (filters['category'] != null && filters['category'] != 'All') {
      // Note: You might want to add a category field to your BlindboxProductModel
      // For now, we'll skip category filtering
    }

    // Sort products
    if (filters['sortBy'] != null) {
      switch (filters['sortBy']) {
        case 'Price: Low to High':
          filtered.sort((a, b) => a.salePrice.compareTo(b.salePrice));
          break;
        case 'Price: High to Low':
          filtered.sort((a, b) => b.salePrice.compareTo(a.salePrice));
          break;
        case 'Newest':
          filtered.sort((a, b) => a.sold.compareTo(b.sold));
          break;
        case 'Popularity':
        default:
          filtered.sort((a, b) => b.sold.compareTo(a.sold));
          break;
      }
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final searchQuery = ref.watch(blindboxSearchQueryProvider);
    final searchResults = ref.watch(blindboxSearchResultsProvider);
    final isLoading = ref.watch(blindboxLoadingProvider);

    return widget.child;
  }

  // Getter methods for the view to access pagination state
  List<BlindboxProductModel> get products => displayedSearchResults;
  bool get isInitialLoading => ref.watch(blindboxLoadingProvider);
  bool get isLoadMoreLoading => isLoadingMore;
  bool get canLoadMore => hasMoreData;
  ScrollController get scrollControllerInstance => scrollController;
  String get searchQuery => currentSearchQuery;
  SearchFilter get currentFilter => selectedFilter;

  String getFilterDisplayName(SearchFilter filter) {
    switch (filter) {
      case SearchFilter.relevance:
        return 'Relevance';
      case SearchFilter.latest:
        return 'Latest';
      case SearchFilter.mostSold:
        return 'Most Sold';
      case SearchFilter.price:
        return 'Price';
    }
  }
}
