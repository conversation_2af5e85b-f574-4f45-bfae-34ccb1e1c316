import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class QRCodeSection extends StatelessWidget {
  final String qrCodeUrl;
  final String inviteLink;

  const QRCodeSection({
    super.key,
    required this.qrCodeUrl,
    required this.inviteLink,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  void _copyToClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: inviteLink));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Invite link copied to clipboard!'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Product image display (4 QR-like squares)
        Container(
          padding: EdgeInsets.symmetric(
            vertical: getResponsivePadding(16, context),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // First row of squares
              Column(
                children: [
                  Row(
                    children: [
                      _buildQRSquare(context, true),
                      SizedBox(width: getResponsivePadding(8, context)),
                      _buildQRSquare(context, true),
                    ],
                  ),
                  SizedBox(height: getResponsivePadding(8, context)),
                  Row(
                    children: [
                      _buildQRSquare(context, true),
                      SizedBox(width: getResponsivePadding(8, context)),
                      _buildQRSquare(context, false), // Checkered pattern
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),

        // Invite link section
        Container(
          margin: EdgeInsets.symmetric(
            horizontal: getResponsivePadding(24, context),
            vertical: getResponsivePadding(16, context),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: getResponsivePadding(12, context),
            vertical: getResponsivePadding(8, context),
          ),
          decoration: BoxDecoration(
            color: const Color(0xFFFFF9C4), // Light yellow background
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  inviteLink,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: getResponsivePadding(8, context)),
              GestureDetector(
                onTap: () => _copyToClipboard(context),
                child: Container(
                  padding: EdgeInsets.all(getResponsivePadding(4, context)),
                  decoration: const BoxDecoration(
                    color: Color(0xFFFCD255),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.content_copy,
                    size: getResponsiveFontSize(16, context),
                    color: Colors.black,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQRSquare(BuildContext context, bool isSolid) {
    final size = getResponsiveFontSize(60, context);

    if (isSolid) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Container(
          margin: EdgeInsets.all(getResponsivePadding(12, context)),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      );
    } else {
      // Checkered pattern
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(4),
        ),
        child: GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            childAspectRatio: 1,
          ),
          itemCount: 16,
          itemBuilder: (context, index) {
            final row = index ~/ 4;
            final col = index % 4;
            final isBlack = (row + col) % 2 == 0;
            return Container(color: isBlack ? Colors.black : Colors.white);
          },
        ),
      );
    }
  }
}
