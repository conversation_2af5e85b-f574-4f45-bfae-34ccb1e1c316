import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
//import 'package:go_router/go_router.dart';
// import 'package:luckymall/modules/shop/widget/ShopCard.dart';
// import 'package:luckymall/data/model/ShoplistModel.dart';
// import 'package:luckymall/modules/shop/widget/ShopCardSkeleton.dart';

class GroupPurchaseView extends StatefulWidget {
  const GroupPurchaseView({super.key});

  @override
  State<GroupPurchaseView> createState() => _GroupPurchaseViewState();
}

class _GroupPurchaseViewState extends State<GroupPurchaseView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70.0),
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFFFCD255), Color(0xFFFDE18E), Color(0xFFFFFFFF)],
            ),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            centerTitle: true,
            elevation: 0,
            leading: IconButton(
              icon: SvgPicture.asset(
                'assets/icons/back.svg',
                colorFilter: const ColorFilter.mode(
                  Colors.black,
                  BlendMode.srcIn,
                ),
              ),
              onPressed: () => Navigator.pop(context),
            ),
            title: const Text(
              'Group Buy',
              style: TextStyle(
                fontSize: 18,
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Color.fromARGB(255, 255, 255, 255),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Coming Soon Icon/Image
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(60),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.construction,
                  size: 60,
                  color: Color(0xFFFFBF00),
                ),
              ),
              const SizedBox(height: 40),

              // Coming Soon Text
              const Text(
                'Coming Soon!',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),

              // Description
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 40),
                child: Text(
                  'We\'re working hard to bring you amazing group purchase deals. Stay tuned for exciting offers and discounts!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black54,
                    height: 1.5,
                  ),
                ),
              ),
              const SizedBox(height: 40),

              // Go Back Button
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFBF00),
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFFFFBF00).withOpacity(0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.arrow_back, color: Colors.white, size: 20),
                      SizedBox(width: 8),
                      Text(
                        'Go Back',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/*
// ORIGINAL CODE - COMMENTED OUT FOR FUTURE USE
// =============================================

class GroupPurchaseView extends StatefulWidget {
  const GroupPurchaseView({super.key});

  @override
  State<GroupPurchaseView> createState() => _GroupPurchaseViewState();
}

class _GroupPurchaseViewState extends State<GroupPurchaseView> {
  // Sample group purchase products (Shop)
  final List<ShopProduct> _groupPurchaseProducts = [
    const ShopProduct(
      id: 'gp1',
      title: 'Wireless Bluetooth Headphones - Group Deal',
      imageUrl:
          'https://www.toysplayer.com.my/data/prod/1718774491_banc661852_1.jpg',
      currentPrice: 89.99,
      originalPrice: 149.99,
      discountPercentage: 40,
      rating: 4.8,
      soldCount: 1250,
      maxSavings: 60.00,
      isFavorite: false,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp2',
      title: 'Smart Watch Fitness Tracker - Group Purchase',
      imageUrl:
          'https://www.toysplayer.com.my/data/prod/gallery/1730439930_1.jpg',
      currentPrice: 199.99,
      originalPrice: 299.99,
      discountPercentage: 33,
      rating: 4.5,
      soldCount: 890,
      maxSavings: 100.00,
      isFavorite: true,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp3',
      title: 'Premium Coffee Maker - Group Buy Special',
      imageUrl:
          'https://www.plushieshop.co.uk/wp-content/uploads/2021/05/1111344994175-e1624151494656.jpg',
      currentPrice: 159.99,
      originalPrice: 249.99,
      discountPercentage: 36,
      rating: 4.7,
      soldCount: 456,
      maxSavings: 90.00,
      isFavorite: false,
      category: 'Kitchen',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp4',
      title: 'Organic Skincare Set - Group Purchase',
      imageUrl:
          'https://ae01.alicdn.com/kf/Sadd4caf41f20437b8b6218ffbf695e0cs.jpg',
      currentPrice: 79.99,
      originalPrice: 129.99,
      discountPercentage: 38,
      rating: 4.6,
      soldCount: 723,
      maxSavings: 50.00,
      isFavorite: false,
      category: 'Beauty',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp5',
      title: 'Wireless Bluetooth Headphones - Group Deal',
      imageUrl:
          'https://down-my.img.susercontent.com/file/my-11134207-7rasj-m12ikbxur4r1fb',
      currentPrice: 89.99,
      originalPrice: 149.99,
      discountPercentage: 40,
      rating: 4.8,
      soldCount: 1250,
      maxSavings: 60.00,
      isFavorite: false,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp6',
      title: 'Wireless Bluetooth Headphones - Group Deal',
      imageUrl:
          'https://i5.walmartimages.com/seo/PlayStation-5-PS5-Console-Disc-Version_594285d5-4251-485f-b7d5-25e042253e75.89174ac515a7f3cd24f493f899b404eb.jpeg',
      currentPrice: 89.99,
      originalPrice: 149.99,
      discountPercentage: 40,
      rating: 4.8,
      soldCount: 1250,
      maxSavings: 60.00,
      isFavorite: false,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp7',
      title: 'Wireless Bluetooth Headphones - Group Deal',
      imageUrl:
          'https://images-cdn.ubuy.com.my/636f7f85212a3b1beb4b48b0-takara-tomy-ultimate-valkyrie-valtryek.jpg',
      currentPrice: 89.99,
      originalPrice: 149.99,
      discountPercentage: 40,
      rating: 4.8,
      soldCount: 1250,
      maxSavings: 60.00,
      isFavorite: false,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp8',
      title: 'Wireless Bluetooth Headphones - Group Deal',
      imageUrl:
          'https://static.wikia.nocookie.net/b-dapedia/images/d/d7/Smash%3DDragold_Official.jpg/revision/latest?cb=20131001164409',
      currentPrice: 89.99,
      originalPrice: 149.99,
      discountPercentage: 40,
      rating: 4.8,
      soldCount: 1250,
      maxSavings: 60.00,
      isFavorite: false,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
  ];

  bool loading = true;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) setState(() => loading = false);
    });
  }

  // Removed blind box group purchase products and category selection

  void onProductTap(BuildContext context, ShopProduct product) {
    context.push('/shop/detail/${product.id}');
  }

  void onFavoriteTap(BuildContext context, ShopProduct product) {
    final index = _groupPurchaseProducts.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      setState(() {
        _groupPurchaseProducts[index] = product.copyWith(
          isFavorite: !product.isFavorite,
        );
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            product.isFavorite
                ? 'Removed from favorites'
                : 'Added to favorites',
          ),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70.0),
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFFFCD255), Color(0xFFFDE18E), Color(0xFFFFFFFF)],
            ),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            centerTitle: true,
            elevation: 0,
            leading: IconButton(
              icon: SvgPicture.asset(
                'assets/icons/back.svg',
                colorFilter: const ColorFilter.mode(
                  Colors.black,
                  BlendMode.srcIn,
                ),
              ),
              onPressed: () => Navigator.pop(context),
            ),
            title: const Text(
              'Group Buy',
              style: TextStyle(
                fontSize: 18,
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: loading
                  ? GridView.builder(
                      padding: const EdgeInsets.only(top: 10, bottom: 20),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 0.65,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                          ),
                      itemCount: 6,
                      itemBuilder: (context, index) => const ShopCardSkeleton(),
                    )
                  : GridView.builder(
                      padding: const EdgeInsets.only(top: 10, bottom: 20),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 0.65,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                          ),
                      itemCount: _groupPurchaseProducts.length,
                      itemBuilder: (context, index) {
                        final product = _groupPurchaseProducts[index];
                        return ShopCard(
                          product: product,
                          onTap: () => onProductTap(context, product),
                          onFavorite: () => onFavoriteTap(context, product),
                        );
                      },
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
*/
