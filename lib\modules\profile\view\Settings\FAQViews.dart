import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import '../../widgets/DeliveryFAQPage.dart';
import '../../widgets/LuckyCardsFAQ.dart';
import '../../widgets/LuckyPointsFAQ.dart';
import '../../widgets/AccountFAQ.dart';
import '../../widgets/ReferralsFAQ.dart';
import '../../widgets/TNGFAQ.dart';
import '../../widgets/VoucherFAQ.dart';
import '../../widgets/DisclaimerFAQ.dart';
import '../../widgets/ReturnRefundFAQ.dart';

class FaqView extends StatelessWidget {
  const FaqView({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Map<String, String>> topics = [
      {'label': 'Delivery', 'icon': 'assets/icons/faq/Delivery.svg'},
      {'label': 'Lucky Cards', 'icon': 'assets/icons/faq/Lucky Cards.svg'},
      {'label': 'Lucky Points', 'icon': 'assets/icons/faq/Lucky Points.svg'},
      {'label': 'Account', 'icon': 'assets/icons/profiles/EmptyProfile.svg'},
      {'label': 'Refferals', 'icon': 'assets/icons/faq/Referrals.svg'},
      {'label': 'TNG', 'icon': 'assets/icons/faq/TNG.svg'},
      {'label': 'Voucher', 'icon': 'assets/icons/Voucher.svg'},
      {'label': 'Disclaimer', 'icon': 'assets/icons/faq/Disclaimer.svg'},
      {
        'label': 'Return & Refund',
        'icon': 'assets/icons/faq/Return Refund.svg',
      },
    ];

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        centerTitle: true,
        title: const Text(
          'FAQ',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      backgroundColor: Colors.white,
      body: Column(
        children: [
          const SizedBox(height: 60),
          const Text(
            'Topics',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 18,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 28),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 18),
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: topics.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                mainAxisSpacing: 18,
                crossAxisSpacing: 18,
                childAspectRatio: 1,
              ),
              itemBuilder: (context, index) {
                final topic = topics[index];
                final label = topic['label']!;
                final iconPath = topic['icon']!;

                return InkWell(
                  borderRadius: BorderRadius.circular(16),
                  onTap: () {
                    switch (label) {
                      case 'Delivery':
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const DeliveryFAQPage()),
                        );
                        break;
                      case 'Lucky Cards':
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const LuckyCardsFAQPage()),
                        );
                        break;
                        case 'Lucky Points':
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const LuckyPointsFAQPage()),
                        );
                        break;
                      case 'Account':
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const AccountFAQPage()),
                        );
                        break;
                      case 'Refferals':
                      Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const ReferralsFAQPage()),
                        );
                        break;
                      case 'TNG':
                      Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const TNGFAQPage()),
                        );
                        break;
                      case 'Voucher':
                      Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const VoucherFAQPage()),
                        );
                        break;
                      case 'Disclaimer':Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const DisclaimerFAQPage()),
                        );
                        break;
                      case 'Return & Refund':
                      Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const ReturnRefundFAQPage()),
                        );
                        break;

                        
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: const Color(0xFFE0E0E0), width: 1.5),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.10),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          iconPath,
                          width: 50,
                          height: 50,
                          color: label == 'TNG'
                              ? null
                              : const Color(0xFFFFBF00),
                        ),
                        const SizedBox(height: 16),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 6),
                          child: Text(
                            label,
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              color: Colors.black,
                              height: 1.2,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          const Spacer(),
          Container(
            width: double.infinity,
            color: const Color(0xFFF6F6F6),
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 18),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Still need help?',
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: const [
                          Text(
                            'Our chat service available:',
                            style: TextStyle(fontSize: 13, color: Colors.black87),
                          ),
                          SizedBox(height: 2),
                          Text(
                            '8:00 am - 12:00 am\nMonday to Sunday',
                            style: TextStyle(fontSize: 13, color: Colors.black54),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 38,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFFFFC700),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                          ),
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(horizontal: 22),
                        ),
                        onPressed: () {
                          // TODO: Contact Us action
                        },
                        child: const Text(
                          'Contact Us',
                          style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
