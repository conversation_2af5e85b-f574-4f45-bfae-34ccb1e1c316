import 'package:flutter/foundation.dart';
import '../data/CharityFundRepository.dart';
import '../model/CharityFund.dart';

class CharityFundVM extends ChangeNotifier {
  final CharityFundRepository _repository = CharityFundRepository();

  CharityFund? _charityFund;
  bool _isLoading = false;
  String? _error;
  DateTime? _lastUpdated;

  // Getters
  CharityFund? get charityFund => _charityFund;
  bool get isLoading => _isLoading;
  String? get error => _error;
  DateTime? get lastUpdated => _lastUpdated;

  // Get formatted amount for display
  String get formattedAmount {
    if (_charityFund == null) return '000';
    return _charityFund!.totalAmount.toString().padLeft(3, '0');
  }

  // Get formatted last updated date
  String get formattedLastUpdated {
    if (_charityFund == null) return '';
    return _charityFund!.formattedLastUpdated;
  }

  // Check if we have data
  bool get hasData => _charityFund != null;

  // Check if cache is valid
  bool get isCacheValid => _repository.isCacheValid;

  // Fetch charity fund data
  Future<void> fetchCharityFund() async {
    if (_isLoading) return;

    try {
      _setLoading(true);
      _clearError();

      print('CharityFundVM: Fetching charity fund data');
      final charityFund = await _repository.getCharityFund();

      _charityFund = charityFund;
      _lastUpdated = DateTime.now();

      print('CharityFundVM: Successfully fetched charity fund: $charityFund');
      notifyListeners();
    } catch (e) {
      print('CharityFundVM: Error fetching charity fund: $e');
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Refresh charity fund data (force refresh)
  Future<void> refreshCharityFund() async {
    print('CharityFundVM: Refreshing charity fund data');
    _repository.clearCache();
    await fetchCharityFund();
  }

  // Get cached data if available
  void loadCachedData() {
    final cachedData = _repository.getCachedCharityFund();
    if (cachedData != null) {
      _charityFund = cachedData;
      _lastUpdated = DateTime.now();
      notifyListeners();
      print('CharityFundVM: Loaded cached charity fund data');
    }
  }

  // Initialize with cached data if available
  void initialize() {
    if (isCacheValid) {
      loadCachedData();
    } else {
      fetchCharityFund();
    }
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    super.dispose();
  }
}
