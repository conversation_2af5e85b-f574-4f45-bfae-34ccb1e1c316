import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../view-model/SharePostVM.dart';

class SharePostView extends StatefulWidget {
  final String? orderType; // For context (blindbox, luckydraw, etc.)
  final String? orderId;

  const SharePostView({super.key, this.orderType, this.orderId});

  @override
  State<SharePostView> createState() => _SharePostViewState();
}

class _SharePostViewState extends State<SharePostView> {
  late SharePostVM _sharePostVM;
  final TextEditingController _captionController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _sharePostVM = SharePostVM();
    _captionController.addListener(() {
      _sharePostVM.updateCaption(_captionController.text);
    });
  }

  @override
  void dispose() {
    _captionController.dispose();
    _sharePostVM.dispose();
    super.dispose();
  }

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  Future<void> _pickMedia() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        _sharePostVM.selectMedia(File(image.path));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to pick image. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handleSubmitPost() async {
    final success = await _sharePostVM.submitPost();

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Post shared successfully!'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop(true); // Return success
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_sharePostVM.errorMessage ?? 'Failed to share post'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _sharePostVM,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black87),
            onPressed: () => Navigator.of(context).pop(),
          ),
          centerTitle: true,
          title: Text(
            'Share Post',
            style: TextStyle(
              fontSize: getResponsiveFontSize(18, context),
              fontWeight: FontWeight.w700,
              color: Colors.black,
            ),
          ),
        ),
        body: Consumer<SharePostVM>(
          builder: (context, vm, _) {
            return Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(getResponsivePadding(16, context)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Profile Section
                        _buildProfileSection(context),

                        SizedBox(height: getResponsivePadding(20, context)),

                        // Caption Input
                        _buildCaptionInput(context, vm),

                        SizedBox(height: getResponsivePadding(20, context)),

                        // Upload Media Section
                        _buildUploadMediaSection(context, vm),

                        SizedBox(height: getResponsivePadding(16, context)),

                        // Info Text
                        Text(
                          'Your post will appear in the public feed.',
                          style: TextStyle(
                            fontSize: getResponsiveFontSize(14, context),
                            color: Colors.grey.shade600,
                          ),
                        ),

                        if (vm.errorMessage != null) ...[
                          SizedBox(height: getResponsivePadding(16, context)),
                          Container(
                            padding: EdgeInsets.all(
                              getResponsivePadding(12, context),
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.red.shade200),
                            ),
                            child: Text(
                              vm.errorMessage!,
                              style: TextStyle(
                                color: Colors.red.shade700,
                                fontSize: getResponsiveFontSize(14, context),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                // Post Button
                _buildPostButton(context, vm),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context) {
    return Row(
      children: [
        // Profile Avatar
        Container(
          width: getResponsivePadding(40, context),
          height: getResponsivePadding(40, context),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey.shade200,
            image: const DecorationImage(
              // TODO: Replace with actual user profile image
              image: AssetImage('assets/images/default_profile.png'),
              fit: BoxFit.cover,
            ),
          ),
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey.shade300,
            ),
            child: Icon(
              Icons.person,
              size: getResponsivePadding(24, context),
              color: Colors.grey.shade600,
            ),
          ),
        ),

        SizedBox(width: getResponsivePadding(12, context)),

        // Username
        Text(
          'Angela', // TODO: Replace with actual username
          style: TextStyle(
            fontSize: getResponsiveFontSize(16, context),
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildCaptionInput(BuildContext context, SharePostVM vm) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          TextField(
            controller: _captionController,
            maxLines: 6,
            maxLength: vm.maxCaptionLength,
            decoration: InputDecoration(
              hintText: 'Write your caption',
              hintStyle: TextStyle(
                color: Colors.grey.shade500,
                fontSize: getResponsiveFontSize(14, context),
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(getResponsivePadding(16, context)),
              counterText: '', // Hide default counter
            ),
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.black87,
            ),
          ),
          // Custom character counter
          Container(
            padding: EdgeInsets.only(
              right: getResponsivePadding(16, context),
              bottom: getResponsivePadding(12, context),
            ),
            alignment: Alignment.centerRight,
            child: Text(
              '${vm.captionLength}/${vm.maxCaptionLength}',
              style: TextStyle(
                fontSize: getResponsiveFontSize(12, context),
                color: Colors.grey.shade500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadMediaSection(BuildContext context, SharePostVM vm) {
    return GestureDetector(
      onTap: vm.selectedMedia == null ? _pickMedia : null,
      child: Container(
        width: getResponsivePadding(100, context),
        height: getResponsivePadding(100, context),
        decoration: BoxDecoration(
          color: vm.selectedMedia == null
              ? const Color(0xFFFCD255)
              : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: vm.selectedMedia != null
              ? Border.all(color: Colors.grey.shade300)
              : null,
        ),
        child: vm.selectedMedia == null
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.camera_alt_outlined,
                    size: getResponsivePadding(32, context),
                    color: Colors.black54,
                  ),
                  SizedBox(height: getResponsivePadding(8, context)),
                  Text(
                    'Upload media',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(12, context),
                      color: Colors.black54,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              )
            : Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      vm.selectedMedia!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Positioned(
                    top: 4,
                    right: 4,
                    child: GestureDetector(
                      onTap: vm.removeMedia,
                      child: Container(
                        padding: EdgeInsets.all(
                          getResponsivePadding(4, context),
                        ),
                        decoration: const BoxDecoration(
                          color: Colors.black54,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.close,
                          size: getResponsivePadding(16, context),
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildPostButton(BuildContext context, SharePostVM vm) {
    return Container(
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      child: SizedBox(
        width: double.infinity,
        height: getResponsivePadding(50, context),
        child: ElevatedButton(
          onPressed: vm.isPosting || !vm.canPost ? null : _handleSubmitPost,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFCD255),
            disabledBackgroundColor: Colors.grey.shade300,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: vm.isPosting
              ? SizedBox(
                  width: getResponsivePadding(20, context),
                  height: getResponsivePadding(20, context),
                  child: const CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black54),
                  ),
                )
              : Text(
                  'Post',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(16, context),
                    fontWeight: FontWeight.w700,
                    color: Colors.black87,
                  ),
                ),
        ),
      ),
    );
  }
}
