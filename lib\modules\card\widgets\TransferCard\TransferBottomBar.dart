import 'package:flutter/material.dart';

class TransferBottomBar extends StatelessWidget {
  final bool isGroupSelected;
  final VoidCallback onToggleGroup;
  final int totalSelected;
  final VoidCallback onConfirm;
  final bool isLoading; // ✅ new: to disable button & show loader

  const TransferBottomBar({
    super.key,
    required this.isGroupSelected,
    required this.onToggleGroup,
    required this.totalSelected,
    required this.onConfirm,
    this.isLoading = false, // ✅ default: not loading
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final scale = (size.width / 390.0).clamp(0.9, 1.2);

    final canConfirm = totalSelected > 0 && !isLoading;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16 * scale, vertical: 10 * scale),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            offset: Offset(0, -2),
            blurRadius: 6,
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Left section: checkbox + total count
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Transform.scale(
                      scale: scale,
                      child: Checkbox(
                        value: isGroupSelected,
                        onChanged: (_) => onToggleGroup(),
                      ),
                    ),
                    Flexible(
                      child: Text(
                        "Select All Cards",
                        style: TextStyle(
                          fontSize: 14 * scale,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                Text(
                  "Total Selected: $totalSelected",
                  style: TextStyle(
                    fontSize: 12 * scale,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(width: 12 * scale),

          // Right section: confirm button
          ElevatedButton(
            onPressed: canConfirm ? onConfirm : null, // ✅ disable when not allowed
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(
                horizontal: 20 * scale,
                vertical: 14 * scale,
              ),
              backgroundColor: Colors.redAccent,
              disabledBackgroundColor: Colors.grey[400], // ✅ disabled state
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12 * scale),
              ),
              elevation: 6,
            ),
            child: isLoading
                ? SizedBox(
                    height: 18 * scale,
                    width: 18 * scale,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    "Confirm",
                    style: TextStyle(
                      fontSize: 14.5 * scale,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
