import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../data/model/OrderModel.dart';

class RefundStatusView extends StatefulWidget {
  final OrderModel order;

  const RefundStatusView({super.key, required this.order});

  @override
  State<RefundStatusView> createState() => _RefundStatusViewState();
}

class _RefundStatusViewState extends State<RefundStatusView> {
  final TextEditingController _trackingController = TextEditingController();
  final TextEditingController _shippingController = TextEditingController();
  final TextEditingController _remarksController = TextEditingController();

  @override
  void dispose() {
    _trackingController.dispose();
    _shippingController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  Color _getStatusColor() {
    if (widget.order.refundStatus == null) return const Color(0xFFFCD255);

    switch (widget.order.refundStatus!) {
      case RefundStatus.refundRequested:
        return const Color(0xFFFCD255); // Yellow
      case RefundStatus.refundApproved:
        return const Color(0xFF10B981); // Green
      case RefundStatus.returnInProgress:
        return const Color(0xFFFCD255); // Yellow
      case RefundStatus.productReceived:
        return const Color(0xFFFCD255); // Yellow
      case RefundStatus.refundCompleted:
      case RefundStatus.exchangeCompleted:
        return const Color(0xFF10B981); // Green
      case RefundStatus.refundRejected:
        return const Color(0xFFEF4444); // Red
    }
  }

  String _getStatusText() {
    if (widget.order.refundStatus == null) return 'In Progress...';
    return widget.order.refundStatus!.displayName;
  }

  String _getStatusDescription() {
    if (widget.order.refundStatus == null) return 'Processing your request...';
    return widget.order.refundStatus!.description;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: _getStatusColor(),
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black87),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: true,
        title: Text(
          'Return / Refund',
          style: TextStyle(
            fontSize: getResponsiveFontSize(18, context),
            fontWeight: FontWeight.w700,
            color: Colors.black,
          ),
        ),
      ),
      body: Column(
        children: [
          // Status Banner
          _buildStatusBanner(context),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: getResponsivePadding(16, context)),

                  // Request ID and Date
                  _buildRequestHeader(context),

                  SizedBox(height: getResponsivePadding(16, context)),

                  // Product Information
                  _buildProductInfo(context),

                  // Status-specific content
                  _buildStatusSpecificContent(context),
                ],
              ),
            ),
          ),

          // Bottom action (if needed)
          _buildBottomAction(context),
        ],
      ),
    );
  }

  Widget _buildStatusBanner(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      color: _getStatusColor(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _getStatusText(),
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: widget.order.refundStatus == RefundStatus.refundRejected
                  ? Colors.white
                  : Colors.black87,
            ),
          ),
          SizedBox(height: getResponsivePadding(4, context)),
          Text(
            _getStatusDescription(),
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: widget.order.refundStatus == RefundStatus.refundRejected
                  ? Colors.white
                  : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Request ID: ${widget.order.refundRequestId ?? widget.order.id}',
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          Text(
            '2025-02-02', // You can format widget.order.createdAt here
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductInfo(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade100,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Prize/Order header
          Row(
            children: [
              Icon(
                Icons.card_giftcard,
                size: getResponsivePadding(16, context),
                color: Colors.black54,
              ),
              SizedBox(width: getResponsivePadding(8, context)),
              Text(
                'Prize',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(12, context),
                  fontWeight: FontWeight.w600,
                  color: Colors.black54,
                ),
              ),
              const Spacer(),
              Text(
                'Order Number: ${widget.order.id}',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(12, context),
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),

          SizedBox(height: getResponsivePadding(16, context)),

          // Product details
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CachedNetworkImage(
                  imageUrl: widget.order.productImage,
                  width: getResponsivePadding(80, context),
                  height: getResponsivePadding(80, context),
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey.shade100,
                    child: Center(
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.grey.shade400,
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey.shade200,
                    child: Icon(Icons.image, color: Colors.grey.shade400),
                  ),
                ),
              ),

              SizedBox(width: getResponsivePadding(16, context)),

              // Product info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.order.productName,
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: getResponsivePadding(8, context)),

                    // Show points or money based on order type
                    if (widget.order.total == 0) ...[
                      Text(
                        'Total Points: 1000 pts',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          fontWeight: FontWeight.w600,
                          color: Colors.red,
                        ),
                      ),
                    ] else ...[
                      Text(
                        'Subtotal: ${widget.order.total.toStringAsFixed(0)} Credits',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          fontWeight: FontWeight.w600,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: getResponsivePadding(16, context)),

          // View More button
          Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'View More',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    color: Colors.grey.shade600,
                  ),
                ),
                SizedBox(width: getResponsivePadding(4, context)),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.grey.shade600,
                  size: getResponsivePadding(16, context),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusSpecificContent(BuildContext context) {
    if (widget.order.refundStatus == null) return const SizedBox.shrink();

    switch (widget.order.refundStatus!) {
      case RefundStatus.refundRequested:
        return const SizedBox.shrink(); // Just the banner is enough

      case RefundStatus.refundApproved:
        return _buildApprovedContent(context);

      case RefundStatus.returnInProgress:
        return _buildReturnInProgressContent(context);

      case RefundStatus.productReceived:
        return _buildProductReceivedContent(context);

      case RefundStatus.refundCompleted:
      case RefundStatus.exchangeCompleted:
        return _buildCompletedContent(context);

      case RefundStatus.refundRejected:
        return _buildRejectedContent(context);
    }
  }

  Widget _buildApprovedContent(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: getResponsivePadding(16, context)),

        // Lucky Mall address
        _buildLuckyMallAddress(context),

        SizedBox(height: getResponsivePadding(16, context)),

        // Return form fields
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: getResponsivePadding(16, context),
          ),
          child: Column(
            children: [
              _buildTextField(
                controller: _trackingController,
                label: 'Tracking Number',
                placeholder: 'Enter tracking number',
              ),

              SizedBox(height: getResponsivePadding(16, context)),

              _buildTextField(
                controller: _shippingController,
                label: 'Shipping Company',
                placeholder: 'Enter courier name',
              ),

              SizedBox(height: getResponsivePadding(16, context)),

              _buildTextField(
                controller: _remarksController,
                label: 'Remarks',
                placeholder: 'Enter remarks',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReturnInProgressContent(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: getResponsivePadding(16, context)),

        // Tracking number
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: getResponsivePadding(16, context),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Tracking number',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              Row(
                children: [
                  Text(
                    'KDDH121002067391',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(14, context),
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(width: getResponsivePadding(8, context)),
                  GestureDetector(
                    onTap: () {
                      Clipboard.setData(
                        const ClipboardData(text: 'KDDH121002067391'),
                      );
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Tracking number copied!'),
                        ),
                      );
                    },
                    child: Icon(
                      Icons.copy,
                      size: getResponsivePadding(16, context),
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        SizedBox(height: getResponsivePadding(24, context)),

        // Tracking timeline
        _buildTrackingTimeline(context),
      ],
    );
  }

  Widget _buildProductReceivedContent(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: getResponsivePadding(24, context)),

        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: getResponsivePadding(16, context),
          ),
          child: Text(
            'We have received the product.\n\nNew product will be sent to you within 7 business days.',
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.black87,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildCompletedContent(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: getResponsivePadding(16, context)),

        // Refund details
        Container(
          margin: EdgeInsets.symmetric(
            horizontal: getResponsivePadding(16, context),
          ),
          padding: EdgeInsets.all(getResponsivePadding(16, context)),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.order.refundStatus ==
                  RefundStatus.refundCompleted) ...[
                Text(
                  widget.order.total == 0 ? 'Points Refund' : 'Credits Refund',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(16, context),
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: getResponsivePadding(8, context)),
                if (widget.order.total == 0) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Point',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                        ),
                      ),
                      Text(
                        '30 pts',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: getResponsivePadding(4, context)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Total Points Refunded',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '30 pts',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Subtotal',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                        ),
                      ),
                      Text(
                        '${widget.order.total.toStringAsFixed(0)} Credits',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: getResponsivePadding(4, context)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Total Credits Refunded',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${widget.order.total.toStringAsFixed(0)} Credits',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ],
          ),
        ),

        SizedBox(height: getResponsivePadding(16, context)),

        // Track progress
        _buildTrackProgress(context),
      ],
    );
  }

  Widget _buildRejectedContent(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: getResponsivePadding(16, context)),

        // Reason section
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: getResponsivePadding(16, context),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Reason',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(16, context),
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              SizedBox(height: getResponsivePadding(8, context)),
              Text(
                widget.order.refundReason ?? 'Rejected reason...',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: getResponsivePadding(24, context)),

        // Reason for application
        _buildReasonForApplication(context),

        SizedBox(height: getResponsivePadding(24, context)),

        // Evidence
        _buildEvidence(context),

        SizedBox(height: getResponsivePadding(24, context)),

        // Contact
        _buildContact(context),
      ],
    );
  }

  Widget _buildLuckyMallAddress(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                color: Colors.red,
                size: getResponsivePadding(20, context),
              ),
              SizedBox(width: getResponsivePadding(8, context)),
              Text(
                'Lucky Mall',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(16, context),
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              SizedBox(width: getResponsivePadding(8, context)),
              Text(
                '011-684 33988',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  color: Colors.grey.shade600,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  Clipboard.setData(
                    const ClipboardData(
                      text:
                          'SUITE 29.04-06, 29TH FLOOR, WISMA CHUANG, 34, Jin Sultan Ismail, Bukit Bintang, 50250 Kuala Lumpur, Wilayah Persekutuan Kuala Lumpur',
                    ),
                  );
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Address copied!')),
                  );
                },
                child: Icon(
                  Icons.copy,
                  size: getResponsivePadding(16, context),
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),

          SizedBox(height: getResponsivePadding(8, context)),

          Text(
            'SUITE 29.04-06, 29TH FLOOR, WISMA CHUANG, 34, Jin Sultan Ismail, Bukit Bintang, 50250 Kuala Lumpur, Wilayah Persekutuan Kuala Lumpur',
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.grey.shade700,
              height: 1.4,
            ),
          ),

          SizedBox(height: getResponsivePadding(8, context)),

          Text(
            'Operation Time: 8:30am - 6:30pm',
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String placeholder,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: getResponsivePadding(8, context)),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: placeholder,
            hintStyle: TextStyle(
              color: Colors.grey.shade400,
              fontSize: getResponsiveFontSize(14, context),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFFCD255)),
            ),
            contentPadding: EdgeInsets.all(getResponsivePadding(12, context)),
          ),
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildTrackingTimeline(BuildContext context) {
    final List<Map<String, dynamic>> trackingEvents = [
      {
        'date': 'Today\n02:37',
        'title':
            'Your parcel has arrived at sorting facility SMH sorting centre',
        'isActive': true,
      },
      {
        'date': '24 May\n05:50',
        'title':
            'Parcel is on the vessel to destination port in East Malaysia. Note that your parcel will be handled over to SPX.',
        'isActive': false,
      },
      {
        'date': '15 May\n23:56',
        'title': 'Parcel is undergoing custom declaration at Port Klang',
        'isActive': false,
      },
      {
        'date': '15 May\n14:17',
        'title':
            'Your parcel has departed from sorting facility DOR sorting centre',
        'isActive': false,
      },
      {
        'date': '15 May\n13:40',
        'title':
            'Your parcel has arrived from sorting facility DOR sorting centre',
        'isActive': false,
      },
      {
        'date': '15 May\n02:19',
        'title': 'Your parcel has departed from sorting facility',
        'isActive': false,
      },
      {
        'date': '14 May\n21:04',
        'title':
            'Your parcel has arrived at sorting facility CSL First Mile Hub',
        'isActive': false,
      },
      {
        'date': '14 May\n16:07',
        'title': 'Your parcel has been picked up by our logistics partner',
        'isActive': false,
      },
      {
        'date': '14 May\n09:03',
        'title': 'Courier assigned for your order, kindly wait for pick up',
        'isActive': false,
      },
    ];

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        children: trackingEvents.asMap().entries.map((entry) {
          final index = entry.key;
          final event = entry.value;
          return _buildTrackingItem(
            context,
            event['date'],
            event['title'],
            event['isActive'],
            index < trackingEvents.length - 1,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTrackingItem(
    BuildContext context,
    String date,
    String title,
    bool isActive,
    bool showLine,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Date
        SizedBox(
          width: getResponsivePadding(50, context),
          child: Text(
            date,
            style: TextStyle(
              fontSize: getResponsiveFontSize(12, context),
              color: Colors.grey.shade500,
            ),
          ),
        ),

        SizedBox(width: getResponsivePadding(16, context)),

        // Timeline indicator
        Column(
          children: [
            Container(
              width: getResponsivePadding(8, context),
              height: getResponsivePadding(8, context),
              decoration: BoxDecoration(
                color: isActive
                    ? const Color(0xFFFCD255)
                    : Colors.grey.shade300,
                shape: BoxShape.circle,
              ),
            ),
            if (showLine)
              Container(
                width: 1,
                height: getResponsivePadding(40, context),
                color: Colors.grey.shade300,
              ),
          ],
        ),

        SizedBox(width: getResponsivePadding(16, context)),

        // Event description
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: getResponsivePadding(16, context)),
            child: Text(
              title,
              style: TextStyle(
                fontSize: getResponsiveFontSize(12, context),
                color: isActive ? Colors.black87 : Colors.grey.shade600,
                fontWeight: isActive ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTrackProgress(BuildContext context) {
    final List<Map<String, String>> progressItems = [
      {
        'date': '14 May\n09:03',
        'title': 'Credits Refunded',
        'subtitle':
            'Your credits have been added to Lucky Mall Wallet. Thank you for your patience!',
      },
      {
        'date': '14 May\n08:16',
        'title': 'Request Processing',
        'subtitle': 'Our customer service team is reviewing your request.',
      },
      {
        'date': '14 May\n08:17',
        'title': 'Refund Initiated',
        'subtitle': 'Cancellation of your item is in process',
      },
    ];

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Track Progress',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          SizedBox(height: getResponsivePadding(16, context)),

          ...progressItems
              .map(
                (item) => _buildProgressItem(
                  context,
                  item['date']!,
                  item['title']!,
                  item['subtitle']!,
                ),
              )
              .toList(),
        ],
      ),
    );
  }

  Widget _buildProgressItem(
    BuildContext context,
    String date,
    String title,
    String subtitle,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: getResponsivePadding(16, context)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date
          SizedBox(
            width: getResponsivePadding(50, context),
            child: Text(
              date,
              style: TextStyle(
                fontSize: getResponsiveFontSize(12, context),
                color: Colors.grey.shade500,
              ),
            ),
          ),

          SizedBox(width: getResponsivePadding(16, context)),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: getResponsivePadding(4, context)),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(12, context),
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReasonForApplication(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reason for application*',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          SizedBox(height: getResponsivePadding(12, context)),

          Container(
            width: double.infinity,
            padding: EdgeInsets.all(getResponsivePadding(12, context)),
            decoration: BoxDecoration(
              color: const Color(0xFFFCD255),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              widget.order.refundReason ?? 'Item is defective or broken',
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEvidence(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Evidence*',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          SizedBox(height: getResponsivePadding(8, context)),

          Text(
            'At least 1 media must be provided. This help us process the after-sale issue more quickly and efficiently.',
            style: TextStyle(
              fontSize: getResponsiveFontSize(12, context),
              color: Colors.grey.shade600,
            ),
          ),

          SizedBox(height: getResponsivePadding(12, context)),

          // Evidence image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.asset(
              'assets/images/evidence_example.jpg', // You can replace with actual evidence
              width: getResponsivePadding(120, context),
              height: getResponsivePadding(120, context),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: getResponsivePadding(120, context),
                  height: getResponsivePadding(120, context),
                  color: Colors.grey.shade200,
                  child: Icon(
                    Icons.image,
                    size: getResponsivePadding(40, context),
                    color: Colors.grey.shade400,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContact(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Contact',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          SizedBox(height: getResponsivePadding(12, context)),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '012- 345 6789',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(16, context),
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),

          SizedBox(height: getResponsivePadding(8, context)),

          Text(
            'We will use this only to follow up on your request. It is auto-filled from your profile data.',
            style: TextStyle(
              fontSize: getResponsiveFontSize(12, context),
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomAction(BuildContext context) {
    if (widget.order.refundStatus == null) return const SizedBox.shrink();

    switch (widget.order.refundStatus!) {
      case RefundStatus.refundApproved:
        return _buildSubmitButton(context);
      case RefundStatus.productReceived:
      case RefundStatus.refundRejected:
        return _buildHelpSupportButton(context);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildSubmitButton(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      child: SizedBox(
        width: double.infinity,
        height: getResponsivePadding(50, context),
        child: ElevatedButton(
          onPressed: () {
            // Handle submit logic
            Navigator.of(context).pop();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFCD255),
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            'Submit',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w700,
              color: Colors.black87,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHelpSupportButton(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      child: SizedBox(
        width: double.infinity,
        height: getResponsivePadding(50, context),
        child: ElevatedButton(
          onPressed: () {
            // Handle help support logic
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFCD255),
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            'Help Support',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w700,
              color: Colors.black87,
            ),
          ),
        ),
      ),
    );
  }
}
