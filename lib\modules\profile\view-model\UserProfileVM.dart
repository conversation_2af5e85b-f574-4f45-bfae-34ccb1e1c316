import 'package:flutter/material.dart';
import '../model/UserInfoModel.dart';
import '../service/UserProfileService.dart';
import '../../../router/auth_guard.dart';
import 'FaceVerificationState.dart';

class UserProfileVM extends ChangeNotifier {
  UserInfoModel? userInfo;
  bool isLoading = false;
  String? errorMessage;
  bool get isLoggedIn => AuthService.isLoggedIn;

  Future<void> fetchUserProfile() async {
    if (!isLoggedIn) {
      userInfo = null;
      errorMessage = 'User not logged in';
      notifyListeners();
      return;
    }

    try {
      isLoading = true;
      errorMessage = null;
      notifyListeners();

      debugPrint('[UserProfileVM] Fetching user profile...');
      userInfo = await ProfileService().getUserInfo();
      debugPrint(
        '[UserProfileVM] User profile loaded successfully: ${userInfo?.nickname}',
      );
      errorMessage = null;

      // Sync face verification state with the global state manager
      _syncFaceVerificationState();
    } catch (e) {
      debugPrint('[UserProfileVM] Error fetching user profile: $e');
      errorMessage = 'Failed to load user profile: $e';
      userInfo = null;
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  void _syncFaceVerificationState() {
    if (userInfo?.faceState != null) {
      final faceState = userInfo!.faceState;
      final stateManager = FaceVerificationStateManager.instance;

      switch (faceState) {
        case 10: // completed
          stateManager.setVerified();
          break;
        case 1: // authenticating
          stateManager.setPendingApproval();
          break;
        case 0: // not authenticated
        case -1: // returned
        default:
          stateManager.setNotVerified();
          break;
      }
    }
  }

  Future<void> refreshProfile() async {
    await fetchUserProfile();
  }

  // Helper getters for profile data
  String get displayName => userInfo?.nickname ?? 'User';
  String get userId => userInfo?.id.toString() ?? 'N/A';
  String get profileImage => userInfo?.avatar ?? '';
  String get phoneNumber => userInfo?.mobile ?? '';
  int get faceVerificationState => userInfo?.faceState ?? 0;

  // For now, we'll use placeholder values for points and card count
  // since they're not available in the current API
  String get points => '0'; // Placeholder until API provides this
  String get cardCount => '0'; // Placeholder until API provides this

  Future<void> saveProfile() async {
    if (userInfo == null) {
      throw Exception('No user data to save');
    }

    try {
      isLoading = true;
      notifyListeners();

      debugPrint('[UserProfileVM] Saving user profile...');
      final success = await ProfileService().updateUserInfo(userInfo!);

      if (success) {
        debugPrint('[UserProfileVM] User profile saved successfully');
        // Refresh the profile to get any updated data from server
        await fetchUserProfile();
      } else {
        throw Exception('Failed to save profile');
      }
    } catch (e) {
      debugPrint('[UserProfileVM] Error saving user profile: $e');
      throw Exception('Failed to save profile: $e');
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  void updateUsername(String value) {
    if (userInfo != null) {
      userInfo = UserInfoModel(
        avatar: userInfo!.avatar,
        nickname: value,
        mobile: userInfo!.mobile,
        memberCode: userInfo!.memberCode,
        birthday: userInfo!.birthday,
        gender: userInfo!.gender,
        faceState: userInfo!.faceState,
      );
      notifyListeners();
    }
  }

  void updateGender(String value) {
    if (userInfo != null) {
      int genderValue = 0; // not set
      if (value.toLowerCase() == 'male') {
        genderValue = 2;
      } else if (value.toLowerCase() == 'female') {
        genderValue = 1;
      }

      userInfo = UserInfoModel(
        avatar: userInfo!.avatar,
        nickname: userInfo!.nickname,
        mobile: userInfo!.mobile,
        memberCode: userInfo!.memberCode,
        birthday: userInfo!.birthday,
        gender: genderValue,
        faceState: userInfo!.faceState,
      );
      notifyListeners();
    }
  }

  void updateBirthDate(String value) {
    if (userInfo != null) {
      DateTime? birthday;
      try {
        birthday = DateTime.parse(value);
      } catch (e) {
        debugPrint('[UserProfileVM] Invalid date format: $value');
      }

      userInfo = UserInfoModel(
        avatar: userInfo!.avatar,
        nickname: userInfo!.nickname,
        mobile: userInfo!.mobile,
        memberCode: userInfo!.memberCode,
        birthday: birthday!,
        gender: userInfo!.gender,
        faceState: userInfo!.faceState,
      );
      notifyListeners();
    }
  }

  void updateProfileImage(String imagePath) {
    if (userInfo != null) {
      userInfo = UserInfoModel(
        avatar: imagePath,
        nickname: userInfo!.nickname,
        mobile: userInfo!.mobile,
        memberCode: userInfo!.memberCode,
        birthday: userInfo!.birthday,
        gender: userInfo!.gender,
        faceState: userInfo!.faceState,
      );
      notifyListeners();
    }
  }
}
