import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../view-model/InviteFriendViewModel.dart';

class ReferralCodeSection extends StatelessWidget {
  const ReferralCodeSection({super.key});

  @override
  Widget build(BuildContext context) {
    final vm = context.watch<InviteFriendViewModel>();
    final code = vm.referralCode;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          const Divider(thickness: 1, color: Colors.grey),
          const SizedBox(height: 8),
          const Text("Referral code", style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.amber, width: 2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              code,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              OutlinedButton(
                onPressed: () {},
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.amber,
                  side: const BorderSide(color: Colors.amber),
                ),
                child: const Text("Copy"),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.black,
                ),
                child: const Text("Share"),
              ),
            ],
          )
        ],
      ),
    );
  }
}