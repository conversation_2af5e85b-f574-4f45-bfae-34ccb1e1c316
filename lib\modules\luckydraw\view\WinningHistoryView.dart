import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';
import 'package:luckymall/modules/luckydraw/widgets/DrawRecordCard.dart';
import 'LatestDrawRecordView.dart' show DrawRecordCardShimmer;
import 'package:luckymall/modules/luckydraw/view-model/WinningHistoryVM.dart';

class WinningHistoryView extends StatelessWidget {
  final int? goodsPointId;

  const WinningHistoryView({super.key, this.goodsPointId});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => WinningHistoryVM(goodsPointId: goodsPointId)..initialize(),
      child: Consumer<WinningHistoryVM>(
        builder: (context, vm, _) {
          if (vm.isLoading) {
            return Scaffold(
              appBar: CustomAppBar(title: 'Winning History'),
              body: ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: 8),
                itemCount: 4,
                itemBuilder: (context, index) => const Padding(
                  padding: EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                  child: DrawRecordCardShimmer(),
                ),
              ),
            );
          }
          if (vm.records.isEmpty) {
            return Scaffold(
              appBar: CustomAppBar(title: 'Winning History'),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 150,
                      height: 150,
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage('assets/images/NoRecord.png'),
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'No Records',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
          return Scaffold(
            appBar: CustomAppBar(title: 'Winning History'),
            body: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: vm.records.length,
              itemBuilder: (context, index) {
                final record = vm.records[index];
                return DrawRecordCard(
                  username: record.username,
                  productTitle: record.productTitle,
                  date: record.date,
                  time: record.time,
                  period: record.period,
                  totalShares: record.totalShares,
                  participationPoint: record.participationPoint,
                  winningName: record.winningName,
                  imageUrl: record.imageUrl,
                );
              },
            ),
          );
        },
      ),
    );
  }
}
