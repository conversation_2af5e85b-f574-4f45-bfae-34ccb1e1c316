import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../modules/profile/model/MyOrderModel.dart';
import '../modules/checkout/view-model/CheckoutVM.dart';

/// Navigation helper class with common navigation methods
class NavigationHelper {
  /// Navigate to login page
  static void goToLogin(BuildContext context) {
    context.go('/login');
  }

  /// Navigate to main shop page
  static void goToShop(BuildContext context) {
    context.go('/shop');
  }

  /// Navigate to main page (Lucky Draw)
  static void goToMainPage(BuildContext context) {
    context.go('/lucky-draw');
  }

  /// Navigate to profile page
  static void goToProfile(BuildContext context) {
    context.go('/profile');
  }

  /// Navigate to lucky draw page
  static void goToLuckyDraw(BuildContext context) {
    context.go('/lucky-draw');
  }

  /// Navigate to blind box page
  static void goToBlindBox(BuildContext context) {
    context.go('/blind-box');
  }

  /// Navigate to card page
  static void goToCard(BuildContext context) {
    context.go('/card');
  }

  /// Navigate to shop cart
  static void goToShopCart(BuildContext context) {
    context.push('/shop/cart');
  }

  /// Navigate to favourites
  static void goToFavourites(BuildContext context) {
    context.push('/shop/favourites');
  }

  /// Navigate to shop product detail
  static void goToShopDetail(BuildContext context, String productId) {
    context.push('/shop/detail/$productId');
  }

  /// Navigate to prize reveal
  static void goToPrizeReveal(BuildContext context) {
    context.push('/lucky-draw/prize-reveal');
  }

  /// Navigate to group purchase
  static void goToGroupPurchase(BuildContext context) {
    context.push('/lucky-draw/group-purchase');
  }

  /// Navigate to participation records
  static void goToParticipationRecords(BuildContext context) {
    context.push('/lucky-draw/participation-records');
  }

  /// Navigate to blindbox shop
  static void goToBlindboxShop(BuildContext context) {
    context.push('/blind-box/shop');
  }

  /// Navigate to blindbox buy page
  static void goToBlindboxBuy(
    BuildContext context, {
    required bool isGroupBuy,
    required Map<String, dynamic> product,
  }) {
    context.push(
      '/blind-box/buy',
      extra: {'isGroupBuy': isGroupBuy, 'product': product},
    );
  }

  /// Navigate to blindbox search page
  static void goToBlindboxSearch(BuildContext context, {String? initialQuery}) {
    context.push('/blind-box/search', extra: {'initialQuery': initialQuery});
  }

  /// Navigate to card pack page
  static void goToCardPack(BuildContext context) {
    context.push('/card/pack');
  }

  /// Navigate to card purchase page
  static void goToCardPurchase(BuildContext context) {
    context.push('/card/purchase');
  }

  /// Navigate to my address page
  static void goToMyAddress(BuildContext context) {
    context.push('/profile/my-address');
  }

  /// Navigate to add address page
  static void goToAddAddress(BuildContext context) {
    context.push('/profile/add-address');
  }

  /// Navigate to update address page
  static void goToUpdateAddress(BuildContext context, String addressId) {
    context.push('/profile/update-address/$addressId');
  }

  /// Navigate to my order page
  static void goToMyOrder(BuildContext context) {
    context.push('/profile/my-order');
  }

  /// Navigate to share post page
  static void goToSharePost(
    BuildContext context, {
    String? orderType,
    String? orderId,
  }) {
    context.push(
      '/profile/share-post',
      extra: {'orderType': orderType, 'orderId': orderId},
    );
  }

  /// Navigate to refund page
  static void goToRefund(BuildContext context, MyOrderModel order) {
    context.push('/profile/refund', extra: order);
  }

  /// Navigate to refund status page
  static void goToRefundStatus(BuildContext context, MyOrderModel order) {
    context.push('/profile/refund-status', extra: order);
  }

  static void goToCompletedOrderDetails(
    BuildContext context,
    MyOrderModel order,
  ) {
    context.push('/profile/completed-order-details', extra: order);
  }

  /// Navigate to cancelled order details page
  static void goToCancelledOrderDetails(
    BuildContext context,
    MyOrderModel order,
  ) {
    context.push('/profile/cancelled-order-details', extra: order);
  }

  /// Navigate to shipping information page
  static void goToShippingInformation(
    BuildContext context,
    MyOrderModel order,
  ) {
    context.push('/profile/shipping-information', extra: order);
  }

  /// Navigate to to receive page
  static void goToReceive(BuildContext context, MyOrderModel order) {
    context.push('/profile/to-receive', extra: order);
  }

  /// Navigate to to ship page
  static void goToToShip(BuildContext context, MyOrderModel order) {
    context.push('/profile/to-ship', extra: order);
  }

  /// Navigate to checkout page for all order types
  static void goToCheckout(BuildContext context, MyOrderModel order) {
    // Map source to CheckoutType
    CheckoutType checkoutType;
    switch (order.source) {
      case 0: // Mall order
        checkoutType = CheckoutType.shop;
        break;
      case 1: // Card mailing
        checkoutType = CheckoutType.physicalCard;
        break;
      case 10: // Points mall order
        checkoutType = CheckoutType.shop;
        break;
      case 30: // Group purchase order
        checkoutType = CheckoutType.luckydraw;
        break;
      default:
        checkoutType = CheckoutType.shop; // fallback
    }

    context.push(
      '/checkout',
      extra: {
        'checkoutType': checkoutType,
        'productData': {
          'orderId': order.id,
          'orderAmount': order.totalPrice ?? 0,
          'productName': order.orderItems?.first.productSkuName ?? 'Product',
          'quantity': order.orderItems?.first.quantity ?? 1,
        },
      },
    );
  }

  /// Go back to previous page
  static void goBack(BuildContext context) {
    if (context.canPop()) {
      context.pop();
    }
  }

  /// Check if can go back
  static bool canGoBack(BuildContext context) {
    return context.canPop();
  }
}
