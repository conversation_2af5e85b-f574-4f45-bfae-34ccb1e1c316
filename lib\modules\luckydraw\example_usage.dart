import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'data/LuckyDrawService.dart';
import 'data/LuckyDrawRepository.dart';
import 'view-model/CategoryVM.dart';
import 'model/LuckyDrawCategory.dart';
import 'model/MegaPrize.dart';
import 'view-model/PrizeRevealVM.dart';
import 'widgets/MegaPrizes.dart';

/// Example usage of the new Lucky Draw Category API integration
///
/// This file demonstrates how to:
/// 1. Fetch categories from the API
/// 2. Use the CategoryVM for state management
/// 3. Filter products by category
/// 4. Handle loading and error states
class CategoryAPIExample extends StatelessWidget {
  const CategoryAPIExample({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => CategoryVM(),
      child: const CategoryExampleView(),
    );
  }
}

class CategoryExampleView extends StatelessWidget {
  const CategoryExampleView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Category API Example'),
        backgroundColor: const Color(0xFFFFBF00),
      ),
      body: Consumer<CategoryVM>(
        builder: (context, categoryVM, child) {
          if (categoryVM.isLoading) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFFBF00)),
              ),
            );
          }

          if (categoryVM.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${categoryVM.errorMessage}',
                    style: const TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => categoryVM.refresh(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Categories (${categoryVM.categories.length})',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    itemCount: categoryVM.categories.length,
                    itemBuilder: (context, index) {
                      final category = categoryVM.categories[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: const Color(0xFFFFBF00),
                            child: Text(
                              category.categoryId.toString(),
                              style: const TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          title: Text(category.categoryName),
                          subtitle: Text('Category ID: ${category.categoryId}'),
                          trailing: const Icon(
                            Icons.check_circle,
                            color: Colors.green,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => categoryVM.refresh(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFFFBF00),
                        ),
                        child: const Text('Refresh Categories'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => categoryVM.clearCache(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey,
                        ),
                        child: const Text('Clear Cache'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Example of category lookup
                if (categoryVM.categories.isNotEmpty) ...[
                  const Text(
                    'Category Lookup Examples:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Hot Goods ID: ${categoryVM.getCategoryIdByName('Hot Goods') ?? 'Not found'}',
                  ),
                  Text(
                    'TNG Voucher ID: ${categoryVM.getCategoryIdByName('TNG Voucher') ?? 'Not found'}',
                  ),
                  Text(
                    'Staff ID: ${categoryVM.getCategoryIdByName('Staff') ?? 'Not found'}',
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }
}

/// Example of direct service usage (without ViewModel)
class DirectServiceExample extends StatefulWidget {
  const DirectServiceExample({super.key});

  @override
  State<DirectServiceExample> createState() => _DirectServiceExampleState();
}

class _DirectServiceExampleState extends State<DirectServiceExample> {
  final LuckyDrawService _service = LuckyDrawService();
  List<LuckyDrawCategory> _categories = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchCategories();
  }

  Future<void> _fetchCategories() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final categories = await _service.getCategories();
      setState(() {
        _categories = categories;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Direct Service Example'),
        backgroundColor: const Color(0xFFFFBF00),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFFBF00)),
              ),
            )
          : _error != null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'Error: $_error',
                    style: const TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _fetchCategories,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            )
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Categories from Direct Service (${_categories.length})',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: ListView.builder(
                      itemCount: _categories.length,
                      itemBuilder: (context, index) {
                        final category = _categories[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            title: Text(category.categoryName),
                            subtitle: Text('ID: ${category.categoryId}'),
                            trailing: Text(
                              category.isAdult ? 'Adult' : 'General',
                              style: TextStyle(
                                color: category.isAdult
                                    ? Colors.red
                                    : Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _fetchCategories,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFFFBF00),
                      ),
                      child: const Text('Refresh Categories'),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}

// Example usage for Mega Prizes API integration

class MegaPrizesExample extends StatefulWidget {
  const MegaPrizesExample({super.key});

  @override
  State<MegaPrizesExample> createState() => _MegaPrizesExampleState();
}

class _MegaPrizesExampleState extends State<MegaPrizesExample> {
  late PrizeRevealVM _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = PrizeRevealVM();
    _initializeData();
  }

  Future<void> _initializeData() async {
    await _viewModel.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mega Prizes Example'),
        backgroundColor: const Color(0xFFD90019),
      ),
      body: Consumer<PrizeRevealVM>(
        builder: (context, vm, child) {
          return RefreshIndicator(
            onRefresh: () => vm.refreshAll(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // API Status
                  _buildApiStatus(vm),
                  const SizedBox(height: 20),

                  // Mega Prizes Display
                  _buildMegaPrizesDisplay(vm),
                  const SizedBox(height: 20),

                  // Raw Data Display
                  _buildRawDataDisplay(vm),
                  const SizedBox(height: 20),

                  // Actions
                  _buildActions(vm),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildApiStatus(PrizeRevealVM vm) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'API Status',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  vm.isMegaPrizesLoading
                      ? Icons.hourglass_empty
                      : Icons.check_circle,
                  color: vm.isMegaPrizesLoading ? Colors.orange : Colors.green,
                ),
                const SizedBox(width: 8),
                Text(
                  vm.isMegaPrizesLoading ? 'Loading...' : 'Loaded',
                  style: TextStyle(
                    color: vm.isMegaPrizesLoading
                        ? Colors.orange
                        : Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('Mega Prizes Count: ${vm.megaPrizes.length}'),
            Text('Products Count: ${vm.allProducts.length}'),
          ],
        ),
      ),
    );
  }

  Widget _buildMegaPrizesDisplay(PrizeRevealVM vm) {
    if (vm.isMegaPrizesLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    if (vm.megaPrizes.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Mega Prizes (No Data)',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              const Text(
                'No API data available - will show "No mega prizes" placeholders',
              ),
              const SizedBox(height: 8),
              // No default prizes - will show "No mega prizes" placeholders in the widget
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Mega Prizes (From API)',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            ...vm.megaPrizes.map((prize) => _buildPrizeItem(prize)),
          ],
        ),
      ),
    );
  }

  Widget _buildPrizeItem(MegaPrize prize) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          // Prize Image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              prize.productSkuImage,
              width: 60,
              height: 60,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 60,
                  height: 60,
                  color: Colors.grey[300],
                  child: const Icon(Icons.image_not_supported),
                );
              },
            ),
          ),
          const SizedBox(width: 12),

          // Prize Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  prize.productSkuName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text('ID: ${prize.id}'),
                Text('Points: ${prize.totalAmount}'),
                Text('Used: ${prize.usedAmount}'),
              ],
            ),
          ),

          // Join Button
          ElevatedButton(
            onPressed: () {
              _viewModel.simulateParticipate();
              // Note: In the actual MegaPrizes widget, this button now navigates to product details
              // and shows a success message automatically
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Joined the lucky draw! (Would navigate to product details)',
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFD90019),
              foregroundColor: Colors.white,
            ),
            child: const Text('JOIN'),
          ),
        ],
      ),
    );
  }

  Widget _buildRawDataDisplay(PrizeRevealVM vm) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Raw API Data',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                vm.megaPrizes.isEmpty
                    ? 'No data available'
                    : vm.megaPrizes
                          .map((p) => p.toJson().toString())
                          .join('\n'),
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions(PrizeRevealVM vm) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Actions', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => vm.refreshMegaPrizes(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFFBF00),
                      foregroundColor: Colors.black,
                    ),
                    child: const Text('Refresh Mega Prizes'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => vm.refreshAll(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFD90019),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Refresh All'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }
}

// Example of creating MegaPrize objects manually (for testing)
List<MegaPrize> createTestMegaPrizes() {
  return [
    MegaPrize(
      id: 1,
      productSpuId: 101,
      productSkuId: 1001,
      productSkuName: "Test Product 1",
      productSkuImage: "https://example.com/image1.jpg",
      usedAmount: 50,
      totalAmount: 1000,
    ),
    MegaPrize(
      id: 2,
      productSpuId: 102,
      productSkuId: 1002,
      productSkuName: "Test Product 2",
      productSkuImage: "https://example.com/image2.jpg",
      usedAmount: 25,
      totalAmount: 2000,
    ),
    MegaPrize(
      id: 3,
      productSpuId: 103,
      productSkuId: 1003,
      productSkuName: "Test Product 3",
      productSkuImage: "https://example.com/image3.jpg",
      usedAmount: 75,
      totalAmount: 3000,
    ),
  ];
}

// Example of API response parsing
Map<String, dynamic> parseApiResponse(Map<String, dynamic> apiResponse) {
  try {
    final status = apiResponse['status'];
    final message = apiResponse['message'];
    final data = apiResponse['data'] as List<dynamic>;
    final timestamp = apiResponse['timestamp'];

    if (status == 200 && data.isNotEmpty) {
      final megaPrizes = data.map((item) => MegaPrize.fromJson(item)).toList();

      return {
        'success': true,
        'megaPrizes': megaPrizes,
        'count': megaPrizes.length,
        'timestamp': timestamp,
      };
    } else {
      return {
        'success': false,
        'error': message ?? 'Unknown error',
        'status': status,
      };
    }
  } catch (e) {
    return {'success': false, 'error': 'Failed to parse API response: $e'};
  }
}
