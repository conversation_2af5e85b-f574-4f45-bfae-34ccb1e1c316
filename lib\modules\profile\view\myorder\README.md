# MyOrder Detail Views Implementation

This implementation provides new order detail views that use the updated `MyOrderDetail` model and service to fetch data from the actual API.

## Files Created/Modified

### New Files
1. **ToReceiveDetailVM.dart** - New ViewModel that uses `MyOrderDetail` model
2. **CompletedOrderDetailsNewView.dart** - New completed order detail view
3. **CancelledOrderDetailsNewView.dart** - New cancelled order detail view  
4. **ToShipNewView.dart** - New to-ship order detail view
5. **OrderDetailNavigator.dart** - Helper class for navigation and status management

### Modified Files
1. **MyOrderService.dart** - Updated `getMyOrderDetail` to return single `MyOrderDetail`
2. **MyOrderModel.dart** - Updated `OrderItem` and `MyOrderDetail` models to match API response
3. **ToReceiveView.dart** - Updated to use new ViewModel and data structure

## API Response Structure

The implementation expects the following API response structure from `/order/{id}`:

```json
{
  "status": 200,
  "message": "success",
  "data": {
    "address": {
      "addressId": 7,
      "postcode": "90000",
      "recipient": "Dexter",
      "country": "60",
      "mobile": "163461438",
      "detailedAddress": "Lot 1,Block A,First Floor, Mile 4, North Road, Taman Indah Jaya, 90000 Sandakan, Sabah"
    },
    "totalPrice": 11.89,
    "deliveryFee": 13.0,
    "cardDeduction": 0,
    "payableAmount": 24.89,
    "list": [
      {
        "productSpuId": 1,
        "productSkuId": 1,
        "productSkuImage": "https://...",
        "productSkuName": "Silicone Folding Camping Cup...",
        "productSkuPropertys": "",
        "quantity": 1,
        "salePrice": 11.89,
        "totalSalePrice": 11.89,
        "cardDeduction": 2,
        "totalCardDeduction": 2,
        "useCardDeduction": 0,
        "payableAmount": 11.89,
        "stock": 1
      }
    ]
  },
  "timestamp": 1755847181573
}
```

## Order States

The system supports the following order states:

- **-10**: Refunded
- **-1**: Cancelled  
- **0**: Waiting for payment
- **10**: Waiting for shipment (To Ship)
- **20**: Waiting for receipt (To Receive)
- **100**: Completed

## Usage

### Basic Navigation

```dart
import 'OrderDetailNavigator.dart';

// Navigate to appropriate detail view based on order state
OrderDetailNavigator.navigateToOrderDetail(
  context,
  orderId,      // int: Order ID
  orderState,   // int: Order state (-10, -1, 0, 10, 20, 100)
);
```

### Using Individual Views

```dart
// For To Receive orders (state: 20)
Navigator.push(context, MaterialPageRoute(
  builder: (context) => ToReceiveView(orderId: orderId),
));

// For Completed orders (state: 100)
Navigator.push(context, MaterialPageRoute(
  builder: (context) => CompletedOrderDetailsNewView(orderId: orderId),
));

// For Cancelled orders (state: -1)
Navigator.push(context, MaterialPageRoute(
  builder: (context) => CancelledOrderDetailsNewView(orderId: orderId),
));

// For To Ship orders (state: 10)
Navigator.push(context, MaterialPageRoute(
  builder: (context) => ToShipNewView(orderId: orderId),
));
```

### Integration with Existing Order Lists

Replace your existing order item tap handlers:

```dart
// Old way (using OrderModel)
onTap: () => Navigator.push(context, MaterialPageRoute(
  builder: (context) => ToReceiveView(order: orderModel),
)),

// New way (using order ID and state)
onTap: () => OrderDetailNavigator.navigateToOrderDetail(
  context,
  order.id,
  order.state,
),
```

## Features

### Data Loading
- Automatic API call to fetch order details using `getMyOrderDetail(orderId)`
- Loading states with progress indicators
- Error handling with retry functionality
- Pull-to-refresh support

### Mock Data
- Tracking information uses mock data (as requested)
- Address information comes from API response
- Product information comes from API response
- Pricing information comes from API response

### Responsive Design
- All views are responsive and adapt to different screen sizes
- Consistent styling across all detail views

### Status-Specific Features

**To Receive View:**
- Tracking timeline with mock data
- Delivery/pickup address information
- Action buttons: Help Support, Join Again

**Completed View:**
- Completion timestamp
- Action buttons: Help Support, Rate Order

**Cancelled View:**
- Cancellation information
- Action buttons: Help Support, Buy Again

**To Ship View:**
- Payment information
- Action buttons: Help Support, Cancel Order

## TODO Items

The following features need to be implemented:

1. **Rate Order functionality** (CompletedOrderDetailsNewView)
2. **Buy Again functionality** (CancelledOrderDetailsNewView)
3. **Cancel Order functionality** (ToShipNewView)
4. **Actual tracking API integration** (replace mock tracking data)
5. **Payment Pending view** (for state: 0)
6. **Refund view** (for state: -10)

## Testing

To test the implementation:

1. Use the `OrderListExample` widget in `OrderDetailNavigator.dart`
2. Or integrate with your existing order list using the provided patterns
3. Make sure your API endpoint `/order/{id}` returns data in the expected format

## Migration Notes

- The old `OrderModel` is still used in some places for backward compatibility
- New views use `MyOrderDetail` model which matches the actual API response
- Gradually migrate existing views to use the new pattern
- Update routing configuration to use order IDs instead of full order objects
