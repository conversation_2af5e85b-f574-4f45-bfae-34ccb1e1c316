import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/LuckyDrawProductDetailsVM.dart';
import '../model/LuckyDrawProductDetails.dart';

class LuckyDrawProductDetailsView extends StatefulWidget {
  final int productId;

  const LuckyDrawProductDetailsView({Key? key, required this.productId})
    : super(key: key);

  @override
  State<LuckyDrawProductDetailsView> createState() =>
      _LuckyDrawProductDetailsViewState();
}

class _LuckyDrawProductDetailsViewState
    extends State<LuckyDrawProductDetailsView> {
  late LuckyDrawProductDetailsVM _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = LuckyDrawProductDetailsVM();
    _loadProductDetails();
  }

  Future<void> _loadProductDetails() async {
    await _viewModel.loadProductDetails(widget.productId);
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Product Details'),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _viewModel.refresh,
            ),
          ],
        ),
        body: Consumer<LuckyDrawProductDetailsVM>(
          builder: (context, viewModel, child) {
            switch (viewModel.loadingState) {
              case ProductDetailsLoadingState.loading:
                return const Center(child: CircularProgressIndicator());

              case ProductDetailsLoadingState.error:
                return _buildErrorView(viewModel);

              case ProductDetailsLoadingState.success:
                return _buildProductDetailsView(viewModel);

              case ProductDetailsLoadingState.idle:
              default:
                return const Center(child: Text('No data available'));
            }
          },
        ),
      ),
    );
  }

  Widget _buildErrorView(LuckyDrawProductDetailsVM viewModel) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error Loading Product',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            viewModel.errorMessage ?? 'Unknown error occurred',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadProductDetails,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildProductDetailsView(LuckyDrawProductDetailsVM viewModel) {
    final product = viewModel.productDetails!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Image
          _buildProductImage(product),
          const SizedBox(height: 16),

          // Product Name
          _buildProductName(product),
          const SizedBox(height: 16),

          // Participation Status
          _buildParticipationStatus(viewModel),
          const SizedBox(height: 16),

          // Product Properties
          if (product.productSkuPropertys.isNotEmpty) ...[
            _buildProductProperties(product),
            const SizedBox(height: 16),
          ],

          // Product Details
          if (product.details.isNotEmpty) ...[
            _buildProductDetails(product),
            const SizedBox(height: 16),
          ],

          // Action Buttons
          _buildActionButtons(viewModel),
        ],
      ),
    );
  }

  Widget _buildProductImage(LuckyDrawProductDetails product) {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[200],
      ),
      child: product.productSkuImage.isNotEmpty
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                product.productSkuImage,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      size: 64,
                      color: Colors.grey,
                    ),
                  );
                },
              ),
            )
          : const Center(
              child: Icon(
                Icons.image_not_supported,
                size: 64,
                color: Colors.grey,
              ),
            ),
    );
  }

  Widget _buildProductName(LuckyDrawProductDetails product) {
    return Text(
      product.productSkuName,
      style: Theme.of(
        context,
      ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
    );
  }

  Widget _buildParticipationStatus(LuckyDrawProductDetailsVM viewModel) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: viewModel.isFullyBooked ? Colors.red[50] : Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: viewModel.isFullyBooked ? Colors.red : Colors.green,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                viewModel.isFullyBooked ? Icons.block : Icons.check_circle,
                color: viewModel.isFullyBooked ? Colors.red : Colors.green,
              ),
              const SizedBox(width: 8),
              Text(
                viewModel.participationText,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: viewModel.isFullyBooked ? Colors.red : Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: viewModel.participationRate / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              viewModel.isFullyBooked ? Colors.red : Colors.green,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${viewModel.productDetails!.usedAmount} / ${viewModel.productDetails!.totalAmount} slots filled',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildProductProperties(LuckyDrawProductDetails product) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Product Properties',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            product.productSkuPropertys,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildProductDetails(LuckyDrawProductDetails product) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Product Details',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            product.details,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(LuckyDrawProductDetailsVM viewModel) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: viewModel.hasAvailableSlots
                ? () {
                    // TODO: Implement participation logic
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Participation feature coming soon!'),
                      ),
                    );
                  }
                : null,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: viewModel.hasAvailableSlots
                  ? Colors.blue
                  : Colors.grey,
            ),
            child: Text(
              viewModel.isFullyBooked ? 'Fully Booked' : 'Participate Now',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        if (viewModel.hasAvailableSlots) ...[
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () {
                // TODO: Implement share logic
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Share feature coming soon!')),
                );
              },
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Share Product',
                style: TextStyle(fontSize: 16),
              ),
            ),
          ),
        ],
      ],
    );
  }

  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }
}
