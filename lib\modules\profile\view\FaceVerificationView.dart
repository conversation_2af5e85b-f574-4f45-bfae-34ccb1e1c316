import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';
import 'package:luckymall/router/navigation_helper.dart';
// import 'package:go_router/go_router.dart';
import '../view-model/FaceVerificationState.dart';
import 'dart:io';

class FaceVerificationView extends StatefulWidget {
  const FaceVerificationView({super.key});

  @override
  State<FaceVerificationView> createState() => _FaceVerificationViewState();
}

class _FaceVerificationViewState extends State<FaceVerificationView>
    with TickerProviderStateMixin {
  bool _isLoading = false;
  bool _isVerified = false;
  // bool _isAlreadyVerified = false; // handled via global state
  bool _showUserAgreement = true;
  bool _showCamera = false;
  bool _showScanning = false;
  // bool _showSuccess = false; // overlay uses _isVerified
  File? _capturedImage;
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;

  // Animation controllers
  late AnimationController _scanAnimationController;
  late AnimationController _pulseAnimationController;
  late Animation<double> _scanAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeCamera();
  }

  void _initializeAnimations() {
    _scanAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scanAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _scanAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(
        parent: _pulseAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  void _initializeCamera() async {
    try {
      _cameras = await availableCameras();
      if (_cameras != null && _cameras!.isNotEmpty) {
        // Find front camera
        CameraDescription? frontCamera;
        for (var camera in _cameras!) {
          if (camera.lensDirection == CameraLensDirection.front) {
            frontCamera = camera;
            break;
          }
        }

        if (frontCamera != null) {
          _cameraController = CameraController(
            frontCamera,
            ResolutionPreset.medium,
            enableAudio: false,
            imageFormatGroup: ImageFormatGroup.jpeg,
          );

          try {
            await _cameraController!.initialize().timeout(
              const Duration(seconds: 10),
              onTimeout: () {
                throw Exception('Camera initialization timeout');
              },
            );
            if (mounted) {
              setState(() {});
            }
          } catch (e) {
            print('Error initializing camera: $e');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Camera initialization failed: $e')),
              );
            }
          }
        } else {
          print('No front camera found');
        }
      }
    } catch (e) {
      print('Error getting cameras: $e');
    }
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    _scanAnimationController.dispose();
    _pulseAnimationController.dispose();
    super.dispose();
  }

  // Camera controller is disposed in dispose(); keep helper removed to avoid unused warnings

  void _handleUserAgreement() async {
    // Check camera permission first
    PermissionStatus status = await Permission.camera.status;

    if (status.isDenied) {
      status = await Permission.camera.request();
    }

    if (status.isPermanentlyDenied) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Camera permission is required. Please enable it in settings.',
          ),
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    if (!status.isGranted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Camera permission denied'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    setState(() {
      _showUserAgreement = false;
      _showCamera = true;
    });
  }

  void _startScanning() async {
    setState(() {
      _showScanning = true;
    });

    // Start animations
    _scanAnimationController.repeat();
    _pulseAnimationController.repeat(reverse: true);

    // Simulate scanning process
    await Future.delayed(const Duration(seconds: 3));

    // Capture photo automatically
    _capturePhoto();
  }

  void _capturePhoto() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Camera not ready. Please wait.')),
      );
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      final XFile photo = await _cameraController!.takePicture();

      if (mounted) {
        setState(() {
          _capturedImage = File(photo.path);
          _showCamera = false;
          _showScanning = false;
          _isLoading = false;
        });

        // Stop animations
        _scanAnimationController.stop();
        _pulseAnimationController.stop();
      }
    } catch (e) {
      print('Error capturing photo: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _showScanning = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error capturing photo: $e')));
      }
    }
  }

  void _handleVerify() async {
    if (_capturedImage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please capture your face first')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate submission process
    await Future.delayed(const Duration(seconds: 3));

    setState(() {
      _isLoading = false;
      _isVerified = true;
    });

    // Mark as pending approval globally
    FaceVerificationStateManager.instance.setPendingApproval();

    // Show pending approval message for 3 seconds then navigate
    await Future.delayed(const Duration(milliseconds: 3000));

    if (mounted) {
      // Since this might be the initial route, always go to profile
      NavigationHelper.goToProfile(context);
    }
  }

  void _retakePhoto() {
    setState(() {
      _capturedImage = null;
      _showCamera = true;
    });
  }

  // void _closeCamera() {}

  @override
  Widget build(BuildContext context) {
    final verificationStatus =
        FaceVerificationStateManager.instance.statusNotifier.value;

    // If already verified, show a completed screen instead of the flow
    if (verificationStatus == FaceVerificationStatus.verified) {
      return Scaffold(
        appBar: CustomAppBar(
          title: 'Face Verification',
          onBack: () => NavigationHelper.goToProfile(context),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 96,
                  height: 96,
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.15),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.verified,
                    color: Colors.green,
                    size: 56,
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'You\'re already verified',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.amber,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'Your face verification has been completed successfully. No further action is required.',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 52,
                  child: ElevatedButton(
                    onPressed: () => NavigationHelper.goToProfile(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.amber,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Back to Profile',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Face Verification',
        onBack: () {
          // Navigate back to profile
          NavigationHelper.goToProfile(context);
        },
      ),
      body: Stack(
        children: [
          SafeArea(
            child: Column(
              children: [
                if (_showUserAgreement) ...[
                  // User Agreement Section
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        children: [
                          const SizedBox(height: 40),
                          // Agreement Icon
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: Color(0xFFFFF3CD),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Icons.description,
                              size: 40,
                              color: Colors.amber,
                            ),
                          ),
                          const SizedBox(height: 32),
                          // Title
                          const Text(
                            'User Agreement',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 28,
                              color: Colors.amber,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          // Subtitle
                          const Text(
                            'Please read and agree to our terms before proceeding to get TNG RM5 reward',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                              height: 1.5,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 32),
                          // Agreement Content
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.grey[50],
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.grey.withOpacity(0.3),
                                ),
                              ),
                              child: SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Face Verification Terms and Conditions',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                        color: Colors.amber,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    const Text(
                                      'By proceeding with face verification, you agree to the following terms:',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    _buildAgreementPoint(
                                      '1. Data Collection',
                                      'We will collect and process your facial biometric data for verification purposes only.',
                                    ),
                                    _buildAgreementPoint(
                                      '2. Data Security',
                                      'Your facial data will be encrypted and stored securely using industry-standard security measures.',
                                    ),
                                    _buildAgreementPoint(
                                      '3. Data Usage',
                                      'Your facial data will only be used for account verification and security purposes.',
                                    ),
                                    _buildAgreementPoint(
                                      '4. Data Retention',
                                      'Your facial data will be retained only as long as necessary for verification purposes.',
                                    ),
                                    _buildAgreementPoint(
                                      '5. Data Sharing',
                                      'We will never share your facial data with third parties without your explicit consent.',
                                    ),
                                    _buildAgreementPoint(
                                      '6. Consent',
                                      'By clicking "Agree", you consent to the collection and processing of your facial biometric data.',
                                    ),
                                    const SizedBox(height: 16),
                                    Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Color(0xFFFFF3CD),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: Colors.amber.withOpacity(0.3),
                                        ),
                                      ),
                                      child: const Row(
                                        children: [
                                          Icon(
                                            Icons.info_outline,
                                            color: Colors.amber,
                                            size: 20,
                                          ),
                                          SizedBox(width: 8),
                                          Expanded(
                                            child: Text(
                                              'Your privacy and data security are our top priorities. We are committed to protecting your personal information.',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.amber,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 24),
                          // Agree Button
                          SizedBox(
                            width: double.infinity,
                            height: 56,
                            child: ElevatedButton(
                              onPressed: _handleUserAgreement,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.amber,
                                foregroundColor: Colors.black,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 0,
                              ),
                              child: const Text(
                                'Agree',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ] else if (_showCamera) ...[
                  // TNG-style Camera Section
                  Expanded(
                    child: Stack(
                      children: [
                        // Camera Preview
                        if (_cameraController != null &&
                            _cameraController!.value.isInitialized)
                          CameraPreview(_cameraController!)
                        else
                          Container(
                            color: Colors.black,
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.amber,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'Initializing camera...',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                        // TNG-style Camera Overlay
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.amber, width: 2),
                            ),
                            child: Column(
                              children: [
                                // Top section
                                Container(
                                  padding: const EdgeInsets.all(20),
                                  child: Row(
                                    children: [
                                      const SizedBox(width: 80),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 10,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.black.withOpacity(0.7),
                                          borderRadius: BorderRadius.circular(
                                            25,
                                          ),
                                        ),
                                        child: const Text(
                                          'Position your face',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                      const Spacer(),
                                      const SizedBox(width: 50),
                                    ],
                                  ),
                                ),

                                const Spacer(),

                                // Center scanning area
                                if (_showScanning) ...[
                                  Center(
                                    child: AnimatedBuilder(
                                      animation: _scanAnimation,
                                      builder: (context, child) {
                                        return Container(
                                          width: 250,
                                          height: 250,
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                              color: Colors.amber,
                                              width: 3,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              20,
                                            ),
                                          ),
                                          child: Stack(
                                            children: [
                                              // Scanning line
                                              Positioned(
                                                top: _scanAnimation.value * 250,
                                                left: 0,
                                                right: 0,
                                                child: Container(
                                                  height: 2,
                                                  decoration: BoxDecoration(
                                                    gradient: LinearGradient(
                                                      colors: [
                                                        Colors.transparent,
                                                        Colors.amber,
                                                        Colors.transparent,
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              // Pulse animation
                                              Center(
                                                child: AnimatedBuilder(
                                                  animation: _pulseAnimation,
                                                  builder: (context, child) {
                                                    return Transform.scale(
                                                      scale:
                                                          _pulseAnimation.value,
                                                      child: Container(
                                                        width: 60,
                                                        height: 60,
                                                        decoration:
                                                            BoxDecoration(
                                                              color: Colors
                                                                  .amber
                                                                  .withOpacity(
                                                                    0.3,
                                                                  ),
                                                              shape: BoxShape
                                                                  .circle,
                                                            ),
                                                        child: const Icon(
                                                          Icons.face,
                                                          color: Colors.white,
                                                          size: 30,
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ] else ...[
                                  // Static face frame
                                  Center(
                                    child: Container(
                                      width: 250,
                                      height: 250,
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Colors.amber,
                                          width: 3,
                                        ),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: const Center(
                                        child: Icon(
                                          Icons.face,
                                          color: Colors.white,
                                          size: 60,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],

                                const Spacer(),

                                // Bottom section
                                Container(
                                  padding: const EdgeInsets.all(24),
                                  child: Column(
                                    children: [
                                      if (!_showScanning) ...[
                                        const Text(
                                          'Ready to scan your face',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        const SizedBox(height: 20),
                                        SizedBox(
                                          width: 80,
                                          height: 80,
                                          child: ElevatedButton(
                                            onPressed: _startScanning,
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.amber,
                                              foregroundColor: Colors.black,
                                              shape: const CircleBorder(),
                                              padding: EdgeInsets.zero,
                                            ),
                                            child: const Icon(
                                              Icons.camera_alt,
                                              size: 40,
                                            ),
                                          ),
                                        ),
                                      ] else ...[
                                        const Text(
                                          'Scanning in progress...',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        const SizedBox(height: 20),
                                        const CircularProgressIndicator(
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                Colors.amber,
                                              ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ] else ...[
                  // TNG-style Verification Section (after photo capture)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        children: [
                          const SizedBox(height: 20),
                          // Title
                          const Text(
                            'Photo Captured Successfully',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                              color: Colors.amber,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 20),
                          const Text(
                            'Your face has been captured. You can retake or proceed with verification.',
                            style: TextStyle(fontSize: 16, color: Colors.grey),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 32),
                          // Image Display Area
                          Container(
                            width: double.infinity,
                            height: 300,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Colors.grey.withOpacity(0.3),
                                width: 2,
                              ),
                            ),
                            child: _capturedImage != null
                                ? ClipRRect(
                                    borderRadius: BorderRadius.circular(14),
                                    child: Image.file(
                                      _capturedImage!,
                                      fit: BoxFit.cover,
                                    ),
                                  )
                                : const Center(
                                    child: Text('No photo captured'),
                                  ),
                          ),
                          const SizedBox(height: 32),
                          // Action Buttons
                          Row(
                            children: [
                              Expanded(
                                child: SizedBox(
                                  height: 56,
                                  child: OutlinedButton(
                                    onPressed: _retakePhoto,
                                    style: OutlinedButton.styleFrom(
                                      backgroundColor: Colors.grey[100],
                                      foregroundColor: Colors.amber,
                                      side: BorderSide.none,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    child: const Text(
                                      'Retake',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: SizedBox(
                                  height: 56,
                                  child: ElevatedButton(
                                    onPressed: _handleVerify,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.amber,
                                      foregroundColor: Colors.black,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      elevation: 0,
                                    ),
                                    child: const Text(
                                      'Submit',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          // Security Message
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Color(0xFFFFF3CD),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.amber.withOpacity(0.2),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.security,
                                  color: Colors.amber,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    'Your face data is encrypted and secure',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.amber,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          // Modal overlay for loading and verification
          if (_isLoading || _isVerified)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(32),
                  margin: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (_isLoading) ...[
                        const CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.amber,
                          ),
                        ),
                        const SizedBox(height: 24),
                        const Text(
                          'Submitting for approval...',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.amber,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Please wait while we submit your face verification for review',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                          textAlign: TextAlign.center,
                        ),
                      ] else if (_isVerified) ...[
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: Colors.amber,
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: const Icon(
                            Icons.hourglass_empty,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                        const SizedBox(height: 24),
                        const Text(
                          'Submission Successful!',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.amber,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Your face verification has been submitted and is pending approval. You will receive a notification once approved.',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Unused helpers removed

  Widget _buildAgreementPoint(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.amber,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}
