import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:luckymall/router/auth_guard.dart';
import 'package:luckymall/modules/authorization/service/AuthHttpService.dart';
import 'package:luckymall/modules/authorization/widgets/login_header.dart';
import 'package:luckymall/modules/authorization/widgets/phone_number_field.dart';
import 'package:luckymall/modules/authorization/widgets/otp_field.dart';
import 'package:luckymall/modules/authorization/widgets/login_actions_otp.dart';
import 'package:luckymall/modules/authorization/widgets/login_button.dart';
import 'package:luckymall/modules/authorization/widgets/register_prompt.dart';

class OtpLoginView extends StatefulWidget {
  const OtpLoginView({super.key});

  @override
  State<OtpLoginView> createState() => _OtpLoginViewState();
}

class _OtpLoginViewState extends State<OtpLoginView> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();
  bool _isLoading = false;
  String _phoneComplete = '';
  int _countryCode =
      60; // Default to MY as per IntlPhoneField initialCountryCode

  @override
  void dispose() {
    _phoneController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    final String mobile = _phoneComplete.trim();
    final String code = _otpController.text.trim();

    if (mobile.isEmpty || code.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter phone and OTP code')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final response = await AuthHttpService.instance.loginWithCode(
      country: _countryCode,
      mobile: mobile,
      code: code,
    );

    // ignore: avoid_print
    print('[OtpLoginView] loginWithCode response => ' + response.toString());

    int status = -1;
    final dynamic statusRaw = response['status'];
    if (statusRaw is int) {
      status = statusRaw;
    } else if (statusRaw is String) {
      status = int.tryParse(statusRaw) ?? -1;
    }
    final String message = response['message']?.toString() ?? '';

    final bool isSuccess = status == 0 || status == 200;

    if (isSuccess) {
      final dynamic dataField = response['data'];
      String? token;
      if (dataField is String) {
        token = dataField;
      } else if (dataField is Map<String, dynamic>) {
        token =
            (dataField['token'] ??
                    dataField['jwt'] ??
                    dataField['jwtToken'] ??
                    dataField['accessToken'])
                ?.toString();
      } else {
        token = dataField?.toString();
      }
      await AuthService.setJwtToken(token, response['timestamp']);
      await AuthService.loginSimple();
      if (!mounted) return;
      context.go('/blind-box/shop');
    } else {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message.isEmpty ? 'Login failed' : message)),
      );
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _handleResendOtp() {
    // Navigate to resend OTP screen or handle resend logic
    // Navigator.push(context, MaterialPageRoute(builder: (context) => ResendOtpView()));
  }

  void _handleRegister() {
    // Navigate to register screen
    context.go('/register');
  }

  Future<void> _handleSendOtpCode() async {
    if (_phoneComplete.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your phone number')),
      );
      return;
    }

    final response = await AuthHttpService.instance.sendCode(
      country: _countryCode,
      mobile: _phoneComplete.trim(),
    );
    print(
      '[OtpLoginView] Sending code => country: ' +
          _countryCode.toString() +
          ', mobile: ' +
          _phoneComplete.trim(),
    );

    final int status = (response['status'] is int)
        ? response['status'] as int
        : -1;
    final String message = response['message']?.toString() ?? '';
    if (status == 0) {
      final dynamic dataField = response['data'];
      String? token;
      if (dataField is String) {
        token = dataField;
      } else if (dataField is Map<String, dynamic>) {
        token =
            dataField['access_token']?.toString() ??
            dataField['token']?.toString() ??
            dataField['accessToken']?.toString();
      }
      await AuthService.setJwtToken(token, response['timestamp']);
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Verification code sent')));
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message.isEmpty ? 'Failed to send code' : message),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFFFFFFF), Color(0xFFFCD255)],
          ),
        ),
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height,
            ),
            child: IntrinsicHeight(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const LoginHeader(),
                  PhoneNumberField(
                    controller: _phoneController,
                    onChanged: (value) {
                      _phoneComplete = value;
                    },
                    onCountryCodeChanged: (dial) {
                      final normalized = dial.replaceAll('+', '');
                      final parsed = int.tryParse(normalized);
                      if (parsed != null) {
                        _countryCode = parsed;
                      }
                    },
                  ),
                  const SizedBox(height: 20),
                  OtpField(
                    controller: _otpController,
                    onChanged: (value) {
                      // Handle OTP changes
                    },
                    onSendCode: _handleSendOtpCode,
                  ),
                  const SizedBox(height: 12),
                  LoginActions(onForgotPassword: _handleResendOtp),
                  const SizedBox(height: 24),
                  LoginButton(
                    onPressed: () {
                      _handleLogin();
                    },
                    isLoading: _isLoading,
                  ),
                  const SizedBox(height: 24),
                  RegisterPrompt(onRegisterTap: _handleRegister),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
