import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import '../model/OpenSoonProduct.dart';

class OpenSoonCard extends StatelessWidget {
  final List<OpenSoonProduct> products;
  final VoidCallback? onProductTap;

  const OpenSoonCard({super.key, required this.products, this.onProductTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      child: Stack(
        children: [
          // Main yellow container
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: const Color(0xFFFFBF00), // Bright yellow background
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const SizedBox(height: 20), // Space for overlapping banner
                  // White inner container
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          offset: const Offset(0, 2),
                          blurRadius: 8,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          // Horizontally scrollable product cards or empty state message
                          SizedBox(
                            height: 200, // Fixed height for the scrollable area
                            child: products.isEmpty
                                ? _buildEmptyState()
                                : ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    itemCount: products.length,
                                    itemBuilder: (context, index) {
                                      return Container(
                                        width: 160, // Fixed width for each card
                                        margin: const EdgeInsets.only(
                                          right: 12,
                                        ),
                                        child: _buildProductCard(
                                          products[index],
                                        ),
                                      );
                                    },
                                  ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // "Opening Soon" banner (overlapping)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFD700), // Dark yellow
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      offset: const Offset(0, 2),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: const Text(
                  'Opening Soon',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.schedule_outlined,
                  size: 32,
                  color: Colors.grey[500],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'No Opening Soon Products',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Check back later for new products\nthat will be available soon!',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  height: 1.3,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductCard(OpenSoonProduct product) {
    final int remainingPoints = product.totalAmount - product.usedAmount;
    final double progress = product.usedAmount / product.totalAmount;
    return GestureDetector(
      onTap: onProductTap,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFFFF8DC), // Light yellow/cream background
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8), // Space for overlapping label
                // Product image area
                Container(
                  height: 80,
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Stack(
                      children: [
                        // Product image
                        CachedNetworkImage(
                          imageUrl: product.productSkuImage,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          placeholder: (context, url) => Container(
                            color: Colors.grey[100],
                            child: const Center(
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Color(0xFFFFBF00),
                              ),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey[200],
                            child: Icon(
                              Icons.image_not_supported,
                              color: Colors.grey[400],
                              size: 30,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Product details with flex to push points to bottom
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Product name
                        Text(
                          product.productSkuName,
                          style: const TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const Spacer(), // This pushes the points to the bottom
                        // Remaining points above point value
                        Text(
                          'Remaining: ${remainingPoints > 0 ? remainingPoints : 0}',
                          style: const TextStyle(
                            fontSize: 10,
                            color: Color.fromARGB(255, 117, 117, 117),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '${product.totalAmount} Pts',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),

                        const SizedBox(height: 6),

                        // Progress bar (dynamic)
                        Container(
                          height: 4,
                          decoration: BoxDecoration(
                            color: const Color(
                              0xFFFFF8DC,
                            ), // Light yellow/cream
                            borderRadius: BorderRadius.circular(2),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: progress.clamp(0.0, 1.0),
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(2),
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFFA500), // Bright orange
                                    Color(0xFFFF8C00), // Darker orange
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // "COMING SOON" label (overlapping)
            Positioned(
              top: 0,
              left: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'COMING SOON',
                  style: TextStyle(
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// OpenSoonProduct moved to lib/modules/luckydraw/model/OpenSoonProduct.dart

class OpenSoonCardShimmer extends StatelessWidget {
  const OpenSoonCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    Color baseColor = Colors.grey[300]!;
    Color highlightColor = Colors.grey[100]!;
    Widget shimmerBox({
      double? width,
      double? height,
      BorderRadius? borderRadius,
      BoxShape shape = BoxShape.rectangle,
    }) {
      return Shimmer.fromColors(
        baseColor: baseColor,
        highlightColor: highlightColor,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: baseColor,
            borderRadius: shape == BoxShape.circle
                ? null
                : (borderRadius ?? BorderRadius.circular(4)),
            shape: shape,
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      child: Stack(
        children: [
          // Main yellow container
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: const Color(0xFFFFBF00),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  // White inner container
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          offset: const Offset(0, 2),
                          blurRadius: 8,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          SizedBox(
                            height: 200,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: 3,
                              itemBuilder: (context, index) {
                                return Container(
                                  width: 160,
                                  margin: const EdgeInsets.only(right: 12),
                                  child: _OpenSoonProductShimmer(),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // "Opening Soon" banner (overlapping, NOT shimmered)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFD700),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      offset: const Offset(0, 2),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: const Text(
                  'Opening Soon',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _OpenSoonProductShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    Color baseColor = Colors.grey[300]!;
    Color highlightColor = Colors.grey[100]!;
    Widget shimmerBox({
      double? width,
      double? height,
      BorderRadius? borderRadius,
      BoxShape shape = BoxShape.rectangle,
    }) {
      return Shimmer.fromColors(
        baseColor: baseColor,
        highlightColor: highlightColor,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: baseColor,
            borderRadius: shape == BoxShape.circle
                ? null
                : (borderRadius ?? BorderRadius.circular(4)),
            shape: shape,
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 245, 245, 238),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              shimmerBox(
                width: double.infinity,
                height: 80,
                borderRadius: BorderRadius.circular(8),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      shimmerBox(
                        width: 100,
                        height: 12,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      const Spacer(),
                      shimmerBox(
                        width: 60,
                        height: 10,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      shimmerBox(
                        width: 80,
                        height: 12,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      const SizedBox(height: 6),
                      shimmerBox(
                        width: double.infinity,
                        height: 4,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          // "COMING SOON" label (overlapping)
          Positioned(
            top: 0,
            left: 8,
            child: shimmerBox(
              width: 60,
              height: 18,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ],
      ),
    );
  }
}

// Note: This widget is now designed to work with real API data
// Use OpenSoonVM to fetch real opening soon products from the API
// Example usage in a real view:
//
// class MyRealView extends StatefulWidget {
//   @override
//   _MyRealViewState createState() => _MyRealViewState();
// }
//
// class _MyRealViewState extends State<MyRealView> {
//   late final OpenSoonVM _viewModel;
//
//   @override
//   void initState() {
//     super.initState();
//     _viewModel = OpenSoonVM();
//     _viewModel.addListener(_onViewModelChanged);
//     _viewModel.fetchOpeningSoonProducts();
//   }
//
//   @override
//   void dispose() {
//     _viewModel.removeListener(_onViewModelChanged);
//     super.dispose();
//   }
//
//   void _onViewModelChanged() {
//     if (mounted) setState(() {});
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     if (_viewModel.isLoading) {
//       return CircularProgressIndicator();
//     }
//
//     if (_viewModel.errorMessage != null) {
//       return Text('Error: ${_viewModel.errorMessage}');
//     }
//
//     return OpenSoonCard(
//       products: _viewModel.productsByProgress,
//       onProductTap: () {
//         // Handle real product tap
//       },
//     );
//   }
// }
