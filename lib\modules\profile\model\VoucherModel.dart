enum VoucherStatus { active, used, expired }

class Voucher {
  final String amount;
  final String minSpend;
  final String validDate;
  final VoucherStatus status;

  Voucher({
    required this.amount,
    required this.minSpend,
    required this.validDate,
    required this.status,
  });

  factory Voucher.fromJson(Map<String, dynamic> json) {
    return Voucher(
      amount: json['amount'],
      minSpend: json['minSpend'],
      validDate: json['validDate'],
      status: VoucherStatus.values.firstWhere(
        (e) => e.toString() == 'VoucherStatus.${json['status']}',
        orElse: () => VoucherStatus.active,
      ),
    );
  }
}
