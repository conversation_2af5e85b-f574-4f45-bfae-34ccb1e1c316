import 'package:flutter/material.dart';
import '../model/MyOrderModel.dart';
import '../service/MyOrderService.dart';

class MyOrderVM extends ChangeNotifier {
  // State variables
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  List<MyOrderModel> _allOrders = [];
  List<MyOrderModel> get allOrders => _allOrders;

  List<MyOrderModel> _filteredOrders = [];
  List<MyOrderModel> get filteredOrders => _filteredOrders;

  int? _selectedStatus;
  int? get selectedStatus => _selectedStatus;

  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  Map<int, int> _orderCounts = {};
  Map<int, int> get orderCounts => _orderCounts;

  // Initialize and load orders
  MyOrderVM() {
    loadOrders();
  }

  // Load all orders from service
  Future<void> loadOrders() async {
    _setLoading(true);
    _clearError();

    try {
      _allOrders = await myOrderService.getMyOrder(null, null, null);
      _updateOrderCounts();
      _applyStatusFilter();
    } catch (e) {
      _setError('Failed to load orders: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Filter orders by status
  void selectStatus(int? status) {
    if (_selectedStatus == status) return;

    _selectedStatus = status;
    _applyStatusFilter();
    notifyListeners();
  }

  // Apply the current status filter
  void _applyStatusFilter() {
    if (_selectedStatus == null) {
      _filteredOrders = List.from(_allOrders);
    } else {
      _filteredOrders = _allOrders
          .where((order) => order.state == _selectedStatus)
          .toList();
    }

    // Sort by creation time (newest first)
    _filteredOrders.sort((a, b) {
      if (a.createdTime == null || b.createdTime == null) return 0;
      return b.createdTime!.compareTo(a.createdTime!);
    });
  }

  // Update order counts for tab badges
  void _updateOrderCounts() {
    _orderCounts = {};

    // Define all possible order states
    final allStates = [
      -1,
      0,
      10,
      20,
      100,
    ]; // Cancelled, To Pay, To Ship, To Receive, Completed

    for (var state in allStates) {
      _orderCounts[state] = _allOrders
          .where((order) => order.state == state)
          .length;
    }
  }

  // Cancel an order
  Future<bool> cancelOrder(int orderId) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement cancel order API call when available
      // For now, we'll just update the order status locally
      final orderIndex = _allOrders.indexWhere((order) => order.id == orderId);
      if (orderIndex != -1) {
        // Create a new order with cancelled status
        final cancelledOrder = MyOrderModel(
          id: _allOrders[orderIndex].id,
          source: _allOrders[orderIndex].source,
          orderType: _allOrders[orderIndex].orderType,
          totalPrice: _allOrders[orderIndex].totalPrice,
          state: -1, // Cancelled status
          isAppraise: _allOrders[orderIndex].isAppraise,
          expiredTime: _allOrders[orderIndex].expiredTime,
          createdTime: _allOrders[orderIndex].createdTime,
          orderItems: _allOrders[orderIndex].orderItems,
        );

        _allOrders[orderIndex] = cancelledOrder;
        _updateOrderCounts();
        _applyStatusFilter();
      }
      return true;
    } catch (e) {
      _setError('Failed to cancel order: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Request refund for an order
  Future<bool> requestRefund(int orderId, String reason) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement refund request API call when available
      // For now, we'll just show success
      return true;
    } catch (e) {
      _setError('Failed to request refund: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get order by ID
  MyOrderModel? getOrderById(int orderId) {
    try {
      return _allOrders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      return null;
    }
  }

  // Refresh orders
  Future<void> refreshOrders() async {
    await loadOrders();
  }

  // Handle payment action
  void handlePayment(int orderId) {
    // In a real app, this would navigate to payment gateway
    debugPrint('Payment initiated for order: $orderId');
  }

  // Handle buy again action
  void handleBuyAgain(MyOrderModel order) {
    // In a real app, this would add the product to cart or navigate to product page
    debugPrint('Buy again for order: ${order.id}');
  }

  // Handle help support action
  void handleHelpSupport(int orderId) {
    // In a real app, this would navigate to support chat or contact page
    debugPrint('Help support for order: $orderId');
  }

  // Handle rate action
  void handleRate(int orderId) {
    // In a real app, this would navigate to rating page
    debugPrint('Rate order: $orderId');
  }

  // Handle share action
  void handleShare(MyOrderModel order) {
    // In a real app, this would open sharing options
    debugPrint('Share order: ${order.id}');
  }

  // Utility methods
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Get formatted status counts for display
  String getStatusCountText(int status) {
    final count = _orderCounts[status] ?? 0;
    return count > 0 ? ' ($count)' : '';
  }

  // Check if there are orders for a specific status
  bool hasOrdersForStatus(int status) {
    return (_orderCounts[status] ?? 0) > 0;
  }

  // Get orders count for all statuses
  int get totalOrdersCount {
    return _allOrders.length;
  }

  // Get status display name
  String getStatusDisplayName(int status) {
    switch (status) {
      case -1:
        return 'Cancelled';
      case 0:
        return 'To Pay';
      case 10:
        return 'To Ship';
      case 20:
        return 'To Receive';
      case 100:
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  // Get status color
  Color getStatusColor(int status) {
    switch (status) {
      case -1:
        return Colors.red;
      case 0:
        return Colors.orange;
      case 10:
        return Colors.blue;
      case 20:
        return Colors.purple;
      case 100:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
