import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; 


class PriceAndQuantitySection extends StatelessWidget {
  final int quantity;
  final double unitPrice;
  final int minQuantity;
  final int stepQuantity;
  final void Function(int) onQuantityChanged;

  const PriceAndQuantitySection({
    super.key,
    required this.quantity,
    required this.unitPrice,
    required this.onQuantityChanged,
    required this.minQuantity,
    required this.stepQuantity,
  });

  // Message dialog for Maximum quantity over 50,000
  void _showMaxDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (_) => Dialog(
        backgroundColor: Colors.transparent,
        child: LayoutBuilder(
          builder: (context, constraints) {
            final double maxWidth = MediaQuery.of(context).size.width * 0.85;
            final double maxHeight = MediaQuery.of(context).size.height * 0.6;

            return ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: maxWidth,
                maxHeight: maxHeight,
              ),
              child: SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFFFFE082), Color(0xFFFFF8E1)],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.orangeAccent, width: 2),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.orangeAccent,
                        blurRadius: 20,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.warning_amber_rounded,
                        size: 60,
                        color: Colors.deepOrange,
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        "Whoa there!",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Colors.deepOrange,
                        ),
                      ),
                      const SizedBox(height: 10),
                      const Text(
                        "You can only purchase up to 50,000 cards.",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 20),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.deepOrange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          onPressed: () => Navigator.pop(context),
                          child: const Text(
                            "Got it!",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              letterSpacing: 1.2,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  void _handleDecrease(BuildContext context) {
    if (quantity <= minQuantity) {
      showDialog(
    context: context,
    barrierDismissible: true,
    builder: (_) {
      return Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color.fromARGB(255, 248, 234, 189), Color.fromRGBO(255, 255, 255, 1)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: const [
              BoxShadow(
                color: Colors.white,
                blurRadius: 30,
                spreadRadius: 5,
                offset: Offset(0, 10),
              ),
              BoxShadow(
                color: Color.fromRGBO(255, 191, 0, 1),
                blurRadius: 12,
                spreadRadius: -6,
                offset: Offset(0, 0),
              ),
            ],
            border: Border.all(color: const Color.fromRGBO(255, 191, 0, 1), width: 1),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.info_outline_rounded,
                  color: Color.fromRGBO(255, 12, 69, 1), size: 48),
              const SizedBox(height: 16),
              const Text(
                "Minimum Purchase Required",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 10),
              const Text(
                "You must purchase at least 5 items to continue.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: () => Navigator.pop(context),
                  child: const Text(
                    "Okay",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
    } else {
      onQuantityChanged(quantity - stepQuantity);
    }
  }

  @override
  Widget build(BuildContext context) {
    final TextEditingController controller =
        TextEditingController(text: quantity.toString());

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 4)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                "RM ${unitPrice.toStringAsFixed(2)}",
                style: const TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              const Spacer(),
              SizedBox(
                height: 40,
                child: Row(
                  children: [
                    _QuantityButton(
                      icon: Icons.remove,
                      onTap: () => _handleDecrease(context),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(6),
                        bottomLeft: Radius.circular(6),
                      ),
                      isDisabledVisual: quantity <= 5,
                    ),
                    SizedBox(
                      width: 50,
                      height: 40,
                      child: TextFormField(
                        controller: controller,
                        keyboardType: TextInputType.number,
                        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                        decoration: const InputDecoration(
                          contentPadding: EdgeInsets.symmetric(vertical: 10),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.zero,
                          ),
                        ),
                        onFieldSubmitted: (val) {
                          final int? newVal = int.tryParse(val);

                          if (newVal != null) {
                            if (newVal < 5) {
                              _handleDecrease(context); // Will show dialog
                            } else if (newVal > 50000) {
                              _showMaxDialog(context);
                            } else {
                              onQuantityChanged(newVal);
                            }
                          }
                        },
                      ),
                    ),
                    _QuantityButton(
                      icon: Icons.add,
                      onTap: () =>  {
                        if (quantity >= 50000) {
                          _showMaxDialog(context)
                        } else {
                          onQuantityChanged(quantity + stepQuantity)
                        }
                      },
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(6),
                        bottomRight: Radius.circular(6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            "Minimum Purchase: $minQuantity Items",
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}

class _QuantityButton extends StatefulWidget {
  final IconData icon;
  final VoidCallback onTap;
  final BorderRadius borderRadius;
  final bool isDisabledVisual;

  const _QuantityButton({
    required this.icon,
    required this.onTap,
    required this.borderRadius,
    this.isDisabledVisual = false,
  });

  @override
  State<_QuantityButton> createState() => _QuantityButtonState();
}

class _QuantityButtonState extends State<_QuantityButton> {
  bool _hovering = false;

  @override
  Widget build(BuildContext context) {
    final Color baseColor = _hovering
        ? Colors.amber
        : (widget.isDisabledVisual ? Colors.grey.shade300 : Colors.grey.shade100);

    return MouseRegion(
      onEnter: (_) => setState(() => _hovering = true),
      onExit: (_) => setState(() => _hovering = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap, // Always call tap — dialog handles logic
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: baseColor,
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: widget.borderRadius,
          ),
          child: Icon(
            widget.icon,
            size: 20,
            color: widget.isDisabledVisual ? Colors.grey : Colors.black,
          ),
        ),
      ),
    );
  }
}
