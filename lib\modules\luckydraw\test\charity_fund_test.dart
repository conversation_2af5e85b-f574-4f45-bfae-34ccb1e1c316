import 'package:flutter_test/flutter_test.dart';
import '../model/CharityFund.dart';

void main() {
  group('CharityFund Model Tests', () {
    test('should create CharityFund from JSON', () {
      // Sample API response
      final json = {
        "status": 200,
        "message": "success",
        "data": 4819,
        "timestamp": 1755829222602,
      };

      final charityFund = CharityFund.fromJson(json);

      expect(charityFund.totalAmount, equals(4819));
      expect(charityFund.lastUpdated, equals(1755829222602));
    });

    test('should handle missing data gracefully', () {
      final json = {
        "status": 200,
        "message": "success",
        "data": null,
        "timestamp": null,
      };

      final charityFund = CharityFund.fromJson(json);

      expect(charityFund.totalAmount, equals(0));
      expect(charityFund.lastUpdated, equals(0));
    });

    test('should format last updated date correctly', () {
      final json = {
        "status": 200,
        "message": "success",
        "data": 4819,
        "timestamp": 1755829222602,
      };

      final charityFund = CharityFund.fromJson(json);
      final formattedDate = charityFund.formattedLastUpdated;

      // The timestamp 1755829222602 corresponds to a specific date
      // We'll just check that it's not empty and has the expected format
      expect(formattedDate, isNotEmpty);
      expect(formattedDate, matches(r'^\d{4}/\d{2}/\d{2}$'));
    });

    test('should convert to JSON correctly', () {
      final charityFund = CharityFund(
        totalAmount: 4819,
        lastUpdated: 1755829222602,
      );

      final json = charityFund.toJson();

      expect(json['data'], equals(4819));
      expect(json['timestamp'], equals(1755829222602));
    });

    test('should handle equality correctly', () {
      final charityFund1 = CharityFund(
        totalAmount: 4819,
        lastUpdated: 1755829222602,
      );

      final charityFund2 = CharityFund(
        totalAmount: 4819,
        lastUpdated: 1755829222602,
      );

      final charityFund3 = CharityFund(
        totalAmount: 5000,
        lastUpdated: 1755829222602,
      );

      expect(charityFund1, equals(charityFund2));
      expect(charityFund1, isNot(equals(charityFund3)));
    });
  });
}
