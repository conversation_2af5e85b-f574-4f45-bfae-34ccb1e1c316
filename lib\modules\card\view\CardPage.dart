import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_svg/flutter_svg.dart';
//Widgets Packages
import '../widgets/CardActionBox.dart';
import '../widgets/CircleIconButton.dart';
import '../widgets/BuyNowButton.dart';
import '../widgets/CardExchangeButton.dart';
import '../widgets/InfoDialog/CardInfoDialog.dart';
import '../widgets/InfoIconButton.dart';
import '../widgets/ScanCodeButton.dart';

class CardPage extends StatefulWidget {
  const CardPage({super.key});

  @override
  State<CardPage> createState() => _CardPageState();
}

class _CardPageState extends State<CardPage> {
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    // Simulate image loading delay
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    const double aspectRatio = 390 / 500;
    const double sideMargin = 24.0;

    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            child: Column(
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // 🔹 Full image container (extends to where AppBar would be)
                    AspectRatio(
                      aspectRatio: aspectRatio,
                      child: isLoading
                          ? Shimmer.fromColors(
                              baseColor: Colors.grey.shade300,
                              highlightColor: Colors.grey.shade100,
                              child: Container(
                                width: double.infinity,
                                color: Colors.white,
                              ),
                            )
                          : Container(
                              width: double.infinity,
                              decoration: const BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(
                                    'assets/images/card/backgroundCard/cardWallpaper.png',
                                  ),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                    ),

                    // 🔹 Buttons over image (top right)
                    Positioned(
                      top: 10 + MediaQuery.of(context).padding.top,
                      right: 16,
                      child: Row(
                        children: [
                          InfoIconButton(
                            icon: Icons.info_outline,
                            onTap: () {
                              showDialog(
                                context: context,
                                builder: (context) => const CardInfoDialog(),
                              );
                            },
                          ),
                          const SizedBox(width: 12),
                          CircleIconButton(
                            icon: SvgPicture.asset(
                              'assets/icons/Customer Support.svg',
                              color: Colors.white,
                            ),
                            onTap: () {},
                          ),
                        ],
                      ),
                    ),

                    // 🔹 Buy Now half in image, half below
                    Positioned(
                      top: (screenWidth / aspectRatio) - 28, // 28px to overlap halfway
                      left: (screenWidth - 332) / 2,
                      child: SizedBox(
                        width: 332,
                        child: BuyNowButton(
                          onTap: () {
                            context.push('/card/purchase');
                          },
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 45),

                // 🔹 First row: Transfer & Card Pack
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final isTablet = constraints.maxWidth > 600;
                      final itemWidth = isTablet
                          ? (constraints.maxWidth - 40) / 2
                          : (constraints.maxWidth - 20) / 2;

                      return Wrap(
                        spacing: 20,
                        runSpacing: 20,
                        alignment: WrapAlignment.center,
                        children: [
                          SizedBox(
                            width: itemWidth,
                            child: CardActionBox(
                              iconAssetPath:
                                  'assets/images/card/icons/transferCard.png',
                              label: 'Transfer Cards',
                              onTap: () {
                                context.push('/card/transfer');
                              },
                            ),
                          ),
                          SizedBox(
                            width: itemWidth,
                            child: CardActionBox(
                              iconAssetPath:
                                  'assets/images/card/icons/cardPack.png',
                              label: 'Card Pack',
                              onTap: () {
                                context.push('/card/pack');
                              },
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),

                const SizedBox(height: 30),

                // 🔹 Second row: Scan / Code (left) and Exchange (right)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: sideMargin),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 110,
                        child: ScanCodeButton(
                          onTap: () {
                            context.push('/card/scan-code');
                          },
                        ),
                      ),
                      const Spacer(),
                      CardExchangeButton(
                        onTap: () {
                          context.push('/exchange');
                        },
                        isEnabled: true,
                        showBadge: true,
                        showClaim: true,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 30),
              ],
            ),
          );
        },
      ),
    );
  }
}
