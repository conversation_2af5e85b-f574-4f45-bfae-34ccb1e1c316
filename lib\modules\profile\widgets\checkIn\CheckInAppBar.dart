import 'package:flutter/material.dart';

class CheckInAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CheckInAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final bool isTablet = screenWidth >= 600;

    return SafeArea(
      bottom: false,
      child: Container(
        height: preferredSize.height,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              blurRadius: 4,
              color: Colors.black12,
              offset: Offset(0, 1),
            )
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Back Button
            SizedBox(
              width: 48,
              height: double.infinity,
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black),
                onPressed: () => Navigator.of(context).pop(),
                tooltip: 'Back',
              ),
            ),

            // Title centered in Expanded, aligns to center regardless of screen width
            Expanded(
              child: Center(
                child: Text(
                  'Daily Check In',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: isTablet ? 22 : 18,
                    color: Colors.black,
                  ),
                ),
              ),
            ),

            // Spacer to balance layout symmetry
            const SizedBox(width: 48),
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(70);
}
