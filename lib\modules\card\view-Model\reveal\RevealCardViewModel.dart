import 'package:flutter/material.dart';
import '../../model/reveal/RevealedCard.dart';

class RevealCardViewModel extends ChangeNotifier {
  List<RevealedCard> _cards = [];

  /// Public getter
  List<RevealedCard> get cards => List.unmodifiable(_cards);

  /// Load newly revealed cards
  void loadCards(List<RevealedCard> revealedCards) {
    _cards = List.from(revealedCards);
    notifyListeners();
  }

  /// Clear all revealed cards
  void clear() {
    _cards.clear();
    notifyListeners();
  }

  /// Utility: Check if cards were already loaded
  bool get hasCards => _cards.isNotEmpty;
}
