import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/CheckoutVM.dart';

class OrderHeaderSection extends StatefulWidget {
  const OrderHeaderSection({super.key});

  @override
  State<OrderHeaderSection> createState() => _OrderHeaderSectionState();
}

class _OrderHeaderSectionState extends State<OrderHeaderSection> {
  late String orderTime;

  @override
  void initState() {
    super.initState();
    // Generate order number and time
    orderTime = _getCurrentTime();
  }

  String _getCurrentTime() {
    final now = DateTime.now();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CheckoutVM>(
      builder: (context, viewModel, child) {
        return Column(
          children: [
            // Pay Now Timer Header (hidden for luckydraw and physicalCard)
            // if (viewModel.checkoutType != CheckoutType.luckydraw &&
            //     viewModel.checkoutType != CheckoutType.physicalCard)
            //   Container(
            //     width: double.infinity,
            //     padding: const EdgeInsets.symmetric(vertical: 12),
            //     decoration: const BoxDecoration(
            //       color: Color(0xFFFFB800), // Orange/amber color
            //     ),
            //     child: const Center(
            //       child: Text(
            //         'Pay Now in 30:00',
            //         style: TextStyle(
            //           color: Colors.black,
            //           fontWeight: FontWeight.w600,
            //           fontSize: 16,
            //         ),
            //       ),
            //     ),
            //   ),

            // Order Details
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(color: Colors.white),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Order Time',
                        style: TextStyle(color: Colors.grey, fontSize: 14),
                      ),
                      Text(
                        orderTime,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
