import 'package:flutter/material.dart';

class CountdownTimer extends StatelessWidget {
  final String timeString;

  const CountdownTimer({super.key, required this.timeString});

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      timeString,
      style: TextStyle(
        fontSize: getResponsiveFontSize(32, context),
        fontWeight: FontWeight.bold,
        color: const Color(0xFFE53E3E), // Red color for timer
        letterSpacing: 2,
      ),
      textAlign: TextAlign.center,
    );
  }
}
