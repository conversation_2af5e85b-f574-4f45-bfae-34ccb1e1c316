import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:go_router/go_router.dart';
import '../model/MegaPrize.dart';

class MegaPrizes extends StatelessWidget {
  final List<MegaPrize> prizes;
  final VoidCallback? onPrizeJoin;

  const MegaPrizes({super.key, required this.prizes, this.onPrizeJoin});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    double scale(double value) => value * screenWidth / 375;
    double clampW(double value, double min, double max) =>
        value.clamp(min, max);

    // Responsive sizes with larger max for big screens
    final double cardWidth = clampW(scale(100), 80, 180); // was 120
    final double cardImageHeight = clampW(scale(70), 50, 140); // was 90
    final double cardImageWidth = clampW(scale(90), 60, 160); // was 110
    final double podiumHeight1 = clampW(scale(105), 70, 200); // was 130
    final double podiumHeight2 = clampW(scale(85), 60, 170); // was 110
    final double podiumHeight3 = clampW(scale(70), 50, 140); // was 100
    final double badgeSize = clampW(scale(35), 24, 60); // was 40
    final double joinBtnHeight = clampW(scale(22), 16, 40); // was 28
    final double joinFontSize = clampW(scale(9), 8, 18); // was 12
    final double titleFontSize = clampW(scale(35), 20, 48); // was 38
    final double prizeFontSize = clampW(scale(10), 8, 20); // was 13
    final double pointsFontSize = clampW(scale(10), 8, 20); // was 13
    final double progressBarHeight = clampW(scale(4), 2, 10); // was 6
    final double progressBarRadius = clampW(scale(2), 1, 6); // was 3
    final double iconSize1 = clampW(scale(24), 16, 36); // was 28
    final double iconSize2 = clampW(scale(20), 14, 32); // was 24
    final double iconSize3 = clampW(scale(18), 12, 28); // was 22
    final double fontSize1 = clampW(scale(20), 12, 28); // was 22
    final double fontSize2 = clampW(scale(18), 10, 24); // was 20
    final double fontSize3 = clampW(scale(14), 8, 20); // was 16

    // Removed unused tallestCardHeight calculation
    // Removed unused totalHeight and podiumAreaHeight

    return Container(
      margin: EdgeInsets.all(clampW(scale(10), 6, 24)),
      decoration: BoxDecoration(
        color: const Color(0xFFD90019),
        border: Border.all(
          color: const Color(0xFFFF4C61),
          width: clampW(scale(5), 3, 12),
        ),
        borderRadius: BorderRadius.circular(clampW(scale(20), 12, 40)),
      ),
      child: Padding(
        padding: EdgeInsets.fromLTRB(
          clampW(scale(8), 4, 24),
          0, // Remove top padding
          clampW(scale(8), 4, 24),
          0, // Keep bottom padding at 0
        ),
        child: Column(
          children: [
            // Title
            Text(
              'MEGA PRIZES',
              style: TextStyle(
                fontSize: titleFontSize,
                fontWeight: FontWeight.bold,
                fontFamily: 'Source Serif Pro',
                color: Colors.white,
                letterSpacing: 2.5,
              ),
            ),
            // Custom Podium with prizes
            Transform.translate(
              offset: Offset(0, 0), // No vertical offset
              child: SizedBox(
                // No fixed height, let content size naturally
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // 2nd place (left)
                    Expanded(
                      child: _buildPodiumColumn(
                        prize: prizes.isNotEmpty && prizes.length > 1
                            ? prizes[1]
                            : null,
                        podiumHeight: podiumHeight2,
                        podiumColor: const Color(0xFFFFBF00),
                        position: 2,
                        icon: Icons.military_tech,
                        iconSize: iconSize2,
                        fontSize: fontSize2,
                        cardWidth: cardWidth,
                        cardImageHeight: cardImageHeight,
                        cardImageWidth: cardImageWidth,
                        badgeSize: badgeSize,
                        joinBtnHeight: joinBtnHeight,
                        joinFontSize: joinFontSize,
                        prizeFontSize: prizeFontSize,
                        pointsFontSize: pointsFontSize,
                        progressBarHeight: progressBarHeight,
                        progressBarRadius: progressBarRadius,
                        context: context,
                        onJoin: onPrizeJoin,
                      ),
                    ),
                    SizedBox(width: clampW(scale(6), 2, 20)),
                    // 1st place (center - tallest)
                    Expanded(
                      child: _buildPodiumColumn(
                        prize: prizes.isNotEmpty ? prizes[0] : null,
                        podiumHeight: podiumHeight1,
                        podiumColor: const Color(0xFFFFBF00),
                        position: 1,
                        icon: Icons.emoji_events,
                        iconSize: iconSize1,
                        fontSize: fontSize1,
                        cardWidth: cardWidth,
                        cardImageHeight: cardImageHeight,
                        cardImageWidth: cardImageWidth,
                        badgeSize: badgeSize,
                        joinBtnHeight: joinBtnHeight,
                        joinFontSize: joinFontSize,
                        prizeFontSize: prizeFontSize,
                        pointsFontSize: pointsFontSize,
                        progressBarHeight: progressBarHeight,
                        progressBarRadius: progressBarRadius,
                        context: context,
                        onJoin: onPrizeJoin,
                      ),
                    ),
                    SizedBox(width: clampW(scale(6), 2, 20)),
                    // 3rd place (right - shortest)
                    Expanded(
                      child: _buildPodiumColumn(
                        prize: prizes.isNotEmpty && prizes.length > 2
                            ? prizes[2]
                            : null,
                        podiumHeight: podiumHeight3,
                        podiumColor: const Color(0xFFFFBF00),
                        position: 3,
                        icon: Icons.workspace_premium,
                        iconSize: iconSize3,
                        fontSize: fontSize3,
                        cardWidth: cardWidth,
                        cardImageHeight: cardImageHeight,
                        cardImageWidth: cardImageWidth,
                        badgeSize: badgeSize,
                        joinBtnHeight: joinBtnHeight,
                        joinFontSize: joinFontSize,
                        prizeFontSize: prizeFontSize,
                        pointsFontSize: pointsFontSize,
                        progressBarHeight: progressBarHeight,
                        progressBarRadius: progressBarRadius,
                        context: context,
                        onJoin: onPrizeJoin,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPodiumColumn({
    required MegaPrize? prize,
    required double podiumHeight,
    required Color podiumColor,
    required int position,
    required IconData icon,
    required double iconSize,
    required double fontSize,
    required double cardWidth,
    required double cardImageHeight,
    required double cardImageWidth,
    required double badgeSize,
    required double joinBtnHeight,
    required double joinFontSize,
    required double prizeFontSize,
    required double pointsFontSize,
    required double progressBarHeight,
    required double progressBarRadius,
    required BuildContext context,
    required VoidCallback? onJoin,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Prize card on top
        _buildPrizeCard(
          prize,
          context,
          cardWidth,
          cardImageHeight,
          cardImageWidth,
          badgeSize,
          prizeFontSize,
          pointsFontSize,
          progressBarHeight,
          progressBarRadius,
        ),
        // Podium rectangle
        Container(
          width: cardWidth,
          height: podiumHeight,
          decoration: BoxDecoration(
            color: podiumColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(cardWidth * 0.08),
              topRight: Radius.circular(cardWidth * 0.08),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Badge image at top
              Padding(
                padding: EdgeInsets.only(top: cardWidth * 0.04),
                child: Image.asset(
                  _getPositionBadgeImage(position),
                  width: badgeSize,
                  height: badgeSize,
                  fit: BoxFit.contain,
                ),
              ),

              // JOIN button at bottom
              Padding(
                padding: EdgeInsets.only(
                  bottom: cardWidth * 0.04,
                  left: cardWidth * 0.04,
                  right: cardWidth * 0.04,
                ),
                child: SizedBox(
                  width: double.infinity,
                  height: joinBtnHeight,
                  child: ElevatedButton(
                    onPressed: prize != null
                        ? () => _handleJoinButtonClick(
                            context,
                            prize,
                            onPrizeJoin,
                          )
                        : null, // Disable button if no prize data
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.black87,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(cardWidth * 0.04),
                      ),
                      padding: EdgeInsets.symmetric(
                        vertical: joinBtnHeight * 0.1,
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'JOIN',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: joinFontSize,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Handle JOIN button click - navigate to product details
  void _handleJoinButtonClick(
    BuildContext context,
    MegaPrize prize,
    VoidCallback? onPrizeJoin,
  ) {
    // First call the original onPrizeJoin callback if provided
    if (onPrizeJoin != null) {
      onPrizeJoin();
    }

    // Then navigate to product details
    try {
      // Convert MegaPrize to Map format for navigation
      final productData = {
        'id': prize.id,
        'productSpuId': prize.productSpuId,
        'productSkuId': prize.productSkuId,
        'productSkuName': prize.productSkuName,
        'productSkuImage': prize.productSkuImage,
        'usedAmount': prize.usedAmount,
        'totalAmount': prize.totalAmount,
        // Add any additional fields that might be needed
        'isMegaPrize': true,
        'position': _getPrizePosition(prize),
      };

      // Navigate to product details page
      context.push('/lucky-draw/product-detail', extra: productData);
    } catch (e) {
      // Handle navigation error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Failed to navigate to product details',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  /// Get the position of the prize based on its index in the list
  int _getPrizePosition(MegaPrize prize) {
    if (prizes.isEmpty) return 0;

    final index = prizes.indexOf(prize);
    if (index == 0) return 1; // 1st place
    if (index == 1) return 2; // 2nd place
    if (index == 2) return 3; // 3rd place
    return index + 1; // Fallback
  }

  Widget _buildPrizeCard(
    MegaPrize? prize,
    BuildContext? context,
    double cardWidth,
    double cardImageHeight,
    double cardImageWidth,
    double badgeSize,
    double prizeFontSize,
    double pointsFontSize,
    double progressBarHeight,
    double progressBarRadius,
  ) {
    // If no prize data, show "No mega prizes" placeholder
    if (prize == null) {
      return Container(
        width: cardWidth,
        height:
            cardImageHeight +
            (cardWidth * 0.06 * 2) +
            prizeFontSize * 2 +
            pointsFontSize * 2 +
            progressBarHeight * 2 +
            badgeSize * 0.7,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(cardWidth * 0.12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              offset: Offset(0, cardWidth * 0.02),
              blurRadius: cardWidth * 0.08,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.priority_high,
              color: Colors.grey[400],
              size: badgeSize * 0.8,
            ),
            SizedBox(height: cardWidth * 0.04),
            Text(
              'No mega prizes',
              style: TextStyle(
                fontSize: prizeFontSize,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: cardWidth * 0.02),
            Text(
              'for now',
              style: TextStyle(
                fontSize: pointsFontSize * 0.8,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Container(
      width: cardWidth,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(cardWidth * 0.12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            offset: Offset(0, cardWidth * 0.02),
            blurRadius: cardWidth * 0.08,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Product Image
          Padding(
            padding: EdgeInsets.all(cardWidth * 0.06),
            child: Container(
              height: cardImageHeight,
              width: cardImageWidth,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(cardWidth * 0.08),
                color: Colors.grey.shade100,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(cardWidth * 0.08),
                child: CachedNetworkImage(
                  imageUrl: prize.productSkuImage,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey.shade200,
                    child: Icon(
                      Icons.image,
                      color: Colors.grey,
                      size: badgeSize * 0.7,
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey.shade200,
                    child: Icon(
                      Icons.error,
                      color: Colors.grey,
                      size: badgeSize * 0.7,
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Product Name
          Padding(
            padding: EdgeInsets.symmetric(horizontal: cardWidth * 0.06),
            child: Text(
              prize.productSkuName,
              style: TextStyle(
                fontSize: prizeFontSize,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Points
          Padding(
            padding: EdgeInsets.symmetric(vertical: cardWidth * 0.02),
            child: Text(
              '${prize.totalAmount} Pts',
              style: TextStyle(
                fontSize: pointsFontSize,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ),

          // Progress Bar
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: cardWidth * 0.08,
              vertical: cardWidth * 0.02,
            ),
            child: Column(
              children: [
                Container(
                  width: double.infinity,
                  height: progressBarHeight,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(progressBarRadius),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: _getProgressValue(
                      1,
                    ), // Default progress for all prizes
                    child: Container(
                      decoration: BoxDecoration(
                        color: _getPositionColor(
                          1,
                        ), // Default color for all prizes
                        borderRadius: BorderRadius.circular(progressBarRadius),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: cardWidth * 0.01),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getPositionColor(int position) {
    switch (position) {
      case 1:
        return const Color(0xFFFFBF00);
      case 2:
        return const Color(0xFFFFBF00);
      case 3:
        return const Color(0xFFFFBF00);
      default:
        return Colors.grey;
    }
  }

  // Removed unused helpers _getPositionIcon and _getPositionText

  String _getPositionBadgeImage(int position) {
    switch (position) {
      case 1:
        return 'assets/images/first.png';
      case 2:
        return 'assets/images/second.png';
      case 3:
        return 'assets/images/third.png';
      default:
        return 'assets/images/first.png';
    }
  }

  double _getProgressValue(int position) {
    switch (position) {
      case 1:
        return 0.85; // 85% progress for 1st place
      case 2:
        return 0.72; // 72% progress for 2nd place
      case 3:
        return 0.58; // 58% progress for 3rd place
      default:
        return 0.50; // 50% default progress
    }
  }
}

class MegaPrizesShimmer extends StatelessWidget {
  const MegaPrizesShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    double scale(double value) => value * screenWidth / 375;
    double clampW(double value, double min, double max) =>
        value.clamp(min, max);
    final double cardWidth = clampW(scale(100), 80, 180);
    final double cardImageHeight = clampW(scale(70), 50, 140);
    final double cardImageWidth = clampW(scale(90), 60, 160);
    final double podiumHeight1 = clampW(scale(105), 70, 200);
    final double podiumHeight2 = clampW(scale(85), 60, 170);
    final double podiumHeight3 = clampW(scale(70), 50, 140);
    final double badgeSize = clampW(scale(35), 24, 60);
    final double joinBtnHeight = clampW(scale(22), 16, 40);
    final double joinFontSize = clampW(scale(9), 8, 18);
    final double prizeFontSize = clampW(scale(10), 8, 20);
    final double pointsFontSize = clampW(scale(10), 8, 20);
    final double progressBarHeight = clampW(scale(4), 2, 10);
    final double progressBarRadius = clampW(scale(2), 1, 6);

    Widget shimmerBox({
      double? width,
      double? height,
      BorderRadius? borderRadius,
      BoxShape shape = BoxShape.rectangle,
    }) {
      return Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: shape == BoxShape.circle
                ? null
                : (borderRadius ?? BorderRadius.circular(4)),
            shape: shape,
          ),
        ),
      );
    }

    Widget shimmerPrizeCard() {
      return Container(
        width: cardWidth,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(cardWidth * 0.12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              offset: Offset(0, cardWidth * 0.02),
              blurRadius: cardWidth * 0.08,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.all(cardWidth * 0.06),
              child: shimmerBox(
                width: cardImageWidth,
                height: cardImageHeight,
                borderRadius: BorderRadius.circular(cardWidth * 0.08),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: cardWidth * 0.06),
              child: shimmerBox(
                width: cardWidth * 0.8,
                height: prizeFontSize + 6,
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: cardWidth * 0.02),
              child: shimmerBox(
                width: cardWidth * 0.5,
                height: pointsFontSize + 4,
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: cardWidth * 0.08,
                vertical: cardWidth * 0.02,
              ),
              child: shimmerBox(
                width: double.infinity,
                height: progressBarHeight,
                borderRadius: BorderRadius.circular(progressBarRadius),
              ),
            ),
            SizedBox(height: cardWidth * 0.01),
          ],
        ),
      );
    }

    Widget shimmerPodium(double podiumHeight) {
      return Container(
        width: cardWidth,
        height: podiumHeight,
        decoration: BoxDecoration(
          color: const Color(0xFFFFBF00),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(cardWidth * 0.08),
            topRight: Radius.circular(cardWidth * 0.08),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsets.only(top: cardWidth * 0.04),
              child: Container(
                width: badgeSize,
                height: badgeSize,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                bottom: cardWidth * 0.04,
                left: cardWidth * 0.04,
                right: cardWidth * 0.04,
              ),
              child: Container(
                width: double.infinity,
                height: joinBtnHeight,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(cardWidth * 0.04),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 2,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    'JOIN',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: joinFontSize,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: EdgeInsets.all(clampW(scale(10), 6, 24)),
      decoration: BoxDecoration(
        color: const Color(0xFFD90019),
        border: Border.all(
          color: const Color(0xFFFF4C61),
          width: clampW(scale(5), 3, 12),
        ),
        borderRadius: BorderRadius.circular(clampW(scale(20), 12, 40)),
      ),
      child: Padding(
        padding: EdgeInsets.fromLTRB(
          clampW(scale(8), 4, 24),
          0,
          clampW(scale(8), 4, 24),
          0,
        ),
        child: Column(
          children: [
            // Static (not shimmered) title
            Text(
              'MEGA PRIZES',
              style: TextStyle(
                fontSize: clampW(scale(35), 20, 48),
                fontWeight: FontWeight.bold,
                fontFamily: 'Source Serif Pro',
                color: Colors.white,
                letterSpacing: 2.5,
              ),
            ),
            Transform.translate(
              offset: Offset(0, 0),
              child: SizedBox(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          shimmerPrizeCard(),
                          shimmerPodium(podiumHeight2),
                        ],
                      ),
                    ),
                    SizedBox(width: clampW(scale(6), 2, 20)),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          shimmerPrizeCard(),
                          shimmerPodium(podiumHeight1),
                        ],
                      ),
                    ),
                    SizedBox(width: clampW(scale(6), 2, 20)),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          shimmerPrizeCard(),
                          shimmerPodium(podiumHeight3),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
