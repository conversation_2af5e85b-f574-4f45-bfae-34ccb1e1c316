class Addressmodel {
  final int? id; // id for address
  final int country; // country code, Malaysia = 60
  final String recipient; // name of the recipient
  final String mobile; // mobile number
  final int postcode; // postcode
  final String detailedAddress; // full detail address
  final bool isDefault; // is default address

  Addressmodel({
    this.id,
    required this.country,
    required this.recipient,
    required this.mobile,
    required this.postcode,
    required this.detailedAddress,
    required this.isDefault,
  });

  factory Addressmodel.fromJson(Map<String, dynamic> json) {
    return Addressmodel(
      id: json['id'] != null ? int.tryParse(json['id'].toString()) : null,
      country: json['country'] != null
          ? int.tryParse(json['country'].toString()) ?? 60
          : 60,
      recipient: json['recipient']?.toString() ?? '',
      mobile: json['mobile']?.toString() ?? '',
      postcode: json['postcode'] != null
          ? int.tryParse(json['postcode'].toString()) ?? 0
          : 0,
      detailedAddress: json['detailedAddress']?.toString() ?? '',
      isDefault: json['isDefault'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // no need to send id
      'recipient': recipient,
      'country': country,
      'mobile': mobile,
      'postcode': postcode,
      'detailedAddress': detailedAddress,
      'isDefault': isDefault,
    };
  }
}
