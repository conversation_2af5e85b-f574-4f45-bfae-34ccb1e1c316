import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../data/model/OrderModel.dart';

class ToShipVM extends ChangeNotifier {
  final OrderModel order;

  // Mock data for demonstration
  late List<PackagingEvent> _packagingEvents;
  late DeliveryInfo? _deliveryInfo;
  late PickupInfo? _pickupInfo;
  String _remark = 'Please handle with care. Fragile items inside.';
  DateTime? _estimatedShipDate;

  ToShipVM({required this.order}) {
    _initializeData();
  }

  void _initializeData() {
    // Initialize packaging events
    _packagingEvents = [
      PackagingEvent(
        timestamp: DateTime.now(),
        title: 'Seller is packaging your order',
        isActive: true,
      ),
      PackagingEvent(
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        title: 'Order confirmed and processing started',
      ),
      PackagingEvent(
        timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        title: 'Payment received and order validated',
      ),
      PackagingEvent(
        timestamp: DateTime.now().subtract(const Duration(hours: 6)),
        title: 'Order placed successfully',
      ),
    ];

    // Initialize delivery/pickup info based on order type
    if (order.deliveryMethod == DeliveryMethod.delivery) {
      _deliveryInfo = DeliveryInfo(
        recipientName: 'Angela',
        phoneNumber: '012-3456789',
        address:
            '1 Blok A Tingkat, Lot 1, Jalan Indah Jaya, 90000 Sandakan, Sabah, Malaysia',
        deliveryPartner: 'J&T Express',
        trackingNumber: 'JT123456789',
      );
      _pickupInfo = null;
    } else {
      _pickupInfo = PickupInfo(
        locationName: 'Lucky Mall',
        phoneNumber: '011-684 33988',
        address:
            'SUITE 29.04-06, 29TH FLOOR, WISMA CHUANG, 34, Jln Sultan Ismail, Bukit Bintang, 50250 Kuala Lumpur, Wilayah Persekutuan Kuala Lumpur',
        operationHours: '8:30am - 6:30pm',
      );
      _deliveryInfo = null;
    }

    // Set estimated ship date (3-5 business days from order)
    _estimatedShipDate = order.createdAt.add(const Duration(days: 4));
  }

  // Getters
  List<PackagingEvent> get packagingEvents => _packagingEvents;
  DeliveryInfo? get deliveryInfo => _deliveryInfo;
  PickupInfo? get pickupInfo => _pickupInfo;
  String get remark => _remark;
  DateTime? get estimatedShipDate => _estimatedShipDate;
  bool get isDelivery => order.deliveryMethod == DeliveryMethod.delivery;
  bool get isPickup => order.deliveryMethod == DeliveryMethod.pickup;
  String get statusBannerMessage =>
      'Seller is packaging your order. Estimated ship date: ${DateFormat('MMM dd').format(_estimatedShipDate!)}';

  // Methods
  Future<void> refreshOrderStatus() async {
    // Simulate API call to refresh order status
    await Future.delayed(const Duration(seconds: 1));

    // Update packaging events with new timestamps
    _packagingEvents = [
      PackagingEvent(
        timestamp: DateTime.now(),
        title: 'Seller is packaging your order',
        isActive: true,
      ),
      PackagingEvent(
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        title: 'Order confirmed and processing started',
      ),
      PackagingEvent(
        timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        title: 'Payment received and order validated',
      ),
      PackagingEvent(
        timestamp: DateTime.now().subtract(const Duration(hours: 6)),
        title: 'Order placed successfully',
      ),
    ];

    notifyListeners();
  }

  void contactSupport() {
    // TODO: Implement contact support functionality
    debugPrint('Contact support for order: ${order.id}');
  }

  void joinAgain() {
    // TODO: Implement join again functionality
    debugPrint('Join again for order: ${order.id}');
  }

  @override
  void dispose() {
    super.dispose();
  }
}

// Data models
class DeliveryInfo {
  final String recipientName;
  final String phoneNumber;
  final String address;
  final String deliveryPartner;
  final String trackingNumber;

  DeliveryInfo({
    required this.recipientName,
    required this.phoneNumber,
    required this.address,
    required this.deliveryPartner,
    required this.trackingNumber,
  });
}

class PickupInfo {
  final String locationName;
  final String phoneNumber;
  final String address;
  final String operationHours;

  PickupInfo({
    required this.locationName,
    required this.phoneNumber,
    required this.address,
    required this.operationHours,
  });
}

class PackagingEvent {
  final DateTime timestamp;
  final String title;
  final String? description;
  final bool isActive;

  PackagingEvent({
    required this.timestamp,
    required this.title,
    this.description,
    this.isActive = false,
  });
}
