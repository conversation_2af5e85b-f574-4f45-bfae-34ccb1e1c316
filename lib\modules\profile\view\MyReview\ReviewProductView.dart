import 'package:flutter/material.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';
import 'package:luckymall/modules/profile/view/MyReview/widgets/ProductReviewSection.dart';
import 'package:luckymall/modules/profile/view/MyReview/widgets/RatingSection.dart';
import 'package:luckymall/modules/profile/view/MyReview/widgets/ReviewTextSection.dart';
import 'package:luckymall/modules/profile/view/MyReview/widgets/UploadMediaSection.dart';
import 'package:luckymall/modules/profile/view/MyReview/widgets/SubmitButton.dart';

class ReviewProductView extends StatefulWidget {
  final String productName;
  final String variation;
  final String size;
  final String price;
  final int quantity;
  final String imageUrl;
  final String orderDate;

  const ReviewProductView({
    super.key,
    required this.productName,
    required this.variation,
    required this.size,
    required this.price,
    required this.quantity,
    required this.imageUrl,
    required this.orderDate,
  });

  @override
  State<ReviewProductView> createState() => _ReviewProductViewState();
}

class _ReviewProductViewState extends State<ReviewProductView> {
  int _rating = 0;
  String _reviewText = '';
  final int _maxCharacters = 100;

  void _onRatingChanged(int rating) {
    setState(() {
      _rating = rating;
    });
  }

  void _onReviewTextChanged(String text) {
    setState(() {
      _reviewText = text;
    });
  }

  void _onSubmitPressed() {
    // Show success popup
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(Icons.check, color: Colors.green, size: 30),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Review Submitted!',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Thank you for your feedback. Your review has been submitted successfully.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(); // Close dialog
                      Navigator.of(context).pop(); // Return to profile
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'Rating / Review'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Review Section
            ProductReviewSection(
              productName: widget.productName,
              variation: widget.variation,
              price: widget.price,
              orderDate: widget.orderDate,
              quantity: widget.quantity,
              imageUrl: widget.imageUrl,
            ),

            const SizedBox(height: 24),

            // Rating Section
            RatingSection(rating: _rating, onRatingChanged: _onRatingChanged),

            const SizedBox(height: 24),

            // Review Text Section
            ReviewTextSection(
              reviewText: _reviewText,
              maxCharacters: _maxCharacters,
              onReviewTextChanged: _onReviewTextChanged,
            ),

            const SizedBox(height: 24),

            // Upload Media Section
            const UploadMediaSection(),

            const SizedBox(height: 40),
          ],
        ),
      ),
      bottomNavigationBar: SubmitButton(
        onPressed: _onSubmitPressed,
        isEnabled: _rating > 0, // Enable only if rating is selected
      ),
    );
  }
}
