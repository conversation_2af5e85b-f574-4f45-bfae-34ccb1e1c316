import 'package:flutter/material.dart';
import '../../model/cardPack/CardItem.dart';
import 'PhysicalCardRowItem.dart';

class PhysicalCardList extends StatelessWidget {
  final List<CardItem> cards;
  final Map<String, int> selectedQuantities;
  final Function(String id, bool selected) onCheckboxChanged;
  final Function(String id, int quantity) onQuantityChanged;

  const PhysicalCardList({
    super.key,
    required this.cards,
    required this.selectedQuantities,
    required this.onCheckboxChanged,
    required this.onQuantityChanged,
  });

  @override
  Widget build(BuildContext context) {
    if (cards.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.only(top: 80),
          child: Text(
            'No cards available',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      itemCount: cards.length,
      separatorBuilder: (_, __) => const SizedBox(height: 16),
      itemBuilder: (context, index) {
        final card = cards[index];
        final selected = selectedQuantities.containsKey(card.id);
        final qty = selectedQuantities[card.id] ?? 1;

        return PhysicalCardRowItem(
          card: card,
          isSelected: selected,
          selectedQuantity: qty,
          onToggle: (val) => onCheckboxChanged(card.id, val),
          onQuantityChanged: (val) => onQuantityChanged(card.id, val),
        );
      },
    );
  }
}
