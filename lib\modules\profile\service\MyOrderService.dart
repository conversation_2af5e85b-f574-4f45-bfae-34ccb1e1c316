import 'package:luckymall/modules/profile/model/MyOrderModel.dart';
import 'package:luckymall/res/utility/network/api_client.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

final myOrderService = MyOrderService();

class MyOrderService {
  final Dio authDio = ApiClient.instance.authDio;

  Future<List<MyOrderModel>> getMyOrder(
    int? state,
    int? limit,
    int? page,
  ) async {
    try {
      final Map<String, dynamic> queryParams = <String, dynamic>{};

      // Order status (-1: Cancelled; 0: Waiting for payment; 10: Waiting for shipment; 20: Waiting for receipt; 100: Completed;)
      if (state != null) {
        queryParams['vo'] = <String, dynamic>{
          'state': state,
        }; // else fetch all order data
      }

      // Add other parameters
      queryParams['limit'] = limit ?? 10;
      queryParams['page'] = page ?? 1;

      final response = await authDio.get(
        '/order',
        queryParameters: queryParams,
      );
      if (response.statusCode == 200) {
        final data = response.data;
        final List<dynamic> orderList = data['data']['list'];
        final List<MyOrderModel> orders = orderList
            .map((json) => MyOrderModel.fromJson(json))
            .toList();
        debugPrint('orders: $orders');
        return orders;
      } else {
        debugPrint('response: ${response.data}');
        throw Exception('Error: ${response.data['message']}');
      }
    } catch (e) {
      debugPrint('Exception: $e');
      throw Exception(e);
    }
  }

  Future<MyOrderDetail> getMyOrderDetail(int id) async {
    try {
      final response = await authDio.get('/order/$id');
      if (response.statusCode == 200) {
        final data = response.data;
        final orderData = data['data'];
        final MyOrderDetail order = MyOrderDetail.fromJson(orderData);
        debugPrint('order detail: $order');
        return order;
      } else {
        debugPrint('response: ${response.data}');
        throw Exception('Error: ${response.data['message']}');
      }
    } on DioException catch (e) {
      debugPrint('DioException: $e');
      throw Exception(e);
    }
  }
}
