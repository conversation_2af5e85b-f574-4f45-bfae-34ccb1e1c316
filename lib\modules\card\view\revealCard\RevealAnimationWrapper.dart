import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../view-model/reveal/RevealCardViewModel.dart';
import '../../model/purchaseCard/PurchaseCardOrder.dart';
import 'RevealAnimationPage.dart';

class RevealAnimationWrapper extends StatelessWidget {
  final PurchaseCardOrder order;

  const RevealAnimationWrapper({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => RevealCardViewModel(),
      child: RevealAnimationPage(order: order),
    );
  }
}
