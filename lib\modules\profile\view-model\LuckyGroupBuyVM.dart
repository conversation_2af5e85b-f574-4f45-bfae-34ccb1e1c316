import 'package:flutter/material.dart';
import '../../../data/model/LuckyGroupBuyRecord.dart';

class LuckyGroupBuyViewVM extends ChangeNotifier {
  List<LuckyGroupBuyRecord> _myGroupRecords = [];
  List<LuckyGroupBuyRecord> _othersGroupRecords = [];
  bool _isLoading = false;

  List<LuckyGroupBuyRecord> get myGroupRecords => _myGroupRecords;
  List<LuckyGroupBuyRecord> get othersGroupRecords => _othersGroupRecords;
  bool get isLoading => _isLoading;

  LuckyGroupBuyViewVM() {
    _loadMockData();
  }

  void _loadMockData() {
    _isLoading = true;
    notifyListeners();

    // Simulate API call delay
    Future.delayed(const Duration(milliseconds: 500), () {
      _myGroupRecords = _generateMockMyGroupRecords();
      _othersGroupRecords = _generateMockOthersGroupRecords();
      _isLoading = false;
      notifyListeners();
    });
  }

  List<LuckyGroupBuyRecord> _generateMockMyGroupRecords() {
    return [
      LuckyGroupBuyRecord(
        id: '1',
        productName: "Russell Taylor's 3D Visible Window Air Fryer",
        productImageUrl: 'https://images.unsplash.com/photo-1574269909862-7e1d70bb8078?w=400&h=300&fit=crop',
        variation: 'White Medium, White Large',
        pricePerUnit: 150.99,
        quantity: 2,
        totalPrice: 301.98,
        status: LuckyGroupBuyStatus.ongoing,
        createdAt: DateTime(2025, 7, 7),
        endTime: DateTime.now().add(const Duration(hours: 5, minutes: 52)),
        groupId: 'GB001',
        currentMembers: 3,
        requiredMembers: 4,
      ),
      LuckyGroupBuyRecord(
        id: '2',
        productName: "Russell Taylor's 3D Visible Window Air Fryer",
        productImageUrl: 'https://images.unsplash.com/photo-1574269909862-7e1d70bb8078?w=400&h=300&fit=crop',
        variation: 'White Medium, White Large',
        pricePerUnit: 150.99,
        quantity: 2,
        totalPrice: 301.98,
        status: LuckyGroupBuyStatus.completed,
        createdAt: DateTime(2025, 7, 1),
        endTime: null,
        groupId: 'GB002',
        currentMembers: 4,
        requiredMembers: 4,
      ),
      LuckyGroupBuyRecord(
        id: '3',
        productName: "Russell Taylor's 3D Visible Window Air Fryer",
        productImageUrl: 'https://images.unsplash.com/photo-1574269909862-7e1d70bb8078?w=400&h=300&fit=crop',
        variation: 'White Medium, White Large',
        pricePerUnit: 150.99,
        quantity: 2,
        totalPrice: 301.98,
        status: LuckyGroupBuyStatus.refundInProgress,
        createdAt: DateTime(2025, 6, 30),
        endTime: null,
        groupId: 'GB003',
        currentMembers: 2,
        requiredMembers: 4,
      ),
      LuckyGroupBuyRecord(
        id: '4',
        productName: "Russell Taylor's 3D Visible Window Air Fryer",
        productImageUrl: 'https://images.unsplash.com/photo-1574269909862-7e1d70bb8078?w=400&h=300&fit=crop',
        variation: 'White Medium, White Large',
        pricePerUnit: 150.99,
        quantity: 2,
        totalPrice: 301.98,
        status: LuckyGroupBuyStatus.completed,
        createdAt: DateTime(2025, 6, 15),
        endTime: null,
        groupId: 'GB004',
        currentMembers: 4,
        requiredMembers: 4,
      ),
      LuckyGroupBuyRecord(
        id: '5',
        productName: "Russell Taylor's 3D Visible Window Air Fryer",
        productImageUrl: 'https://images.unsplash.com/photo-1574269909862-7e1d70bb8078?w=400&h=300&fit=crop',
        variation: 'White Medium, White Large',
        pricePerUnit: 150.99,
        quantity: 2,
        totalPrice: 301.98,
        status: LuckyGroupBuyStatus.failed,
        createdAt: DateTime(2025, 6, 2),
        endTime: null,
        groupId: 'GB005',
        currentMembers: 1,
        requiredMembers: 4,
      ),
    ];
  }

  List<LuckyGroupBuyRecord> _generateMockOthersGroupRecords() {
    return [
      LuckyGroupBuyRecord(
        id: '6',
        productName: 'Samsung Galaxy Tab A8',
        productImageUrl: 'https://encrypted-tbn2.gstatic.com/shopping?q=tbn:ANd9GcRZc4r8DvZoIH9nyGXmL7Y116HEPf8UlYGpDkpdAnLsrfqI5WmvDDC6TY6tBJ1VZ-VzAs7H15OCTXHdP-_bWBlvtdUIUN19NZbC9fb3aVU98aw5XFPClgBAAV8',
        variation: 'WiFi 32GB, Dark Gray',
        pricePerUnit: 599.00,
        quantity: 1,
        totalPrice: 599.00,
        status: LuckyGroupBuyStatus.ongoing,
        createdAt: DateTime(2025, 7, 5),
        endTime: DateTime.now().add(const Duration(hours: 12, minutes: 30)),
        groupId: 'GB006',
        currentMembers: 2,
        requiredMembers: 5,
      ),
      LuckyGroupBuyRecord(
        id: '7',
        productName: 'Xiaomi Redmi Note 12',
        productImageUrl: 'https://i02.appmifile.com/368_operatorx_operatorx_xm/16/03/2023/ff9aad5fe02f20585f5565ab039c6a4c.jpg',
        variation: '128GB, Midnight Black',
        pricePerUnit: 799.00,
        quantity: 1,
        totalPrice: 799.00,
        status: LuckyGroupBuyStatus.completed,
        createdAt: DateTime(2025, 6, 28),
        endTime: null,
        groupId: 'GB007',
        currentMembers: 6,
        requiredMembers: 6,
      ),
    ];
  }

  // API-ready methods for future implementation
  Future<void> fetchMyGroupRecords() async {
    _isLoading = true;
    notifyListeners();

    try {
      // TODO: Replace with actual API call
      // final response = await ApiService.getMyLuckyGroupBuyRecords();
      // _myGroupRecords = response.map((json) => LuckyGroupBuyRecord.fromJson(json)).toList();

      // For now, use mock data
      await Future.delayed(const Duration(seconds: 1));
      _myGroupRecords = _generateMockMyGroupRecords();
    } catch (error) {
      // Handle error
      debugPrint('Error fetching my group records: $error');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> fetchOthersGroupRecords() async {
    _isLoading = true;
    notifyListeners();

    try {
      // TODO: Replace with actual API call
      // final response = await ApiService.getOthersLuckyGroupBuyRecords();
      // _othersGroupRecords = response.map((json) => LuckyGroupBuyRecord.fromJson(json)).toList();

      // For now, use mock data
      await Future.delayed(const Duration(seconds: 1));
      _othersGroupRecords = _generateMockOthersGroupRecords();
    } catch (error) {
      // Handle error
      debugPrint('Error fetching others group records: $error');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refreshData() async {
    await Future.wait([fetchMyGroupRecords(), fetchOthersGroupRecords()]);
  }

  void onRecordTap(LuckyGroupBuyRecord record) {
    // TODO: Navigate to record details or group details page
    debugPrint('Tapped on record: ${record.id}');
  }
}
