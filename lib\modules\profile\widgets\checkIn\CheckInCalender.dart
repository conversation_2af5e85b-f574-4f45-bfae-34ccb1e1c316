import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../view-model/CheckInViewModel.dart';
import 'CalenderDayCell.dart';
import 'TotalPointsDisplay.dart';
import 'package:intl/intl.dart';

class CheckInCalendar extends StatelessWidget {
  const CheckInCalendar({super.key});

  @override
  Widget build(BuildContext context) {
    final vm = Provider.of<CheckInViewModel>(context);
    final monthText = DateFormat('MMMM yyyy').format(vm.currentMonth);
    final days = vm.daysInMonth;

    // Responsive top margin based on screen height
    final double topSpacing = 70;

    return Padding(
      padding: EdgeInsets.only(top: topSpacing), // ~5% height, clamped
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [
            BoxShadow(
              blurRadius: 6,
              color: Colors.black12,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            SizedBox(height: 10),
            // Header row: Month & Total Points
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Month with arrows
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_left),
                      onPressed: vm.goToPreviousMonth,
                    ),
                    Text(
                      monthText,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.arrow_right),
                      onPressed: vm.goToNextMonth,
                    ),
                  ],
                ),

                // Total Points on the right
                const TotalPointsDisplay(),
              ],
            ),

            const SizedBox(height: 15),

            // Days of week (Mon–Sun)
            Row(
              
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: const [
                _WeekdayLabel("Mon"),
                _WeekdayLabel("Tue"),
                _WeekdayLabel("Wed"),
                _WeekdayLabel("Thu"),
                _WeekdayLabel("Fri"),
                _WeekdayLabel("Sat"),
                _WeekdayLabel("Sun"),
              ],
            ),

            const SizedBox(height: 10),

            // Calendar grid
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: days.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                mainAxisSpacing: 8,
                crossAxisSpacing: 8,
                childAspectRatio: 1,
              ),
              itemBuilder: (context, index) {
                final day = days[index];
                if (day == null) {
                  return const SizedBox(); // Empty placeholder for padding
                }
                return CalendarDayCell(day: day);
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _WeekdayLabel extends StatelessWidget {
  final String label;
  const _WeekdayLabel(this.label);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Center(
        child: Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
            color: Colors.black54,
          ),
        ),
      ),
    );
  }
}
