import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class HistoryFilterBar extends StatelessWidget {
  final String sortOrder;
  final Function(String) onSortOrderChanged;
  final String selectedFilter;
  final Function(String) onFilterChanged;

  final bool showDatePickers;
  final DateTime startDate;
  final DateTime endDate;
  final VoidCallback onStartTap;
  final VoidCallback onEndTap;

  final List<String> filters = [
    'All Record',
    'Purchase Card',
    'Transfer Card',
    'Discount used',
    'Gift',
    'Physical Card',
    'Exchange',
  ];

  HistoryFilterBar({
    super.key,
    required this.sortOrder,
    required this.onSortOrderChanged,
    required this.selectedFilter,
    required this.onFilterChanged,
    required this.showDatePickers,
    required this.startDate,
    required this.endDate,
    required this.onStartTap,
    required this.onEndTap,
  });

  @override
  Widget build(BuildContext context) {
    final scale = MediaQuery.of(context).size.width / 390;
    final dateFormat = DateFormat('MMM yyyy');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showDatePickers) ...[
          Row(
            children: [
              _buildDateField('Start', startDate, onStartTap, dateFormat, scale),
              SizedBox(width: 12 * scale),
              _buildDateField('End', endDate, onEndTap, dateFormat, scale),
            ],
          ),
          SizedBox(height: 14 * scale),
        ],
        Row(
          children: [
            ToggleButtons(
              isSelected: ['Latest', 'Oldest'].map((s) => s == sortOrder).toList(),
              onPressed: (index) {
                onSortOrderChanged(index == 0 ? 'Latest' : 'Oldest');
              },
              borderRadius: BorderRadius.circular(8),
              selectedColor: Colors.amber,
              fillColor: Colors.grey.shade100,
              color: Colors.grey,
              constraints: BoxConstraints(minWidth: 80 * scale, minHeight: 36 * scale),
              textStyle: TextStyle(fontSize: 14 * scale, fontWeight: FontWeight.w600),
              children: const [
                Text('Latest'),
                Text('Oldest'),
              ],
            ),
            SizedBox(width: 12 * scale),
            Expanded(
              child: Container(
                height: 36 * scale,
                padding: EdgeInsets.symmetric(horizontal: 12 * scale),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.black12),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: selectedFilter,
                    isExpanded: true,
                    icon: Icon(Icons.expand_more, size: 18 * scale),
                    style: TextStyle(fontSize: 14 * scale, color: Colors.black87),
                    items: filters.map((filter) {
                      return DropdownMenuItem(
                        value: filter,
                        child: Text(
                          filter,
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList(),
                    onChanged: (val) {
                      if (val != null) {
                        onFilterChanged(val);
                      }
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField(String label, DateTime date, VoidCallback onTap, DateFormat formatter, double scale) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(fontSize: 12 * scale, color: Colors.black54),
            ),
            const SizedBox(height: 4),
            Container(
              padding: EdgeInsets.only(bottom: 6 * scale),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade400, width: 1.4),
                ),
              ),
              child: Text(
                formatter.format(date),
                style: TextStyle(
                  fontSize: 15 * scale,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
