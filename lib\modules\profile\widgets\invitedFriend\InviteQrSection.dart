import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../view-model/InviteFriendViewModel.dart';

class InviteQrSection extends StatelessWidget {
  const InviteQrSection({super.key});

  @override
  Widget build(BuildContext context) {
    final vm = context.watch<InviteFriendViewModel>();
    final isTablet = MediaQuery.of(context).size.width > 600;
    final qrSize = isTablet ? 320.0 : 285.0;
    final codeBoxWidth = qrSize;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircleAvatar(
            radius: isTablet ? 60 : 50,
            backgroundColor: Colors.white,
            backgroundImage: vm.profileImageUrl.isNotEmpty
                ? NetworkImage(vm.profileImageUrl)
                : null,
            child: vm.profileImageUrl.isEmpty
                ? const Icon(Icons.person, size: 36, color: Colors.black)
                : null,
          ),
          const SizedBox(height: 16),
          Container(
            width: qrSize,
            height: qrSize,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.amberAccent, width: 4),
              borderRadius: BorderRadius.circular(12),
              color: Colors.white,
            ),
            padding: const EdgeInsets.all(8),
            child: Image.asset(
              vm.qrCodeImageUrl,
              fit: BoxFit.contain,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: const [
              Expanded(child: Divider(thickness: 1, color: Colors.grey)),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(
                  "Referral code",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(child: Divider(thickness: 1, color: Colors.grey)),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            width: codeBoxWidth,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.amber, width: 2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Text(
                vm.referralCode,
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: codeBoxWidth,
            height: 48,
            child: Row(
              children: [
                Expanded(
                  child: SizedBox(
                    height: double.infinity,
                    child: OutlinedButton(
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: vm.referralCode));
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Referral code copied')),
                        );
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.amber,
                        side: const BorderSide(color: Colors.amber),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: EdgeInsets.zero,
                      ),
                      child: const Text("Copy"),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: SizedBox(
                    height: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Share.share('Join me using my referral code: ${vm.referralCode}');
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.amber,
                        foregroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: EdgeInsets.zero,
                      ),
                      child: const Text("Share"),
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
