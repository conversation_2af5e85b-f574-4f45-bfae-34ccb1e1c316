import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';
import 'package:luckymall/modules/luckydraw/widgets/ReviewCategory.dart';
import 'package:luckymall/modules/luckydraw/widgets/AllReviewCards.dart';
import 'package:luckymall/modules/luckydraw/view-model/ReviewDetailsVM.dart';

class ReviewDetailsView extends StatelessWidget {
  const ReviewDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ReviewDetailsVM()..initialize(),
      child: Consumer<ReviewDetailsVM>(
        builder: (context, vm, _) {
          return Scaffold(
            appBar: const CustomAppBar(title: 'All Reviews'),
            body: Column(
              children: [
                // Category Filter
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: ReviewCategory(
                    onCategorySelected: (category) {
                      vm.setCategory(category);
                    },
                    initialCategory: vm.selectedCategory,
                  ),
                ),

                // All Reviews
                Expanded(
                  child: AllReviewCards(selectedCategory: vm.selectedCategory),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
