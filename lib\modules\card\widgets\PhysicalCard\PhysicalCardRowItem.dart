import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For FilteringTextInputFormatter
import '../../model/cardPack/CardItem.dart';

class PhysicalCardRowItem extends StatelessWidget {
  final CardItem card;
  final bool isSelected;
  final int selectedQuantity;
  final ValueChanged<bool> onToggle;
  final ValueChanged<int> onQuantityChanged;
  
  const PhysicalCardRowItem({
    super.key,
    required this.card,
    required this.isSelected,
    required this.selectedQuantity,
    required this.onToggle,
    required this.onQuantityChanged,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMinQty = selectedQuantity <= 1;
    final isMaxQty = selectedQuantity >= card.quantity;

    final controller = TextEditingController(text: '$selectedQuantity');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 6,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Checkbox
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: Checkbox(
              value: isSelected,
              onChanged: (value) => onToggle(value ?? false),
            ),
          ),

          // Card Image
          ClipRRect(
            borderRadius: BorderRadius.circular(5),
            child: Image.asset(
              card.imagePath,
              width: screenWidth * 0.14,
              height: screenWidth * 0.18,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(width: 10),

          // Card Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  card.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  'Owned: ${card.quantity}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),

          // Quantity Control
          Container(
            height: 36,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Minus
                InkWell(
                  onTap: isMinQty ? null : () => onQuantityChanged(selectedQuantity - 1),
                  child: Container(
                    width: 36,
                    height: 36,
                    color: isMinQty ? Colors.grey[300] : Colors.grey[400],
                    alignment: Alignment.center,
                    child: const Icon(Icons.remove, color: Colors.white, size: 18),
                  ),
                ),

                // Quantity Input (centered)
                SizedBox(
                  width: 40,
                  height: 36,
                  child: TextField(
                    controller: controller,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly, // only 0-9
                    ],
                    onChanged: (val) {
                      final parsed = int.tryParse(val);
                      if (parsed == null || parsed < 1) {
                        controller.text = '1';
                        controller.selection = TextSelection.fromPosition(
                          const TextPosition(offset: 1),
                        );
                        onQuantityChanged(1);
                      } else if (parsed > card.quantity) {
                        controller.text = '${card.quantity}';
                        controller.selection = TextSelection.fromPosition(
                          TextPosition(offset: controller.text.length),
                        );
                        onQuantityChanged(card.quantity);
                      } else {
                        onQuantityChanged(parsed);
                      }
                    },
                    textAlign: TextAlign.center,
                    textAlignVertical: TextAlignVertical.center,
                    decoration: const InputDecoration(
                      isCollapsed: true,
                      contentPadding: EdgeInsets.zero,
                      border: InputBorder.none,
                    ),
                    style: const TextStyle(fontSize: 14, height: 2.0, color: Colors.black),
                  ),
                ),

                // Plus
                InkWell(
                  onTap: isMaxQty ? null : () => onQuantityChanged(selectedQuantity + 1),
                  child: Container(
                    width: 36,
                    height: 36,
                    color: isMaxQty ? Colors.grey[300] : Colors.amber,
                    alignment: Alignment.center,
                    child: const Icon(Icons.add, color: Colors.white, size: 18),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
