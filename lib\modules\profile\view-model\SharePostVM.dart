import 'package:flutter/material.dart';
import 'dart:io';

class SharePostVM extends ChangeNotifier {
  String _caption = '';
  File? _selectedMedia;
  bool _isPosting = false;
  String? _errorMessage;
  final int _maxCaptionLength = 300;

  // Getters
  String get caption => _caption;
  File? get selectedMedia => _selectedMedia;
  bool get isPosting => _isPosting;
  String? get errorMessage => _errorMessage;
  int get maxCaptionLength => _maxCaptionLength;
  int get captionLength => _caption.length;
  bool get canPost => _caption.trim().isNotEmpty || _selectedMedia != null;

  // Update caption
  void updateCaption(String newCaption) {
    if (newCaption.length <= _maxCaptionLength) {
      _caption = newCaption;
      _clearError();
      notifyListeners();
    }
  }

  // Select media file
  void selectMedia(File mediaFile) {
    _selectedMedia = mediaFile;
    _clearError();
    notifyListeners();
  }

  // Remove selected media
  void removeMedia() {
    _selectedMedia = null;
    notifyListeners();
  }

  // Clear error message
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  // Post the content
  Future<bool> submitPost() async {
    if (!canPost) {
      _errorMessage = 'Please add a caption or upload media to post';
      notifyListeners();
      return false;
    }

    _isPosting = true;
    _clearError();
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // TODO: Replace with actual API call
      // final result = await ApiService.createPost(
      //   caption: _caption,
      //   mediaFile: _selectedMedia,
      // );

      // Simulate success for now
      _isPosting = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isPosting = false;
      _errorMessage = 'Failed to post. Please try again.';
      notifyListeners();
      return false;
    }
  }

  // Reset the form
  void reset() {
    _caption = '';
    _selectedMedia = null;
    _isPosting = false;
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
