import 'package:flutter/material.dart';
import '../data/LuckyDrawRepository.dart';
import '../model/LuckyDrawCategory.dart';

class CategoryVM extends ChangeNotifier {
  final LuckyDrawRepository repository;

  CategoryVM({LuckyDrawRepository? repository})
    : repository = repository ?? MockLuckyDrawRepository();

  bool isLoading = false;
  String? errorMessage;
  List<LuckyDrawCategory> categories = [];

  // Cache for category ID lookups to avoid repeated API calls
  final Map<String, int> _categoryIdCache = {};

  Future<void> fetchCategories() async {
    isLoading = true;
    errorMessage = null;
    notifyListeners();

    try {
      print('CategoryVM: Fetching categories...');
      final result = await repository.fetchCategories();

      categories = result;

      // Update cache with new category data
      _updateCategoryIdCache();

      print('CategoryVM: Fetched ${categories.length} categories');
    } catch (e) {
      errorMessage = 'Failed to load categories: ${e.toString()}';
      print('CategoryVM: Error fetching categories: $e');
      categories = [];
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refresh() async {
    await fetchCategories();
  }

  /// Get category ID by name using cached data
  /// Returns null if category not found
  int? getCategoryIdByName(String categoryName) {
    return _categoryIdCache[categoryName.toLowerCase()];
  }

  /// Get category by ID
  LuckyDrawCategory? getCategoryById(int categoryId) {
    try {
      return categories.firstWhere((cat) => cat.categoryId == categoryId);
    } catch (e) {
      return null;
    }
  }

  /// Get all category names for display
  List<String> get categoryNames {
    return categories.map((cat) => cat.categoryName).toList();
  }

  /// Get categories excluding adult content (if needed)
  List<LuckyDrawCategory> get nonAdultCategories {
    return categories.where((cat) => !cat.isAdult).toList();
  }

  /// Check if a category exists
  bool hasCategory(String categoryName) {
    return _categoryIdCache.containsKey(categoryName.toLowerCase());
  }

  /// Update the category ID cache
  void _updateCategoryIdCache() {
    _categoryIdCache.clear();
    for (final category in categories) {
      _categoryIdCache[category.categoryName.toLowerCase()] =
          category.categoryId;
    }
    print(
      'CategoryVM: Updated category cache with ${_categoryIdCache.length} entries',
    );
  }

  /// Clear cache (useful for testing or when data becomes stale)
  void clearCache() {
    _categoryIdCache.clear();
    print('CategoryVM: Category cache cleared');
  }
}
