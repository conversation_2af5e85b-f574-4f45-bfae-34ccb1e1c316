import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';
import 'package:luckymall/modules/luckydraw/widgets/SuccessJoinPopup.dart';
import 'package:luckymall/modules/luckydraw/view/LuckydrawReviewView.dart';

// TODO: Move to a central config if available
const String _publicWebsiteBaseUrl = 'https://luckymall.com.my';

class JoinRaffleBottomSheet extends StatefulWidget {
  final Map<String, dynamic> product;

  const JoinRaffleBottomSheet({super.key, required this.product});

  @override
  State<JoinRaffleBottomSheet> createState() => _JoinRaffleBottomSheetState();
}

class _JoinRaffleBottomSheetState extends State<JoinRaffleBottomSheet> {
  int _pointsToUse = 1;
  final TextEditingController _pointsController = TextEditingController(
    text: '1',
  );

  int get _availablePoints =>
      9999; // This could be user-specific, keeping default for now
  int get _servingRemaining {
    final totalAmount = widget.product['totalAmount'] ?? 0;
    final usedAmount = widget.product['usedAmount'] ?? 0;
    return (totalAmount - usedAmount).clamp(0, totalAmount);
  }

  int get _totalPoints => widget.product['totalAmount'] ?? 0;
  int get _currentParticipants => widget.product['usedAmount'] ?? 0;

  @override
  void initState() {
    super.initState();
    _pointsController.addListener(() {
      final value = int.tryParse(_pointsController.text) ?? 1;
      if (value != _pointsToUse) {
        setState(() {
          _pointsToUse = value.clamp(1, _availablePoints);
        });
      }
    });
  }

  @override
  void dispose() {
    _pointsController.dispose();
    super.dispose();
  }

  void _updatePoints(int newValue) {
    setState(() {
      _pointsToUse = newValue.clamp(1, _availablePoints);
      _pointsController.text = _pointsToUse.toString();
    });
  }

  void _resetPoints() {
    _updatePoints(1);
  }

  void _participateAllRemaining() {
    _updatePoints(_servingRemaining);
  }

  void _incrementPoints() {
    _updatePoints(_pointsToUse + 1);
  }

  void _decrementPoints() {
    _updatePoints(_pointsToUse - 1);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with close button
          Container(
            padding: const EdgeInsets.all(10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Join Raffle',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: const Icon(Icons.close, size: 24, color: Colors.black),
                ),
              ],
            ),
          ),

          // Available Points
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Available Points: $_availablePoints',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
          ),
          const SizedBox(height: 8),

          // Product details card
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                // Product image
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child:
                      widget.product['productSkuImage'] != null &&
                          widget.product['productSkuImage'].isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            widget.product['productSkuImage'],
                            fit: BoxFit.cover,
                            width: 60,
                            height: 60,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(
                                Icons.image,
                                color: Colors.grey,
                                size: 30,
                              );
                            },
                          ),
                        )
                      : const Icon(Icons.image, color: Colors.grey, size: 30),
                ),
                const SizedBox(width: 12),
                // Product details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.product['productSkuName'] ?? 'Product Name',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Variation: Standard',
                        style: TextStyle(fontSize: 11, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),

          // Points input section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Enter Points to Use',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                Text(
                  'Serving Remaining: $_servingRemaining',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // Participate All Remaining button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _participateAllRemaining,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber[600],
                  foregroundColor: Colors.black,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Participate All Remaining servings',
                  style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ),
          const SizedBox(height: 10),

          // Points adjustment controls
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Minus button
                Container(
                  width: 35,
                  height: 35,
                  decoration: BoxDecoration(
                    color: Colors.grey[400],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: _pointsToUse > 1 ? _decrementPoints : null,
                    icon: const Icon(Icons.remove, color: Colors.white),
                    padding: EdgeInsets.zero,
                  ),
                ),
                // Points input field
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 12),
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextField(
                      controller: _pointsController,
                      textAlign: TextAlign.center,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(vertical: 12),
                      ),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                // Plus button
                Container(
                  width: 35,
                  height: 35,
                  decoration: BoxDecoration(
                    color: Colors.amber[600],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: _pointsToUse < _availablePoints
                        ? _incrementPoints
                        : null,
                    icon: const Icon(Icons.add, color: Colors.white),
                    padding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),

          // Bottom action buttons
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                // Reset button
                Expanded(
                  child: GestureDetector(
                    onTap: _resetPoints,
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade400,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Center(
                        child: Text(
                          'Reset',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                // Participate button
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                      // Show success popup overlay
                      SuccessJoinPopupOverlay.show(
                        context: context,
                        points: _pointsToUse,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      decoration: BoxDecoration(
                        color: const Color(0xFFffbf00),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          '+$_pointsToUse points',
                          style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Bottom safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom + 10),
        ],
      ),
    );
  }
}

class PointProductDetails extends StatefulWidget {
  final Map<String, dynamic> product;

  const PointProductDetails({super.key, required this.product});

  @override
  State<PointProductDetails> createState() => _PointProductDetailsState();
}

class _PointProductDetailsState extends State<PointProductDetails> {
  bool _descExpanded = false;
  final ScrollController _scrollController = ScrollController();
  double _appBarOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    double offset = _scrollController.offset;
    double opacity = (offset / 120).clamp(0, 1);
    if (opacity != _appBarOpacity) {
      setState(() {
        _appBarOpacity = opacity;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    super.dispose();
  }

  bool get _isTngProduct {
    // Check if this is a TNG product based on product data
    final productName =
        widget.product['productSkuName']?.toString().toLowerCase() ?? '';
    // Note: category field removed from API response, using product name only for now
    return productName.contains('tng') || productName.contains('e-wallet');
  }

  /// Calculate progress factor for the progress bar based on API data
  double _calculateProgressFactor() {
    final totalAmount = widget.product['totalAmount'] ?? 0;
    final usedAmount = widget.product['usedAmount'] ?? 0;

    if (totalAmount <= 0) return 0.0;

    // Calculate the percentage of slots filled
    final progress = usedAmount / totalAmount;

    // Ensure progress is between 0.0 and 1.0
    return progress.clamp(0.0, 1.0);
  }

  /// Calculate progress percentage for display
  int _calculateProgressPercentage() {
    final totalAmount = widget.product['totalAmount'] ?? 0;
    final usedAmount = widget.product['usedAmount'] ?? 0;

    if (totalAmount <= 0) return 0;

    final percentage = (usedAmount / totalAmount) * 100;
    return percentage.round();
  }

  /// Get remaining slots text
  String _getRemainingSlots() {
    final totalAmount = widget.product['totalAmount'] ?? 0;
    final usedAmount = widget.product['usedAmount'] ?? 0;
    final remaining = totalAmount - usedAmount;

    if (remaining <= 0) return '0';
    if (remaining == 1) return '1';
    return remaining.toString();
  }

  /// Get progress bar color based on progress level
  Color _getProgressBarColor() {
    final progress = _calculateProgressFactor();

    if (progress >= 0.9) {
      return Colors.red[600]!; // Almost full - red
    } else if (progress >= 0.7) {
      return Colors.orange[600]!; // Getting full - orange
    } else if (progress >= 0.5) {
      return Colors.amber[600]!; // Half full - amber
    } else {
      return Colors.green[600]!; // Lots of slots - green
    }
  }

  /// Check if product is fully booked
  bool get _isFullyBooked {
    final totalAmount = widget.product['totalAmount'] ?? 0;
    final usedAmount = widget.product['usedAmount'] ?? 0;
    return totalAmount > 0 && usedAmount >= totalAmount;
  }

  /// Get remaining slots count
  int get _remainingSlots {
    final totalAmount = widget.product['totalAmount'] ?? 0;
    final usedAmount = widget.product['usedAmount'] ?? 0;
    return (totalAmount - usedAmount).clamp(0, totalAmount);
  }

  /// Build TNG precaution notice
  Widget _buildTngPrecautionNotice(
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(
        horizontal: scaleW(16),
        vertical: scaleH(18),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning_amber,
                color: Colors.red[600],
                size: scaleW(20),
              ),
              SizedBox(width: scaleW(8)),
              Text(
                'TNG eWallet Notice',
                style: TextStyle(
                  fontSize: scaleText(16),
                  fontWeight: FontWeight.bold,
                  color: Colors.red[600],
                ),
              ),
            ],
          ),
          SizedBox(height: scaleH(12)),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(scaleW(12)),
            decoration: BoxDecoration(
              color: Colors.red[50],
              borderRadius: BorderRadius.circular(scaleW(8)),
              border: Border.all(color: Colors.red[200]!),
            ),
            child: Text(
              'Winners can choose to claim their prize as either a TNG eWallet voucher or have it credited to their Lucky Mall Wallet.',
              style: TextStyle(
                fontSize: scaleText(14),
                color: Colors.black87,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build product properties section using API data
  Widget _buildProductProperties(
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(
        horizontal: scaleW(16),
        vertical: scaleH(18),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.blue[600],
                size: scaleW(20),
              ),
              SizedBox(width: scaleW(8)),
              Text(
                'Product Properties',
                style: TextStyle(
                  fontSize: scaleText(16),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: scaleH(12)),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(scaleW(12)),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(scaleW(8)),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Text(
              widget.product['productSkuPropertys']?.toString() ?? '',
              style: TextStyle(
                fontSize: scaleText(14),
                color: Colors.black87,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375; // base width 375
    double scaleH(double value) =>
        value * screenHeight / 812; // base height 812
    double scaleText(double value) => value * screenWidth / 375;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product image
                SizedBox(
                  width: screenWidth,
                  height: screenWidth * 0.75,
                  child:
                      widget.product['productSkuImage'] != null &&
                          widget.product['productSkuImage'].isNotEmpty
                      ? Image.network(
                          widget.product['productSkuImage'],
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[200],
                              child: Icon(
                                Icons.image_not_supported,
                                color: Colors.grey,
                                size: scaleW(60),
                              ),
                            );
                          },
                        )
                      : Container(
                          color: Colors.grey[200],
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: scaleW(60),
                          ),
                        ),
                ),
                _buildProductInfo(context, scaleW, scaleH, scaleText),
                Divider(
                  height: 1,
                  thickness: scaleH(8),
                  color: const Color(0xFFF5F5F5),
                ),
                _buildPointsInfo(scaleW, scaleH, scaleText),
                Divider(
                  height: 1,
                  thickness: scaleH(8),
                  color: const Color(0xFFF5F5F5),
                ),
                _buildWinningHistory(scaleW, scaleH, scaleText),
                Divider(
                  height: 1,
                  thickness: scaleH(8),
                  color: const Color(0xFFF5F5F5),
                ),
                // Show product details if details exist, otherwise show description
                if (widget.product['details']?.toString().isNotEmpty == true)
                  _buildProductDetails(scaleW, scaleH, scaleText)
                else
                  _buildProductDescription(scaleW, scaleH, scaleText),

                // Always show TNG precaution notice for TNG products
                if (_isTngProduct) ...[
                  Divider(
                    height: 1,
                    thickness: scaleH(8),
                    color: const Color(0xFFF5F5F5),
                  ),
                  _buildTngPrecautionNotice(scaleW, scaleH, scaleText),
                ],

                // Show product properties if available from API
                if (widget.product['productSkuPropertys']
                        ?.toString()
                        .isNotEmpty ==
                    true) ...[
                  Divider(
                    height: 1,
                    thickness: scaleH(8),
                    color: const Color(0xFFF5F5F5),
                  ),
                  _buildProductProperties(scaleW, scaleH, scaleText),
                ],
                Divider(
                  height: 1,
                  thickness: scaleH(8),
                  color: const Color(0xFFF5F5F5),
                ),
                // Only show product ratings for non-TNG products
                if (!_isTngProduct)
                  _buildProductRatings(scaleW, scaleH, scaleText),
              ],
            ),
          ),
          // Overlay AppBar
          Container(
            height: kToolbarHeight + MediaQuery.of(context).padding.top,
            color: Colors.white.withOpacity(_appBarOpacity),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: Padding(
                padding: EdgeInsets.only(left: scaleW(12)),
                child: IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: Colors.black,
                    size: scaleW(24),
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              actions: [
                Padding(
                  padding: EdgeInsets.only(right: scaleW(8)),
                  child: IconButton(
                    icon: Stack(
                      children: [
                        Icon(
                          Icons.shopping_cart_outlined,
                          color: Colors.black,
                          size: scaleW(24),
                        ),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            padding: EdgeInsets.all(scaleW(2)),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            constraints: BoxConstraints(
                              minWidth: scaleW(12),
                              minHeight: scaleW(12),
                            ),
                            child: Text(
                              '1',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: scaleText(8),
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                    onPressed: () {
                      context.push('/shop/cart');
                    },
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(right: scaleW(12)),
                  child: IconButton(
                    icon: Icon(
                      Icons.share,
                      color: Colors.black,
                      size: scaleW(24),
                    ),
                    onPressed: () {
                      final productName =
                          widget.product['productSkuName']?.toString() ??
                          'this product';
                      final dynamic idDynamic = widget.product['id'];
                      final String? idString = idDynamic?.toString();
                      final String url = idString != null && idString.isNotEmpty
                          ? (_publicWebsiteBaseUrl +
                                '/lucky-draw/product/' +
                                idString)
                          : _publicWebsiteBaseUrl;
                      final shareText = productName + ' - ' + url;
                      Share.share(shareText);
                    },
                  ),
                ),
              ],
              titleSpacing: 0,
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomActionBar(
        context,
        scaleW,
        scaleH,
        scaleText,
      ),
    );
  }

  Widget _buildProductInfo(
    BuildContext context,
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        scaleW(16),
        scaleH(20),
        scaleW(16),
        scaleH(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product title
          Text(
            widget.product['productSkuName'] ?? 'Product Title',
            style: TextStyle(
              fontSize: scaleText(18),
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          // SKU Properties if available
          if (widget.product['productSkuPropertys']?.toString().isNotEmpty ==
              true) ...[
            SizedBox(height: scaleH(8)),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'SKU Properties:',
                  style: TextStyle(
                    fontSize: scaleText(14),
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: scaleW(8)),
                Expanded(
                  child: Text(
                    widget.product['productSkuPropertys'].toString(),
                    style: TextStyle(
                      fontSize: scaleText(14),
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPointsInfo(
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(
        horizontal: scaleW(16),
        vertical: scaleH(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top row with Total Shares on left and Participated Points on right
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left side - Total Shares
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total Shares:',
                      style: TextStyle(
                        fontSize: scaleText(13),
                        fontWeight: FontWeight.w600,
                        color: Colors.red,
                      ),
                    ),
                    Text(
                      '${widget.product['totalAmount'] ?? 0} Points',
                      style: TextStyle(
                        fontSize: scaleText(16),
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
              // Right side - Participated Points
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Participated Points:',
                    style: TextStyle(
                      fontSize: scaleText(13),
                      fontWeight: FontWeight.w600,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    '${widget.product['usedAmount'] ?? 0}',
                    style: TextStyle(
                      fontSize: scaleText(13),
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: scaleH(10)),
          // Progress bar positioned below Total Shares
          Container(
            height: scaleH(6),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(scaleW(3)),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: _calculateProgressFactor(),
              child: Container(
                decoration: BoxDecoration(
                  color: _getProgressBarColor(),
                  borderRadius: BorderRadius.circular(scaleW(3)),
                ),
              ),
            ),
          ),
          SizedBox(height: scaleH(8)),
          // Progress information below the bar
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                '${_getRemainingSlots()} points remaining',
                style: TextStyle(
                  fontSize: scaleText(12),
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWinningHistory(
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(
        horizontal: scaleW(16),
        vertical: scaleH(18),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Winning History',
                style: TextStyle(
                  fontSize: scaleText(16),
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                onPressed: () {
                  final int? goodsPointId = (widget.product['id'] as num?)
                      ?.toInt();
                  context.push(
                    '/lucky-draw/winning-history',
                    extra: {'goodsPointId': goodsPointId},
                  );
                },
                icon: Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[600],
                  size: scaleW(16),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductDescription(
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    final int maxLines = _descExpanded ? 100 : 3;
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(
        horizontal: scaleW(16),
        vertical: scaleH(18),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description,
                color: Colors.orange[600],
                size: scaleW(20),
              ),
              SizedBox(width: scaleW(8)),
              Text(
                'Product Description',
                style: TextStyle(
                  fontSize: scaleText(16),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: scaleH(12)),
          Text(
            widget.product['details']?.toString().isNotEmpty == true
                ? widget.product['details'].toString()
                : 'Product description will be available soon...',
            style: TextStyle(fontSize: scaleText(14), height: 1.5),
            maxLines: maxLines,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: scaleH(8)),
          GestureDetector(
            onTap: () => setState(() => _descExpanded = !_descExpanded),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: 1,
                  width: scaleW(40),
                  color: Colors.grey[300],
                ),
                SizedBox(width: scaleW(8)),
                Text(
                  _descExpanded ? 'See Less' : 'See More',
                  style: TextStyle(fontSize: scaleText(14), color: Colors.grey),
                ),
                SizedBox(width: scaleW(4)),
                Icon(
                  _descExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Colors.grey,
                  size: scaleW(18),
                ),
                SizedBox(width: scaleW(8)),
                Container(
                  height: 1,
                  width: scaleW(40),
                  color: Colors.grey[300],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductDetails(
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    // Get the details from API response
    final details = widget.product['details']?.toString();

    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(
        horizontal: scaleW(16),
        vertical: scaleH(18),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.blue[600],
                size: scaleW(20),
              ),
              SizedBox(width: scaleW(8)),
              Text(
                'Product Details',
                style: TextStyle(
                  fontSize: scaleText(16),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: scaleH(12)),

          // Display API details if available, otherwise show fallback content
          if (details != null && details.isNotEmpty) ...[
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(scaleW(12)),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(scaleW(8)),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Text(
                details,
                style: TextStyle(
                  fontSize: scaleText(14),
                  color: Colors.black87,
                  height: 1.5,
                ),
              ),
            ),
          ] else ...[
            // Fallback content when no details from API
            _buildHighlightItem(
              Icons.flash_on,
              "Product highlights will be available soon...",
              scaleW,
              scaleH,
              scaleText,
            ),
            SizedBox(height: scaleH(8)),
            _buildHighlightItem(
              Icons.store,
              "More information about this product will be displayed here.",
              scaleW,
              scaleH,
              scaleText,
            ),
          ],

          // Show additional info for TNG products if details exist
          if (details != null && details.isNotEmpty && _isTngProduct) ...[
            SizedBox(height: scaleH(16)),
            Container(
              padding: EdgeInsets.all(scaleW(12)),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(scaleW(8)),
                border: Border.all(color: Colors.red[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.warning_amber,
                        color: Colors.red[600],
                        size: scaleW(16),
                      ),
                      SizedBox(width: scaleW(6)),
                      Text(
                        'Notice:',
                        style: TextStyle(
                          fontSize: scaleText(14),
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: scaleH(6)),
                  Text(
                    'Winners can choose to claim their prize as either a TNG eWallet voucher or have it credited to their Lucky Mall Wallet.',
                    style: TextStyle(
                      fontSize: scaleText(12),
                      color: Colors.black87,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHighlightItem(
    IconData icon,
    String text,
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: Colors.green[600], size: scaleW(16)),
        SizedBox(width: scaleW(8)),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: scaleText(13),
              color: Colors.black87,
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReviewItem(
    Map<String, dynamic> review,
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: scaleH(18)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row 1: Avatar, Name, Date
          Row(
            children: [
              CircleAvatar(
                radius: scaleW(16),
                backgroundColor: Colors.grey,
                child: Icon(
                  Icons.person,
                  size: scaleW(18),
                  color: Colors.white,
                ),
              ),
              SizedBox(width: scaleW(8)),
              Expanded(
                child: Text(
                  'Anonymous',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: scaleText(12),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                '2025-01-15',
                style: TextStyle(color: Colors.grey, fontSize: scaleText(12)),
              ),
            ],
          ),
          SizedBox(height: scaleH(6)),
          // Row 2: Stars, Helpful
          Row(
            children: [
              Row(
                children: List.generate(
                  5,
                  (index) => Icon(
                    index < (review['rating'] ?? 0)
                        ? Icons.star
                        : Icons.star_border,
                    color: Colors.amber[700],
                    size: scaleW(16),
                  ),
                ),
              ),
              SizedBox(width: scaleW(8)),
              Text(
                'Helpful (0)',
                style: TextStyle(
                  fontSize: scaleText(12),
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(width: scaleW(2)),
              Icon(
                Icons.thumb_up_alt_rounded,
                size: scaleW(14),
                color: Colors.amber,
              ),
            ],
          ),
          SizedBox(height: scaleH(4)),
          // Row 3: Variation
          if (review['variation'] != null && review['variation'].isNotEmpty)
            Text(
              'Variation: ${review['variation']}',
              style: TextStyle(fontSize: scaleText(12), color: Colors.grey),
            ),
          if (review['variation'] != null && review['variation'].isNotEmpty)
            SizedBox(height: scaleH(6)),
          // Row 4: Comment
          Text(
            'Review text will be available soon...',
            style: TextStyle(fontSize: scaleText(14), color: Colors.black87),
          ),
          // Row 5: Review images (conditional)
          // Note: Review images will be available soon
        ],
      ),
    );
  }

  Widget _buildProductRatings(
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    final List<dynamic> reviews = []; // Reviews will be available soon

    return Container(
      color: const Color(0xFFFFF8E1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: scaleW(16),
              vertical: scaleH(12),
            ),
            child: Row(
              children: [
                Text(
                  widget.product['score']?.toString() ?? '4.9',
                  style: TextStyle(
                    fontSize: scaleText(20),
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                SizedBox(width: scaleW(4)),
                Icon(Icons.star, color: Colors.amber, size: scaleW(20)),
                SizedBox(width: scaleW(8)),
                Expanded(
                  child: Text(
                    'Product Ratings & Reviews',
                    style: TextStyle(
                      fontSize: scaleText(15),
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (reviews.isNotEmpty)
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const LuckydrawReviewView(),
                        ),
                      );
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: scaleW(14),
                        vertical: scaleH(6),
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(scaleW(8)),
                      ),
                      child: Text(
                        'View All',
                        style: TextStyle(
                          fontSize: scaleText(13),
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // Reviews content
          Container(
            color: Colors.white,
            padding: EdgeInsets.fromLTRB(scaleW(16), 0, scaleW(16), scaleH(24)),
            child: reviews.isEmpty
                ? Center(
                    child: Text(
                      'No reviews available',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: scaleText(14),
                      ),
                    ),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: reviews.length,
                    itemBuilder: (context, index) {
                      final review = reviews[index];
                      return _buildReviewItem(
                        review,
                        scaleW,
                        scaleH,
                        scaleText,
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar(
    BuildContext context,
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    return Container(
      height: scaleH(80).clamp(60, 100), // Ensure minimum and maximum height
      padding: EdgeInsets.all(
        scaleW(16).clamp(12, 20),
      ), // Ensure minimum and maximum padding
      color: Colors.white,
      child: ElevatedButton(
        onPressed: _isFullyBooked
            ? null
            : () {
                // Handle participate action
                _showParticipateDialog(context);
              },
        style: ElevatedButton.styleFrom(
          backgroundColor: _isFullyBooked
              ? Colors.grey[400]
              : Colors.amber[600],
          foregroundColor: Colors.black,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(scaleW(8).clamp(6, 12)),
          ),
          textStyle: TextStyle(
            fontSize: scaleText(18).clamp(14, 22),
            fontWeight: FontWeight.bold,
          ),
        ),
        child: Text(
          _isFullyBooked ? 'FULLY BOOKED' : 'PARTICIPATE',
          style: TextStyle(
            fontSize: scaleText(15).clamp(12, 18),
            fontWeight: FontWeight.w600,
            color: Colors.black, // Ensure text color is explicitly set
          ),
        ),
      ),
    );
  }

  void _showParticipateDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return JoinRaffleBottomSheet(product: widget.product);
      },
    );
  }
}
