import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class MerchantPolicyview extends StatelessWidget {
  const MerchantPolicyview({super.key});

  @override
  Widget build(BuildContext context) {
    // Randomly generated merchant terms and policy sections
    final List<_TermsSection> sections = [
      _TermsSection(
        title: "Introduction",
        content:
            "These Merchant Terms and Conditions govern your use of the Lucky Mall platform as a merchant. Please read these terms carefully before listing or selling products.",
      ),
      _TermsSection(
        title: "1. Merchant Registration",
        content:
            "Merchants must provide accurate business information and comply with all applicable laws and regulations. Lucky Mall reserves the right to approve or reject merchant applications.",
      ),
      _TermsSection(
        title: "2. Product Listings",
        content:
            "All product listings must be accurate and not misleading. Prohibited items, counterfeit goods, and illegal products are strictly forbidden.",
      ),
      _TermsSection(
        title: "3. Order Fulfillment",
        content:
            "Merchants are responsible for timely order processing, packaging, and shipping. Delays or failure to fulfill orders may result in penalties or suspension.",
      ),
      _TermsSection(
        title: "4. Returns and Refunds",
        content:
            "Merchants must adhere to Lucky Mall's return and refund policies. Promptly process returns and issue refunds as required.",
      ),
      _TermsSection(
        title: "5. Fees and Payments",
        content:
            "Lucky Mall may charge service fees or commissions on sales. Payment terms and schedules will be communicated to merchants.",
      ),
      _TermsSection(
        title: "6. Intellectual Property",
        content:
            "Merchants must have the rights to all content and products listed. Infringement of intellectual property rights is prohibited.",
      ),
      _TermsSection(
        title: "7. Data Privacy",
        content:
            "Merchants must protect customer data and use it only for order fulfillment and customer service purposes.",
      ),
      _TermsSection(
        title: "8. Termination",
        content:
            "Lucky Mall reserves the right to suspend or terminate merchant accounts for violations of these terms or applicable laws.",
      ),
      _TermsSection(
        title: "9. Contact Us",
        content:
            "For questions regarding merchant policies, please contact our merchant support <NAME_EMAIL>.",
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        centerTitle: true,
        title: const Text(
          'Merchant Terms and Conditions',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Lucky Mall Merchant Policy",
              style: TextStyle(fontWeight: FontWeight.w700, fontSize: 18),
            ),
            const SizedBox(height: 18),
            ...sections.map(
              (section) => Padding(
                padding: const EdgeInsets.only(bottom: 18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      section.title,
                      style: TextStyle(
                        fontWeight: section.title == "Introduction"
                            ? FontWeight.w700
                            : FontWeight.w600,
                        fontSize: section.title == "Introduction" ? 16 : 15,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      section.content,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TermsSection {
  final String title;
  final String content;
  _TermsSection({required this.title, required this.content});
}
