import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import '../../blindbox/widgets/PostCard.dart';
import '../view-model/PostProfileViewVM.dart';

class PostProfileView extends StatefulWidget {
  const PostProfileView({super.key, required this.userName});

  final String userName;

  @override
  State<PostProfileView> createState() => _PostProfileViewState();
}

class _PostProfileViewState extends State<PostProfileView> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => PostProfileViewVM(userName: widget.userName),
      child: Consumer<PostProfileViewVM>(
        builder: (context, vm, _) {
          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              backgroundColor: Color(0xFFFFEAAB),
              elevation: 0,
              toolbarHeight: 0,
            ),
            body: SingleChildScrollView(
              child: Column(
                children: [
                  // Yellow background with profile section
                  Stack(
                    children: [
                      Container(
                        width: double.infinity,
                        height: 150,
                        decoration: const BoxDecoration(
                          color: Color(0xFFFFEAAB),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(20),
                            bottomRight: Radius.circular(20),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 10,
                        left: 16,
                        child: GestureDetector(
                          onTap: () => context.pop(),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.8),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.arrow_back_ios_new,
                              color: Colors.black,
                              size: PostProfileViewVM.getResponsiveFontSize(
                                20,
                                context,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Column(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/profiles/EmptyProfile.svg',
                              width: 70,
                              height: 70,
                              colorFilter: const ColorFilter.mode(
                                Colors.black,
                                BlendMode.srcIn,
                              ),
                            ),
                            SizedBox(
                              height: PostProfileViewVM.getResponsivePadding(
                                8,
                                context,
                              ),
                            ),
                            Text(
                              vm.userName,
                              style: TextStyle(
                                fontSize:
                                    PostProfileViewVM.getResponsiveFontSize(
                                      16,
                                      context,
                                    ),
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                              ),
                            ),
                            SizedBox(
                              height: PostProfileViewVM.getResponsivePadding(
                                12,
                                context,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  // Stats section
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(
                      horizontal: PostProfileViewVM.getResponsivePadding(
                        40,
                        context,
                      ),
                      vertical: PostProfileViewVM.getResponsivePadding(
                        24,
                        context,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Column(
                          children: [
                            Text(
                              vm.blindBoxCount.toString(),
                              style: TextStyle(
                                fontSize:
                                    PostProfileViewVM.getResponsiveFontSize(
                                      20,
                                      context,
                                    ),
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            SizedBox(
                              height: PostProfileViewVM.getResponsivePadding(
                                4,
                                context,
                              ),
                            ),
                            Text(
                              "Blind Box",
                              style: TextStyle(
                                fontSize:
                                    PostProfileViewVM.getResponsiveFontSize(
                                      14,
                                      context,
                                    ),
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            Text(
                              vm.cardCount.toString(),
                              style: TextStyle(
                                fontSize:
                                    PostProfileViewVM.getResponsiveFontSize(
                                      20,
                                      context,
                                    ),
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            SizedBox(
                              height: PostProfileViewVM.getResponsivePadding(
                                4,
                                context,
                              ),
                            ),
                            Text(
                              "Card",
                              style: TextStyle(
                                fontSize:
                                    PostProfileViewVM.getResponsiveFontSize(
                                      14,
                                      context,
                                    ),
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Post Grid or Loading State
                  Container(
                    padding: EdgeInsets.fromLTRB(
                      PostProfileViewVM.getResponsivePadding(12, context),
                      PostProfileViewVM.getResponsivePadding(8, context),
                      PostProfileViewVM.getResponsivePadding(12, context),
                      PostProfileViewVM.getResponsivePadding(24, context),
                    ),
                    child: vm.isLoading
                        ? MasonryGridView.count(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            crossAxisCount:
                                PostProfileViewVM.getResponsiveCrossAxisCount(
                                  context,
                                ),
                            mainAxisSpacing:
                                PostProfileViewVM.getResponsivePadding(
                                  8,
                                  context,
                                ),
                            crossAxisSpacing:
                                PostProfileViewVM.getResponsivePadding(
                                  8,
                                  context,
                                ),
                            itemCount: 6,
                            itemBuilder: (context, index) {
                              return const PostCardShimmer();
                            },
                          )
                        : vm.userPosts.isEmpty
                        ? Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: PostProfileViewVM.getResponsivePadding(
                                40,
                                context,
                              ),
                            ),
                            child: Center(
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.photo_library_outlined,
                                    size:
                                        PostProfileViewVM.getResponsiveFontSize(
                                          48,
                                          context,
                                        ),
                                    color: Colors.grey[400],
                                  ),
                                  SizedBox(
                                    height:
                                        PostProfileViewVM.getResponsivePadding(
                                          16,
                                          context,
                                        ),
                                  ),
                                  Text(
                                    "No post yet 💤",
                                    style: TextStyle(
                                      fontSize:
                                          PostProfileViewVM.getResponsiveFontSize(
                                            16,
                                            context,
                                          ),
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        : MasonryGridView.count(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            crossAxisCount:
                                PostProfileViewVM.getResponsiveCrossAxisCount(
                                  context,
                                ),
                            mainAxisSpacing:
                                PostProfileViewVM.getResponsivePadding(
                                  8,
                                  context,
                                ),
                            crossAxisSpacing:
                                PostProfileViewVM.getResponsivePadding(
                                  8,
                                  context,
                                ),
                            itemCount: vm.userPosts.length,
                            itemBuilder: (context, index) {
                              final post = vm.userPosts[index];
                              return PostCard(
                                username: post['username'] as String,
                                isFavorite: (post['isFavorite'] as bool?) ?? false,
                                imageUrl: post['imageUrl'] as String,
                                title: post['title'] as String,
                                likeCount: post['likeCount'] as int,
                                onTap: () {
                                  context.push(
                                    '/blind-box/post/${post['id']}',
                                    extra: {
                                      ...post,
                                      'images': [post['imageUrl']],
                                    },
                                  );
                                },
                              );
                            },
                          ),
                  ),

                  // Pull to refresh indicator (if needed)
                  if (vm.isLoading && vm.userPosts.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.all(
                        PostProfileViewVM.getResponsivePadding(16, context),
                      ),
                      child: const CircularProgressIndicator(
                        color: Color(0xFFFFEAAB),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
