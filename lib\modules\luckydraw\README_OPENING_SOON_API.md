# Opening Soon Products API Integration

This document describes the integration of the opening soon products API endpoint for the Lucky Draw module.

## API Endpoint

**GET** `/product/point/opening`

**Base URL:** `https://api.luckymall.com.my`

**Response Structure:**
```json
{
  "status": 0,
  "message": "",
  "data": [
    {
      "id": 0,
      "productSpuId": 0,
      "productSkuId": 0,
      "productSkuName": "",
      "productSkuImage": "",
      "usedAmount": 0,
      "totalAmount": 0,
      "score": 0,
      "sold": 0
    }
  ],
  "timestamp": 0
}
```

## Implementation Files

### 1. Model
- **File:** `lib/modules/luckydraw/model/OpenSoonProduct.dart`
- **Purpose:** Data model for opening soon products
- **Fields:** Maps exactly to the API response (excluding `estimatedOpenDate` and `priority` as requested)

### 2. Service
- **File:** `lib/modules/luckydraw/data/OpenSoonProductService.dart`
- **Purpose:** Handles API calls to the opening soon endpoint
- **Method:** `getOpeningSoonProducts()`

### 3. Repository
- **File:** `lib/modules/luckydraw/data/LuckyDrawRepository.dart`
- **Purpose:** Abstraction layer that uses the service
- **Method:** `fetchOpeningSoonProducts()`

### 4. View Model
- **File:** `lib/modules/luckydraw/view-model/OpenSoonVM.dart`
- **Purpose:** Business logic and state management for opening soon products
- **Features:** Loading states, error handling, data sorting

## Usage Examples

### 1. Basic API Call

```dart
import '../data/OpenSoonProductService.dart';

final service = openSoonProductService;

try {
  final products = await service.getOpeningSoonProducts();
  print('Fetched ${products.length} opening soon products');
} catch (e) {
  print('Error: $e');
}
```

### 2. Using the Repository

```dart
import '../data/LuckyDrawRepository.dart';

final repository = MockLuckyDrawRepository();

try {
  final products = await repository.fetchOpeningSoonProducts();
  print('Fetched ${products.length} opening soon products');
} catch (e) {
  print('Error: $e');
}
```

### 3. Using the View Model

```dart
import '../view-model/OpenSoonVM.dart';

final viewModel = OpenSoonVM();

// Listen to changes
viewModel.addListener(() {
  if (viewModel.isLoading) {
    print('Loading opening soon products...');
  } else if (viewModel.errorMessage != null) {
    print('Error: ${viewModel.errorMessage}');
  } else {
    print('Loaded ${viewModel.products.length} products');
  }
});

// Fetch data
await viewModel.fetchOpeningSoonProducts();

// Access sorted data
final nearlyReady = viewModel.nearlyReadyProducts; // Progress > 90%
final byProgress = viewModel.productsByProgress;   // Sorted by progress
final byScore = viewModel.productsByScore;         // Sorted by score
```

### 4. Integration in Views

```dart
import '../view-model/OpenSoonVM.dart';

class MyView extends StatefulWidget {
  @override
  _MyViewState createState() => _MyViewState();
}

class _MyViewState extends State<MyView> {
  late final OpenSoonVM _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = OpenSoonVM();
    _viewModel.addListener(_onViewModelChanged);
    _viewModel.fetchOpeningSoonProducts();
  }

  @override
  void dispose() {
    _viewModel.removeListener(_onViewModelChanged);
    super.dispose();
  }

  void _onViewModelChanged() {
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (_viewModel.isLoading) {
      return CircularProgressIndicator();
    }

    if (_viewModel.errorMessage != null) {
      return Text('Error: ${_viewModel.errorMessage}');
    }

    return ListView.builder(
      itemCount: _viewModel.products.length,
      itemBuilder: (context, index) {
        final product = _viewModel.products[index];
        return ListTile(
          title: Text(product.productSkuName),
          subtitle: Text('Progress: ${product.calculatedProgressPercentage.toStringAsFixed(1)}%'),
          leading: Image.network(product.productSkuImage),
        );
      },
    );
  }
}
```

## Model Features

### Helper Getters

- `calculatedProgressPercentage`: Progress percentage (0-100%)
- `remainingPoints`: Points needed to complete
- `isReadyToOpen`: Whether product is ready to open
- `productId`: Legacy compatibility (converts id to string)
- `productName`: Legacy compatibility (returns productSkuName)
- `imageUrl`: Legacy compatibility (returns productSkuImage)
- `pointValue`: Legacy compatibility (returns totalAmount)
- `currentPoints`: Legacy compatibility (returns usedAmount)
- `progressPercentage`: Legacy compatibility (returns calculatedProgressPercentage)

### Data Validation

- Handles null values in API response with sensible defaults
- Progress percentage is clamped between 0.0 and 100.0
- Remaining points cannot be negative

## Error Handling

The service includes comprehensive error handling for:
- Network timeouts
- HTTP errors
- API errors (non-zero status)
- Unexpected exceptions

All errors are logged with detailed information for debugging.

## Testing

A comprehensive test suite is available in `test/api_integration_test.dart` covering:
- JSON parsing
- Progress calculations
- Edge cases
- Null value handling

## Migration Notes

### Removed Fields
The following fields were removed from the model as requested:
- `estimatedOpenDate`: Not provided by the API
- `priority`: Not provided by the API

### Updated Views
The following views have been updated to use real API data instead of hardcoded mock data:
- `PrizeRevealView`: Now uses `OpenSoonVM` for real data
- `OpenSoonCard`: Widget remains unchanged but receives real data

## Future Enhancements

Potential improvements for future versions:
1. Add caching for offline support
2. Implement pagination for large product lists
3. Add filtering by category or point range
4. Real-time updates for progress changes
5. Push notifications for products ready to open
