import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TransferAccountIdDialog extends StatefulWidget {
  const TransferAccountIdDialog({super.key});

  @override
  State<TransferAccountIdDialog> createState() => _TransferAccountIdDialogState();
}

class _TransferAccountIdDialogState extends State<TransferAccountIdDialog> {
  final TextEditingController _controller = TextEditingController();
  String? _errorText;

  void _handleConfirm() {
    final accountId = _controller.text.trim();

    // Validate: must be 1–12 digits only
    if (accountId.isEmpty || !RegExp(r'^\d{1,12}$').hasMatch(accountId)) {
      setState(() {
        _errorText = 'Please enter a valid Account ID (up to 12 digits only)';
      });
      return;
    }

    Navigator.pop(context, accountId);
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scale = (screenWidth / 390.0).clamp(0.9, 1.2);

    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 40),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(20 * scale),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              'Transfer by Account ID',
              style: TextStyle(
                fontSize: 18 * scale,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),

            // Account ID Input
            TextField(
              controller: _controller,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly, // ✅ digits only
                LengthLimitingTextInputFormatter(12), // ✅ max 12
              ],
              decoration: InputDecoration(
                labelText: 'Recipient\'s Account ID',
                hintText: 'Example: ************',
                labelStyle: TextStyle(fontSize: 14 * scale),
                hintStyle: TextStyle(color: Colors.grey[400]),
                errorText: _errorText,
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.amber.shade700, width: 1.5),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.amber.shade700, width: 2),
                ),
              ),
              onChanged: (_) {
                if (_errorText != null) {
                  setState(() => _errorText = null);
                }
              },
            ),

            const SizedBox(height: 24),

            // Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      fontSize: 14 * scale,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _handleConfirm,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.redAccent,
                    padding: EdgeInsets.symmetric(horizontal: 20 * scale, vertical: 12 * scale),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6 * scale),
                    ),
                  ),
                  child: Text(
                    'Confirm',
                    style: TextStyle(
                      fontSize: 14 * scale,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
