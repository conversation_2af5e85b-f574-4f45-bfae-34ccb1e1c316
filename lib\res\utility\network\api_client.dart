import 'package:dio/dio.dart';
import 'package:luckymall/router/auth_guard.dart';
import 'package:flutter/foundation.dart';
import 'package:luckymall/res/utility/jwt_token_helper.dart';

/// Centralized API client providing configured Dio instances.
/// - `authDio` automatically attaches the latest JWT token from `AuthService`
///   as a Bearer Authorization header for each request.
/// - `publicDio` is for endpoints that do not require authentication.
class ApiClient {
  ApiClient._internal() {
    authDio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Normalize absolute URLs pointing to our base host to path-only
          final Uri originalUri = options.uri;
          if (originalUri.host == _baseApiHost) {
            final String normalizedPath = originalUri.path.startsWith('/')
                ? originalUri.path
                : '/${originalUri.path}';
            options.path = normalizedPath;
            if (originalUri.query.isNotEmpty) {
              final mergedQuery = <String, dynamic>{}
                ..addAll(originalUri.queryParameters)
                ..addAll(options.queryParameters);
              options.queryParameters = mergedQuery;
            }
          }

          debugPrint('[ApiClient] Interceptor: Getting JWT token...');
          final String? token = await AuthService.getJwtToken();
          final int? timestamp = await AuthService.getTimestamp();

          // Ensure we sign only the path portion (exclude base URL)
          final Uri resolvedUri = options.uri;
          final String endpointPath = resolvedUri.path.startsWith('/')
              ? resolvedUri.path
              : '/${resolvedUri.path}';

          if (token != null &&
              token.isNotEmpty &&
              timestamp != null &&
              timestamp.toString().isNotEmpty) {
            try {
              final String signature = await SignatureHelper.getSignature(
                token,
                timestamp.toString(),
                endpointPath,
                options.method,
              );

              debugPrint(
                '[ApiClient] Interceptor: Token received: ${token.substring(0, 20)}...',
              );
              options.headers['Authorization'] = token;
              options.headers['Timestamp'] = timestamp;
              options.headers['Signature'] = signature;
              debugPrint(
                '[ApiClient] Interceptor: Authorization header added.',
              );
            } catch (e, s) {
              debugPrint(
                '[ApiClient] Interceptor: Signature generation failed: $e',
              );
              if (kDebugMode) {
                debugPrint('$s');
              }
              // Proceed without signature if unavailable, but still attach token/timestamp
              options.headers['Authorization'] = token;
              options.headers['Timestamp'] = timestamp;
            }
          } else {
            debugPrint(
              '[ApiClient] Interceptor: Missing token or timestamp. Skipping auth headers.',
            );
          }

          debugPrint(
            '[ApiClient] Interceptor: Final headers: ${options.headers}',
          );
          return handler.next(options);
        },
      ),
    );

    // Public client normalization for absolute URLs
    publicDio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final Uri originalUri = options.uri;
          if (originalUri.host == _baseApiHost) {
            final String normalizedPath = originalUri.path.startsWith('/')
                ? originalUri.path
                : '/${originalUri.path}';
            options.path = normalizedPath;
            if (originalUri.query.isNotEmpty) {
              final mergedQuery = <String, dynamic>{}
                ..addAll(originalUri.queryParameters)
                ..addAll(options.queryParameters);
              options.queryParameters = mergedQuery;
            }
          }
          return handler.next(options);
        },
      ),
    );
  }

  static final ApiClient instance = ApiClient._internal();

  static const String _baseApiUrl = 'https://api.luckymall.com.my';
  static final String _baseApiHost = Uri.parse(_baseApiUrl).host;

  final Dio authDio = Dio(
    BaseOptions(
      baseUrl: _baseApiUrl,
      connectTimeout: Duration(seconds: 30),
      receiveTimeout: Duration(seconds: 30),
      sendTimeout: Duration(seconds: 30),
      headers: <String, dynamic>{'Content-Type': 'application/json'},
    ),
  );

  final Dio publicDio = Dio(
    BaseOptions(
      baseUrl: _baseApiUrl,
      connectTimeout: Duration(seconds: 30),
      receiveTimeout: Duration(seconds: 30),
      sendTimeout: Duration(seconds: 30),
      headers: <String, dynamic>{'Content-Type': 'application/json'},
    ),
  );
}
