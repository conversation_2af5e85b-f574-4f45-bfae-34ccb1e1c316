import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

class ImageSliderWithDots extends StatelessWidget {
  final List<String> imageUrls;
  final int currentIndex;
  final ValueChanged<int>? onPageChanged;

  const ImageSliderWithDots({
    super.key,
    required this.imageUrls,
    required this.currentIndex,
    required this.onPageChanged,
  });

  bool _isNetworkImage(String path) {
    return path.startsWith('http://') || path.startsWith('https://');
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    const double aspectRatio = 390 / 334;
    final double sliderHeight = screenWidth / aspectRatio;

    final List<Widget> sliderItems = imageUrls.isEmpty
        ? [
            _buildPlaceholder(screenWidth, sliderHeight) // Fallback if empty
          ]
        : imageUrls.map((url) {
            if (_isNetworkImage(url)) {
              return CachedNetworkImage(
                imageUrl: url,
                width: screenWidth,
                fit: BoxFit.cover,
                placeholder: (context, _) => _buildShimmer(screenWidth, sliderHeight),
                errorWidget: (context, _, __) =>
                    _buildErrorImage(screenWidth, sliderHeight),
              );
            } else {
              return Image.asset(
                url,
                width: screenWidth,
                fit: BoxFit.cover,
                errorBuilder: (context, _, __) =>
                    _buildErrorImage(screenWidth, sliderHeight),
              );
            }
          }).toList();

    return SizedBox(
      width: screenWidth,
      height: sliderHeight,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          CarouselSlider(
            options: CarouselOptions(
              height: sliderHeight,
              autoPlay: sliderItems.length > 1,
              viewportFraction: 1.0,
              onPageChanged: (index, _) => onPageChanged?.call(index),
            ),
            items: sliderItems,
          ),
          if (sliderItems.length > 1)
            Positioned(
              bottom: 12,
              child: Row(
                children: List.generate(sliderItems.length, (index) {
                  final isActive = currentIndex == index;
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width: isActive ? 12 : 8,
                    height: isActive ? 12 : 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      color: isActive ? Colors.amber : Colors.white54,
                      shape: BoxShape.circle,
                    ),
                  );
                }),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildShimmer(double width, double height) {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(width: width, height: height, color: Colors.white),
    );
  }

  Widget _buildErrorImage(double width, double height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey.shade200,
      child: const Icon(Icons.broken_image, size: 50, color: Colors.grey),
    );
  }

  Widget _buildPlaceholder(double width, double height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey.shade200,
      child: const Icon(Icons.image_not_supported, size: 50, color: Colors.grey),
    );
  }
}
