import 'package:flutter/material.dart';

class ProductReview {
  final String id;
  final String userName;
  final double rating;
  final String comment;
  final String date;
  final String variation;
  final int helpfulCount;

  const ProductReview({
    required this.id,
    required this.userName,
    required this.rating,
    required this.comment,
    required this.date,
    required this.variation,
    required this.helpfulCount,
  });
}

class ProductReviewsSection extends StatelessWidget {
  final double rating;
  final List<ProductReview> reviews;
  final VoidCallback? onViewAllPressed;
  final bool isLoading;
  final Function(String, BuildContext)? onHelpfulPressed;
  final bool Function(String)? isReviewHelpfulLoading;

  const ProductReviewsSection({
    super.key,
    required this.rating,
    required this.reviews,
    this.onViewAllPressed,
    this.isLoading = false,
    this.onHelpfulPressed,
    this.isReviewHelpfulLoading,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFFFF8E1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: getResponsivePadding(16, context),
              vertical: getResponsivePadding(12, context),
            ),
            child: Row(
              children: [
                Text(
                  rating.toStringAsFixed(1),
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(20, context),
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                SizedBox(width: getResponsiveFontSize(4, context)),
                Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: getResponsiveFontSize(20, context),
                ),
                SizedBox(width: getResponsiveFontSize(8, context)),
                Text(
                  'Product Ratings & Reviews',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(15, context),
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                if (reviews.isNotEmpty) ...[
                  SizedBox(width: getResponsiveFontSize(8, context)),
                  Text(
                    '(${reviews.length})',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(14, context),
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
                const Spacer(),
                if (onViewAllPressed != null)
                  GestureDetector(
                    onTap: onViewAllPressed,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: getResponsivePadding(14, context),
                        vertical: getResponsivePadding(6, context),
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(
                          getResponsiveFontSize(8, context),
                        ),
                      ),
                      child: Text(
                        'View All',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(13, context),
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // Reviews
          Container(
            color: Colors.white,
            padding: EdgeInsets.fromLTRB(
              getResponsivePadding(16, context),
              getResponsivePadding(8, context),
              getResponsivePadding(16, context),
              getResponsivePadding(24, context),
            ),
            child: isLoading
                ? Column(
                    children: List.generate(
                      3,
                      (index) => _buildReviewSkeleton(context),
                    ),
                  )
                : reviews.isEmpty
                ? _buildNoReviewsState(context)
                : Column(
                    children: reviews
                        .map((review) => _buildReviewCard(context, review))
                        .toList(),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewCard(BuildContext context, ProductReview review) {
    // Responsive font scaling
    final double screenWidth = MediaQuery.of(context).size.width;
    final double baseFontSize = screenWidth > 800 ? 18 : 14;
    final double smallFontSize = screenWidth > 800 ? 15 : 12;

    return Container(
      margin: EdgeInsets.only(bottom: getResponsivePadding(18, context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row 1: Avatar, Name, Date
          Row(
            children: [
              CircleAvatar(
                radius: getResponsiveFontSize(16, context),
                backgroundColor: Colors.grey,
                child: Icon(
                  Icons.person,
                  size: getResponsiveFontSize(18, context),
                  color: Colors.white,
                ),
              ),
              SizedBox(width: getResponsiveFontSize(8, context)),
              Text(
                review.userName,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: smallFontSize,
                ),
              ),
              const Spacer(),
              Text(
                review.date,
                style: TextStyle(color: Colors.grey, fontSize: smallFontSize),
              ),
            ],
          ),
          SizedBox(height: getResponsiveFontSize(6, context)),
          // Row 2: Stars, Helpful
          Row(
            children: [
              Row(
                children: List.generate(
                  5,
                  (index) => Icon(
                    index < review.rating ? Icons.star : Icons.star_border,
                    color: Colors.amber[700],
                    size: getResponsiveFontSize(16, context),
                  ),
                ),
              ),
              SizedBox(width: getResponsiveFontSize(8, context)),
              Text(
                'Helpful (${review.helpfulCount})',
                style: TextStyle(
                  fontSize: smallFontSize,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(width: getResponsiveFontSize(2, context)),
              GestureDetector(
                onTap: onHelpfulPressed != null
                    ? () => onHelpfulPressed!(review.id, context)
                    : null,
                child:
                    isReviewHelpfulLoading != null &&
                        isReviewHelpfulLoading!(review.id)
                    ? SizedBox(
                        width: getResponsiveFontSize(14, context),
                        height: getResponsiveFontSize(14, context),
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.amber,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.thumb_up_alt_rounded,
                        size: getResponsiveFontSize(14, context),
                        color: onHelpfulPressed != null
                            ? Colors.amber
                            : Colors.grey[400],
                      ),
              ),
            ],
          ),
          SizedBox(height: getResponsiveFontSize(4, context)),
          // Row 3: Variation
          Text(
            'Variation: ${review.variation}',
            style: TextStyle(fontSize: smallFontSize, color: Colors.grey),
          ),
          SizedBox(height: getResponsiveFontSize(6, context)),
          // Row 4: Comment
          Text(
            review.comment,
            style: TextStyle(fontSize: baseFontSize, color: Colors.black87),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewSkeleton(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;

    return Container(
      margin: EdgeInsets.only(bottom: getResponsivePadding(18, context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row 1: Avatar, Name, Date
          Row(
            children: [
              CircleAvatar(
                radius: getResponsiveFontSize(16, context),
                backgroundColor: Colors.grey[300],
                child: Icon(
                  Icons.person,
                  size: getResponsiveFontSize(18, context),
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(width: getResponsiveFontSize(8, context)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: getResponsiveFontSize(16, context),
                      width: screenWidth * 0.4, // Adjust width for skeleton
                      color: Colors.grey[300],
                    ),
                    SizedBox(height: getResponsiveFontSize(4, context)),
                    Container(
                      height: getResponsiveFontSize(14, context),
                      width: screenWidth * 0.3, // Adjust width for skeleton
                      color: Colors.grey[300],
                    ),
                  ],
                ),
              ),
              SizedBox(width: getResponsiveFontSize(8, context)),
              Container(
                height: getResponsiveFontSize(14, context),
                width: getResponsiveFontSize(
                  50,
                  context,
                ), // Adjust width for skeleton
                color: Colors.grey[300],
              ),
            ],
          ),
          SizedBox(height: getResponsiveFontSize(6, context)),
          // Row 2: Stars, Helpful
          Row(
            children: [
              Row(
                children: List.generate(
                  5,
                  (index) => Icon(
                    Icons.star,
                    size: getResponsiveFontSize(16, context),
                    color: Colors.grey[300],
                  ),
                ),
              ),
              SizedBox(width: getResponsiveFontSize(8, context)),
              Container(
                height: getResponsiveFontSize(14, context),
                width: getResponsiveFontSize(
                  50,
                  context,
                ), // Adjust width for skeleton
                color: Colors.grey[300],
              ),
              SizedBox(width: getResponsiveFontSize(2, context)),
              Icon(
                Icons.thumb_up_alt_rounded,
                size: getResponsiveFontSize(14, context),
                color: Colors.grey[300],
              ),
            ],
          ),
          SizedBox(height: getResponsiveFontSize(4, context)),
          // Row 3: Variation
          Container(
            height: getResponsiveFontSize(14, context),
            width: screenWidth * 0.7, // Adjust width for skeleton
            color: Colors.grey[300],
          ),
          SizedBox(height: getResponsiveFontSize(6, context)),
          // Row 4: Comment
          Container(
            height: getResponsiveFontSize(16, context),
            width: screenWidth * 0.9, // Adjust width for skeleton
            color: Colors.grey[300],
          ),
        ],
      ),
    );
  }

  Widget _buildNoReviewsState(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double baseFontSize = screenWidth > 800 ? 18 : 14;
    final double smallFontSize = screenWidth > 800 ? 15 : 12;

    return Container(
      padding: EdgeInsets.fromLTRB(
        getResponsivePadding(16, context),
        getResponsivePadding(8, context),
        getResponsivePadding(16, context),
        getResponsivePadding(24, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.comment_outlined,
            size: getResponsiveFontSize(40, context),
            color: Colors.grey[400],
          ),
          SizedBox(height: getResponsiveFontSize(12, context)),
          Text(
            'No reviews yet! Be the first to leave one.',
            style: TextStyle(
              fontSize: baseFontSize,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: getResponsiveFontSize(8, context)),
          Text(
            'Share your experience with others and help them make an informed decision.',
            style: TextStyle(fontSize: smallFontSize, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }
}
