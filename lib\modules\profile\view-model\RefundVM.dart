import 'package:flutter/material.dart';
import 'dart:io';
import '../../../data/model/OrderModel.dart';

enum RefundReason {
  defectiveOrBroken,
  wrongColour,
  missingParts,
  wrongSize,
  other,
}

extension RefundReasonExtension on RefundReason {
  String get displayName {
    switch (this) {
      case RefundReason.defectiveOrBroken:
        return 'Item is defective or broken';
      case RefundReason.wrongColour:
        return 'Wrong colour';
      case RefundReason.missingParts:
        return 'Missing part / accessories';
      case RefundReason.wrongSize:
        return 'Wrong size';
      case RefundReason.other:
        return 'Other reasons...';
    }
  }
}

class RefundVM extends ChangeNotifier {
  final OrderModel order;

  RefundReason? _selectedReason;
  String _otherReasonText = '';
  List<File> _evidenceFiles = [];
  bool _isSubmitting = false;
  String? _errorMessage;
  final String _userPhoneNumber =
      '012- 345 6789'; // TODO: Get from user profile

  RefundVM({required this.order});

  // Getters
  RefundReason? get selectedReason => _selectedReason;
  String get otherReasonText => _otherReasonText;
  List<File> get evidenceFiles => _evidenceFiles;
  bool get isSubmitting => _isSubmitting;
  String? get errorMessage => _errorMessage;
  String get userPhoneNumber => _userPhoneNumber;

  bool get canSubmit =>
      _selectedReason != null &&
      _evidenceFiles.isNotEmpty &&
      (_selectedReason != RefundReason.other ||
          _otherReasonText.trim().isNotEmpty);

  String get eligibilityMessage =>
      'You are eligible for a return and exchange if defect is confirmed.';

  String get evidenceRequirement =>
      'At least 1 media must be provided. This help us process the after-sales issue more quickly and efficiently.';

  String get contactInfo =>
      'We will use this only to follow up on your request. It is auto-filled from your profile data.';

  // Methods
  void selectReason(RefundReason reason) {
    _selectedReason = reason;
    _clearError();
    notifyListeners();
  }

  void updateOtherReasonText(String text) {
    _otherReasonText = text;
    _clearError();
    notifyListeners();
  }

  void addEvidenceFile(File file) {
    if (_evidenceFiles.length < 5) {
      // Max 5 files
      _evidenceFiles.add(file);
      _clearError();
      notifyListeners();
    }
  }

  void removeEvidenceFile(int index) {
    if (index >= 0 && index < _evidenceFiles.length) {
      _evidenceFiles.removeAt(index);
      notifyListeners();
    }
  }

  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  String _getReasonForSubmission() {
    if (_selectedReason == null) return '';

    if (_selectedReason == RefundReason.other) {
      return _otherReasonText.trim();
    }

    return _selectedReason!.displayName;
  }

  Future<bool> submitRefund() async {
    if (!canSubmit) {
      _errorMessage = 'Please select a reason and provide evidence';
      notifyListeners();
      return false;
    }

    _isSubmitting = true;
    _clearError();
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // TODO: Replace with actual API call
      // final result = await ApiService.submitRefund(
      //   orderId: order.id,
      //   reason: _getReasonForSubmission(),
      //   evidenceFiles: _evidenceFiles,
      //   contactNumber: _userPhoneNumber,
      // );

      _isSubmitting = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isSubmitting = false;
      _errorMessage = 'Failed to submit refund request. Please try again.';
      notifyListeners();
      return false;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
