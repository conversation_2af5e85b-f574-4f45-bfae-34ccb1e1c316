import 'package:pointycastle/export.dart' as pc;
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:asn1lib/asn1lib.dart';
import 'dart:convert';
import 'dart:typed_data';

class SignatureHelper {
  static Future<String> getSignature(
    String token,
    String timestamp,
    String endpoint,
    String method,
  ) async {
    final decodedToken = JwtDecoder.decode(token);

    // Attempt to locate the PEM public key from jwt payload
    String? publicKey;
    final dynamic payload = decodedToken['payload'];
    if (payload is Map<String, dynamic>) {
      publicKey = payload['jti'] as String?; // jti is publicKey, this is from the client
    }
    publicKey ??= decodedToken['jti'] as String?;

    if (publicKey == null || publicKey.isEmpty) {
      throw const FormatException(
        'JWT does not contain a usable public key (expected in jti/publicKey claim).',
      );
    }

    final publicKeyDER = parsePublicKeyFromPem(publicKey);

    final encryptionContent = '$method\n$endpoint\n$timestamp';

    final signature = encryptStringWithPublicRSA(encryptionContent, publicKeyDER);
    return signature;
  }

  static Uint8List parsePublicKeyFromPem(String pemString) {
    final publicKeyPEM = pemString
        .replaceAll("-----BEGIN PUBLIC KEY-----", "")
        .replaceAll("-----END PUBLIC KEY-----", "")
        .replaceAll("\n", "");
    final publicKeyDER = base64.decode(publicKeyPEM);
    return publicKeyDER;
  }

  static String encryptStringWithPublicRSA(
    String content,
    Uint8List publicKeyDER,
  ) {
    // Parse SubjectPublicKeyInfo (SPKI) → RSAPublicKey(modulus, exponent)
    final ASN1Parser topLevelParser = ASN1Parser(publicKeyDER);
    final ASN1Sequence topLevelSeq =
        topLevelParser.nextObject() as ASN1Sequence;
    final ASN1BitString publicKeyBitString =
        topLevelSeq.elements[1] as ASN1BitString;

    final Uint8List publicKeyBytes = publicKeyBitString.contentBytes();
    final ASN1Parser publicKeyParser = ASN1Parser(publicKeyBytes);
    final ASN1Sequence publicKeySeq =
        publicKeyParser.nextObject() as ASN1Sequence;

    final ASN1Integer modulusAsn1 = publicKeySeq.elements[0] as ASN1Integer;
    final ASN1Integer exponentAsn1 = publicKeySeq.elements[1] as ASN1Integer;

    final pc.RSAPublicKey rsaPublicKey = pc.RSAPublicKey(
      modulusAsn1.valueAsBigInteger,
      exponentAsn1.valueAsBigInteger,
    );

    // Encrypt using RSA with PKCS#1 v1.5 padding
    final pc.PKCS1Encoding cipher = pc.PKCS1Encoding(pc.RSAEngine());
    cipher.init(true, pc.PublicKeyParameter<pc.RSAPublicKey>(rsaPublicKey));

    final Uint8List inputBytes = Uint8List.fromList(utf8.encode(content));

    // Handle data in blocks according to RSA maximum input size
    final int inputBlockSize = cipher.inputBlockSize;
    final BytesBuilder out = BytesBuilder();
    for (int offset = 0; offset < inputBytes.length; offset += inputBlockSize) {
      final int chunkSize = (offset + inputBlockSize <= inputBytes.length)
          ? inputBlockSize
          : (inputBytes.length - offset);
      final Uint8List block = inputBytes.sublist(offset, offset + chunkSize);
      final Uint8List encryptedBlock = cipher.process(block);
      out.add(encryptedBlock);
    }

    return base64.encode(out.toBytes());
  }
}
