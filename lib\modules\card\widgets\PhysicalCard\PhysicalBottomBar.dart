import 'package:flutter/material.dart';

class PhysicalBottomBar extends StatelessWidget {
  final bool isGroupSelected;
  final VoidCallback onToggleGroup;
  final int totalSelected;
  final VoidCallback onConfirm;

  const PhysicalBottomBar({
    super.key,
    required this.isGroupSelected,
    required this.onToggleGroup,
    required this.totalSelected,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final scale = (size.width / 390.0).clamp(0.9, 1.2); // base width for iPhone 12-like screen
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16 * scale, vertical: 10 * scale),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            offset: Offset(0, -2),
            blurRadius: 6,
          )
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Checkbox group (left)
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Transform.scale(
                      scale: scale,
                      child: Checkbox(
                        value: isGroupSelected,
                        onChanged: (_) => onToggleGroup(),
                      ),
                    ),
                    Flexible(
                      child: Text(
                        "Select All (Group)",
                        style: TextStyle(
                          fontSize: 14 * scale,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                Text(
                  "Total Selected: $totalSelected",
                  style: TextStyle(
                    fontSize: 12 * scale,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(width: 12 * scale),

          // Confirm button (right)
          ElevatedButton(
            onPressed: onConfirm,
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(
                horizontal: 20 * scale,
                vertical: 14 * scale,
              ),
              backgroundColor: Colors.redAccent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12 * scale),
              ),
              elevation: 6,
            ),
            child: Text(
              "Confirm",
              style: TextStyle(
                fontSize: 14.5 * scale,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
