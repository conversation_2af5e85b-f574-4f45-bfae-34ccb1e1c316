import 'package:flutter/material.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';
import 'package:luckymall/modules/profile/widgets/ToReviewCard.dart';
import 'package:luckymall/modules/profile/widgets/ReviewedCard.dart';

class ReviewPageView extends StatefulWidget {
  const ReviewPageView({super.key});

  @override
  State<ReviewPageView> createState() => _ReviewPageViewState();
}

class _ReviewPageViewState extends State<ReviewPageView> {
  int _selectedTabIndex = 0;

  // Mock data for the mystery box product
  final List<Map<String, dynamic>> _toReviewItems = [
    {
      'productName': "My Lucky Mall Exclusive Snacks Mystery Box",
      'variation': 'Default',
      'size': '',
      'price': 'RM30.00',
      'quantity': 1,
      'imageUrl':
          'assets/images/ANGPAO OPEN.png', // Using existing image as placeholder
    },
    {
      'productName': "My Lucky Mall Exclusive Snacks Mystery Box",
      'variation': 'Default',
      'size': '',
      'price': 'RM30.00',
      'quantity': 1,
      'imageUrl': 'assets/images/ANGPAO OPEN.png',
    },
    {
      'productName': "My Lucky Mall Exclusive Snacks Mystery Box",
      'variation': 'Default',
      'size': '',
      'price': 'RM30.00',
      'quantity': 1,
      'imageUrl': 'assets/images/ANGPAO OPEN.png',
    },
    {
      'productName': "My Lucky Mall Exclusive Snacks Mystery Box",
      'variation': 'Default',
      'size': '',
      'price': 'RM30.00',
      'quantity': 1,
      'imageUrl': 'assets/images/ANGPAO OPEN.png',
    },
  ];

  // Mock data for reviewed items
  final List<Map<String, dynamic>> _reviewedItems = [
    {
      'productName': 'Casual T-Shirt',
      'variation': 'Blue',
      'imageUrl': 'assets/images/logoLM.png',
      'rating': 5,
      'reviewText':
          'Received with perfect condition. Super soft and comfy! The material feels good on the skin and fits perfectly. Love the casual look!',
      'reviewDate': '2025/06/15',
    },
    {
      'productName': 'Digital Watch',
      'variation': 'Black',
      'imageUrl': 'assets/images/logoLM.png',
      'rating': 5,
      'reviewText':
          'Elegant and functional! I wear this every day. Battery life is great and the design matches any outfit. RECOMMEND 👍',
      'reviewDate': '2025/06/01',
    },
    {
      'productName': 'Women Shoes',
      'variation': 'Black',
      'imageUrl': 'assets/images/logoLM.png',
      'rating': 5,
      'reviewText':
          'Received with perfect condition. Comfortable. I wore them all day without pain.',
      'reviewDate': '2025/05/31',
    },
    {
      'productName': 'Women Handbag',
      'variation': 'Khaki',
      'imageUrl': 'assets/images/logoLM.png',
      'rating': 5,
      'reviewText':
          'This bag goes with everything! I get compliments every time I use it.',
      'reviewDate': '2025/05/11',
    },
    {
      'productName': 'Stainless Steel Electric Kettle (2L)',
      'variation': 'Silver',
      'imageUrl': 'assets/images/logoLM.png',
      'rating': 5,
      'reviewText':
          'The packaging is really good and the delivery is fast. The quality of the goods is also quite good. I like it so much, thank you!',
      'reviewDate': '2025/05/01',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'Review Page'),
      body: Column(
        children: [
          // Tab Bar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedTabIndex = 0;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: _selectedTabIndex == 0
                                ? Colors.orange
                                : Colors.transparent,
                            width: 2,
                          ),
                        ),
                      ),
                      child: Text(
                        'To Review',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: _selectedTabIndex == 0
                              ? Colors.orange
                              : Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedTabIndex = 1;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: _selectedTabIndex == 1
                                ? Colors.orange
                                : Colors.transparent,
                            width: 2,
                          ),
                        ),
                      ),
                      child: Text(
                        'Reviewed',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: _selectedTabIndex == 1
                              ? Colors.orange
                              : Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Content based on selected tab
          Expanded(
            child: _selectedTabIndex == 0
                ? _buildToReviewContent()
                : _buildReviewedContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildToReviewContent() {
    return ListView.builder(
      padding: const EdgeInsets.only(top: 8),
      itemCount: _toReviewItems.length,
      itemBuilder: (context, index) {
        final item = _toReviewItems[index];
        return ToReviewCard(
          productName: item['productName'],
          variation: item['variation'],
          size: item['size'],
          price: item['price'],
          quantity: item['quantity'],
          imageUrl: item['imageUrl'],
        );
      },
    );
  }

  Widget _buildReviewedContent() {
    return ListView.builder(
      padding: const EdgeInsets.only(top: 8),
      itemCount: _reviewedItems.length,
      itemBuilder: (context, index) {
        final item = _reviewedItems[index];
        return ReviewedCard(
          productName: item['productName'],
          variation: item['variation'],
          imageUrl: item['imageUrl'],
          rating: item['rating'],
          reviewText: item['reviewText'],
          reviewDate: item['reviewDate'],
        );
      },
    );
  }
}
