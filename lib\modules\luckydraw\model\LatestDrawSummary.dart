class LatestDrawSummary {
  final String itemName;
  final String imageUrl;
  final String winningId;
  final String dateTime;
  final String winnerName;
  final String avatarUrl;

  const LatestDrawSummary({
    required this.itemName,
    required this.imageUrl,
    required this.winningId,
    required this.dateTime,
    required this.winnerName,
    required this.avatarUrl,
  });

  factory LatestDrawSummary.fromJson(Map<String, dynamic> json) {
    return LatestDrawSummary(
      itemName: json['itemName']?.toString() ?? '',
      imageUrl: json['imageUrl']?.toString() ?? '',
      winningId: json['winningId']?.toString() ?? '',
      dateTime: json['dateTime']?.toString() ?? '',
      winnerName: json['winnerName']?.toString() ?? '',
      avatarUrl: json['avatarUrl']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'itemName': itemName,
      'imageUrl': imageUrl,
      'winningId': winningId,
      'dateTime': dateTime,
      'winnerName': winnerName,
      'avatarUrl': avatarUrl,
    };
  }

  LatestDrawSummary copyWith({
    String? itemName,
    String? imageUrl,
    String? winningId,
    String? dateTime,
    String? winnerName,
    String? avatarUrl,
  }) {
    return LatestDrawSummary(
      itemName: itemName ?? this.itemName,
      imageUrl: imageUrl ?? this.imageUrl,
      winningId: winningId ?? this.winningId,
      dateTime: dateTime ?? this.dateTime,
      winnerName: winnerName ?? this.winnerName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
    );
  }

  @override
  String toString() {
    return 'LatestDrawSummary(itemName: $itemName, winnerName: $winnerName, winningId: $winningId, dateTime: $dateTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LatestDrawSummary &&
        other.winningId == winningId &&
        other.itemName == itemName &&
        other.dateTime == dateTime;
  }

  @override
  int get hashCode =>
      winningId.hashCode ^ itemName.hashCode ^ dateTime.hashCode;
}
