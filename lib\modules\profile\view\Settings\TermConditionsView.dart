import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class TermsAndConditionView extends StatelessWidget {
  const TermsAndConditionView({super.key});

  @override
  Widget build(BuildContext context) {
    // Randomly generated terms and conditions sections
    final List<_TermsSection> sections = [
      _TermsSection(
        title: "Introduction",
        content:
            "Welcome to Lucky Mall! By accessing or using our services, you agree to be bound by these Terms and Conditions. Please read them carefully before using our app.",
      ),
      _TermsSection(
        title: "1. User Account",
        content:
            "You must register for an account to access certain features. You are responsible for maintaining the confidentiality of your account and password.",
      ),
      _TermsSection(
        title: "2. Eligibility",
        content:
            "Our services are intended for users who are at least 18 years old. By using Lucky Mall, you represent that you meet this requirement.",
      ),
      _TermsSection(
        title: "3. User Conduct",
        content:
            "You agree not to misuse the services or help anyone else do so. Prohibited activities include violating laws, infringing intellectual property, or interfering with the app’s operation.",
      ),
      _TermsSection(
        title: "4. Purchases and Payments",
        content:
            "All purchases made through Lucky Mall are subject to our payment terms. Prices and availability of products are subject to change without notice.",
      ),
      _TermsSection(
        title: "5. Returns and Refunds",
        content:
            "Please refer to our Return and Refund Policy for information about returns, exchanges, and refunds.",
      ),
      _TermsSection(
        title: "6. Privacy Policy",
        content:
            "Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information.",
      ),
      _TermsSection(
        title: "7. Limitation of Liability",
        content:
            "Lucky Mall is not liable for any indirect, incidental, or consequential damages arising from your use of the app.",
      ),
      _TermsSection(
        title: "8. Changes to Terms",
        content:
            "We may update these Terms and Conditions from time to time. Continued use of the app constitutes acceptance of the new terms.",
      ),
      _TermsSection(
        title: "9. Contact Us",
        content:
            "If you have any questions about these Terms and Conditions, please contact our support <NAME_EMAIL>.",
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        centerTitle: true,
        title: const Text(
          'Terms and Condition',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Lucky Mall User Registration Agreement",
              style: TextStyle(fontWeight: FontWeight.w700, fontSize: 18),
            ),
            const SizedBox(height: 18),
            ...sections.map(
              (section) => Padding(
                padding: const EdgeInsets.only(bottom: 18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      section.title,
                      style: TextStyle(
                        fontWeight: section.title == "Introduction"
                            ? FontWeight.w700
                            : FontWeight.w600,
                        fontSize: section.title == "Introduction" ? 16 : 15,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      section.content,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TermsSection {
  final String title;
  final String content;
  _TermsSection({required this.title, required this.content});
}
