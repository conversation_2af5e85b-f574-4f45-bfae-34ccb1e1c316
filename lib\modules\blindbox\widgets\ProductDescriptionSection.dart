import 'package:flutter/material.dart';

class ProductDescriptionSection extends StatefulWidget {
  final String title;
  final String description;
  final bool initiallyExpanded;

  const ProductDescriptionSection({
    super.key,
    required this.title,
    required this.description,
    this.initiallyExpanded = false,
  });

  @override
  State<ProductDescriptionSection> createState() =>
      _ProductDescriptionSectionState();
}

class _ProductDescriptionSectionState extends State<ProductDescriptionSection> {
  late bool _descExpanded;

  @override
  void initState() {
    super.initState();
    _descExpanded = widget.initiallyExpanded;
  }

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description,
                color: Colors.orange[600],
                size: getResponsiveFontSize(20, context),
              ),
              SizedBox(width: getResponsiveFontSize(8, context)),
              Text(
                widget.title,
                style: TextStyle(
                  fontSize: getResponsiveFontSize(16, context),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: getResponsiveFontSize(12, context)),
          Text(
            widget.description,
            style: TextStyle(fontSize: getResponsiveFontSize(14, context)),
            maxLines: _descExpanded ? null : 3,
            overflow: _descExpanded ? null : TextOverflow.ellipsis,
          ),
          SizedBox(height: getResponsiveFontSize(8, context)),
          GestureDetector(
            onTap: () => setState(() => _descExpanded = !_descExpanded),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: 1,
                  width: getResponsiveFontSize(40, context),
                  color: Colors.grey[300],
                ),
                SizedBox(width: getResponsiveFontSize(8, context)),
                Text(
                  _descExpanded ? 'See Less' : 'See More',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    color: Colors.grey,
                  ),
                ),
                SizedBox(width: getResponsiveFontSize(4, context)),
                Icon(
                  _descExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Colors.grey,
                  size: getResponsiveFontSize(18, context),
                ),
                SizedBox(width: getResponsiveFontSize(8, context)),
                Container(
                  height: 1,
                  width: getResponsiveFontSize(40, context),
                  color: Colors.grey[300],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
