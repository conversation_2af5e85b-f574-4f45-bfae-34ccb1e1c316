class LuckyDrawProductReview {
  final String reviewId;
  final String reviewerName;
  final String avatarUrl;
  final String productId;
  final String productName;
  final double rating; // Changed from int to double to match API
  final String variation;
  final String reviewText;
  final List<String> imageUrls;
  final int helpfulCount;
  final String date;
  final String category;
  final bool verified;
  final int orderType; // Add orderType field for ReviewCard compatibility

  const LuckyDrawProductReview({
    required this.reviewId,
    required this.reviewerName,
    required this.avatarUrl,
    required this.productId,
    required this.productName,
    required this.rating,
    required this.variation,
    required this.reviewText,
    required this.imageUrls,
    required this.helpfulCount,
    required this.date,
    required this.category,
    required this.verified,
    required this.orderType,
  });

  factory LuckyDrawProductReview.fromJson(Map<String, dynamic> json) {
    return LuckyDrawProductReview(
      reviewId: json['reviewId']?.toString() ?? '',
      reviewerName: json['reviewerName']?.toString() ?? '',
      avatarUrl: json['avatarUrl']?.toString() ?? '',
      productId: json['productId']?.toString() ?? '',
      productName: json['productName']?.toString() ?? '',
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      variation: json['variation']?.toString() ?? '',
      reviewText: json['reviewText']?.toString() ?? '',
      imageUrls:
          (json['imageUrls'] as List?)?.map((e) => e.toString()).toList() ??
          const [],
      helpfulCount: (json['helpfulCount'] as num?)?.toInt() ?? 0,
      date: json['date']?.toString() ?? '',
      category: json['category']?.toString() ?? '',
      verified: json['verified'] as bool? ?? false,
      orderType: (json['orderType'] as num?)?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reviewId': reviewId,
      'reviewerName': reviewerName,
      'avatarUrl': avatarUrl,
      'productId': productId,
      'productName': productName,
      'rating': rating,
      'variation': variation,
      'reviewText': reviewText,
      'imageUrls': imageUrls,
      'helpfulCount': helpfulCount,
      'date': date,
      'category': category,
      'verified': verified,
      'orderType': orderType,
    };
  }

  LuckyDrawProductReview copyWith({
    String? reviewId,
    String? reviewerName,
    String? avatarUrl,
    String? productId,
    String? productName,
    double? rating,
    String? variation,
    String? reviewText,
    List<String>? imageUrls,
    int? helpfulCount,
    String? date,
    String? category,
    bool? verified,
    int? orderType,
  }) {
    return LuckyDrawProductReview(
      reviewId: reviewId ?? this.reviewId,
      reviewerName: reviewerName ?? this.reviewerName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      rating: rating ?? this.rating,
      variation: variation ?? this.variation,
      reviewText: reviewText ?? this.reviewText,
      imageUrls: imageUrls ?? this.imageUrls,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      date: date ?? this.date,
      category: category ?? this.category,
      verified: verified ?? this.verified,
      orderType: orderType ?? this.orderType,
    );
  }

  @override
  String toString() {
    return 'LuckyDrawProductReview(reviewId: $reviewId, reviewerName: $reviewerName, rating: $rating, verified: $verified)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LuckyDrawProductReview && other.reviewId == reviewId;
  }

  @override
  int get hashCode => reviewId.hashCode;
}
