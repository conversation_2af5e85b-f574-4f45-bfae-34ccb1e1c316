import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import '../../../../data/model/OrderModel.dart';

class ShippingInformationView extends StatelessWidget {
  final OrderModel order;

  const ShippingInformationView({super.key, required this.order});

  // Responsive utility functions
  static double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  static double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black87),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: true,
        title: Text(
          'Shipping Information',
          style: TextStyle(
            fontSize: getResponsiveFontSize(16, context),
            fontWeight: FontWeight.w700,
            color: Colors.black,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Delivered Status Banner
            _buildDeliveredStatus(context),

            SizedBox(height: getResponsivePadding(16, context)),

            // Product and Tracking Info
            _buildProductAndTracking(context),

            SizedBox(height: getResponsivePadding(16, context)),

            // Shipping Timeline
            _buildShippingTimeline(context),

            SizedBox(height: getResponsivePadding(32, context)),

            // Bottom Action Button
            _buildBottomAction(context),

            SizedBox(height: getResponsivePadding(32, context)),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveredStatus(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      color: Colors.green.shade50,
      child: Text(
        'Delivered on 08 Mac',
        style: TextStyle(
          fontSize: getResponsiveFontSize(16, context),
          fontWeight: FontWeight.w600,
          color: Colors.green.shade700,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildProductAndTracking(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: getResponsivePadding(60, context),
              height: getResponsivePadding(60, context),
              color: Colors.grey.shade100,
              child: CachedNetworkImage(
                imageUrl: order.productImage,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey.shade200,
                  child: const Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Color(0xFFFCD255),
                      ),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey.shade200,
                  child: Icon(Icons.image, color: Colors.grey.shade400),
                ),
              ),
            ),
          ),

          SizedBox(width: getResponsivePadding(12, context)),

          // Product and tracking info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order.productName,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: getResponsivePadding(8, context)),
                Text(
                  'Delivery Partner: JNT Express (East Malaysia)',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(12, context),
                    color: Colors.grey.shade600,
                  ),
                ),
                SizedBox(height: getResponsivePadding(4, context)),
                Row(
                  children: [
                    Text(
                      'Tracking Number: ',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(12, context),
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      'IM29202920292',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(12, context),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(width: getResponsivePadding(4, context)),
                    GestureDetector(
                      onTap: () {
                        Clipboard.setData(
                          const ClipboardData(text: 'IM29202920292'),
                        );
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Tracking number copied!'),
                          ),
                        );
                      },
                      child: Icon(
                        Icons.copy,
                        size: getResponsiveFontSize(14, context),
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShippingTimeline(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Shipping Timeline',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: getResponsivePadding(16, context)),

          // Timeline events
          ..._getMockTrackingEvents().asMap().entries.map((entry) {
            final index = entry.key;
            final event = entry.value;
            return _buildTimelineEvent(
              context,
              event,
              index: index,
              totalEvents: _getMockTrackingEvents().length,
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildTimelineEvent(
    BuildContext context,
    TrackingEvent event, {
    required int index,
    required int totalEvents,
  }) {
    String timeLabel;
    if (index == 0 && event.isActive) {
      // Show "Today HH:mm" for the active event
      timeLabel = 'Today\n${DateFormat('HH:mm').format(event.timestamp)}';
    } else {
      // Show "dd MMM\nHH:mm" for other events
      timeLabel = DateFormat('dd MMM\nHH:mm').format(event.timestamp);
    }

    return Padding(
      padding: EdgeInsets.only(bottom: getResponsivePadding(16, context)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Time stamp with bullet point
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date/time
              SizedBox(
                width: getResponsivePadding(50, context),
                child: Text(
                  timeLabel,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(12, context),
                    color: index == 0 && event.isActive
                        ? const Color(0xFFF59E0B)
                        : Colors.grey.shade600,
                    fontWeight: index == 0 && event.isActive
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                  textAlign: TextAlign.left,
                ),
              ),

              SizedBox(width: getResponsivePadding(12, context)),

              // Bullet point with connecting line
              Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                      top: getResponsivePadding(2, context),
                    ),
                    width: getResponsivePadding(6, context),
                    height: getResponsivePadding(6, context),
                    decoration: BoxDecoration(
                      color: index == 0 && event.isActive
                          ? const Color(0xFFF59E0B)
                          : Colors.grey.shade400,
                      shape: BoxShape.circle,
                    ),
                  ),
                  // Add connecting line if not the last event
                  if (index < totalEvents - 1)
                    Container(
                      width: 1,
                      height: getResponsivePadding(40, context),
                      color: Colors.grey.shade300,
                    ),
                ],
              ),
            ],
          ),

          SizedBox(width: getResponsivePadding(12, context)),

          // Event description
          Expanded(
            child: Text(
              event.title,
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: index == 0 && event.isActive
                    ? const Color(0xFFF59E0B)
                    : Colors.black87,
                fontWeight: index == 0 && event.isActive
                    ? FontWeight.w500
                    : FontWeight.normal,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomAction(BuildContext context) {
    // Determine button text based on order type
    String buttonText;
    switch (order.type) {
      case OrderType.storeOrder:
      case OrderType.blindBox:
      case OrderType.physicalCard:
        buttonText = 'Buy Again';
        break;
      case OrderType.prize:
      case OrderType.luckyGroupBuy:
        buttonText = 'Join Again';
        break;
      default:
        buttonText = 'Buy Again';
    }

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: SizedBox(
        width: double.infinity,
        height: getResponsivePadding(56, context),
        child: ElevatedButton(
          onPressed: () {
            // Navigate based on order type
            switch (order.type) {
              case OrderType.storeOrder:
                context.go('/shop');
                break;
              case OrderType.blindBox:
                context.go('/blind-box');
                break;
              case OrderType.prize:
              case OrderType.luckyGroupBuy:
                context.go('/lucky-draw');
                break;
              default:
                context.go('/shop');
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFCD255),
            foregroundColor: Colors.black87,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            buttonText,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  // Mock tracking events for demonstration
  List<TrackingEvent> _getMockTrackingEvents() {
    return [
      TrackingEvent(
        timestamp: DateTime(2025, 3, 8, 2, 31),
        title:
            'Your parcel has arrived at sorting facility: SBH sorting centre',
        isActive: true,
      ),
      TrackingEvent(
        timestamp: DateTime(2025, 2, 26, 0, 0),
        title:
            'Parcel is on the vessel to destination port in East Malaysia. Note that your parcel will be handed over to SPX.',
      ),
      TrackingEvent(
        timestamp: DateTime(2025, 2, 15, 23, 59),
        title: 'Parcel is undergoing custom declaration at Port Klang',
      ),
      TrackingEvent(
        timestamp: DateTime(2025, 2, 15, 13, 17),
        title:
            'Your parcel has departed from sorting facility: DDR sorting centre',
      ),
      TrackingEvent(
        timestamp: DateTime(2025, 2, 15, 3, 40),
        title:
            'Your parcel has arrived from sorting facility: DDR sorting centre',
      ),
      TrackingEvent(
        timestamp: DateTime(2025, 2, 15, 0, 13),
        title: 'Your parcel has departed from sorting facility',
      ),
      TrackingEvent(
        timestamp: DateTime(2025, 2, 14, 21, 4),
        title:
            'Your parcel has arrived at sorting facility: CSL First Mile Hub',
      ),
      TrackingEvent(
        timestamp: DateTime(2025, 2, 14, 19, 43),
        title: 'Your parcel has been picked up by our logistics partner',
      ),
      TrackingEvent(
        timestamp: DateTime(2025, 2, 14, 9, 3),
        title: 'Courier assigned for your order, kindly wait for pick up',
      ),
      TrackingEvent(
        timestamp: DateTime(2025, 2, 14, 8, 18),
        title: 'Seller is preparing to ship your parcel',
      ),
      TrackingEvent(
        timestamp: DateTime(2025, 2, 14, 8, 17),
        title: 'Order is place',
      ),
    ];
  }
}

// Tracking event model
class TrackingEvent {
  final DateTime timestamp;
  final String title;
  final String? description;
  final bool isActive;

  TrackingEvent({
    required this.timestamp,
    required this.title,
    this.description,
    this.isActive = false,
  });
}
