import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../view-model/UserProfileVM.dart';
import '../model/UserInfoModel.dart';

class EditUserProfileView extends StatefulWidget {
  const EditUserProfileView({super.key});

  static const Color gold = Color(0xFFFFBF00);

  @override
  State<EditUserProfileView> createState() => _EditUserProfileViewState();
}

class _EditUserProfileViewState extends State<EditUserProfileView> {
  late TextEditingController usernameController;
  late TextEditingController genderController;
  late TextEditingController birthDateController;
  late TextEditingController phoneController;

  UserInfoModel?
  _lastUser; // Track last loaded user to avoid overwriting while typing
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    usernameController = TextEditingController();
    genderController = TextEditingController();
    birthDateController = TextEditingController();
    phoneController = TextEditingController();
  }

  @override
  void dispose() {
    usernameController.dispose();
    genderController.dispose();
    birthDateController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final vm = Provider.of<UserProfileVM>(context);
    final user = vm.userInfo;

    // Only update controllers if user changed (avoid overwriting while typing)
    if (user != null && user != _lastUser) {
      _lastUser = user;
      usernameController.text = user.nickname ?? '';
      genderController.text = _getGenderText(user.gender);
      birthDateController.text = _getBirthDateText(user.birthday);
      phoneController.text = user.mobile ?? '';
    }
  }

  String _getGenderText(int? gender) {
    switch (gender) {
      case 1:
        return 'Female';
      case 2:
        return 'Male';
      default:
        return '';
    }
  }

  String _getBirthDateText(DateTime? birthday) {
    if (birthday == null) {
      return '';
    }
    return '${birthday.year}-${birthday.month.toString().padLeft(2, '0')}-${birthday.day.toString().padLeft(2, '0')}';
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        final vm = Provider.of<UserProfileVM>(context, listen: false);
        vm.updateProfileImage(image.path);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile picture updated'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Failed to pick image: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    final vm = Provider.of<UserProfileVM>(context);
    final UserInfoModel? user = vm.userInfo;

    if (user == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(top: 60),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Profile Avatar with clickable camera icon
                Center(
                  child: Stack(
                    alignment: Alignment.bottomRight,
                    children: [
                      _buildProfileAvatar(user),
                      Positioned(
                        right: -1,
                        bottom: -1,
                        child: GestureDetector(
                          onTap: _pickImageFromGallery,
                          child: Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white,
                              border: Border.all(color: Colors.grey.shade300),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            padding: const EdgeInsets.all(5),
                            child: const Icon(
                              Icons.camera_alt,
                              size: 28,
                              color: EditUserProfileView.gold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),
                // User ID
                Center(
                  child: Text(
                    "ID: ${user.memberCode ?? 'N/A'}",
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ),

                // Editable Card
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Card(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _editableTile(
                          label: "Username",
                          controller: usernameController,
                          onChanged: vm.updateUsername,
                        ),
                        _divider(),
                        _genderTile(
                          label: "Gender",
                          controller: genderController,
                          onGenderSelected: vm.updateGender,
                        ),
                        _divider(),
                        _birthDateTile(
                          label: "Birth Date",
                          controller: birthDateController,
                          onDateSelected: vm.updateBirthDate,
                        ),
                        _divider(),
                        _editableTile(
                          label: "Phone Number",
                          controller: phoneController,
                          readOnly: true,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // Save and Cancel Buttons
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      ElevatedButton(
                        onPressed: vm.isLoading
                            ? null
                            : () async {
                                try {
                                  showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (_) => const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                  await vm.saveProfile();
                                  if (!mounted) return;
                                  Navigator.pop(
                                    context,
                                  ); // Close loading dialog
                                  Navigator.pop(context); // Go back
                                } catch (e) {
                                  Navigator.pop(
                                    context,
                                  ); // Close dialog if error
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Failed to save profile: $e',
                                      ),
                                    ),
                                  );
                                }
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: EditUserProfileView.gold,
                          foregroundColor: Colors.black,
                          minimumSize: const Size(double.infinity, 48),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: vm.isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.black,
                                  ),
                                ),
                              )
                            : const Text("Save Changes"),
                      ),
                      const SizedBox(height: 12),
                      OutlinedButton(
                        onPressed: vm.isLoading
                            ? null
                            : () => Navigator.pop(context),
                        style: OutlinedButton.styleFrom(
                          minimumSize: const Size(double.infinity, 48),
                          side: const BorderSide(color: Colors.red),
                          foregroundColor: Colors.red,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text("Cancel"),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileAvatar(UserInfoModel user) {
    if (user.avatar == null || user.avatar!.isEmpty) {
      return Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ClipOval(
          child: Transform.scale(
            scale: 1.3,
            child: SvgPicture.asset(
              'assets/icons/profiles/EmptyProfile.svg',
              fit: BoxFit.contain,
              colorFilter: const ColorFilter.mode(
                Colors.black,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      );
    } else if (user.avatar!.startsWith('http')) {
      return CircleAvatar(
        radius: 50,
        backgroundImage: NetworkImage(user.avatar!),
      );
    } else {
      // Handle local file path
      return CircleAvatar(
        radius: 50,
        backgroundImage: FileImage(File(user.avatar!)),
        onBackgroundImageError: (exception, stackTrace) {
          // Fallback to asset if file doesn't exist
          debugPrint('Error loading profile image: $exception');
        },
      );
    }
  }

  Widget _editableTile({
    required String label,
    required TextEditingController controller,
    Function(String)? onChanged,
    bool readOnly = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  onChanged: onChanged,
                  readOnly: readOnly,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                ),
              ),
              if (!readOnly)
                const Icon(
                  Icons.edit,
                  color: EditUserProfileView.gold,
                  size: 18,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _genderTile({
    required String label,
    required TextEditingController controller,
    required Function(String) onGenderSelected,
  }) {
    void _showGenderPicker() {
      showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (context) {
          return SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 12),
                const Text(
                  "Select Gender",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const Divider(),
                ListTile(
                  title: const Text("Male"),
                  onTap: () {
                    controller.text = "Male";
                    onGenderSelected("Male");
                    Navigator.pop(context);
                    setState(() {});
                  },
                ),
                ListTile(
                  title: const Text("Female"),
                  onTap: () {
                    controller.text = "Female";
                    onGenderSelected("Female");
                    Navigator.pop(context);
                    setState(() {});
                  },
                ),
                const SizedBox(height: 10),
              ],
            ),
          );
        },
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: _showGenderPicker,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      controller.text.isEmpty
                          ? "Select gender"
                          : controller.text,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ),
              ),
              InkWell(
                onTap: _showGenderPicker,
                child: const Icon(
                  Icons.edit,
                  color: EditUserProfileView.gold,
                  size: 18,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _birthDateTile({
    required String label,
    required TextEditingController controller,
    required Function(String) onDateSelected,
  }) {
    Future<void> _pickDate() async {
      DateTime initialDate =
          DateTime.tryParse(controller.text) ?? DateTime(2000);
      DateTime? picked = await showDatePicker(
        context: context,
        initialDate: initialDate,
        firstDate: DateTime(1900),
        lastDate: DateTime.now(),
      );
      if (picked != null) {
        String formatted =
            "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";
        controller.text = formatted;
        onDateSelected(formatted);
        setState(() {});
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: _pickDate,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      controller.text.isEmpty
                          ? "Select birth date"
                          : controller.text,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ),
              ),
              InkWell(
                onTap: _pickDate,
                child: const Icon(
                  Icons.edit,
                  color: EditUserProfileView.gold,
                  size: 18,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _divider() {
    return Divider(
      height: 1,
      color: Colors.grey.shade300,
      indent: 16,
      endIndent: 16,
    );
  }
}
