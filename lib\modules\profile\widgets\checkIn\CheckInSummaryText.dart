import 'package:flutter/material.dart';

class CheckInSummaryText extends StatelessWidget {
  final int consecutiveDays;

  const CheckInSummaryText({super.key, required this.consecutiveDays});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Adjust font size based on screen size
    final double fontSize = screenWidth < 600 ? 14 : 18;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12),
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
          children: [
            const TextSpan(text: ' Streak Alert! You’ve checked in for '),
            TextSpan(
              text: '$consecutiveDays ',
              style: TextStyle(
                color: Colors.red[600],
                fontWeight: FontWeight.bold,
                fontSize: fontSize + 2,
              ),
            ),
            const TextSpan(text: ' days in a row! Keep it going'),
          ],
        ),
      ),
    );
  }
}
