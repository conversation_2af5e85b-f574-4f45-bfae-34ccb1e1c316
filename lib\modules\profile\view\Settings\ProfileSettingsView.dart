import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:luckymall/router/auth_guard.dart';

class ProfileSettingsView extends StatelessWidget {
  const ProfileSettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    final List<_SettingItem> items = [
      _SettingItem(
        'Change Password',
        onTap: () {
          context.push('/profile/change-password');
        },
      ),
      _SettingItem(
        'Deactivate Account',
        onTap: () {
          context.push('/profile/deactivate-account');
        },
      ),
      _SettingItem(
        'Switch Language',
        onTap: () {
          context.push('/profile/switch-language');
        },
      ),

      _SettingItem(
        'Terms and Condition',
        onTap: () {
          context.push('/profile/terms-and-conditions');
        },
      ),
      _SettingItem(
        'Privacy and Policy',
        onTap: () {
          context.push('/profile/privacy-policy');
        },
      ),
      _SettingItem(
        'Return and Refund Policy',
        onTap: () {
          context.push('/profile/return-policy');
        },
      ),
      _SettingItem(
        'Merchant Terms and Policy',
        onTap: () {
          context.push('/profile/merchant-policy');
        },
      ),
      _SettingItem(
        'FAQ',
        onTap: () {
          context.push('/profile/faq');
        },
      ),
      _SettingItem(
        'About Us',
        onTap: () {
          context.push('/profile/about-us');
        },
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        title: const Text('', style: TextStyle(color: Colors.black)),
        centerTitle: true,
      ),
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
              itemCount: items.length,
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                final item = items[index];
                return Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: item.onTap,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      height: 56,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF6F6F6),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.03),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            item.title,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.black,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Icon(
                            Icons.arrow_forward_ios,
                            size: 18,
                            color: Colors.black,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
            child: SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFFC700),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                onPressed: () {
                  // Log out action
                  AuthService.logout();
                  context.go('/login');
                },
                child: const Text(
                  'Log Out',
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                    fontSize: 18,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _SettingItem {
  final String title;
  final VoidCallback onTap;
  _SettingItem(this.title, {required this.onTap});
}
