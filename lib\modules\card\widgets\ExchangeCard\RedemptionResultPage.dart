import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class RedemptionResultPage extends StatelessWidget {
  final bool isSuccess;
  final String message;

  const RedemptionResultPage({
    super.key,
    required this.isSuccess,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final iconSize = screenWidth < 400 ? 100.0 : 120.0;

    final iconAsset = isSuccess
        ? 'assets/icons/other/success.svg'
        : 'assets/icons/other/failed.svg';

    final title = isSuccess ? "Redemption Successful!" : "Oops... Failed";
    final titleColor = isSuccess ? Colors.green[600] : Colors.red[400];
    final bgColor = isSuccess ? Colors.green[50] : Colors.red[50];

    return Scaffold(
      backgroundColor: bgColor,
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Icon / illustration
                SvgPicture.asset(
                  iconAsset,
                  width: iconSize,
                  height: iconSize,
                ),
                const SizedBox(height: 24),

                // Title
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: titleColor,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12),

                // Message
                Text(
                  message,
                  style: const TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 40),

                // Button
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.amber[600],
                    padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 6,
                    shadowColor: Colors.amberAccent,
                  ),
                  onPressed: () {
                    Navigator.popUntil(context, (route) => route.isFirst);
                  },
                  child: const Text(
                    "Back to Home",
                    style: TextStyle(fontSize: 16, color: Colors.white),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
