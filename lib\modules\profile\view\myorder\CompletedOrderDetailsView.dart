import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';
import '../../../../data/model/OrderModel.dart';

class CompletedOrderDetailsView extends StatelessWidget {
  final OrderModel order;

  const CompletedOrderDetailsView({super.key, required this.order});

  // Responsive utility functions
  static double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  static double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black87),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: true,
        title: Text(
          'Completed Order Details',
          style: TextStyle(
            fontSize: getResponsiveFontSize(16, context),
            fontWeight: FontWeight.w700,
            color: Colors.black,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Banner
            _buildStatusBanner(context),

            SizedBox(height: getResponsivePadding(16, context)),

            // Order Information
            _buildOrderInfo(context),

            SizedBox(height: getResponsivePadding(16, context)),

            // Delivery/Pickup Information based on delivery method
            if (order.deliveryMethod == DeliveryMethod.delivery)
              _buildDeliveryInfo(context)
            else
              _buildPickupInfo(context),

            SizedBox(height: getResponsivePadding(16, context)),

            // Prize Section
            _buildPrizeSection(context),

            SizedBox(height: getResponsivePadding(16, context)),

            // Points Summary
            _buildPointsSummary(context),

            SizedBox(height: getResponsivePadding(32, context)),

            // Bottom Action Button
            _buildBottomAction(context),

            SizedBox(height: getResponsivePadding(32, context)),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBanner(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(getResponsivePadding(12, context)),
      color: const Color(0xFFFCD255),
      child: Text(
        'Your Order is completed',
        style: TextStyle(
          fontSize: getResponsiveFontSize(14, context),
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildOrderInfo(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        children: [
          _buildDetailRow(context, 'Order Number', order.id, hasIcon: true),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildDetailRow(
            context,
            'Order Time',
            '${order.createdAt.year}-${order.createdAt.month.toString().padLeft(2, '0')}-${order.createdAt.day.toString().padLeft(2, '0')} ${order.createdAt.hour.toString().padLeft(2, '0')}:${order.createdAt.minute.toString().padLeft(2, '0')}',
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value, {
    bool hasIcon = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            color: Colors.black87,
          ),
        ),
        Row(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            if (hasIcon) ...[
              SizedBox(width: getResponsivePadding(4, context)),
              GestureDetector(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: value));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Order number copied!')),
                  );
                },
                child: Icon(
                  Icons.copy,
                  size: getResponsiveFontSize(16, context),
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildDeliveryInfo(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Navigate to shipping information page
        context.push('/profile/shipping-information', extra: order);
      },
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: getResponsivePadding(16, context),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow(
              context,
              'Delivery Partner',
              'JNT Express (East Malaysia)',
            ),
            SizedBox(height: getResponsivePadding(8, context)),
            _buildDetailRow(
              context,
              'Tracking Number',
              'IM29202920292',
              hasIcon: true,
            ),
            SizedBox(height: getResponsivePadding(16, context)),

            // Delivery status with clickable row
            Container(
              padding: EdgeInsets.all(getResponsivePadding(16, context)),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200, width: 1),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    size: getResponsiveFontSize(20, context),
                    color: Colors.green,
                  ),
                  SizedBox(width: getResponsivePadding(8, context)),
                  Expanded(
                    child: Text(
                      'Parcel has been delivered',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  Text(
                    '2025-02-24 12:30',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(12, context),
                      color: Colors.grey.shade600,
                    ),
                  ),
                  SizedBox(width: getResponsivePadding(4, context)),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: getResponsiveFontSize(16, context),
                    color: Colors.grey.shade600,
                  ),
                ],
              ),
            ),
            SizedBox(height: getResponsivePadding(16, context)),

            // Recipient address
            Container(
              padding: EdgeInsets.all(getResponsivePadding(16, context)),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200, width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: getResponsiveFontSize(20, context),
                        color: Colors.black87,
                      ),
                      SizedBox(width: getResponsivePadding(8, context)),
                      Text(
                        'Angela',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '012-3456789',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          color: const Color(0xFF3B82F6),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: getResponsivePadding(8, context)),
                  Padding(
                    padding: EdgeInsets.only(
                      left: getResponsivePadding(28, context),
                    ),
                    child: Text(
                      '1 Blok A Tingkat, Lot 1, Jalan Indah Jaya, 90000 Sandakan, Sabah, Malaysia',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(12, context),
                        color: Colors.grey.shade600,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPickupInfo(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow(context, 'Pickup Date', '2025-03-06'),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildDetailRow(context, 'Pickup Time', '10:30 am'),
          SizedBox(height: getResponsivePadding(16, context)),

          // Location section
          Container(
            padding: EdgeInsets.all(getResponsivePadding(16, context)),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200, width: 1),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: getResponsiveFontSize(20, context),
                      color: Colors.black87,
                    ),
                    SizedBox(width: getResponsivePadding(8, context)),
                    Text(
                      'Lucky Mall',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '011-684 33988',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                        color: const Color(0xFF3B82F6),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: getResponsivePadding(8, context)),
                Padding(
                  padding: EdgeInsets.only(
                    left: getResponsivePadding(28, context),
                  ),
                  child: Text(
                    'SUITE 29.04-06, 29TH FLOOR, WISMA CHUANG, 34, Jln Sultan Ismail, Bukit Bintang, 50250 Kuala Lumpur, Wilayah Persekutuan Kuala Lumpur',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(12, context),
                      color: Colors.grey.shade600,
                      height: 1.4,
                    ),
                  ),
                ),
                SizedBox(height: getResponsivePadding(12, context)),
                Text(
                  'Operation Time: 8:30am - 6:30pm',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    fontWeight: FontWeight.w600,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrizeSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order type badge
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: getResponsivePadding(8, context),
              vertical: getResponsivePadding(4, context),
            ),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.card_giftcard,
                  size: getResponsiveFontSize(16, context),
                  color: Colors.black54,
                ),
                SizedBox(width: getResponsivePadding(4, context)),
                Text(
                  order.type.typeDisplayName,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(12, context),
                    fontWeight: FontWeight.w600,
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: getResponsivePadding(12, context)),

          // Product details
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: getResponsivePadding(60, context),
                  height: getResponsivePadding(60, context),
                  color: Colors.grey.shade100,
                  child: CachedNetworkImage(
                    imageUrl: order.productImage,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Color(0xFFFCD255),
                          ),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey.shade200,
                      child: Icon(Icons.image, color: Colors.grey.shade400),
                    ),
                  ),
                ),
              ),

              SizedBox(width: getResponsivePadding(12, context)),

              // Product Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      order.productName,
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: getResponsivePadding(4, context)),
                    Text(
                      'Default',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(12, context),
                        color: Colors.grey.shade600,
                      ),
                    ),
                    SizedBox(height: getResponsivePadding(8, context)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'RM${order.total.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: getResponsiveFontSize(14, context),
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        Text(
                          'x${order.quantity}',
                          style: TextStyle(
                            fontSize: getResponsiveFontSize(12, context),
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: getResponsivePadding(8, context)),
                    Text(
                      'See Details',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(12, context),
                        color: const Color(0xFF3B82F6),
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPointsSummary(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        children: [
          _buildDetailRow(
            context,
            'Total Payment',
            'RM${order.total.toStringAsFixed(2)}',
          ),
        ],
      ),
    );
  }

  Widget _buildBottomAction(BuildContext context) {
    // Determine button text based on order type
    String buttonText;
    switch (order.type) {
      case OrderType.storeOrder:
      case OrderType.blindBox:
      case OrderType.physicalCard:
        buttonText = 'Buy Again';
        break;
      case OrderType.prize:
      case OrderType.luckyGroupBuy:
        buttonText = 'Join Again';
        break;
      default:
        buttonText = 'Buy Again';
    }

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: SizedBox(
        width: double.infinity,
        height: getResponsivePadding(56, context),
        child: ElevatedButton(
          onPressed: () {
            // Navigate based on order type
            switch (order.type) {
              case OrderType.storeOrder:
                context.go('/shop');
                break;
              case OrderType.blindBox:
                context.go('/blind-box');
                break;
              case OrderType.prize:
              case OrderType.luckyGroupBuy:
                context.go('/lucky-draw');
                break;
              default:
                context.go('/shop');
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFCD255),
            foregroundColor: Colors.black87,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            buttonText,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }
}
