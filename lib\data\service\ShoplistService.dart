import 'dart:convert';
import 'package:dio/dio.dart';
import '../model/ShoplistModel.dart';
import 'package:luckymall/res/utility/network/api_client.dart';

final shopService = ShopService();

class ShopService {
  final Dio publicDio = ApiClient.instance.publicDio;
  final Dio authDio = ApiClient.instance.authDio;

  // Fetch all products with pagination support
  Future<Map<String, dynamic>> fetchProducts({
    int? limit = 10,
    int? page = 1,
    String? sortBy,
    bool? ascending,
    int? categoryId,
    String? searchQuery,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParams = <String, dynamic>{};

      // Add pagination
      if (limit != null) queryParams['limit'] = limit;
      if (page != null) queryParams['page'] = page;

      // Add category filter
      if (categoryId != null) queryParams['categoryId'] = categoryId;

      // Add search query
      if (searchQuery != null && searchQuery.trim().isNotEmpty) {
        queryParams['productSkuName'] = searchQuery;
      }

      // Add price range filter
      if (minPrice != null) queryParams['minPrice'] = minPrice;
      if (maxPrice != null) queryParams['maxPrice'] = maxPrice;

      // Add sorting
      if (sortBy != null) {
        queryParams['sortBy'] = sortBy;
        queryParams['ascending'] = ascending ?? false;
      }

      const url = 'https://api.luckymall.com.my/product';

      final response = await publicDio.get(url, queryParameters: queryParams);

      if (response.statusCode == 200) {
        final responseData = response.data;
        
        if (responseData["status"] == 200 && responseData["data"] != null) {
          final List<dynamic> productList = responseData["data"]["list"] ?? [];
          final products = productList
              .map((json) => ShopProduct.fromApiJson(json))
              .toList();

          return {
            'products': products,
            'totalCount': responseData["data"]["totalCount"] ?? products.length,
            'pageSize': responseData["data"]["pageSize"] ?? limit ?? 10,
            'totalPage': responseData["data"]["totalPage"] ?? 1,
            'currentPage': responseData["data"]["currentPage"] ?? page ?? 1,
          };
        } else {
          print("⚠️ No products found. Status: ${responseData["status"]}, Message: ${responseData["message"]}");
          return {
            'products': <ShopProduct>[],
            'totalCount': 0,
            'pageSize': limit ?? 10,
            'totalPage': 0,
            'currentPage': page ?? 1,
          };
        }
      } else {
        throw Exception("Failed to load products: HTTP ${response.statusCode}");
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  // Fetch categories
  Future<List<ShopCategory>> fetchCategories() async {
    try {
      const url = 'https://api.luckymall.com.my/product/categorys';

      final response = await publicDio.get(url);

      if (response.statusCode == 200) {
        final responseData = response.data;
        
        print("DEBUG Categories API Response: $responseData");
        
        if (responseData["status"] == 200 && responseData["data"] != null) {
          // Categories API returns data as direct array, not wrapped in "list"
          final List<dynamic> categoryList = responseData["data"] as List<dynamic>;
          return categoryList
              .map((json) => ShopCategory.fromApiJson(json))
              .toList();
        } else {
          print("⚠️ No categories found. Status: ${responseData["status"]}, Message: ${responseData["message"]}");
          return [];
        }
      } else {
        throw Exception("Failed to load categories: HTTP ${response.statusCode}");
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  // Search products (now uses the main fetchProducts method)
  Future<List<ShopProduct>> searchProducts(String query) async {
    try {
      final result = await fetchProducts(searchQuery: query);
      return result['products'] as List<ShopProduct>;
    } catch (e) {
      throw Exception("Failed to search products: $e");
    }
  }

  // Get products by category (now uses the main fetchProducts method)
  Future<List<ShopProduct>> getProductsByCategory(String categoryId) async {
    try {
      print("Fetching products for category ID: $categoryId");
      final result = await fetchProducts(categoryId: int.tryParse(categoryId));
      return result['products'] as List<ShopProduct>;
    } catch (e) {
      throw Exception("Failed to load category products: $e");
    }
  }

  // Get single product by ID
  Future<ShopProduct?> getProductById(String productId) async {
    try {
      final url = 'https://api.luckymall.com.my/product/$productId';

      final response = await publicDio.get(url);

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData["status"] == 200 && responseData["data"] != null) {
          return ShopProduct.fromApiJson(responseData["data"]);
        } else {
          return null;
        }
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception("Failed to load product details: HTTP ${response.statusCode}");
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        return null;
      }
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  // Backward compatibility - simple fetch all products
  static Future<List<ShopProduct>> fetchAllProducts() async {
    final result = await shopService.fetchProducts();
    return result['products'] as List<ShopProduct>;
  }

  // Backward compatibility - simple fetch categories
  static Future<List<ShopCategory>> getAllCategories() async {
    return await shopService.fetchCategories();
  }
}
