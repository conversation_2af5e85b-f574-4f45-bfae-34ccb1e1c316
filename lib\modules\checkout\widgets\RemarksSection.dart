import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/CheckoutVM.dart';

class RemarksSection extends StatelessWidget {
  const RemarksSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CheckoutVM>(
      builder: (context, viewModel, child) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(color: Colors.white),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Remark',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: viewModel.remarksController,
                      decoration: const InputDecoration(
                        hintText: 'Remark input by user...',
                        hintStyle: TextStyle(color: Colors.grey),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                      ),
                      style: const TextStyle(fontSize: 14, color: Colors.black),
                    ),
                  ),
                  Icon(Icons.edit, color: Colors.grey, size: 18),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
