import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../view-model/scanCode/ScanCodeViewModel.dart';
import '../../widgets/scanCode/ScanCodeTab.dart';
import '../../widgets/scanCode/CustomeScanAppBar.dart';

class ScanCodePage extends StatelessWidget {
  const ScanCodePage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ScanCodeViewModel(),
      child: const Scaffold(
        appBar: CustomScanAppBar(title: 'Scan Code'),
        body: ScanCodeTab(),
      ),
    );
  }
}
