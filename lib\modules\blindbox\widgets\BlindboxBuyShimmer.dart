import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class BlindboxBuyShimmer extends StatelessWidget {
  const BlindboxBuyShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Safe<PERSON>rea(
        child: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: SingleChildScrollView(
            child: <PERSON>umn(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image section
                _imageSkeleton(context),
                const SizedBox(height: 12),
                // Info section
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          _box(height: 28, width: 120, radius: 8), // price
                          const Spacer(),
                          _circle(diameter: 36), // fav icon
                          const SizedBox(width: 8),
                          _box(height: 14, width: 70, radius: 6), // sold count
                        ],
                      ),
                      const SizedBox(height: 10),
                      _box(height: 16, width: 160, radius: 6), // points offer
                      const SizedBox(height: 12),
                      _box(
                        height: 20,
                        width: double.infinity,
                        radius: 6,
                      ), // title line 1
                      const SizedBox(height: 8),
                      _box(
                        height: 18,
                        width: MediaQuery.of(context).size.width * 0.6,
                        radius: 6,
                      ), // title line 2
                      const SizedBox(height: 10),
                      _box(height: 14, width: 140, radius: 6), // variation
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                const _DividerBlock(),
                // Variation chips skeleton
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          _iconBox(size: 20),
                          const SizedBox(width: 8),
                          _box(height: 16, width: 120, radius: 6),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: List.generate(
                          6,
                          (i) => _box(
                            height: 32,
                            width: 80 + (i % 3) * 20,
                            radius: 8,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const _DividerBlock(),
                // Description section
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          _iconBox(size: 20),
                          const SizedBox(width: 8),
                          _box(height: 16, width: 160, radius: 6),
                        ],
                      ),
                      const SizedBox(height: 12),
                      _box(height: 14, width: double.infinity, radius: 6),
                      const SizedBox(height: 8),
                      _box(
                        height: 14,
                        width: MediaQuery.of(context).size.width * 0.9,
                        radius: 6,
                      ),
                      const SizedBox(height: 8),
                      _box(
                        height: 14,
                        width: MediaQuery.of(context).size.width * 0.8,
                        radius: 6,
                      ),
                    ],
                  ),
                ),
                const _DividerBlock(),
                // Reviews section
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          _box(height: 18, width: 30, radius: 6),
                          const SizedBox(width: 6),
                          _iconBox(size: 18),
                          const SizedBox(width: 10),
                          _box(height: 16, width: 180, radius: 6),
                          const Spacer(),
                          _box(height: 28, width: 80, radius: 8),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 0,
                          vertical: 4,
                        ),
                        child: Column(
                          children: List.generate(
                            3,
                            (index) => _reviewSkeleton(context),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: _bottomBarSkeleton(),
    );
  }

  Widget _imageSkeleton(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    final double height = (width.clamp(0, 500)) * 0.75; // similar ratio as real
    return _box(
      height: height > 400 ? 400 : height,
      width: double.infinity,
      radius: 0,
    );
  }

  static Widget _box({
    double height = 16,
    double width = double.infinity,
    double radius = 8,
  }) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(radius),
      ),
    );
  }

  static Widget _circle({double diameter = 40}) {
    return Container(
      height: diameter,
      width: diameter,
      decoration: BoxDecoration(color: Colors.white, shape: BoxShape.circle),
    );
  }

  static Widget _iconBox({double size = 20}) {
    return _box(height: size, width: size, radius: 6);
  }

  Widget _reviewSkeleton(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _circle(diameter: 32),
              const SizedBox(width: 8),
              _box(height: 14, width: screenWidth * 0.4, radius: 6),
              const Spacer(),
              _box(height: 12, width: 60, radius: 6),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Row(
                children: List.generate(
                  5,
                  (i) => Padding(
                    padding: const EdgeInsets.only(right: 4),
                    child: _iconBox(size: 14),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              _box(height: 12, width: 80, radius: 6),
              const SizedBox(width: 4),
              _iconBox(size: 14),
            ],
          ),
          const SizedBox(height: 6),
          _box(height: 12, width: screenWidth * 0.7, radius: 6),
          const SizedBox(height: 6),
          _box(height: 14, width: screenWidth * 0.9, radius: 6),
        ],
      ),
    );
  }

  Widget _bottomBarSkeleton() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
        child: Row(
          children: [
            Expanded(child: _box(height: 44, radius: 12)),
            const SizedBox(width: 12),
            Expanded(child: _box(height: 44, radius: 12)),
          ],
        ),
      ),
    );
  }
}

class _DividerBlock extends StatelessWidget {
  const _DividerBlock();

  @override
  Widget build(BuildContext context) {
    return const Divider(height: 1, thickness: 8, color: Color(0xFFF5F5F5));
  }
}
