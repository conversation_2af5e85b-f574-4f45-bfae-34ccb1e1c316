import 'package:flutter/material.dart';

class BlindboxRevealVM extends ChangeNotifier {
  // Reveal state
  bool _isRevealed = false;
  bool get isRevealed => _isRevealed;

  // Prize information
  Map<String, dynamic>? _prizeInfo;
  Map<String, dynamic>? get prizeInfo => _prizeInfo;

  // Loading states
  bool _isRevealing = false;
  bool get isRevealing => _isRevealing;

  bool _isProcessingAction = false;
  bool get isProcessingAction => _isProcessingAction;

  // Blindbox order information
  final Map<String, dynamic> orderInfo;

  BlindboxRevealVM({required this.orderInfo});

  // Calculate refund amount (80% of original price)
  double get refundAmount {
    final originalPrice = orderInfo['price'] ?? 0.0;
    return (originalPrice * 0.8).roundToDouble();
  }

  // Reveal the blindbox
  Future<void> revealBlindbox() async {
    if (_isRevealing || _isRevealed) return;

    _isRevealing = true;
    notifyListeners();

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // Mock prize data - in real app, this would come from API
      _prizeInfo = {
        'name': 'Premium Gaming Headset',
        'category': 'Electronics',
        'value': 299.99,
        'image': 'assets/images/card/icons/example1.jpg',
        'description': 'High-quality gaming headset with noise cancellation',
        'isSpecial': true,
      };

      _isRevealed = true;
    } catch (e) {
      // Handle error
    } finally {
      _isRevealing = false;
      notifyListeners();
    }
  }

  // Accept the prize
  Future<void> acceptPrize() async {
    if (_isProcessingAction) return;

    _isProcessingAction = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // In real app, this would call API to confirm prize acceptance
      // and update order status
    } catch (e) {
      // Handle error
    } finally {
      _isProcessingAction = false;
      notifyListeners();
    }
  }

  // Request refund
  Future<void> requestRefund() async {
    if (_isProcessingAction) return;

    _isProcessingAction = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // In real app, this would call API to process refund
      // and update order status
    } catch (e) {
      // Handle error
    } finally {
      _isProcessingAction = false;
      notifyListeners();
    }
  }

  // No custom disposal needed
}
