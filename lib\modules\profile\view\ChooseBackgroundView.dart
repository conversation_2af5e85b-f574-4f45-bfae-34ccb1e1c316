import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import '../view-model/BackgroundStateManager.dart';

class ChooseBackgroundView extends StatefulWidget {
  const ChooseBackgroundView({super.key});

  @override
  State<ChooseBackgroundView> createState() => _ChooseBackgroundViewState();
}

class _ChooseBackgroundViewState extends State<ChooseBackgroundView> {
  int selectedBackgroundIndex = 0;
  int selectedTabIndex = 0; // 0: Default, 1: Fantasy, 2: Zodiac
  int selectedFantasyCategory = 0; // For Fantasy tab subcategories
  int selectedZodiacCategory = 0; // For Zodiac tab subcategories

  // Default backgrounds
  final List<BackgroundOption> defaultBackgrounds = [
    BackgroundOption(id: 0, name: 'Yellow', color: const Color(0xFFFFE082)),
    BackgroundOption(id: 1, name: 'Green', color: const Color(0xFF81C784)),
    BackgroundOption(id: 2, name: 'Blue', color: const Color(0xFF64B5F6)),
  ];

  // Fantasy categories with image paths instead of emoji icons
  final List<FantasyCategory> fantasyCategories = [
    FantasyCategory(
      id: 0,
      name: 'Royal',
      imagePath: 'assets/icons/cardCategory/King02.svg',
    ),
    FantasyCategory(
      id: 1,
      name: 'Knight',
      imagePath: 'assets/icons/cardCategory/Knight02.svg',
    ),
    FantasyCategory(
      id: 2,
      name: 'Wizard',
      imagePath: 'assets/icons/cardCategory/Wizard02.svg',
    ),
    FantasyCategory(
      id: 3,
      name: 'Smith',
      imagePath: 'assets/icons/cardCategory/Smith02.svg',
    ),
    FantasyCategory(
      id: 4,
      name: 'Elf',
      imagePath: 'assets/icons/cardCategory/Elf02.svg',
    ),
  ];

  // Royal subcategories (Queen/King)
  final List<BackgroundOption> royalBackgrounds = [
    BackgroundOption(
      id: 0,
      name: 'King',
      color: const Color(0xFF8D6E63),
      icon: '👑',
      isLocked: true,
    ),
    BackgroundOption(
      id: 1,
      name: 'Queen',
      color: const Color(0xFF8D6E63),
      icon: '👸',
      isLocked: true,
    ),
  ];

  // Generic fantasy backgrounds (for Knight, Wizard, Smith, Elf) - some locked
  final List<BackgroundOption> genericFantasyBackgrounds = List.generate(
    13,
    (index) => BackgroundOption(
      id: index,
      name: 'Style ${index + 1}',
      color: _getFantasyColor(index),
      isLocked: index >= 8, // Last 5 are locked
    ),
  );

  // Zodiac categories
  final List<ZodiacCategory> zodiacCategories = [
    ZodiacCategory(id: 0, name: 'Aries', icon: '♈'),
    ZodiacCategory(id: 1, name: 'Taurus', icon: '♉'),
    ZodiacCategory(id: 2, name: 'Gemini', icon: '♊'),
    ZodiacCategory(id: 3, name: 'Cancer', icon: '♋'),
    ZodiacCategory(id: 4, name: 'Leo', icon: '♌'),
    ZodiacCategory(id: 5, name: 'Virgo', icon: '♍'),
    ZodiacCategory(id: 6, name: 'Libra', icon: '♎'),
    ZodiacCategory(id: 7, name: 'Scorpio', icon: '♏'),
    ZodiacCategory(id: 8, name: 'Sagittarius', icon: '♐'),
    ZodiacCategory(id: 9, name: 'Capricorn', icon: '♑'),
    ZodiacCategory(id: 10, name: 'Aquarius', icon: '♒'),
    ZodiacCategory(id: 11, name: 'Pisces', icon: '♓'),
  ];

  // Zodiac backgrounds (24 for each zodiac) - some locked
  List<BackgroundOption> getZodiacBackgrounds(int zodiacIndex) {
    return List.generate(
      24,
      (index) => BackgroundOption(
        id: index,
        name: '${zodiacCategories[zodiacIndex].name} ${index + 1}',
        color: _getZodiacColor(zodiacIndex, index),
        isLocked: index >= 12, // Last 12 are locked
      ),
    );
  }

  static Color _getFantasyColor(int index) {
    final colors = [
      Color(0xFFFFE082),
      Color(0xFF81C784),
      Color(0xFF64B5F6),
      Color(0xFFFF8A65),
      Color(0xFFBA68C8),
      Color(0xFFFFAB91),
      Color(0xFF80CBC4),
      Color(0xFFFF7043),
      Color(0xFFF48FB1),
      Color(0xFF90CAF9),
      Color(0xFFA5D6A7),
      Color(0xFFFFCC02),
      Color(0xFFCE93D8),
    ];
    return colors[index % colors.length];
  }

  static Color _getZodiacColor(int zodiacIndex, int styleIndex) {
    final baseColors = [
      Color(0xFFE57373),
      Color(0xFF81C784),
      Color(0xFF64B5F6),
      Color(0xFFFFB74D),
      Color(0xFFBA68C8),
      Color(0xFF4DB6AC),
      Color(0xFFFF8A65),
      Color(0xFFF06292),
      Color(0xFF9575CD),
      Color(0xFF4FC3F7),
      Color(0xFFAED581),
      Color(0xFFFFD54F),
    ];

    final base = baseColors[zodiacIndex];
    final opacity = 0.3 + (styleIndex % 8) * 0.1;
    return base.withOpacity(opacity.clamp(0.3, 1.0));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        title: const Text(
          'Choose Your Background',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          _buildPreviewSection(),
          _buildTabSection(),
          Expanded(child: _buildBackgroundContent()),
          _buildConfirmButton(),
        ],
      ),
    );
  }

  Widget _buildPreviewSection() {
    final screenWidth = MediaQuery.of(context).size.width;
    final previewHeight = screenWidth > 600 ? 200.0 : 160.0;

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade300,
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: previewHeight,
          decoration: _getPreviewDecoration(),
          child: Stack(
            children: [
              Positioned(
                top: 20,
                left: 0,
                right: 0,
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundColor: Colors.white.withOpacity(0.3),
                      child: const Icon(
                        Icons.person,
                        size: 35,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Angela',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const Positioned(
                bottom: 12,
                left: 16,
                child: Text(
                  'Preview',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabSection() {
    final tabs = ['Default', 'Fantasy', 'Zodiac'];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final title = entry.value;

          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  selectedTabIndex = index;
                  selectedBackgroundIndex = 0;
                  selectedFantasyCategory = 0;
                  selectedZodiacCategory = 0;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: selectedTabIndex == index
                          ? const Color(0xFFFFA726)
                          : Colors.transparent,
                      width: 3,
                    ),
                  ),
                ),
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: selectedTabIndex == index
                        ? const Color(0xFFFFA726)
                        : Colors.grey,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildBackgroundContent() {
    switch (selectedTabIndex) {
      case 0: // Default
        return _buildDefaultContent();
      case 1: // Fantasy
        return _buildFantasyContent();
      case 2: // Zodiac
        return _buildZodiacContent();
      default:
        return Container();
    }
  }

  Widget _buildDefaultContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (int i = 0; i < defaultBackgrounds.length; i++)
            _buildDefaultCard(i),
        ],
      ),
    );
  }

  Widget _buildFantasyContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Fantasy categories row - Updated size to 55x55
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              for (int i = 0; i < fantasyCategories.length; i++)
                _buildFantasyCategoryCard(i),
            ],
          ),
          const SizedBox(height: 20),

          // Show subcategories based on selected category
          Expanded(child: _buildFantasySubcategories()),
        ],
      ),
    );
  }

  Widget _buildZodiacContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Zodiac categories - Updated size to 55x55
          Container(
            height: 120,
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 6,
                childAspectRatio: 1,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: zodiacCategories.length,
              itemBuilder: (context, index) => _buildZodiacCategoryCard(index),
            ),
          ),
          const SizedBox(height: 20),

          // Show zodiac backgrounds
          Expanded(child: _buildZodiacBackgrounds()),
        ],
      ),
    );
  }

  Widget _buildDefaultCard(int index) {
    final option = defaultBackgrounds[index];
    final isSelected =
        selectedBackgroundIndex == index && selectedTabIndex == 0;

    return GestureDetector(
      onTap: () => setState(() => selectedBackgroundIndex = index),
      child: Container(
        width: 105,
        height: 65,
        decoration: BoxDecoration(
          color: option.color,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? const Color(0xFFFFA726) : Colors.grey.shade300,
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Stack(
          children: [
            if (isSelected)
              const Positioned(
                top: 4,
                right: 4,
                child: CircleAvatar(
                  radius: 8,
                  backgroundColor: Color(0xFFFFA726),
                  child: Icon(Icons.check, size: 10, color: Colors.white),
                ),
              ),
            Positioned(
              bottom: 4,
              left: 4,
              right: 4,
              child: Text(
                option.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Updated Fantasy Category Card - Size changed to 55x55
  Widget _buildFantasyCategoryCard(int index) {
    final category = fantasyCategories[index];
    final isSelected = selectedFantasyCategory == index;

    return GestureDetector(
      onTap: () => setState(() {
        selectedFantasyCategory = index;
        selectedBackgroundIndex = 0;
      }),
      child: Container(
        width: 55, // Changed from 49 to 55
        height: 55, // Changed from 49 to 55
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFFF4D4) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? const Color(0xFFFFA726) : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 28, // Slightly increased icon size
              height: 28,
              child: category.imagePath.endsWith('.svg')
                  ? SvgPicture.asset(
                      category.imagePath,
                      fit: BoxFit.contain,
                      colorFilter: ColorFilter.mode(
                        isSelected ? const Color(0xFFFFA726) : Colors.black54,
                        BlendMode.srcIn,
                      ),
                    )
                  : Image.asset(category.imagePath, fit: BoxFit.contain),
            ),
            const SizedBox(height: 2),
            Text(
              category.name,
              style: TextStyle(
                color: isSelected ? const Color(0xFFFFA726) : Colors.black54,
                fontSize: 8,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFantasySubcategories() {
    if (selectedFantasyCategory == 0) {
      // Royal - show King/Queen
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          for (int i = 0; i < royalBackgrounds.length; i++) _buildRoyalCard(i),
        ],
      );
    } else {
      // Other categories - show 13 backgrounds
      return GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          childAspectRatio: 1.2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: genericFantasyBackgrounds.length,
        itemBuilder: (context, index) => _buildGenericFantasyCard(index),
      );
    }
  }

  // Updated Royal Card with blur effect for locked items
  Widget _buildRoyalCard(int index) {
    final option = royalBackgrounds[index];
    final isSelected =
        selectedBackgroundIndex == index && selectedTabIndex == 1;
    final isLocked = option.isLocked ?? false;

    return GestureDetector(
      onTap: isLocked
          ? null
          : () => setState(() => selectedBackgroundIndex = index),
      child: Container(
        width: 105,
        height: 65,
        decoration: BoxDecoration(
          color: option.color,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? const Color(0xFFFFA726) : Colors.grey.shade300,
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Stack(
          children: [
            // Blur effect for locked items
            if (isLocked)
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(7),
                  child: Container(
                    color: option.color,
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                      child: Container(color: Colors.transparent),
                    ),
                  ),
                ),
              ),
            // Lock icon for locked items
            if (isLocked)
              const Center(
                child: Icon(Icons.lock, color: Colors.white70, size: 24),
              ),
            // Check mark for selected items (only if not locked)
            if (isSelected && !isLocked)
              const Positioned(
                top: 4,
                right: 4,
                child: CircleAvatar(
                  radius: 8,
                  backgroundColor: Color(0xFFFFA726),
                  child: Icon(Icons.check, size: 10, color: Colors.white),
                ),
              ),
            // Name label
            Positioned(
              bottom: 4,
              left: 4,
              right: 4,
              child: Text(
                option.name,
                style: TextStyle(
                  color: isLocked ? Colors.white70 : Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Updated Generic Fantasy Card with blur effect for locked items
  Widget _buildGenericFantasyCard(int index) {
    final option = genericFantasyBackgrounds[index];
    final isSelected =
        selectedBackgroundIndex == index && selectedTabIndex == 1;
    final isLocked = option.isLocked ?? false;

    return GestureDetector(
      onTap: isLocked
          ? null
          : () => setState(() => selectedBackgroundIndex = index),
      child: Container(
        decoration: BoxDecoration(
          color: option.color,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? const Color(0xFFFFA726) : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Stack(
          children: [
            // Blur effect for locked items
            if (isLocked)
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(7),
                  child: Container(
                    color: option.color,
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                      child: Container(color: Colors.transparent),
                    ),
                  ),
                ),
              ),
            // Lock icon for locked items
            if (isLocked)
              const Center(
                child: Icon(Icons.lock, color: Colors.white70, size: 20),
              ),
            // Check mark for selected items (only if not locked)
            if (isSelected && !isLocked)
              const Positioned(
                top: 4,
                right: 4,
                child: CircleAvatar(
                  radius: 8,
                  backgroundColor: Color(0xFFFFA726),
                  child: Icon(Icons.check, size: 10, color: Colors.white),
                ),
              ),
            // Name label
            Positioned(
              bottom: 4,
              left: 4,
              right: 4,
              child: Text(
                option.name,
                style: TextStyle(
                  color: isLocked ? Colors.white70 : Colors.white,
                  fontSize: 8,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Updated Zodiac Category Card - Size changed to 55x55 and bigger icon
  Widget _buildZodiacCategoryCard(int index) {
    final category = zodiacCategories[index];
    final isSelected = selectedZodiacCategory == index;

    return GestureDetector(
      onTap: () => setState(() {
        selectedZodiacCategory = index;
        selectedBackgroundIndex = 0;
      }),
      child: Container(
        width: 55, // Changed to match fantasy category size
        height: 55,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFFF4D4) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? const Color(0xFFFFA726) : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              category.icon,
              style: const TextStyle(fontSize: 18), // Increased from 12 to 18
            ),
            const SizedBox(height: 2),
            Text(
              category.name,
              style: TextStyle(
                color: isSelected ? const Color(0xFFFFA726) : Colors.black54,
                fontSize: 6,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Updated Zodiac Backgrounds with blur effect for locked items
  Widget _buildZodiacBackgrounds() {
    final backgrounds = getZodiacBackgrounds(selectedZodiacCategory);

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        childAspectRatio: 1.2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: backgrounds.length,
      itemBuilder: (context, index) {
        final option = backgrounds[index];
        final isSelected =
            selectedBackgroundIndex == index && selectedTabIndex == 2;
        final isLocked = option.isLocked ?? false;

        return GestureDetector(
          onTap: isLocked
              ? null
              : () => setState(() => selectedBackgroundIndex = index),
          child: Container(
            decoration: BoxDecoration(
              color: option.color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected
                    ? const Color(0xFFFFA726)
                    : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Stack(
              children: [
                // Blur effect for locked items
                if (isLocked)
                  Positioned.fill(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(7),
                      child: Container(
                        color: option.color,
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                          child: Container(color: Colors.transparent),
                        ),
                      ),
                    ),
                  ),
                // Lock icon for locked items
                if (isLocked)
                  const Center(
                    child: Icon(Icons.lock, color: Colors.white70, size: 20),
                  ),
                // Check mark for selected items (only if not locked)
                if (isSelected && !isLocked)
                  const Positioned(
                    top: 4,
                    right: 4,
                    child: CircleAvatar(
                      radius: 8,
                      backgroundColor: Color(0xFFFFA726),
                      child: Icon(Icons.check, size: 10, color: Colors.white),
                    ),
                  ),
                // Number label
                Positioned(
                  bottom: 4,
                  left: 4,
                  right: 4,
                  child: Text(
                    '${index + 1}',
                    style: TextStyle(
                      color: isLocked ? Colors.white70 : Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  BoxDecoration _getPreviewDecoration() {
    switch (selectedTabIndex) {
      case 0: // Default
        return BoxDecoration(
          color: defaultBackgrounds[selectedBackgroundIndex].color,
        );
      case 1: // Fantasy
        if (selectedFantasyCategory == 0) {
          return BoxDecoration(
            color: royalBackgrounds[selectedBackgroundIndex].color,
          );
        } else {
          return BoxDecoration(
            color: genericFantasyBackgrounds[selectedBackgroundIndex].color,
          );
        }
      case 2: // Zodiac
        final backgrounds = getZodiacBackgrounds(selectedZodiacCategory);
        return BoxDecoration(color: backgrounds[selectedBackgroundIndex].color);
      default:
        return BoxDecoration(color: defaultBackgrounds[0].color);
    }
  }

  Widget _buildConfirmButton() {
    return Container(
      margin: const EdgeInsets.all(16),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _saveBackground,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFFBF00),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        child: const Text(
          'Confirm',
          style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  void _saveBackground() {
    // Check if selected background is locked
    bool isSelectedLocked = false;

    switch (selectedTabIndex) {
      case 1: // Fantasy
        if (selectedFantasyCategory == 0) {
          isSelectedLocked =
              royalBackgrounds[selectedBackgroundIndex].isLocked ?? false;
        } else {
          isSelectedLocked =
              genericFantasyBackgrounds[selectedBackgroundIndex].isLocked ??
              false;
        }
        break;
      case 2: // Zodiac
        final backgrounds = getZodiacBackgrounds(selectedZodiacCategory);
        isSelectedLocked =
            backgrounds[selectedBackgroundIndex].isLocked ?? false;
        break;
    }

    if (isSelectedLocked) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('This background is locked!'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    // Get the selected background color
    Color selectedColor = BackgroundStateManager().getBackgroundFromSelection(
      tabIndex: selectedTabIndex,
      backgroundIndex: selectedBackgroundIndex,
      fantasyCategory: selectedFantasyCategory,
      zodiacCategory: selectedZodiacCategory,
    );

    // Update the background in the state manager
    BackgroundStateManager().updateBackground(selectedColor);

    // Generate description for the snackbar
    String selectedName = _getSelectedBackgroundName();

    // Show confirmation message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Background "$selectedName" saved!'),
        backgroundColor: const Color(0xFFFFA726),
        duration: const Duration(seconds: 2),
      ),
    );

    // Navigate back to profile
    context.pop();
  }

  String _getSelectedBackgroundName() {
    switch (selectedTabIndex) {
      case 0:
        return defaultBackgrounds[selectedBackgroundIndex].name;
      case 1:
        if (selectedFantasyCategory == 0) {
          return '${fantasyCategories[selectedFantasyCategory].name} - ${royalBackgrounds[selectedBackgroundIndex].name}';
        } else {
          return '${fantasyCategories[selectedFantasyCategory].name} - ${genericFantasyBackgrounds[selectedBackgroundIndex].name}';
        }
      case 2:
        return '${zodiacCategories[selectedZodiacCategory].name} - Style ${selectedBackgroundIndex + 1}';
      default:
        return 'Default';
    }
  }
}

// Updated Data classes with isLocked property
class BackgroundOption {
  final int id;
  final String name;
  final Color color;
  final String? icon;
  final bool? isLocked;

  BackgroundOption({
    required this.id,
    required this.name,
    required this.color,
    this.icon,
    this.isLocked = false,
  });
}

class FantasyCategory {
  final int id;
  final String name;
  final String imagePath;

  FantasyCategory({
    required this.id,
    required this.name,
    required this.imagePath,
  });
}

class ZodiacCategory {
  final int id;
  final String name;
  final String icon;

  ZodiacCategory({required this.id, required this.name, required this.icon});
}
