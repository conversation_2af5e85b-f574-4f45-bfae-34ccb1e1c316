import 'package:flutter/material.dart';

class NotificationView extends StatefulWidget {
  const NotificationView({super.key});

  @override
  State<NotificationView> createState() => _NotificationViewState();
}

class _NotificationViewState extends State<NotificationView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  final List<String> notifications = [
    "Your friend <PERSON>, use your referral code, click here to claim RM1.00!",
    "Your friend <PERSON>, use your referral code, click here to claim RM1.00!",
    "Your friend <PERSON>, use your referral code, click here to claim RM1.00!",
    "Your friend <PERSON>, use your referral code, click here to claim RM1.00!",
    "Your friend <PERSON>, use your referral code, click here to claim RM1.00!",
    "Your friend <PERSON>, use your referral code, click here to claim RM1.00!",
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this, initialIndex: 2);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Add this method to show the closed angpao popup
  void _showAngpaoClosed(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 32,
            vertical: 80,
          ),
          child: GestureDetector(
            onTap: () {
              Navigator.of(context).pop(); // Close closed angpao
              _showAngpaoOpened(context); // Show opened angpao
            },
            child: Image.asset(
              'assets/images/ANGPAO CLOSED.png', // Place your closed angpao image here
              fit: BoxFit.contain,
            ),
          ),
        );
      },
    );
  }

  // Add this method to show the opened angpao popup
  void _showAngpaoOpened(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 32,
            vertical: 80,
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Image.asset(
                'assets/images/ANGPAO OPEN.png', // Place your opened angpao image here
                fit: BoxFit.contain,
              ),
              Positioned(
                top: 120,
                child: Column(
                  children: const [
                    Text(
                      "You got",
                      style: TextStyle(
                        fontSize: 20,
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 12),
                    Text(
                      "RM 1.00",
                      style: TextStyle(
                        fontSize: 36,
                        color: Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNotificationCard(String text, bool highlighted) {
    return GestureDetector(
      onTap: () {
        _showAngpaoClosed(context);
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: highlighted ? const Color(0xFFFFF7E0) : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: const Color(0xFFEFEFEF),
                  shape: BoxShape.circle,
                ),
                child: const Center(
                  child: Text(
                    "Icon",
                    style: TextStyle(fontSize: 13, color: Colors.black54),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  "📢 $text",
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    height: 1.5,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: BackButton(color: Colors.black),
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          "My Notifications",
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.mail_outline, color: Colors.black),
            onPressed: () {},
          ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(48),
          child: Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              indicatorColor: Color(0xFFFFB800),
              labelColor: Color(0xFFFFB800),
              unselectedLabelColor: Colors.black,
              labelStyle: TextStyle(fontWeight: FontWeight.bold),
              tabs: [
                Tab(text: "Announcements"),
                Tab(text: "System"),
                Tab(text: "Angpao"),
              ],
            ),
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          Center(child: Text("No Announcements")),
          Center(child: Text("No System Notifications")),
          ListView.builder(
            padding: const EdgeInsets.only(bottom: 24),
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              return _buildNotificationCard(
                notifications[index],
                index < 3, // Highlight first 3
              );
            },
          ),
        ],
      ),
    );
  }
}
