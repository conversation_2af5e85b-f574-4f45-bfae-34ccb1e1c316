import 'package:dio/dio.dart';
import '../model/LuckyDrawProduct.dart';
import '../model/LuckyDrawCategory.dart';
import '../model/PrizeRevealProduct.dart';

final luckyDrawService = LuckyDrawService();

class LuckyDrawService {
  final Dio dio = Dio();

  // Cache for categories to avoid repeated API calls
  List<LuckyDrawCategory>? _cachedCategories;
  DateTime? _lastCacheTime;
  static const Duration _cacheValidDuration = Duration(minutes: 30);

  Future<Map<String, dynamic>> getLuckyDrawProducts({
    int? limit = 10,
    int? page = 1,
    String? sortBy,
    bool? ascending,
    int? categoryId,
    String? productSkuName,
    List<int>? totalAmount,
  }) async {
    try {
      print(
        'Service: getLuckyDrawProducts called with limit: $limit, page: $page, categoryId: $categoryId, totalAmount: $totalAmount',
      );

      // Build query parameters according to the API specification.
      // Some backends (including this endpoint) expect flattened keys for
      // nested query objects, e.g. `vo.totalAmount=10&vo.totalAmount=100`
      // instead of `vo[totalAmount][]=...`. So we use dot-notation keys.
      final Map<String, dynamic> queryParams = {
        if (categoryId != null) 'vo.categoryId': categoryId,
        if (productSkuName != null) 'vo.productSkuName': productSkuName,
        if (totalAmount != null) 'vo.totalAmount': totalAmount,
        'orderItem.column': sortBy ?? 'created_at', // Default sort column
        'orderItem.asc': ascending ?? false, // Default descending order
        'limit': limit,
        'page': page,
      };

      print('Service: Query parameters: $queryParams');
      print(
        'Service: Making API call to: https://api.luckymall.com.my/product/point',
      );

      final response = await dio.get(
        'https://api.luckymall.com.my/product/point',
        queryParameters: queryParams,
      );

      print('Service: API response status: ${response.statusCode}');
      print('Service: API response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        print('Service: Response data status: ${responseData['status']}');
        print('Service: Response data message: ${responseData['message']}');

        if (responseData['status'] == 200) {
          final List<dynamic> productList = responseData['data']['list'] ?? [];
          print('Service: Product list from API: $productList');

          final products = productList
              .map((json) => LuckyDrawProduct.fromJson(json))
              .toList();

          print('Service: Parsed products: ${products.length}');

          return {
            'products': products,
            'totalCount': responseData['data']['totalCount'] ?? 0,
            'pageSize': responseData['data']['pageSize'] ?? 10,
            'totalPage': responseData['data']['totalPage'] ?? 1,
            'currentPage': responseData['data']['currentPage'] ?? 1,
          };
        } else {
          throw Exception('API Error: ${responseData['message']}');
        }
      } else {
        throw Exception('Failed to load products: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('Service: DioException: ${e.message}');
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print('Service: Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  Future<Map<String, dynamic>> getLuckyDrawProductsByCategory(
    int categoryId, {
    int? limit = 10,
    int? page = 1,
    String? sortBy,
    bool? ascending,
  }) async {
    return getLuckyDrawProducts(
      limit: limit,
      page: page,
      sortBy: sortBy,
      ascending: ascending,
      categoryId: categoryId,
    );
  }

  Future<Map<String, dynamic>> searchLuckyDrawProducts(
    String query, {
    int? limit = 10,
    int? page = 1,
    String? sortBy,
    bool? ascending,
  }) async {
    return getLuckyDrawProducts(
      limit: limit,
      page: page,
      sortBy: sortBy ?? 'relevance',
      ascending: ascending ?? false,
      productSkuName: query,
    );
  }

  Future<Map<String, dynamic>> getLuckyDrawProductsByPointRange({
    required int minPoints,
    required int maxPoints,
    int? categoryId,
    int? limit = 10,
    int? page = 1,
    String? sortBy,
    bool? ascending,
  }) async {
    return getLuckyDrawProducts(
      limit: limit,
      page: page,
      sortBy: sortBy ?? 'totalAmount',
      ascending: ascending ?? true,
      categoryId: categoryId,
      totalAmount: [minPoints, maxPoints],
    );
  }

  /// Get detailed information for a specific product by productSpuId
  Future<LuckyDrawProduct?> getProductDetail(int productSpuId) async {
    try {
      final response = await dio.get(
        'https://api.luckymall.com.my/product/point/$productSpuId',
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['status'] == 0 && responseData['data'] != null) {
          return LuckyDrawProduct.fromJson(responseData['data']);
        } else {
          return null;
        }
      } else {
        return null;
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  // Helper method to convert category names to category IDs
  // This method is now deprecated since we fetch categories from API
  // Use getCategories() method instead for dynamic category management
  int? getCategoryId(String category) {
    // This method is kept for backward compatibility but should be replaced
    // with dynamic category fetching from the API
    print(
      'Service: Warning: Using deprecated hardcoded category mapping for: $category',
    );

    // For now, return null to force API usage
    return null;
  }

  /// Find category ID by category name using cached categories
  /// This method provides dynamic category mapping based on API data
  int? findCategoryIdByName(String categoryName) {
    // This would need to be implemented with a cached list of categories
    // For now, we'll need to fetch categories each time or implement caching
    print('Service: findCategoryIdByName called for: $categoryName');
    return null;
  }

  /// Get category ID by name using cached categories
  /// This is the recommended method to use instead of getCategoryId
  Future<int?> getCategoryIdByName(String categoryName) async {
    try {
      final categories = await getCategories();
      final category = categories.firstWhere(
        (cat) => cat.categoryName.toLowerCase() == categoryName.toLowerCase(),
        orElse: () => LuckyDrawCategory(categoryId: -1, categoryName: ''),
      );

      if (category.categoryId != -1) {
        return category.categoryId;
      }
      return null;
    } catch (e) {
      print('Service: Error finding category ID for "$categoryName": $e');
      return null;
    }
  }

  // Test method to check if API is accessible
  Future<bool> testAPI() async {
    try {
      print('Service: Testing API connectivity...');
      final response = await dio.get(
        'https://api.luckymall.com.my/product/point',
        queryParameters: {'limit': 1, 'page': 1},
      );
      print('Service: API test successful. Status: ${response.statusCode}');
      print('Service: API test response: ${response.data}');
      return response.statusCode == 200;
    } catch (e) {
      print('Service: API test failed: $e');
      return false;
    }
  }

  /// Fetch categories for lucky draw products
  Future<List<LuckyDrawCategory>> getCategories({
    bool forceRefresh = false,
  }) async {
    // Check if cache is still valid
    if (!forceRefresh &&
        _cachedCategories != null &&
        _lastCacheTime != null &&
        DateTime.now().difference(_lastCacheTime!) < _cacheValidDuration) {
      print(
        'Service: Returning cached categories (${_cachedCategories!.length} items)',
      );
      return _cachedCategories!;
    }

    try {
      print('Service: Fetching categories from API...');

      final response = await dio.get(
        'https://api.luckymall.com.my/product/point/categorys',
      );

      print('Service: Categories API response status: ${response.statusCode}');
      print('Service: Categories API response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        print(
          'Service: Categories response data status: ${responseData['status']}',
        );
        print(
          'Service: Categories response data message: ${responseData['message']}',
        );

        // Updated to handle status 200 (success) instead of 0
        if (responseData['status'] == 200) {
          final List<dynamic> categoryList = responseData['data'] ?? [];
          print('Service: Category list from API: $categoryList');

          final categories = categoryList
              .map((json) => LuckyDrawCategory.fromJson(json))
              .toList();

          // Update cache
          _cachedCategories = categories;
          _lastCacheTime = DateTime.now();

          print('Service: Parsed categories: ${categories.length}');
          return categories;
        } else {
          throw Exception('API Error: ${responseData['message']}');
        }
      } else {
        throw Exception('Failed to load categories: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('Service: DioException in getCategories: ${e.message}');
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print('Service: Unexpected error in getCategories: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  /// Clear the categories cache
  void clearCategoriesCache() {
    _cachedCategories = null;
    _lastCacheTime = null;
    print('Service: Categories cache cleared');
  }

  /// Fetch mega prizes from the API
  Future<List<dynamic>> getMegaPrizes() async {
    try {
      print('Service: Fetching mega prizes from API...');

      final response = await dio.get(
        'https://api.luckymall.com.my/product/point/mega',
      );

      print('Service: Mega prizes API response status: ${response.statusCode}');
      print('Service: Mega prizes API response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        print(
          'Service: Mega prizes response data status: ${responseData['status']}',
        );
        print(
          'Service: Mega prizes response data message: ${responseData['message']}',
        );

        if (responseData['status'] == 200) {
          final List<dynamic> megaPrizesList = responseData['data'] ?? [];
          print('Service: Mega prizes list from API: $megaPrizesList');

          return megaPrizesList;
        } else {
          throw Exception('API Error: ${responseData['message']}');
        }
      } else {
        throw Exception('Failed to load mega prizes: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('Service: DioException in getMegaPrizes: ${e.message}');
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print('Service: Unexpected error in getMegaPrizes: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  /// Fetch prize reveal products from the API
  Future<List<PrizeRevealProduct>> getPrizeRevealProducts() async {
    try {
      print('Service: Fetching prize reveal products from API...');

      final response = await dio.get(
        'https://api.luckymall.com.my/product/point/reveal',
      );

      print(
        'Service: Prize reveal API response status: ${response.statusCode}',
      );
      print('Service: Prize reveal API response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        print(
          'Service: Prize reveal response data status: ${responseData['status']}',
        );
        print(
          'Service: Prize reveal response data message: ${responseData['message']}',
        );

        if (responseData['status'] == 0) {
          final List<dynamic> prizeRevealList = responseData['data'] ?? [];
          print('Service: Prize reveal list from API: $prizeRevealList');

          final prizeRevealProducts = prizeRevealList
              .map((json) => PrizeRevealProduct.fromJson(json))
              .toList();

          print(
            'Service: Parsed prize reveal products: ${prizeRevealProducts.length}',
          );
          return prizeRevealProducts;
        } else {
          throw Exception('API Error: ${responseData['message']}');
        }
      } else {
        throw Exception(
          'Failed to load prize reveal products: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      print('Service: DioException in getPrizeRevealProducts: ${e.message}');
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print('Service: Unexpected error in getPrizeRevealProducts: $e');
      throw Exception('Unexpected error: $e');
    }
  }
}
