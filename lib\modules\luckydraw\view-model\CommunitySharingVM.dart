import 'package:flutter/material.dart';
import '../data/LuckyDrawRepository.dart';
import '../model/CommunityPost.dart';

enum CommunitySortBy { latest, popular, trending }

class CommunitySharingVM extends ChangeNotifier {
  final LuckyDrawRepository repository;

  CommunitySharingVM({LuckyDrawRepository? repository})
    : repository = repository ?? MockLuckyDrawRepository();

  bool isLoading = true;
  bool isRefreshing = false;
  String? errorMessage;
  List<CommunityPost> posts = [];
  CommunitySortBy sortBy = CommunitySortBy.latest;
  int currentPage = 1;
  bool hasMorePosts = true;

  // Pagination
  static const int postsPerPage = 20;

  Future<void> initialize() async {
    await fetchCommunityPosts();
  }

  Future<void> fetchCommunityPosts({bool refresh = false}) async {
    if (refresh) {
      isRefreshing = true;
      currentPage = 1;
      hasMorePosts = true;
    } else {
      isLoading = true;
    }

    errorMessage = null;
    notifyListeners();

    try {
      final newPosts = await repository.fetchCommunityPosts(
        page: currentPage,
        limit: postsPerPage,
        sortBy: sortBy.name,
      );

      if (refresh) {
        posts = newPosts;
      } else if (currentPage == 1) {
        posts = newPosts;
      } else {
        posts.addAll(newPosts);
      }

      hasMorePosts = newPosts.length == postsPerPage;
    } catch (e) {
      errorMessage = 'Failed to load community posts: ${e.toString()}';
      if (refresh || currentPage == 1) {
        posts = [];
      }
    } finally {
      isLoading = false;
      isRefreshing = false;
      notifyListeners();
    }
  }

  Future<void> loadMorePosts() async {
    if (!hasMorePosts || isLoading) return;

    currentPage++;
    await fetchCommunityPosts();
  }

  Future<void> refreshPosts() async {
    await fetchCommunityPosts(refresh: true);
  }

  Future<void> changeSortBy(CommunitySortBy newSortBy) async {
    if (sortBy == newSortBy) return;

    sortBy = newSortBy;
    currentPage = 1;
    await fetchCommunityPosts();
  }

  Future<void> likePost(String postId) async {
    try {
      // Find the post and update locally first for immediate UI feedback
      final postIndex = posts.indexWhere((post) => post.id == postId);
      if (postIndex != -1) {
        final currentPost = posts[postIndex];
        final updatedPost = currentPost.copyWith(
          isLiked: !currentPost.isLiked,
          likeCount: currentPost.isLiked
              ? currentPost.likeCount - 1
              : currentPost.likeCount + 1,
        );
        posts[postIndex] = updatedPost;
        notifyListeners();

        // Make API call
        await repository.likePost(postId, !currentPost.isLiked);
      }
    } catch (e) {
      // Revert the change if API call fails
      final postIndex = posts.indexWhere((post) => post.id == postId);
      if (postIndex != -1) {
        final currentPost = posts[postIndex];
        final revertedPost = currentPost.copyWith(
          isLiked: !currentPost.isLiked,
          likeCount: currentPost.isLiked
              ? currentPost.likeCount - 1
              : currentPost.likeCount + 1,
        );
        posts[postIndex] = revertedPost;
        notifyListeners();
      }

      errorMessage = 'Failed to like post: ${e.toString()}';
      notifyListeners();
    }
  }

  Future<void> createPost({
    required String caption,
    required String postImage,
    String? productName,
    String? prizeType,
  }) async {
    try {
      await repository.createCommunityPost(
        caption: caption,
        postImage: postImage,
        productName: productName,
        prizeType: prizeType,
      );

      // Refresh posts to show the new post
      await refreshPosts();
    } catch (e) {
      errorMessage = 'Failed to create post: ${e.toString()}';
      notifyListeners();
    }
  }

  Future<void> deletePost(String postId) async {
    try {
      await repository.deleteCommunityPost(postId);

      // Remove the post from local list
      posts.removeWhere((post) => post.id == postId);
      notifyListeners();
    } catch (e) {
      errorMessage = 'Failed to delete post: ${e.toString()}';
      notifyListeners();
    }
  }

  void clearError() {
    errorMessage = null;
    notifyListeners();
  }
}
