import 'package:flutter/material.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:go_router/go_router.dart';
import 'dart:math';

class DeactivateAccountView extends StatefulWidget {
  const DeactivateAccountView({super.key});

  @override
  State<DeactivateAccountView> createState() => _DeactivateAccountViewState();
}

class _DeactivateAccountViewState extends State<DeactivateAccountView> {
  bool isChecked = false;
  bool showOtpPage = false;

  final String phoneNumber = '+60 13 860 4691'; // Mocked
  final TextEditingController otpController = TextEditingController();

  String? sentCode;
  bool codeSent = false;
  bool codeVerified = false;
  String? errorText;

  void _showWarningDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
        contentPadding: const EdgeInsets.fromLTRB(24, 24, 24, 8),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: const [
                Icon(Icons.warning_amber_rounded, color: Colors.red, size: 32),
                SizedBox(width: 8),
                Text(
                  'WARNING!',
                  style: TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.w700,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text.rich(
              TextSpan(
                children: [
                  TextSpan(text: 'You need to clear all of your assets ('),
                  TextSpan(
                    text: 'ongoing orders, cards, and points',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  TextSpan(text: ') before you can deactivate your account.'),
                ],
              ),
              style: TextStyle(fontSize: 15, color: Colors.black),
            ),
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerRight,
              child: GestureDetector(
                onTap: () {
                  context.pop();
                  setState(() {
                    showOtpPage = true;
                  });
                },
                child: const Text(
                  'I Understand',
                  style: TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _sendCode() {
    // Generate a random 6-digit code (mockup)
    final random = Random();
    sentCode = (100000 + random.nextInt(900000)).toString();
    codeSent = true;
    codeVerified = false;
    errorText = null;
    otpController.clear();
    setState(() {});
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Mock verification code: $sentCode'),
        duration: const Duration(seconds: 45),
      ),
    );
  }

  void _verifyCode(String value) {
    if (sentCode != null && value == sentCode) {
      setState(() {
        codeVerified = true;
        errorText = null;
      });
    } else {
      setState(() {
        codeVerified = false;
        errorText = 'Invalid verification code';
      });
    }
  }

  Widget _buildIntroPage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _DeactivationExplanation(),
        const SizedBox(height: 28),
        Row(
          children: [
            Checkbox(
              value: isChecked,
              onChanged: (val) => setState(() => isChecked = val ?? false),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              activeColor: Colors.black,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            const SizedBox(width: 4),
            const Text(
              "I understand",
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 16,
                color: Colors.black,
              ),
            ),
          ],
        ),
        const Spacer(),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: isChecked
                  ? Colors.red
                  : Colors.red.withOpacity(0.5),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(14),
              ),
              elevation: 0,
            ),
            onPressed: isChecked ? _showWarningDialog : null,
            child: const Text(
              'Deactivate Account',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 18,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOtpPage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _DeactivationExplanation(),
        const SizedBox(height: 24),

        const Text(
          'Phone Number',
          style: TextStyle(fontWeight: FontWeight.w500, fontSize: 15),
        ),
        const SizedBox(height: 8),
        IntlPhoneField(
          initialCountryCode: 'MY',
          enabled: false,
          initialValue: phoneNumber,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: const BorderSide(color: Color(0xFFDDDDDD)),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(
              vertical: 16,
              horizontal: 12,
            ),
            suffixIcon: TextButton(
              onPressed: _sendCode,
              child: const Text(
                'Send Code',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                  fontSize: 15,
                ),
              ),
            ),
            suffixIconConstraints: const BoxConstraints(
              minHeight: 36,
              minWidth: 90,
            ),
          ),
          style: const TextStyle(fontSize: 16),
        ),
        const SizedBox(height: 20),

        const Text(
          'Verification Code',
          style: TextStyle(fontWeight: FontWeight.w500, fontSize: 15),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: otpController,
          keyboardType: TextInputType.number,
          maxLength: 6,
          decoration: InputDecoration(
            counterText: "",
            hintText: 'Please enter your OTP code',
            hintStyle: const TextStyle(color: Colors.grey),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14),
              borderSide: const BorderSide(color: Color(0xFFDDDDDD)),
            ),
            contentPadding: const EdgeInsets.symmetric(
              vertical: 16,
              horizontal: 12,
            ),
            filled: true,
            fillColor: Colors.white,
            errorText: errorText,
          ),
          onChanged: (value) {
            if (value.length == 6) {
              _verifyCode(value);
            } else {
              setState(() {
                codeVerified = false;
                errorText = null;
              });
            }
          },
        ),
        const SizedBox(height: 8),

        const Text(
          'Please clear all of your assets first before deleting your account',
          style: TextStyle(
            color: Colors.red,
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),

        const Spacer(),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: codeVerified ? Colors.red : Colors.grey,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(14),
              ),
              elevation: 0,
            ),
            onPressed: codeVerified
                ? () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Account deactivated!'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  }
                : null,
            child: const Text(
              'Deactivate Account',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 18,
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () {
            if (showOtpPage) {
              setState(() => showOtpPage = false);
            } else {
              context.pop();
            }
          },
        ),
        title: const Text(
          'Deactivate Account',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        centerTitle: true,
      ),
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
        child: Column(
          children: [
            Expanded(child: showOtpPage ? _buildOtpPage() : _buildIntroPage()),
          ],
        ),
      ),
    );
  }
}

class _DeactivationExplanation extends StatelessWidget {
  const _DeactivationExplanation();

  @override
  Widget build(BuildContext context) {
    TextStyle titleStyle = const TextStyle(
      fontWeight: FontWeight.w700,
      fontSize: 16,
    );
    TextStyle bodyStyle = const TextStyle(fontSize: 15, color: Colors.black);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('1. Deactivation Grace Period', style: titleStyle),
        Padding(
          padding: const EdgeInsets.only(bottom: 4.0),
          child: Text(
            "When you request to deactivate your account, a 7-day grace period will begin. During this period:",
            style: bodyStyle,
          ),
        ),
        const _BulletText('Your account will remain active.'),
        const _BulletText(
          'You can cancel the deactivation anytime by logging in.',
        ),
        const SizedBox(height: 16),
        Text('2. Automatic Cancellation', style: titleStyle),
        Padding(
          padding: const EdgeInsets.only(bottom: 4.0),
          child: Text(
            "If you log in during the 7-day grace period, your deactivation request will be automatically cancelled.",
            style: bodyStyle,
          ),
        ),
        const _BulletText('Your account will remain active.'),
        const _BulletText('No further action is required from you.'),
        const SizedBox(height: 16),
        Text('3. Reinitiate Deactivation', style: titleStyle),
        Padding(
          padding: const EdgeInsets.only(bottom: 4.0),
          child: RichText(
            text: TextSpan(
              style: bodyStyle,
              children: [
                const TextSpan(
                  text:
                      "If you still wish to deactivate your account after cancellation, you must repeat the ",
                ),
                TextSpan(
                  text: "deactivation process",
                  style: bodyStyle.copyWith(fontWeight: FontWeight.bold),
                ),
                const TextSpan(text: " from the beginning."),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _BulletText extends StatelessWidget {
  final String text;
  const _BulletText(this.text);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 0, top: 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text("• ", style: TextStyle(fontSize: 18, height: 1.4)),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 15, height: 1.4),
            ),
          ),
        ],
      ),
    );
  }
}
