import 'package:flutter/material.dart';
import '../../widgets/HistoryCard/HistoryFilterSection.dart';
import '../../widgets/HistoryCard/HistoryTabSelector.dart';
import '../../widgets/HistoryCard/HistoryCardList.dart';
import '../../widgets/loadings/HistoryCard/HistoryCardListShimmer.dart';

class HistoryCardPage extends StatefulWidget {
  const HistoryCardPage({super.key});

  @override
  State<HistoryCardPage> createState() => _HistoryCardPageState();
}

class _HistoryCardPageState extends State<HistoryCardPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> tabs = ['Today', 'Month', 'Year'];

  String currentSort = 'Latest';
  String selectedFilter = 'All Record';

  DateTime startDate = DateTime.now();
  DateTime endDate = DateTime.now();

  bool isLoading = true;
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: tabs.length, vsync: this);

    startDate = DateTime(DateTime.now().year, DateTime.now().month, 1);
    endDate = DateTime(DateTime.now().year, DateTime.now().month + 1, 0);

    // Simulate loading
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() => isLoading = false);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
  
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: Builder(
          builder: (context) {
            final screenWidth = MediaQuery.of(context).size.width;
            final scale = (screenWidth / 390).clamp(0.9, 1.2);
            final fontSize = 16 * scale;

            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Color(0x33000000),
                    offset: Offset(0, 4),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: SafeArea(
                bottom: false,
                child: Container(
                  height: 70 * scale,
                  padding: EdgeInsets.symmetric(horizontal: 16 * scale),
                  child: Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black87),
                        iconSize: 24 * scale,
                        onPressed: () => Navigator.pop(context),
                      ),
                      Expanded(
                        child: Center(
                          child: Text(
                            "My Card Record",
                            style: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 48 * scale), // spacing to balance
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
      body: Container(
        color: const Color(0xFFF5F5F5),
        child :Column(
          children: [
            const SizedBox(height: 8),
            HistoryTabSelector(controller: _tabController),
            AnimatedBuilder(
              animation: _tabController,
              builder: (context, _) {
                final currentTab = tabs[_tabController.index];
                final showDatePickers = currentTab == 'Month' || currentTab == 'Year';

              return HistoryFilterSection(
                  startDate: startDate,
                  endDate: endDate,
                  onStartDateChanged: (newDate) => setState(() => startDate = newDate),
                  onEndDateChanged: (newDate) => setState(() => endDate = newDate),
                  sortOrder: currentSort,
                  selectedFilter: selectedFilter,
                  onSortOrderChanged: (newOrder) => setState(() => currentSort = newOrder),
                  onFilterChanged: (newFilter) => setState(() => selectedFilter = newFilter),
                  isYearMode: currentTab == 'Year',
                  showDatePickers: showDatePickers,
                );
              },
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Today
                  isLoading
                    ? const HistoryCardListShimmer()
                    : HistoryCardList(
                      mode: 'today',
                      sortOrder: currentSort,
                      selectedFilter: selectedFilter,
                    ),

                  // Month
                  isLoading
                    ? const HistoryCardListShimmer()
                    : HistoryCardList(
                      mode: 'month',
                      sortOrder: currentSort,
                      selectedFilter: selectedFilter,
                      startDate: startDate,
                      endDate: endDate,
                    ),

                  // Year
                  isLoading
                    ? const HistoryCardListShimmer()
                    : HistoryCardList(
                      mode: 'year',
                      sortOrder: currentSort,
                      selectedFilter: selectedFilter,
                      startDate: startDate,
                      endDate: endDate,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
