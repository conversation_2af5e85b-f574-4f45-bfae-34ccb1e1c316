import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';

class TopContainer extends StatelessWidget {
  final VoidCallback onHistoryTap;
  final VoidCallback onPhysicalCardsTap;
  final VoidCallback onSelectCategory;
  final int cardCount;
  final bool showArchived;
  final ValueChanged<bool> onToggleArchived;

  const TopContainer({
    super.key,
    required this.onHistoryTap,
    required this.onPhysicalCardsTap,
    required this.onSelectCategory,
    required this.cardCount,
    required this.showArchived,
    required this.onToggleArchived,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final double screenWidth = size.width;
    final double screenHeight = size.height;

    // Responsive sizes
    final double containerHeight = screenHeight < 700 ? 140 : 160;
    final double bottomBarHeight = containerHeight * 0.38;
    final double buttonHeight = screenHeight < 700 ? 44 : 48;
    final double fontSize = screenWidth < 350 ? 12 : (screenWidth > 600 ? 15 : 13);
    final double iconSize = screenWidth < 350 ? 22 : (screenWidth > 600 ? 30 : 26);

    return Column(
      children: [
        const SizedBox(height: 12),
        SizedBox(
          height: containerHeight,
          child: Stack(
            children: [
              // Yellow Top Container
              Container(
                height: containerHeight,
                margin: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color.fromRGBO(255, 191, 0, 1), Color.fromRGBO(244, 210, 109, 1)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: const [
                    BoxShadow(
                      color: Color.fromARGB(66, 0, 0, 0),
                      blurRadius: 12,
                      offset: Offset(2, 4),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.only(
                    left: 16,
                    right: 0,
                    top: screenHeight < 700 ? 10 : 16,
                    bottom: bottomBarHeight,
                  ),
                  child: Row(
                    children: [
                      // History Button
                      Expanded(
                        child: SizedBox(
                          height: buttonHeight,
                          child: _GameTabButton(
                            icon: Icons.history,
                            label: "History",
                            onTap: onHistoryTap,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(10),
                              bottomRight: Radius.circular(10),
                              topRight: Radius.circular(5),
                              bottomLeft: Radius.circular(5),
                            ),
                            fontSize: fontSize,
                            iconSize: iconSize,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Physical Cards Button
                      Expanded(
                        child: SizedBox(
                          height: buttonHeight,
                          child: _GameTabButton(
                            icon: Icons.credit_card,
                            label: "Physical Card",
                            onTap: onPhysicalCardsTap,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(10),
                              bottomRight: Radius.circular(10),
                              topRight: Radius.circular(5),
                              bottomLeft: Radius.circular(5),
                            ),
                            fontSize: fontSize,
                            iconSize: iconSize,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Select Category Button
                      SizedBox(
                        height: buttonHeight,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.black87,
                            shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(28),
                                bottomLeft: Radius.circular(28),
                              ),
                            ),
                            elevation: 2,
                            padding: EdgeInsets.symmetric(
                              horizontal: screenWidth < 350 ? 10 : (screenWidth > 600 ? 24 : 18),
                            ),
                          ),
                          onPressed: onSelectCategory,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Flexible(
                                child: Text(
                                  "Select Category",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: fontSize,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Icon(Icons.play_arrow_sharp, size: iconSize - 6),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Bottom Grey Container
              Positioned(
                left: 12,
                right: 12,
                bottom: 0,
                child: Container(
                  height: bottomBarHeight,
                  decoration: const BoxDecoration(
                    color: Color(0xFFD9D9D9),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Color.fromARGB(31, 0, 0, 0),
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: screenWidth < 350 ? 8 : (screenWidth > 600 ? 20 : 14),
                    vertical: 8,
                  ),
                  child: Row(
                    children: [
                      Text(
                        'Collect Cards',
                        style: TextStyle(
                          fontSize: fontSize + 1,
                          color: Colors.black87,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Card count
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: const [
                            BoxShadow(
                              color: Color.fromARGB(25, 0, 0, 0),
                              blurRadius: 1,
                              offset: Offset(2, 2),
                            ),
                          ],
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: screenWidth < 350 ? 10 : 14,
                          vertical: 6,
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.diamond, color: Colors.grey, size: iconSize - 8),
                            const SizedBox(width: 4),
                            Text(
                              '$cardCount',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: fontSize,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      Icon(Icons.archive, color: Colors.black54, size: iconSize - 2),
                      const SizedBox(width: 4),
                      Text(
                        'Archived',
                        style: TextStyle(
                          fontSize: fontSize,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(width: 6),
                      // Toggle switch
                      FlutterSwitch(
                        value: showArchived,
                        onToggle: onToggleArchived,
                        width: screenWidth < 350 ? 48 : (screenWidth > 600 ? 68 : 60),
                        height: screenWidth < 350 ? 26 : (screenWidth > 600 ? 34 : 30),
                        toggleSize: screenWidth < 350 ? 20 : (screenWidth > 600 ? 30 : 26),
                        activeColor: Colors.amber,
                        inactiveColor: Colors.grey.shade600,
                        toggleColor: Colors.white,
                        padding: 2,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _GameTabButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final BorderRadius borderRadius;
  final double fontSize;
  final double iconSize;

  const _GameTabButton({
    required this.icon,
    required this.label,
    required this.onTap,
    required this.borderRadius,
    required this.fontSize,
    required this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      borderRadius: borderRadius,
      elevation: 2,
      child: InkWell(
        borderRadius: borderRadius,
        onTap: onTap,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Colors.grey[700], size: iconSize),
              const SizedBox(height: 2),
              Text(
                label,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey[800],
                  fontWeight: FontWeight.normal,
                  fontSize: fontSize,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
