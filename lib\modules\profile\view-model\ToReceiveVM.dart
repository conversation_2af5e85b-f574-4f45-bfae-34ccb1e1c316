import 'package:flutter/material.dart';
import '../../../data/model/OrderModel.dart';

enum TrackingStatus {
  processing,
  shipped,
  outForDelivery,
  readyForPickup,
  delivered,
  pickedUp,
}

extension TrackingStatusExtension on TrackingStatus {
  String get displayName {
    switch (this) {
      case TrackingStatus.processing:
        return 'Processing';
      case TrackingStatus.shipped:
        return 'Shipped';
      case TrackingStatus.outForDelivery:
        return 'Out for delivery';
      case TrackingStatus.readyForPickup:
        return 'Ready for pickup';
      case TrackingStatus.delivered:
        return 'Delivered';
      case TrackingStatus.pickedUp:
        return 'Picked up';
    }
  }

  String get description {
    switch (this) {
      case TrackingStatus.processing:
        return 'Your order is being prepared';
      case TrackingStatus.shipped:
        return 'Your parcel is on the way';
      case TrackingStatus.outForDelivery:
        return 'Out for delivery - arriving soon!';
      case TrackingStatus.readyForPickup:
        return 'Your Order is ready for pickup!';
      case TrackingStatus.delivered:
        return 'Your order has been delivered';
      case TrackingStatus.pickedUp:
        return 'Your order has been picked up';
    }
  }
}

class TrackingEvent {
  final DateTime timestamp;
  final String title;
  final String? description;
  final bool isActive;

  TrackingEvent({
    required this.timestamp,
    required this.title,
    this.description,
    this.isActive = false,
  });
}

class DeliveryInfo {
  final String recipientName;
  final String phoneNumber;
  final String address;
  final String deliveryPartner;
  final String trackingNumber;

  DeliveryInfo({
    required this.recipientName,
    required this.phoneNumber,
    required this.address,
    required this.deliveryPartner,
    required this.trackingNumber,
  });
}

class PickupInfo {
  final String locationName;
  final String phoneNumber;
  final String address;
  final String operationHours;

  PickupInfo({
    required this.locationName,
    required this.phoneNumber,
    required this.address,
    required this.operationHours,
  });
}

class ToReceiveVM extends ChangeNotifier {
  final OrderModel order;
  bool _isLoading = false;
  String? _errorMessage;

  // Mock data - replace with actual API calls
  late final TrackingStatus _currentStatus;
  late final List<TrackingEvent> _trackingEvents;
  late final DeliveryInfo? _deliveryInfo;
  late final PickupInfo? _pickupInfo;
  final String _remark = 'Remark Input by user 123456 qwertyiuop';

  ToReceiveVM({required this.order}) {
    _initializeTrackingData();
  }

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  TrackingStatus get currentStatus => _currentStatus;
  List<TrackingEvent> get trackingEvents => _trackingEvents;
  DeliveryInfo? get deliveryInfo => _deliveryInfo;
  PickupInfo? get pickupInfo => _pickupInfo;
  String get remark => _remark;
  bool get isDelivery => order.deliveryMethod == DeliveryMethod.delivery;
  bool get isPickup => order.deliveryMethod == DeliveryMethod.pickup;

  String get statusBannerMessage => _currentStatus.description;

  void _initializeTrackingData() {
    if (isDelivery) {
      _currentStatus = TrackingStatus.outForDelivery;
      _deliveryInfo = DeliveryInfo(
        recipientName: 'Angela',
        phoneNumber: '************',
        address:
            '1 Blok A Tingkat, Lot 1, Jalan Indah Jaya, 90000 Sandakan, Sabah, Malaysia',
        deliveryPartner: 'J&T Express',
        trackingNumber: 'KDDH12100267391',
      );
      _pickupInfo = null;

      _trackingEvents = [
        TrackingEvent(
          timestamp: DateTime(2025, 3, 1, 2, 31),
          title:
              'Your parcel has arrived at sorting facility: SBH sorting centre',
          isActive: true,
        ),
        TrackingEvent(
          timestamp: DateTime(2025, 2, 26, 0, 0),
          title:
              'Parcel is on the vessel to destination port in East Malaysia. Note that your parcel will be handed over to SPX.',
        ),
        TrackingEvent(
          timestamp: DateTime(2025, 2, 15, 23, 59),
          title: 'Parcel is undergoing custom declaration at Port Klang',
        ),
        TrackingEvent(
          timestamp: DateTime(2025, 2, 15, 13, 17),
          title:
              'Your parcel has departed from sorting facility: DDR sorting centre',
        ),
        TrackingEvent(
          timestamp: DateTime(2025, 2, 15, 3, 40),
          title:
              'Your parcel has arrived from sorting facility: DDR sorting centre',
        ),
        TrackingEvent(
          timestamp: DateTime(2025, 2, 15, 0, 13),
          title: 'Your parcel has departed from sorting facility',
        ),
        TrackingEvent(
          timestamp: DateTime(2025, 2, 14, 21, 4),
          title:
              'Your parcel has arrived at sorting facility: CSL First Mile Hub',
        ),
        TrackingEvent(
          timestamp: DateTime(2025, 2, 14, 19, 43),
          title: 'Your parcel has been picked up by our logistics partner',
        ),
        TrackingEvent(
          timestamp: DateTime(2025, 2, 14, 9, 3),
          title: 'Courier assigned for your order, kindly wait for pick up',
        ),
        TrackingEvent(
          timestamp: DateTime(2025, 2, 14, 8, 18),
          title: 'Seller is preparing to ship your parcel',
        ),
        TrackingEvent(
          timestamp: DateTime(2025, 2, 14, 8, 17),
          title: 'Order is place',
        ),
      ];
    } else {
      _currentStatus = TrackingStatus.readyForPickup;
      _deliveryInfo = null;
      _pickupInfo = PickupInfo(
        locationName: 'Lucky Mall',
        phoneNumber: '011-684 33988',
        address:
            'SUITE 29.04-06, 29TH FLOOR, WISMA CHUANG, 34, Jln Sultan Ismail, Bukit Bintang, 50250 Kuala Lumpur, Wilayah Persekutuan Kuala Lumpur',
        operationHours: '8:30am - 6:30pm',
      );

      _trackingEvents = [];
    }
  }

  Future<void> refreshTracking() async {
    _isLoading = true;
    _clearError();
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // TODO: Replace with actual API call
      // final result = await ApiService.getTrackingInfo(order.id);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to refresh tracking information';
      notifyListeners();
    }
  }

  Future<void> contactSupport() async {
    // TODO: Implement contact support functionality
    debugPrint('Contact support for order: ${order.id}');
  }

  Future<void> joinAgain() async {
    // TODO: Implement join again functionality for prize orders
    debugPrint('Join again for order: ${order.id}');
  }

  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
