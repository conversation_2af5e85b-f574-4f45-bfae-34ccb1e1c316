import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onBack;
  final double height;
  final bool showBack;

  const CustomAppBar({
    super.key,
    required this.title,
    this.onBack,
    this.height = kToolbarHeight,
    this.showBack = true,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375;
    double scaleH(double value) => value * screenHeight / 812;
    double scaleText(double value) => value * screenWidth / 375;

    return Container(
      decoration: BoxDecoration(color: Colors.white),
      child: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        elevation: 0,
        leading: showBack
            ? IconButton(
                icon: SvgPicture.asset(
                  'assets/icons/back.svg',
                  width: scaleW(24),
                  height: scaleW(24),
                ),
                onPressed: onBack ?? () => Navigator.pop(context),
                padding: EdgeInsets.only(left: scaleW(8)),
                constraints: BoxConstraints(
                  minWidth: scaleW(40),
                  minHeight: scaleH(40),
                ),
              )
            : SizedBox(width: scaleW(40)),
        title: Text(
          title,
          style: TextStyle(
            fontSize: scaleText(18),
            color: Colors.black,
            fontWeight: FontWeight.w600,
          ),
        ),
        toolbarHeight: scaleH(height),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(60.0); // For true responsiveness, consider passing context height or using a builder
}
