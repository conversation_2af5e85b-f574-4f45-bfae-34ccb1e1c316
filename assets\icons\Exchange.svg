<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 26.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 35 35" style="enable-background:new 0 0 35 35;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{fill:url(#SVGID_00000029025103418531166890000017513812466679112073_);}
	.st2{fill:url(#SVGID_00000069365446444027705850000017791402558098181277_);}
	.st3{fill:#FFBF00;}
</style>
<g>
	<g>
		<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="16.2346" y1="7.6768" x2="16.8429" y2="26.2912">
			<stop  offset="0" style="stop-color:#FFBF00"/>
			<stop  offset="1" style="stop-color:#FFF7C1"/>
		</linearGradient>
		<path class="st0" d="M15.86,15.54c0,0.27,0.11,0.49,0.32,0.65c0.21,0.16,0.52,0.3,0.92,0.42v-2.18c-0.42,0.07-0.72,0.2-0.93,0.39
			C15.96,15.01,15.86,15.25,15.86,15.54z"/>
		
			<linearGradient id="SVGID_00000055688570967056184350000005445945731448146859_" gradientUnits="userSpaceOnUse" x1="18.3246" y1="7.6085" x2="18.933" y2="26.2229">
			<stop  offset="0" style="stop-color:#FFBF00"/>
			<stop  offset="1" style="stop-color:#FFF7C1"/>
		</linearGradient>
		<path style="fill:url(#SVGID_00000055688570967056184350000005445945731448146859_);" d="M19.03,18.76
			c-0.22-0.17-0.53-0.31-0.94-0.42v2.16c0.43-0.06,0.75-0.19,0.96-0.38c0.21-0.19,0.31-0.42,0.31-0.7
			C19.36,19.15,19.25,18.93,19.03,18.76z"/>
		
			<linearGradient id="SVGID_00000097467543467030868270000009795975663146113722_" gradientUnits="userSpaceOnUse" x1="17.2822" y1="7.6426" x2="17.8905" y2="26.257">
			<stop  offset="0" style="stop-color:#FFBF00"/>
			<stop  offset="1" style="stop-color:#FFF7C1"/>
		</linearGradient>
		<path style="fill:url(#SVGID_00000097467543467030868270000009795975663146113722_);" d="M17.6,10.54c-3.84,0-6.96,3.12-6.96,6.96
			s3.12,6.96,6.96,6.96s6.96-3.12,6.96-6.96S21.44,10.54,17.6,10.54z M20.2,21c-0.48,0.46-1.19,0.74-2.11,0.84v1.37h-1v-1.36
			c-0.6-0.04-1.17-0.15-1.71-0.33c-0.54-0.18-0.97-0.41-1.28-0.68l0.55-1.23c0.3,0.24,0.67,0.45,1.1,0.62
			c0.43,0.17,0.88,0.27,1.34,0.31v-2.44c-0.58-0.14-1.06-0.29-1.45-0.46c-0.39-0.17-0.71-0.41-0.97-0.73
			c-0.26-0.32-0.39-0.75-0.39-1.28c0-0.66,0.24-1.22,0.72-1.69c0.48-0.46,1.18-0.74,2.09-0.83v-1.38h1v1.36
			c0.46,0.02,0.9,0.1,1.34,0.23c0.44,0.13,0.82,0.3,1.14,0.51l-0.5,1.23c-0.62-0.36-1.28-0.58-1.98-0.66v2.47
			c0.59,0.14,1.07,0.29,1.46,0.45c0.39,0.16,0.71,0.41,0.98,0.73c0.26,0.32,0.4,0.75,0.4,1.28C20.93,19.97,20.68,20.54,20.2,21z"/>
	</g>
	<path class="st3" d="M17.6,8.49c-4.97,0-9.01,4.04-9.01,9.01s4.04,9.01,9.01,9.01s9.01-4.04,9.01-9.01S22.57,8.49,17.6,8.49z
		 M17.6,25.28c-4.29,0-7.78-3.49-7.78-7.78s3.49-7.78,7.78-7.78s7.78,3.49,7.78,7.78S21.89,25.28,17.6,25.28z"/>
	<g>
		<path class="st3" d="M30.91,14.29l-0.71,0.12c-1.41-5.78-6.57-9.89-12.6-9.89c-4.99,0-9.46,2.79-11.65,7.27
			c-0.15,0.31-0.02,0.67,0.28,0.82c0.3,0.15,0.67,0.02,0.82-0.28c1.99-4.06,6.03-6.59,10.55-6.59c5.44,0,10.09,3.68,11.39,8.87
			l-0.95,0.17c-0.43,0.08-0.59,0.61-0.27,0.91l1.77,1.65c0.26,0.24,0.67,0.17,0.83-0.15l1.11-2.15
			C31.68,14.66,31.35,14.21,30.91,14.29z"/>
		<path class="st3" d="M29.15,21.97c-0.31-0.14-0.67,0-0.81,0.31c-1.89,4.23-6.1,6.97-10.73,6.97c-5.59,0-10.35-3.89-11.49-9.3
			l0.84-0.14c0.43-0.07,0.59-0.61,0.27-0.91l-1.76-1.66c-0.26-0.24-0.67-0.17-0.83,0.14l-1.12,2.15c-0.2,0.39,0.13,0.84,0.56,0.77
			l0.82-0.14c1.24,6.01,6.51,10.33,12.7,10.33c5.12,0,9.77-3.02,11.85-7.7C29.6,22.47,29.46,22.1,29.15,21.97z"/>
	</g>
</g>
</svg>
