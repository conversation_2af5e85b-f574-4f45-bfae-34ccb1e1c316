import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_svg/flutter_svg.dart';

class BalanceView extends StatefulWidget {
  const BalanceView ({super.key});

  @override
  State<BalanceView> createState() => _BalanceState();
}

class _BalanceState extends State<BalanceView> {
  int selectedTab = 0; // 0 for Transaction History, 1 for Withdraw History
  bool isTouchNGoLinked = false; // Set this based on your app's logic

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5D982), // Yellow background
      appBar: AppBar(
        backgroundColor: const Color(0xFFF5D982),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Lucky Mall Wallet',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Yellow background section with balance card
          Container(
            color: const Color(0xFFF5D982),
            padding: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * 0.04,
              vertical: 16.0,
            ),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.05),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  const Text(
                    'Available Balance',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                  Text(
                    '50.00 Credits',
                    style: TextStyle(
                      fontSize: MediaQuery.of(context).size.width * 0.07,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.015),
                  // Conversion rate display
                  const Text(
                    '1 Credit = RM 1',
                    style: TextStyle(
                      fontSize: 17,
                      color: Colors.black54,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.025),
                  // Balance breakdown
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(child: _buildBalanceItem('Friend Referral', '20.00 Credits')),
                      Expanded(child: _buildBalanceItem('Refunds', '15.00 Credits')),
                      Expanded(child: _buildBalanceItem('Commission', '15.00 Credits')),
                    ],
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.025),
                  // Withdraw button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // Always show the Touch 'n Go dialog first
                        _showTouchNGoLinkDialog();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFD32F2F),
                        padding: EdgeInsets.symmetric(
                          vertical: MediaQuery.of(context).size.height * 0.02,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Withdraw',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // White background section with tabs and transactions
          Expanded(
            child: Container(
              color: Colors.white,
              padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tab headers
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () => setState(() => selectedTab = 0),
                          child: _buildTabHeader('Transaction History', selectedTab == 0),
                        ),
                        SizedBox(width: MediaQuery.of(context).size.width * 0.08),
                        GestureDetector(
                          onTap: () => setState(() => selectedTab = 1),
                          child: _buildTabHeader('Withdraw history', selectedTab == 1),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                  // Transaction list
                  Expanded(
                    child: ListView(
                      children: selectedTab == 0 
                        ? _buildTransactionHistory()
                        : _buildWithdrawHistory(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showTouchNGoLinkDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final screenHeight = MediaQuery.of(context).size.height;
        final isTablet = screenWidth > 600;
        final isMobile = screenWidth <= 600;
        
        // Responsive sizing
        final dialogWidth = isTablet ? 400.0 : screenWidth * 0.9;
        final dialogPadding = isMobile ? 16.0 : 24.0;
        final logoSize = isMobile ? 48.0 : 64.0;
        final titleFontSize = isMobile ? 18.0 : 20.0;
        
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 8,
          insetPadding: EdgeInsets.all(isMobile ? 16.0 : 40.0),
          child: Container(
            width: dialogWidth,
            constraints: BoxConstraints(
              maxHeight: screenHeight * 0.8,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: SingleChildScrollView(
              child: IntrinsicHeight(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header with TNG logo
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(dialogPadding),
                      child: Column(
                        children: [
                          Container(
                            width: logoSize,
                            height: logoSize,
                            child: Image.asset(
                              'assets/images/tngLogo.png',
                              width: logoSize,
                              height: logoSize,
                              fit: BoxFit.contain,
                            ),
                          ),
                          SizedBox(height: isMobile ? 12 : 16),
                          Text(
                            'Touch \'n Go Account Not Linked',
                            style: TextStyle(
                              fontSize: titleFontSize,
                              fontWeight: FontWeight.w700,
                              color: Colors.black87,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    
                    // Content
                    Flexible(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: dialogPadding),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'You haven\'t linked a Touch \'n Go account to proceed with withdrawal.',
                              style: TextStyle(
                                fontSize: isMobile ? 14 : 15,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: isMobile ? 16 : 20),
                            
                            // Info card
                            Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(isMobile ? 16 : 20),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFFF3E0),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.orange.withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        size: isMobile ? 16 : 18,
                                        color: Colors.orange,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          'Link your Touch \'n Go account to enable withdrawals',
                                          style: TextStyle(
                                            fontSize: isMobile ? 13 : 14,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.orange,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: isMobile ? 8 : 10),
                                  Text(
                                    'Once linked, you can withdraw your credits directly to your Touch \'n Go eWallet.',
                                    style: TextStyle(
                                      fontSize: isMobile ? 12 : 13,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFFE65100),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            
                            SizedBox(height: isMobile ? 16 : 20),
                            Flexible(
                              child: Text(
                                'Would you like to link your account now?',
                                style: TextStyle(
                                  fontSize: isMobile ? 14 : 15,
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    SizedBox(height: isMobile ? 24 : 32),
                    
                    // Action buttons
                    Padding(
                      padding: EdgeInsets.all(dialogPadding),
                      child: isMobile ? 
                        // Stack buttons vertically on mobile for better touch targets
                        Column(
                          children: [
                            SizedBox(
                              width: double.infinity,
                              child: TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    side: BorderSide(
                                      color: Colors.grey.shade300,
                                      width: 1,
                                    ),
                                  ),
                                ),
                                child: Text(
                                  'Cancel',
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 12),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  // Navigate to Touch 'n Go linking page
                                  // context.push('/profile/link-touchngo');
                                  _handleLinkAccount();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF295DAA),
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 0,
                                  shadowColor: Colors.transparent,
                                ),
                                child: const Text(
                                  'Open Touch n Go and link',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ) :
                        // Row layout for tablets
                        Row(
                          children: [
                            Expanded(
                              child: TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    side: BorderSide(
                                      color: Colors.grey.shade300,
                                      width: 1,
                                    ),
                                  ),
                                ),
                                child: Text(
                                  'Cancel',
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  // Navigate to Touch 'n Go linking page
                                  // context.push('/profile/link-touchngo');
                                  _handleLinkAccount();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF295DAA),
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 0,
                                  shadowColor: Colors.transparent,
                                ),
                                child: const Text(
                                  'Open Touch n Go and link',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleLinkAccount() {
    // Navigate directly to the withdraw page when "Open Touch n Go and link" is clicked
    context.push('/profile/withdraw');
  }

  Widget _buildBalanceItem(String label, String amount) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          amount,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildTabHeader(String title, bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isActive ? Colors.grey.shade100 : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        boxShadow: isActive ? [
          BoxShadow(
            color: Colors.grey.withOpacity(0.4),
            blurRadius: 4,
            offset: const Offset(1, 1),
            spreadRadius: -1,
          ),
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 2,
            offset: const Offset(-1, -1),
            spreadRadius: -1,
          ),
        ] : null,
      ),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
          color: isActive ? Colors.black : Colors.grey,
        ),
      ),
    );
  }

  List<Widget> _buildTransactionHistory() {
    return [
      _buildTransactionItem(
        'Group Buy',
        '03 July 2025',
        '-25.00 Credits',
        Colors.red,
      ),
      _buildTransactionItem(
        'Lucky Draw',
        '02 July 2025',
        '+10.00 Credits',
        Colors.green,
      ),
      _buildTransactionItem(
        'Lucky Draw',
        '01 July 2025',
        '+10.00 Credits',
        Colors.green,
      ),
      _buildTransactionItem(
        'Redeem Voucher',
        '30 Jun 2025',
        '+10.00 Credits',
        Colors.green,
      ),
      _buildTransactionItem(
        'Redeem Voucher',
        '28 Jun 2025',
        '+10.00 Credits',
        Colors.green,
      ),
      _buildTransactionItem(
        'Redeem Voucher',
        '27 Jun 2025',
        '+10.00 Credits',
        Colors.green,
      ),
    ];
  }

  List<Widget> _buildWithdrawHistory() {
    return [
      _buildTransactionItem(
        'Bank Transfer',
        '25 Jun 2025',
        '-30.00 Credits',
        Colors.red,
      ),
      _buildTransactionItem(
        'PayPal Withdrawal',
        '20 Jun 2025',
        '-50.00 Credits',
        Colors.red,
      ),
      _buildTransactionItem(
        'Bank Transfer',
        '15 Jun 2025',
        '-25.00 Credits',
        Colors.red,
      ),
    ];
  }

  Widget _buildTransactionItem(
    String title,
    String date,
    String amount,
    Color amountColor,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                date,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: amountColor,
            ),
          ),
        ],
      ),
    );
  }
}