import 'package:flutter/material.dart';

class CheckInBanner extends StatelessWidget {
  const CheckInBanner({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Adjust height: 200 for mobile, 300 for tablets and large screens
    final isTablet = screenWidth > 600;
    final bannerHeight = isTablet ? 300.0 : 200.0;

    return Container(
      height: bannerHeight,
      width: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFFFFF3B0), Color(0xFFFFE066)], // light yellow gradient
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Image.asset(
        'assets/images/bannerCheckIn.png',
        fit: BoxFit.contain, // Keeps full image visible
        alignment: Alignment.topCenter,
      ),
    );
  }
}
