enum ParticipationCardState { claimPrize, waiting, notWin, alreadyClaimed }

enum PrizeType { normal, tngVoucher }

class ParticipationRecord {
  final String productName;
  final String imageUrl;
  final String date;
  final String batchNumber;
  final String participationPoints;
  final ParticipationCardState state;
  final PrizeType prizeType;

  const ParticipationRecord({
    required this.productName,
    required this.imageUrl,
    required this.date,
    required this.batchNumber,
    required this.participationPoints,
    required this.state,
    this.prizeType = PrizeType.normal,
  });

  factory ParticipationRecord.fromJson(Map<String, dynamic> json) {
    return ParticipationRecord(
      productName: json['productName']?.toString() ?? '',
      imageUrl: json['imageUrl']?.toString() ?? '',
      date: json['date']?.toString() ?? '',
      batchNumber: json['batchNumber']?.toString() ?? '',
      participationPoints: json['participationPoints']?.toString() ?? '',
      state: _parseState(json['state']?.toString()),
      prizeType: _parsePrizeType(json['prizeType']?.toString()),
    );
  }

  static ParticipationCardState _parseState(String? value) {
    switch (value) {
      case 'claimPrize':
        return ParticipationCardState.claimPrize;
      case 'waiting':
        return ParticipationCardState.waiting;
      case 'notWin':
        return ParticipationCardState.notWin;
      case 'alreadyClaimed':
        return ParticipationCardState.alreadyClaimed;
      default:
        return ParticipationCardState.waiting;
    }
  }

  static PrizeType _parsePrizeType(String? value) {
    switch (value) {
      case 'tngVoucher':
        return PrizeType.tngVoucher;
      case 'normal':
      default:
        return PrizeType.normal;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'productName': productName,
      'imageUrl': imageUrl,
      'date': date,
      'batchNumber': batchNumber,
      'participationPoints': participationPoints,
      'state': state.name,
      'prizeType': prizeType.name,
    };
  }
}
