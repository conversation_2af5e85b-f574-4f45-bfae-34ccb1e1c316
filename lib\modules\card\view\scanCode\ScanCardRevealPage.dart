import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../model/reveal/RevealCard.dart';

class ScanCardRevealPage extends StatefulWidget {
  final List<RevealCard> cards;

  const ScanCardRevealPage({super.key, required this.cards});

  @override
  State<ScanCardRevealPage> createState() => _ScanCardRevealPageState();
}

class _ScanCardRevealPageState extends State<ScanCardRevealPage> {
  final ValueNotifier<double> _tiltNotifier = ValueNotifier(0);
  final PageController _pageController = PageController(viewportFraction: 0.75);
  final ValueNotifier<bool> _showLeftArrow = ValueNotifier(false);
  final ValueNotifier<bool> _showRightArrow = ValueNotifier(true);

  int _currentPage = 0;

  @override
  void initState() {
    super.initState();

    _pageController.addListener(() {
      final page = _pageController.page ?? 0;
      final maxPage = (widget.cards.length - 1);

      // Use rounded page index
      final roundedPage = page.round();
      if (_currentPage != roundedPage) {
        setState(() {
          _currentPage = roundedPage;
        });
      }

      // Show left arrow only if not first page
      _showLeftArrow.value = roundedPage > 0;

      // Show right arrow only if not last page
      _showRightArrow.value = roundedPage < maxPage;
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _tiltNotifier.dispose();
    _showLeftArrow.dispose();
    _showRightArrow.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isGrid = widget.cards.length > 5;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(56),
        child: AppBar(
          automaticallyImplyLeading: false, // removes back button
          centerTitle: true,
          title: const Text(
            'Reveal Cards',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              shadows: [
                Shadow(color: Colors.amberAccent, blurRadius: 8),
              ],
            ),
          ),
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFFFFD54F), Color(0xFFFFB300)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          elevation: 6,
          shadowColor: Colors.amber.shade700,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: isGrid
            ? _buildResponsiveGrid(context, size)
            : _buildPageViewReveal(size),
      ),
      bottomNavigationBar: _buildBottomBar(context),
    );
  }

  Widget _buildPageViewReveal(Size size) {
    final cardHeight = size.height * 0.55;
    final cardWidth = cardHeight * 0.714;

    return Center(
      child: SizedBox(
        height: cardHeight,
        child: Stack(
          alignment: Alignment.center,
          children: [
            PageView.builder(
              controller: _pageController,
              itemCount: widget.cards.length,
              itemBuilder: (context, index) {
                final scale = index == _currentPage ? 1.0 : 0.85;
                return AnimatedScale(
                  scale: scale,
                  duration: const Duration(milliseconds: 300),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: SizedBox(
                      width: cardWidth,
                      height: cardHeight,
                      child: _buildCardItem(widget.cards[index]),
                    ),
                  ),
                );
              },
            ),

            // Left arrow
            Positioned(
              left: 8,
              child: ValueListenableBuilder<bool>(
                valueListenable: _showLeftArrow,
                builder: (context, visible, child) {
                  return AnimatedOpacity(
                    opacity: visible ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 500),
                    child: child,
                  );
                },
                child: _animatedArrow(Icons.arrow_back_ios),
              ),
            ),

            // Right arrow
            Positioned(
              right: 8,
              child: ValueListenableBuilder<bool>(
                valueListenable: _showRightArrow,
                builder: (context, visible, child) {
                  return AnimatedOpacity(
                    opacity: visible ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 500),
                    child: child,
                  );
                },
                child: _animatedArrow(Icons.arrow_forward_ios),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _animatedArrow(IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color.fromARGB(148, 255, 193, 7),
        shape: BoxShape.circle,
      ),
      child: TweenAnimationBuilder<double>(
        tween: Tween(begin: 1.0, end: 1.2),
        duration: const Duration(seconds: 1),
        curve: Curves.easeInOut,
        builder: (context, scale, child) {
          return Transform.scale(scale: scale, child: child);
        },
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildBottomBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.amber.shade700, Colors.amber.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.amber.shade200.withOpacity(0.6),
            blurRadius: 12,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildInteractiveButton(
              context,
              icon: Icons.home,
              label: 'Card Page',
              onPressed: () => context.go('/card'),
              bgColor: Colors.amber.shade200, // light yellow
              fgColor: Colors.black87,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildInteractiveButton(
              context,
              icon: Icons.qr_code_scanner,
              label: 'Scan Code',
              onPressed: () => context.go('/card/scan-code'),
              bgColor: Colors.white, // white bg
              fgColor: Colors.amber.shade700, // amber text for contrast
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInteractiveButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color bgColor,
    required Color fgColor,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20, color: fgColor),
      label: Text(label, style: TextStyle(fontWeight: FontWeight.bold, color: fgColor)),
      style: ElevatedButton.styleFrom(
        backgroundColor: bgColor,
        foregroundColor: fgColor,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        shadowColor: Colors.amber.shade900,
        elevation: 8,
        animationDuration: const Duration(milliseconds: 200),
      ),
    );
  }


  Widget _buildResponsiveGrid(BuildContext context, Size size) {
    int crossAxisCount = size.width > 900
        ? 4
        : size.width > 600
            ? 3
            : 2;

    return GridView.builder(
      padding: const EdgeInsets.only(top: 12),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: 0.714,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: widget.cards.length,
      itemBuilder: (context, index) {
        return AnimatedOpacity(
          opacity: 1.0,
          duration: Duration(milliseconds: 400 + (index * 100)),
          child: _buildCardItem(widget.cards[index]),
        );
      },
    );
  }

  Widget _buildCardItem(RevealCard card) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 150),
      tween: Tween(begin: 0, end: 0),
      builder: (context, tilt, child) {
        return GestureDetector(
          onTapDown: (_) {
            _tiltNotifier.value = 0.05;
          },
          onTapUp: (_) {
            _tiltNotifier.value = 0;
          },
          onTapCancel: () {
            _tiltNotifier.value = 0;
          },
          child: MouseRegion(
            onEnter: (_) => _tiltNotifier.value = -0.05,
            onExit: (_) => _tiltNotifier.value = 0,
            child: AnimatedBuilder(
              animation: _tiltNotifier,
              builder: (context, _) {
                return Transform(
                  alignment: FractionalOffset.center,
                  transform: Matrix4.identity()
                    ..setEntry(3, 2, 0.001)
                    ..rotateY(_tiltNotifier.value),
                  child: AspectRatio(
                    aspectRatio: 63 / 88,
                    child: Card(
                      color: Colors.white,
                      elevation: 8,
                      shadowColor: Colors.amberAccent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Image.asset(
                          card.imagePath,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
