import 'package:flutter/material.dart';

class VoucherFAQPage extends StatelessWidget {
  const VoucherFAQPage({super.key});

  final List<Map<String, String>> faqs = const [
    {
      'question': 'What is a voucher?',
      'answer': 'A voucher is a code that gives you discounts or rewards when making purchases in the app.'
    },
    {
      'question': 'How do I apply a voucher?',
      'answer': 'You can apply a voucher at checkout by entering the code in the voucher section before payment.'
    },
    {
      'question': 'Can I use multiple vouchers?',
      'answer': 'No, only one voucher can be applied per transaction unless otherwise stated.'
    },
    {
      'question': 'Why is my voucher not working?',
      'answer': 'Make sure the voucher is valid, not expired, and applicable to the selected items.'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        iconTheme: const IconThemeData(color: Colors.black),
        centerTitle: true,
        title: const Text(
          'Voucher FAQ',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: faqs.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final faq = faqs[index];
          return Card(
            elevation: 1,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ExpansionTile(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              title: Row(
                children: [
                  const Icon(Icons.card_giftcard, color: Colors.black),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      faq['question']!,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
              children: [
                Text(
                  faq['answer']!,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
