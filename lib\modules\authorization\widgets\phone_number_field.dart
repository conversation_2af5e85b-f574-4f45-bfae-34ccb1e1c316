import 'package:flutter/material.dart';
import 'package:intl_phone_field/intl_phone_field.dart';

class PhoneNumberField extends StatelessWidget {
  final TextEditingController? controller;
  final String? hintText;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final bool isRequired;
  final Function(String)? onCountryCodeChanged;

  const PhoneNumberField({
    super.key,
    this.controller,
    this.hintText,
    this.onChanged,
    this.validator,
    this.isRequired = true,
    this.onCountryCodeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Phone Number Label
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                Text(
                  'Phone Number',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 13,
                  ),
                ),
                if (isRequired)
                  const Text(
                    ' *',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                      fontSize: 13,
                    ),
                  ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),
        // Phone Number Field
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Container(
            width: double.infinity, // Ensure full width
            height: 48, // Set a fixed height for consistency
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.black),
            ),
            child: IntlPhoneField(
              controller: controller,
              initialCountryCode: 'MY',
              showDropdownIcon: true,
              disableLengthCheck: true,
              style: const TextStyle(fontSize: 13),
              keyboardType: TextInputType.phone,
              onChanged: (phone) {
                if (onChanged != null) {
                  final String national = phone.number
                      .trim()
                      .replaceAll(' ', '')
                      .replaceAll('-', '');
                  onChanged!(national);
                }
              },
              onCountryChanged: (country) {
                if (onCountryCodeChanged != null) {
                  onCountryCodeChanged!(country.dialCode);
                }
              },
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: hintText ?? 'Please enter your phone number',
                hintStyle: const TextStyle(color: Colors.grey),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 12,
                ),
                counterText: '',
              ),
            ),
          ),
        ),
      ],
    );
  }
}
