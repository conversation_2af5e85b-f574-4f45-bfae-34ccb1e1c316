import 'package:flutter/material.dart';

class DisclaimerFAQPage extends StatelessWidget {
  const DisclaimerFAQPage({super.key});

  final List<Map<String, String>> faqs = const [
    {
      'question': 'What is a disclaimer?',
      'answer': 'A disclaimer clarifies responsibilities and limits liability for the content or services provided.'
    },
    {
      'question': 'Is the information in the app guaranteed to be accurate?',
      'answer': 'We strive for accuracy, but we cannot guarantee all information is always up-to-date or error-free.'
    },
    {
      'question': 'Can I hold the company liable for any loss?',
      'answer': 'The company is not liable for any indirect or consequential loss arising from the use of the app.'
    },
    {
      'question': 'Where can I read the full disclaimer?',
      'answer': 'You can read the full disclaimer in the Terms & Conditions section within the app settings.'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        iconTheme: const IconThemeData(color: Colors.black),
        centerTitle: true,
        title: const Text(
          'Disclaimer FAQ',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: faqs.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final faq = faqs[index];
          return Card(
            elevation: 1,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ExpansionTile(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              title: Row(
                children: [
                  const Icon(Icons.info_outline, color: Colors.black),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      faq['question']!,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
              children: [
                Text(
                  faq['answer']!,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
