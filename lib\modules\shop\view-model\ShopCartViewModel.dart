import 'package:flutter/material.dart';
import '../../../data/model/ShopCartModel.dart';
import '../../../data/service/ShopCartService.dart';

class ShopCartViewModel extends ChangeNotifier {
  List<ShopCartItem> items = [];
  bool isLoading = true;

  Future<void> loadCart() async {
    isLoading = true;
    notifyListeners();
    items = await ShopCartService.fetchCartItems();
    isLoading = false;
    notifyListeners();
  }

  void toggleSelect(int index, bool? value) {
    items[index].selected = value ?? false;
    notifyListeners();
  }

  void changeQuantity(int index, int delta) {
    final item = items[index];
    if (item.quantity + delta > 0) {
      item.quantity += delta;
      notifyListeners();
    }
  }

  void selectAll(bool? value) {
    for (var item in items) {
      item.selected = value ?? false;
    }
    notifyListeners();
  }

  double get total => items
      .where((item) => item.selected)
      .fold(0.0, (sum, item) => sum + item.price * item.quantity);

  int get selectedCount => items.where((item) => item.selected).length;
}