// RevealFlowWrapper.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../model/purchaseCard/PurchaseCardOrder.dart';
import '../../view-model/reveal/RevealCardViewModel.dart';
import 'RevealAnimationPage.dart';
import 'RevealCardGridPage.dart';

class RevealFlowWrapper extends StatelessWidget {
  final PurchaseCardOrder order;

  const RevealFlowWrapper({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => RevealCardViewModel(),
      child: _RevealFlowNavigator(order: order),
    );
  }
}

class _RevealFlowNavigator extends StatelessWidget {
  final PurchaseCardOrder order;

  const _RevealFlowNavigator({required this.order});

  @override
  Widget build(BuildContext context) {
    return Navigator(
      onGenerateRoute: (settings) {
        if (settings.name == '/reveal-view') {
          return MaterialPageRoute(
            builder: (_) => const RevealCardGridPage(),
          );
        } else {
          return MaterialPageRoute(
            builder: (_) => RevealAnimationPage(order: order),
          );
        }
      },
    );
  }
}
