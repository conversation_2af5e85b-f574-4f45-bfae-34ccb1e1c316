import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:async';
import '../model/PrizeRevealProduct.dart';

class PrizeRevealCards extends StatefulWidget {
  final List<PrizeRevealProduct> products;
  final VoidCallback? onProductTap;
  final Function(PrizeRevealProduct)? onJoinNextBatch;

  const PrizeRevealCards({
    super.key,
    required this.products,
    this.onProductTap,
    this.onJoinNextBatch,
  });

  @override
  State<PrizeRevealCards> createState() => _PrizeRevealCardsState();
}

class _PrizeRevealCardsState extends State<PrizeRevealCards> {
  late List<Timer> _timers;
  late List<int> _remainingSeconds;

  @override
  void initState() {
    super.initState();
    _initializeTimers();
  }

  void _initializeTimers() {
    _remainingSeconds = widget.products.map((p) {
      // Parse the prizeDrawTime string to get countdown seconds
      // For now, we'll use a default value if parsing fails
      try {
        // Assuming prizeDrawTime is in a format that can be parsed
        // You may need to adjust this based on the actual format from the API
        return 3600; // Default to 1 hour if parsing fails
      } catch (e) {
        return 3600;
      }
    }).toList();
    _startTimers();
  }

  void _startTimers() {
    _timers = List.generate(widget.products.length, (index) {
      return Timer.periodic(const Duration(seconds: 1), (timer) {
        if (mounted) {
          setState(() {
            if (_remainingSeconds[index] > 0) {
              _remainingSeconds[index]--;
            } else {
              timer.cancel();
            }
          });
        }
      });
    });
  }

  String _formatTime(int totalSeconds) {
    int hours = totalSeconds ~/ 3600;
    int minutes = (totalSeconds % 3600) ~/ 60;
    int seconds = totalSeconds % 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    for (var timer in _timers) {
      timer.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Responsive sizing
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scale(double size) => size * screenWidth / 375;
    double vScale(double size) => size * screenHeight / 812;
    final cardWidth = scale(140);
    final cardHeight = vScale(220);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: scale(10), vertical: vScale(8)),
      child: Stack(
        children: [
          // Main yellow container
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: const Color(0xFFFFBF00), // Bright yellow background
              border: Border.all(
                color: const Color(0xFFFFD556),
                width: scale(5),
              ),
              borderRadius: BorderRadius.circular(scale(16)),
            ),
            child: Padding(
              padding: EdgeInsets.all(scale(16)),
              child: Column(
                children: [
                  SizedBox(height: vScale(20)), // Space for overlapping banner
                  // Light beige inner container
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: const Color.fromARGB(
                        255,
                        255,
                        255,
                        255,
                      ), // Light beige background
                      borderRadius: BorderRadius.circular(scale(12)),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(scale(16)),
                      child: Column(
                        children: [
                          // Horizontally scrollable product cards
                          SizedBox(
                            height:
                                cardHeight, // Responsive height for the scrollable area
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: widget.products.length,
                              itemBuilder: (context, index) {
                                return Container(
                                  width:
                                      cardWidth, // Responsive width for each card
                                  margin: EdgeInsets.only(right: scale(12)),
                                  child: _buildProductCard(
                                    widget.products[index],
                                    index,
                                    scale,
                                    vScale,
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // "Prize Reveal" banner (overlapping)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: scale(20),
                  vertical: vScale(6),
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFD700), // Dark yellow
                  borderRadius: BorderRadius.circular(scale(20)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      offset: Offset(0, vScale(2)),
                      blurRadius: scale(4),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Text(
                  'Prize Reveal',
                  style: TextStyle(
                    fontSize: scale(16),
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(
    PrizeRevealProduct product,
    int index,
    double Function(double) scale,
    double Function(double) vScale,
  ) {
    return GestureDetector(
      onTap: widget.onProductTap,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFFFF0C2), // Light beige background
          borderRadius: BorderRadius.circular(scale(12)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image area
            Container(
              height: vScale(100),
              width: double.infinity,
              margin: EdgeInsets.symmetric(
                horizontal: scale(8),
                vertical: vScale(8),
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(scale(8)),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(scale(8)),
                child: Stack(
                  children: [
                    // Product image
                    CachedNetworkImage(
                      imageUrl: product.imageUrl,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      placeholder: (context, url) => Container(
                        color: Colors.grey[100],
                        child: Center(
                          child: SizedBox(
                            width: scale(24),
                            height: scale(24),
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Color(0xFFFFBF00),
                            ),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey[200],
                        child: Icon(
                          Icons.image_not_supported,
                          color: Colors.grey[400],
                          size: scale(30),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Product details
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: scale(8)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product name
                    Text(
                      product.productName,
                      style: TextStyle(
                        fontSize: scale(12),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const Spacer(),

                    // Countdown timer
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(vertical: vScale(4)),
                      child: Center(
                        child: Text(
                          _formatTime(_remainingSeconds[index]),
                          style: TextStyle(
                            fontSize: scale(12),
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ),

                    // Join Next Batch button - only show if nextBatch > 0
                    if (product.nextBatch != null && product.nextBatch! > 0)
                      Container(
                        width: double.infinity,
                        height: vScale(24),
                        margin: EdgeInsets.symmetric(horizontal: scale(16)),
                        child: ElevatedButton(
                          onPressed: widget.onJoinNextBatch != null
                              ? () => widget.onJoinNextBatch!(product)
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFFFFBF00),
                            foregroundColor: Colors.black87,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(scale(6)),
                            ),
                            padding: EdgeInsets.zero,
                          ),
                          child: Text(
                            'Join Next Batch',
                            style: TextStyle(
                              fontSize: scale(10),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),

                    SizedBox(height: vScale(4)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
