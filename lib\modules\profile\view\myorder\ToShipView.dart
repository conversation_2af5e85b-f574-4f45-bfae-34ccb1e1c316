import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../view-model/ToShipVM.dart';
import '../../../../data/model/OrderModel.dart';
import '../../widgets/OrderStatusBadge.dart';

class ToShipView extends StatefulWidget {
  final OrderModel order;

  const ToShipView({super.key, required this.order});

  @override
  State<ToShipView> createState() => _ToShipViewState();
}

class _ToShipViewState extends State<ToShipView> {
  late ToShipVM _toShipVM;

  @override
  void initState() {
    super.initState();
    _toShipVM = ToShipVM(order: widget.order);
  }

  @override
  void dispose() {
    _toShipVM.dispose();
    super.dispose();
  }

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _toShipVM,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black87),
            onPressed: () => Navigator.of(context).pop(),
          ),
          centerTitle: true,
          title: Text(
            'To Ship',
            style: TextStyle(
              fontSize: getResponsiveFontSize(18, context),
              fontWeight: FontWeight.w700,
              color: Colors.black,
            ),
          ),
        ),
        body: Consumer<ToShipVM>(
          builder: (context, vm, _) {
            return RefreshIndicator(
              color: const Color(0xFFFCD255),
              onRefresh: vm.refreshOrderStatus,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Status Banner
                    _buildStatusBanner(context, vm),

                    SizedBox(height: getResponsivePadding(16, context)),

                    // Order Details
                    _buildOrderDetails(context, vm),

                    SizedBox(height: getResponsivePadding(16, context)),

                    // Address/Location Section
                    if (vm.isDelivery) ...[
                      _buildDeliveryAddress(context, vm),
                      SizedBox(height: getResponsivePadding(16, context)),
                    ] else ...[
                      _buildPickupLocation(context, vm),
                      SizedBox(height: getResponsivePadding(16, context)),
                    ],

                    // Product Section
                    _buildProductSection(context, vm),

                    SizedBox(height: getResponsivePadding(16, context)),

                    // Pricing/Points Section
                    if (vm.isDelivery) ...[
                      _buildPricingSection(context, vm),
                      SizedBox(height: getResponsivePadding(16, context)),
                      _buildRemarkSection(context, vm),
                      SizedBox(height: getResponsivePadding(16, context)),
                      _buildPaymentSection(context, vm),
                      SizedBox(height: getResponsivePadding(16, context)),
                      _buildPackagingTimeline(context, vm),
                    ] else ...[
                      _buildPointsSection(context, vm),
                      SizedBox(height: getResponsivePadding(32, context)),
                      _buildActionButtons(context, vm),
                    ],

                    SizedBox(height: getResponsivePadding(32, context)),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatusBanner(BuildContext context, ToShipVM vm) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(getResponsivePadding(12, context)),
      color: const Color(0xFFFCD255),
      child: Text(
        vm.statusBannerMessage,
        style: TextStyle(
          fontSize: getResponsiveFontSize(14, context),
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildOrderDetails(BuildContext context, ToShipVM vm) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        children: [
          _buildDetailRow('Order Number', widget.order.id, hasIcon: true),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildDetailRow(
            'Order Time',
            DateFormat('yyyy-MM-dd HH:mm').format(widget.order.createdAt),
          ),
          if (vm.estimatedShipDate != null) ...[
            SizedBox(height: getResponsivePadding(8, context)),
            _buildDetailRow(
              'Estimated Ship Date',
              DateFormat('yyyy-MM-dd').format(vm.estimatedShipDate!),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool hasIcon = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            color: Colors.black87,
          ),
        ),
        Row(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            if (hasIcon) ...[
              SizedBox(width: getResponsivePadding(4, context)),
              GestureDetector(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: value));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Order number copied!')),
                  );
                },
                child: Icon(
                  Icons.copy,
                  size: getResponsiveFontSize(16, context),
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildDeliveryAddress(BuildContext context, ToShipVM vm) {
    final deliveryInfo = vm.deliveryInfo!;
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: getResponsiveFontSize(20, context),
                color: Colors.black87,
              ),
              SizedBox(width: getResponsivePadding(8, context)),
              Text(
                '${deliveryInfo.recipientName} ${deliveryInfo.phoneNumber}',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          SizedBox(height: getResponsivePadding(8, context)),
          Padding(
            padding: EdgeInsets.only(left: getResponsivePadding(28, context)),
            child: Text(
              deliveryInfo.address,
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: Colors.black87,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPickupLocation(BuildContext context, ToShipVM vm) {
    final pickupInfo = vm.pickupInfo!;
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: getResponsiveFontSize(20, context),
                color: Colors.black87,
              ),
              SizedBox(width: getResponsivePadding(8, context)),
              Text(
                '${pickupInfo.locationName} ${pickupInfo.phoneNumber}',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              Icon(
                Icons.copy,
                size: getResponsiveFontSize(16, context),
                color: Colors.grey.shade600,
              ),
            ],
          ),
          SizedBox(height: getResponsivePadding(8, context)),
          Padding(
            padding: EdgeInsets.only(left: getResponsivePadding(28, context)),
            child: Text(
              pickupInfo.address,
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: Colors.black87,
                height: 1.4,
              ),
            ),
          ),
          SizedBox(height: getResponsivePadding(12, context)),
          Text(
            'Operation Time: ${pickupInfo.operationHours}',
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: FontWeight.w600,
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductSection(BuildContext context, ToShipVM vm) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order type badge
          if (vm.isPickup) ...[
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: getResponsivePadding(8, context),
                vertical: getResponsivePadding(4, context),
              ),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.card_giftcard,
                    size: getResponsiveFontSize(16, context),
                    color: Colors.black54,
                  ),
                  SizedBox(width: getResponsivePadding(4, context)),
                  Text(
                    widget.order.type.typeDisplayName,
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(12, context),
                      fontWeight: FontWeight.w600,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: getResponsivePadding(12, context)),
          ],

          // Product details
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: getResponsivePadding(60, context),
                  height: getResponsivePadding(60, context),
                  color: Colors.grey.shade100,
                  child: widget.order.productImage.startsWith('http')
                      ? Image.network(
                          widget.order.productImage,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey.shade200,
                              child: Icon(
                                Icons.image,
                                color: Colors.grey.shade400,
                              ),
                            );
                          },
                        )
                      : Image.asset(
                          widget.order.productImage,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey.shade200,
                              child: Icon(
                                Icons.image,
                                color: Colors.grey.shade400,
                              ),
                            );
                          },
                        ),
                ),
              ),

              SizedBox(width: getResponsivePadding(12, context)),

              // Product info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.order.productName,
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: getResponsivePadding(4, context)),
                    if (vm.isDelivery) ...[
                      Text(
                        'RM${widget.order.total.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ] else ...[
                      Text(
                        'RM${widget.order.total.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Quantity
              Text(
                'x${widget.order.quantity}',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPricingSection(BuildContext context, ToShipVM vm) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        children: [
          _buildPriceRow('Product Subtotal Price', widget.order.subtotal),
          _buildPriceRow('Delivery Fees', 10.00),
          _buildPriceRow('New User Discount', -37.25, isDiscount: true),
          _buildPriceRow('Voucher Used x1', -5.00, isDiscount: true),
          _buildPriceRow('Card Discount Applied', -1.00, isDiscount: true),
          SizedBox(height: getResponsivePadding(8, context)),
          Divider(color: Colors.grey.shade300),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildPriceRow('Total Payment', widget.order.total, isBold: true),
        ],
      ),
    );
  }

  Widget _buildPriceRow(
    String label,
    double amount, {
    bool isDiscount = false,
    bool isBold = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: getResponsivePadding(4, context)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: isBold ? FontWeight.w600 : FontWeight.normal,
              color: Colors.black87,
            ),
          ),
          Text(
            '${isDiscount ? '' : ''}RM${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: isBold ? FontWeight.w600 : FontWeight.normal,
              color: isDiscount ? Colors.red : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointsSection(BuildContext context, ToShipVM vm) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        children: [
          _buildPointsRow('Product Total Points', 30),
          _buildPointsRow('Points Used To Join', 2),
        ],
      ),
    );
  }

  Widget _buildPointsRow(String label, int points) {
    return Padding(
      padding: EdgeInsets.only(bottom: getResponsivePadding(8, context)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.black87,
            ),
          ),
          Text(
            points.toString(),
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRemarkSection(BuildContext context, ToShipVM vm) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Remark',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: getResponsivePadding(8, context)),
          Text(
            vm.remark,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.black87,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSection(BuildContext context, ToShipVM vm) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        children: [
          _buildPaymentRow('Payment Type', 'Billplz Pay - FPX'),
          _buildPaymentRow('Payment Time', '2025-02-20 12:00'),
          _buildPaymentRow('Order Confirmed', '2025-02-20 12:05'),
        ],
      ),
    );
  }

  Widget _buildPaymentRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: getResponsivePadding(8, context)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.black87,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackagingTimeline(BuildContext context, ToShipVM vm) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Progress',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: getResponsivePadding(16, context)),
          ...vm.packagingEvents.asMap().entries.map((entry) {
            final index = entry.key;
            final event = entry.value;
            return _buildPackagingEvent(
              context,
              event,
              index: index,
              totalEvents: vm.packagingEvents.length,
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildPackagingEvent(
    BuildContext context,
    PackagingEvent event, {
    required int index,
    required int totalEvents,
  }) {
    String timeLabel;
    if (index == 0 && event.isActive) {
      // Show "Today HH:mm" for the active event
      timeLabel = 'Today\n${DateFormat('HH:mm').format(event.timestamp)}';
    } else {
      // Show "dd MMM\nHH:mm" for other events
      timeLabel = DateFormat('dd MMM\nHH:mm').format(event.timestamp);
    }

    return Padding(
      padding: EdgeInsets.only(bottom: getResponsivePadding(16, context)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Time stamp with bullet point
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date/time
              SizedBox(
                width: getResponsivePadding(50, context),
                child: Text(
                  timeLabel,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(12, context),
                    color: index == 0 && event.isActive
                        ? const Color(0xFFF59E0B)
                        : Colors.grey.shade600,
                    fontWeight: index == 0 && event.isActive
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                  textAlign: TextAlign.left,
                ),
              ),

              SizedBox(width: getResponsivePadding(12, context)),

              // Bullet point with connecting line
              Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                      top: getResponsivePadding(2, context),
                    ),
                    width: getResponsivePadding(6, context),
                    height: getResponsivePadding(6, context),
                    decoration: BoxDecoration(
                      color: index == 0 && event.isActive
                          ? const Color(0xFFF59E0B)
                          : Colors.grey.shade400,
                      shape: BoxShape.circle,
                    ),
                  ),
                  // Add connecting line if not the last event
                  if (index < totalEvents - 1)
                    Container(
                      width: 1,
                      height: getResponsivePadding(40, context),
                      color: Colors.grey.shade300,
                    ),
                ],
              ),
            ],
          ),

          SizedBox(width: getResponsivePadding(12, context)),

          // Event description
          Expanded(
            child: Text(
              event.title,
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: index == 0 && event.isActive
                    ? const Color(0xFFF59E0B)
                    : Colors.black87,
                fontWeight: index == 0 && event.isActive
                    ? FontWeight.w500
                    : FontWeight.normal,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, ToShipVM vm) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: vm.contactSupport,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFFF59E0B),
                side: const BorderSide(color: Color(0xFFF59E0B), width: 1),
                elevation: 0,
                padding: EdgeInsets.symmetric(
                  vertical: getResponsivePadding(12, context),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Help Support',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(width: getResponsivePadding(12, context)),
          Expanded(
            child: ElevatedButton(
              onPressed: vm.joinAgain,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFCD255),
                foregroundColor: Colors.black87,
                elevation: 0,
                padding: EdgeInsets.symmetric(
                  vertical: getResponsivePadding(12, context),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Join Again',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
