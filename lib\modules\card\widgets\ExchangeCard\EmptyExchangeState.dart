import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class EmptyExchangeState extends StatelessWidget {
  const EmptyExchangeState({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final iconSize = screenWidth * 0.3; // ~20% of width
    final fontSize = screenWidth < 360 ? 14.0 : 16.0;
    final spacing = screenWidth < 360 ? 12.0 : 16.0;

    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.white, // force white background
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'assets/icons/other/NoRecord.svg',
                width: iconSize,
                height: iconSize,
                colorFilter: ColorFilter.mode(
                  Colors.grey.shade500,
                  BlendMode.srcIn,
                ),
              ),
              SizedBox(height: spacing),
              Text(
                "No Active Rewards",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: fontSize,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
