import 'package:flutter/material.dart';
import '../view-model/BlindboxSearchVM.dart';

class SearchFilterTabs extends StatelessWidget {
  final SearchFilter selectedFilter;
  final Function(SearchFilter) onFilterSelected;

  const SearchFilterTabs({
    super.key,
    required this.selectedFilter,
    required this.onFilterSelected,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  @override
  Widget build(BuildContext context) {
    final filters = SearchFilter.values;

    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Row(
        children: filters.map((filter) {
          final isSelected = filter == selectedFilter;
          final filterName = _getFilterDisplayName(filter);

          return Expanded(
            child: GestureDetector(
              onTap: () => onFilterSelected(filter),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: getResponsivePadding(8, context),
                  vertical: getResponsivePadding(12, context),
                ),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: isSelected
                          ? const Color(0xFFFCD255)
                          : Colors.transparent,
                      width: 3,
                    ),
                  ),
                ),
                child: Text(
                  filterName,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected
                        ? const Color(0xFFFCD255)
                        : Colors.grey[600],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  String _getFilterDisplayName(SearchFilter filter) {
    switch (filter) {
      case SearchFilter.relevance:
        return 'Relevance';
      case SearchFilter.latest:
        return 'Latest';
      case SearchFilter.mostSold:
        return 'Most Sold';
      case SearchFilter.price:
        return 'Price';
    }
  }
}
