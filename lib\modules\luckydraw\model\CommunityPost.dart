class CommunityPost {
  final String id;
  final String userName;
  final String avatarUrl;
  final String postImage;
  final String caption;
  final String timeAgo;
  final int likeCount;
  final bool isLiked;
  final String createdAt;
  final String? productName;
  final String? prizeType;

  const CommunityPost({
    required this.id,
    required this.userName,
    required this.avatarUrl,
    required this.postImage,
    required this.caption,
    required this.timeAgo,
    required this.likeCount,
    required this.isLiked,
    required this.createdAt,
    this.productName,
    this.prizeType,
  });

  factory CommunityPost.fromJson(Map<String, dynamic> json) {
    return CommunityPost(
      id: json['id']?.toString() ?? '',
      userName: json['userName']?.toString() ?? '',
      avatarUrl: json['avatarUrl']?.toString() ?? '',
      postImage: json['postImage']?.toString() ?? '',
      caption: json['caption']?.toString() ?? '',
      timeAgo: json['timeAgo']?.toString() ?? '',
      likeCount: (json['likeCount'] as num?)?.toInt() ?? 0,
      isLiked: json['isLiked'] as bool? ?? false,
      createdAt: json['createdAt']?.toString() ?? '',
      productName: json['productName']?.toString(),
      prizeType: json['prizeType']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userName': userName,
      'avatarUrl': avatarUrl,
      'postImage': postImage,
      'caption': caption,
      'timeAgo': timeAgo,
      'likeCount': likeCount,
      'isLiked': isLiked,
      'createdAt': createdAt,
      'productName': productName,
      'prizeType': prizeType,
    };
  }

  CommunityPost copyWith({
    String? id,
    String? userName,
    String? avatarUrl,
    String? postImage,
    String? caption,
    String? timeAgo,
    int? likeCount,
    bool? isLiked,
    String? createdAt,
    String? productName,
    String? prizeType,
  }) {
    return CommunityPost(
      id: id ?? this.id,
      userName: userName ?? this.userName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      postImage: postImage ?? this.postImage,
      caption: caption ?? this.caption,
      timeAgo: timeAgo ?? this.timeAgo,
      likeCount: likeCount ?? this.likeCount,
      isLiked: isLiked ?? this.isLiked,
      createdAt: createdAt ?? this.createdAt,
      productName: productName ?? this.productName,
      prizeType: prizeType ?? this.prizeType,
    );
  }

  @override
  String toString() {
    return 'CommunityPost(id: $id, userName: $userName, caption: $caption, likeCount: $likeCount, isLiked: $isLiked)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommunityPost && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
