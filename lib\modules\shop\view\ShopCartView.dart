import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/ShopCartViewModel.dart';
import '../widget/CartItemWidget.dart';

class ShopCartView extends StatelessWidget {
  const ShopCartView({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ShopCartViewModel()..loadCart(),
      child: const _ShopCartViewBody(),
    );
  }
}

class _ShopCartViewBody extends StatelessWidget {
  const _ShopCartViewBody();

  @override
  Widget build(BuildContext context) {
    return Consumer<ShopCartViewModel>(
      builder: (context, vm, child) {
        if (vm.isLoading) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }
        return Scaffold(
          backgroundColor: const Color(0xFFF8F6FB),
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0.5,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.black),
              onPressed: () => Navigator.pop(context),
            ),
            title: Row(
              children: [
                const Text(
                  "Shopping Cart",
                  style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                ),
                Text(
                  "  (${vm.items.length})",
                  style: const TextStyle(color: Colors.black, fontWeight: FontWeight.normal, fontSize: 16),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {},
                child: const Text("Edit", style: TextStyle(color: Colors.black)),
              ),
            ],
          ),
          body: Column(
            children: [
              const Divider(height: 1, thickness: 1, color: Color(0xFFE0E0E0)),
              Expanded(
                child: ListView.separated(
                  itemCount: vm.items.length,
                  separatorBuilder: (_, __) => const Divider(height: 1, color: Color(0xFFE0E0E0)),
                  itemBuilder: (context, index) {
                    final item = vm.items[index];
                    return CartItemWidget(
                      item: item,
                      onSelected: (val) => vm.toggleSelect(index, val),
                      onIncrease: () => vm.changeQuantity(index, 1),
                      onDecrease: () => vm.changeQuantity(index, -1),
                    );
                  },
                ),
              ),
              const Divider(height: 1, thickness: 1, color: Color(0xFFE0E0E0)),
              Container(
                color: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                child: Row(
                  children: [
                    Checkbox(
                      value: vm.items.isNotEmpty && vm.items.every((e) => e.selected),
                      onChanged: (val) => vm.selectAll(val),
                    ),
                    const Text("Select All"),
                    const Spacer(),
                    Text(
                      "RM${vm.total.toStringAsFixed(2)}",
                      style: const TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.amber[600],
                        foregroundColor: Colors.black,
                        padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      onPressed: vm.selectedCount > 0 ? () {} : null,
                      child: Text("Checkout (${vm.selectedCount})"),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}