import 'package:flutter/material.dart';
import '../../model/exchangeCard/ExchangeCardModel.dart';

class ExchangeCardItem extends StatelessWidget {
  final ExchangeCardModel model;
  final bool isEligible;
  final VoidCallback onTap;

  const ExchangeCardItem({
    super.key,
    required this.model,
    required this.isEligible,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final Color bgColor = Colors.white;
    final Color textColor = isEligible ? Colors.black : Colors.grey[500]!;
    final Color subtitleColor = isEligible ? Colors.amber[800]! : Colors.grey[500]!;
    final Color requirementColor = isEligible ? Colors.black87 : Colors.grey[500]!;
    final Color buttonColor = isEligible ? Colors.red : Colors.grey[400]!;
    final Color buttonTextColor = Colors.white;

    // Decide which image to show
    final String imageUrl = isEligible ? model.imageUrl : model.bwImageUrl;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: const [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 5,
              offset: Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Image
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: model.isNetworkImage
                  ? Image.network(
                      imageUrl,
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(width: 50, height: 50, color: Colors.white);
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 50,
                          height: 50,
                          color: Colors.white,
                          child: const Icon(Icons.broken_image, color: Colors.grey),
                        );
                      },
                    )
                  : Image.asset(
                      imageUrl,
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                    ),
            ),
            const SizedBox(width: 12),

            // Texts
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    model.type,
                    style: TextStyle(
                      fontSize: 14,
                      color: subtitleColor,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    model.requirementDetail,
                    style: TextStyle(
                      fontSize: 12,
                      color: requirementColor,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 12),

            // Button
            SizedBox(
              width: 90,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: buttonColor,
                  borderRadius: BorderRadius.circular(6),
                ),
                alignment: Alignment.center,
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    "Claim",
                    style: TextStyle(
                      color: buttonTextColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
