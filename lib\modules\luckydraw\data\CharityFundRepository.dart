import '../model/CharityFund.dart';
import 'CharityFundService.dart';

class CharityFundRepository {
  final CharityFundService _service = charityFundService;

  Future<CharityFund> getCharityFund() async {
    try {
      print('Repository: Getting charity fund data');
      final charityFund = await _service.getCharityFund();
      print('Repository: Successfully retrieved charity fund: $charityFund');
      return charityFund;
    } catch (e) {
      print('Repository: Error getting charity fund: $e');
      rethrow;
    }
  }

  // Get cached data if available
  CharityFund? getCachedCharityFund() {
    return _service.getCachedCharityFund();
  }

  // Force refresh by clearing cache
  void clearCache() {
    _service.clearCache();
  }

  // Check if cache is valid
  bool get isCacheValid => _service.isCacheValid;
}
