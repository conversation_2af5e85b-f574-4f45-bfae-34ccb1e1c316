import 'package:flutter/material.dart';
import '../widgets/PointProductCard.dart';
import '../widgets/PointProductDetails.dart';
import '../widgets/SkeletonLoader.dart';

class ProductSearchPage extends StatefulWidget {
  final List<Map<String, dynamic>> allProducts;

  const ProductSearchPage({super.key, required this.allProducts});

  @override
  State<ProductSearchPage> createState() => _ProductSearchPageState();
}

class _ProductSearchPageState extends State<ProductSearchPage> {
  final TextEditingController _searchController = TextEditingController();
  String searchQuery = '';
  List<Map<String, dynamic>> searchResults = [];
  List<Map<String, dynamic>> displayedResults = []; // For pagination
  bool isSearching = false;
  bool hasSearched = false;
  List<String> recentSearches = [];
  List<String> filters = ['Relevance', 'Latest', 'Points'];
  int selectedFilterIndex = 0;
  bool isPointsAscending = true;

  // Pagination state
  bool _isLoadingMore = false;
  bool _hasMoreProducts = true;
  int _currentPage = 1;
  static const int _pageSize = 10;

  @override
  void initState() {
    super.initState();
    // Auto focus the search field when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(FocusNode());
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) {
      setState(() {
        searchResults = [];
        displayedResults = [];
        hasSearched = false;
        isSearching = false;
        _resetPagination();
      });
      return;
    }

    setState(() {
      isSearching = true;
      hasSearched = true;
      _resetPagination();
    });

    // Simulate network delay
    Future.delayed(const Duration(milliseconds: 300), () {
      final results = widget.allProducts.where((product) {
        return product['productName'].toString().toLowerCase().contains(
          query.toLowerCase(),
        );
      }).toList();

      setState(() {
        searchResults = results;
        isSearching = false;
        _updateDisplayedResults();
      });

      // Apply current filter to search results
      if (hasSearched && searchResults.isNotEmpty) {
        _applySorting();
      }

      // Add to recent searches if not already present
      if (!recentSearches.contains(query) && query.trim().isNotEmpty) {
        setState(() {
          recentSearches.insert(0, query);
          if (recentSearches.length > 5) {
            recentSearches = recentSearches.take(5).toList();
          }
        });
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      searchQuery = '';
      searchResults = [];
      displayedResults = [];
      hasSearched = false;
      isSearching = false;
      _resetPagination();
    });
  }

  // Reset pagination when filters change
  void _resetPagination() {
    _currentPage = 1;
    _hasMoreProducts = true;
    _isLoadingMore = false;
  }

  // Update displayed results based on current page
  void _updateDisplayedResults() {
    final endIndex = (_currentPage * _pageSize).clamp(0, searchResults.length);
    displayedResults = searchResults.take(endIndex).toList();
    _hasMoreProducts = displayedResults.length < searchResults.length;
  }

  // Load more products when user scrolls near the bottom
  Future<void> _loadMoreProducts() async {
    if (_isLoadingMore || !_hasMoreProducts) {
      return;
    }

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Load next page of products
      _currentPage++;
      _updateDisplayedResults();
    } catch (e) {
      print('Error loading more products: $e');
      // Reset loading state on error
      _isLoadingMore = false;
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  // Add this function to handle sorting by points
  void _sortByPoints() {
    setState(() {
      List<Map<String, dynamic>> listToSort = hasSearched
          ? searchResults
          : widget.allProducts;
      listToSort.sort((a, b) {
        final aPoints = a['totalAmount'] ?? 0;
        final bPoints = b['totalAmount'] ?? 0;
        return isPointsAscending
            ? aPoints.compareTo(bPoints)
            : bPoints.compareTo(aPoints);
      });
      if (hasSearched) {
        searchResults = List<Map<String, dynamic>>.from(listToSort);
        _updateDisplayedResults();
      }
    });
  }

  // Sort by relevance (search match quality + rating)
  void _sortByRelevance() {
    setState(() {
      List<Map<String, dynamic>> listToSort = hasSearched
          ? searchResults
          : widget.allProducts;

      listToSort.sort((a, b) {
        // First priority: Search relevance (exact matches first)
        final aName = (a['productName'] ?? a['productSkuName'] ?? '')
            .toString()
            .toLowerCase();
        final bName = (b['productName'] ?? b['productSkuName'] ?? '')
            .toString()
            .toLowerCase();
        final query = searchQuery.toLowerCase();

        // Check if query is at the beginning of product name (higher relevance)
        final aStartsWithQuery = aName.startsWith(query);
        final bStartsWithQuery = bName.startsWith(query);

        if (aStartsWithQuery && !bStartsWithQuery) return -1;
        if (!aStartsWithQuery && bStartsWithQuery) return 1;

        // Second priority: Rating/score
        final aScore = (a['score'] ?? 0.0).toDouble();
        final bScore = (b['score'] ?? 0.0).toDouble();
        if (aScore != bScore) {
          return bScore.compareTo(aScore); // Higher score first
        }

        // Third priority: Popularity (more participants)
        final aParticipants = a['usedAmount'] ?? 0;
        final bParticipants = b['usedAmount'] ?? 0;
        return bParticipants.compareTo(
          aParticipants,
        ); // More participants first
      });

      if (hasSearched) {
        searchResults = List<Map<String, dynamic>>.from(listToSort);
        _updateDisplayedResults();
      }
    });
  }

  // Sort by latest (newest products first)
  void _sortByLatest() {
    setState(() {
      List<Map<String, dynamic>> listToSort = hasSearched
          ? searchResults
          : widget.allProducts;

      listToSort.sort((a, b) {
        // Sort by ID (assuming higher ID = newer product)
        final aId = a['id'] ?? 0;
        final bId = b['id'] ?? 0;
        return bId.compareTo(aId); // Newest first (higher ID)
      });

      if (hasSearched) {
        searchResults = List<Map<String, dynamic>>.from(listToSort);
        _updateDisplayedResults();
      }
    });
  }

  // Apply sorting based on selected filter
  void _applySorting() {
    switch (filters[selectedFilterIndex]) {
      case 'Relevance':
        _sortByRelevance();
        break;
      case 'Latest':
        _sortByLatest();
        break;
      case 'Points':
        _sortByPoints();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          // Custom search header to match shop UI
          Container(
            color: const Color(0xFFFFE066),
            padding: const EdgeInsets.fromLTRB(20, 50, 20, 16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.black,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 12),
                          child: Icon(
                            Icons.search,
                            color: Colors.grey,
                            size: 20,
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            controller: _searchController,
                            autofocus: true,
                            onChanged: (value) {
                              setState(() {
                                searchQuery = value;
                              });
                              _performSearch(value);
                            },
                            onSubmitted: (value) {
                              _performSearch(value);
                            },
                            decoration: const InputDecoration(
                              hintText: 'Search Products',
                              border: InputBorder.none,
                              isDense: true,
                              contentPadding: EdgeInsets.symmetric(vertical: 8),
                            ),
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () => _performSearch(searchQuery),
                          child: Container(
                            height: 32,
                            margin: const EdgeInsets.symmetric(
                              vertical: 2,
                              horizontal: 4,
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 18),
                            decoration: BoxDecoration(
                              color: Colors.red[700],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Center(
                              child: Text(
                                'Search',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Filter/category bar
          Container(
            color: Colors.white,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(filters.length, (index) {
                final isSelected = selectedFilterIndex == index;
                final isPoints = filters[index] == 'Points';
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      if (isPoints) {
                        if (selectedFilterIndex == index) {
                          isPointsAscending = !isPointsAscending;
                        } else {
                          isPointsAscending = true;
                          selectedFilterIndex = index;
                        }
                        _applySorting(); // Apply sorting when points filter changes
                      } else {
                        selectedFilterIndex = index;
                        _applySorting(); // Apply sorting when other filters change
                      }
                    });
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 12.0,
                          horizontal: 8,
                        ),
                        child: Row(
                          children: [
                            Text(
                              filters[index],
                              style: TextStyle(
                                color: isSelected
                                    ? Color(0xFFFFA500)
                                    : Colors.grey[600],
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                fontSize: 16,
                              ),
                            ),
                            if (isPoints && isSelected)
                              Icon(
                                isPointsAscending
                                    ? Icons.arrow_upward
                                    : Icons.arrow_downward,
                                size: 16,
                                color: Color(0xFFFFA500),
                              ),
                          ],
                        ),
                      ),
                      if (isSelected)
                        Container(
                          height: 3,
                          width: 32,
                          decoration: BoxDecoration(
                            color: Color(0xFFFFA500),
                            borderRadius: BorderRadius.circular(2),
                            boxShadow: [
                              BoxShadow(
                                color: Color(0x33FFA500),
                                blurRadius: 4,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                );
              }),
            ),
          ),
          Divider(height: 1, thickness: 1, color: Colors.grey),
          // Rest of the body content
          Expanded(
            child: Column(
              children: [
                // Search suggestions or recent searches
                if (!hasSearched && recentSearches.isNotEmpty)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.all(16),
                          child: Text(
                            'Recent Searches',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        Expanded(
                          child: ListView.builder(
                            itemCount: recentSearches.length,
                            itemBuilder: (context, index) {
                              return ListTile(
                                leading: Icon(
                                  Icons.history,
                                  color: Colors.grey[600],
                                ),
                                title: Text(recentSearches[index]),
                                onTap: () {
                                  _searchController.text =
                                      recentSearches[index];
                                  setState(() {
                                    searchQuery = recentSearches[index];
                                  });
                                  _performSearch(recentSearches[index]);
                                },
                                trailing: IconButton(
                                  icon: Icon(
                                    Icons.close,
                                    color: Colors.grey[400],
                                    size: 20,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      recentSearches.removeAt(index);
                                    });
                                  },
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                // Search results
                if (hasSearched)
                  Expanded(
                    child: Column(
                      children: [
                        // Results header
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          color: Colors.grey[50],
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                isSearching
                                    ? 'Searching...'
                                    : 'Found ${searchResults.length} result${searchResults.length != 1 ? 's' : ''} for "$searchQuery"',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black54,
                                ),
                              ),
                              if (!isSearching && searchResults.isNotEmpty)
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.green,
                                  size: 16,
                                ),
                            ],
                          ),
                        ),

                        // Results content with scroll pagination
                        Expanded(
                          child: isSearching
                              ? const PointProductGridSkeleton()
                              : searchResults.isEmpty
                              ? Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.search_off,
                                        size: 64,
                                        color: Colors.grey[400],
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'No products found',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Try searching with different keywords',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey[500],
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : NotificationListener<ScrollNotification>(
                                  onNotification:
                                      (ScrollNotification scrollInfo) {
                                        if (scrollInfo
                                            is ScrollEndNotification) {
                                          // Check if we're near the bottom
                                          if (scrollInfo.metrics.pixels >=
                                              scrollInfo
                                                      .metrics
                                                      .maxScrollExtent -
                                                  300) {
                                            _loadMoreProducts();
                                          }
                                        }
                                        return false;
                                      },
                                  child: SingleChildScrollView(
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    child: Column(
                                      children: [
                                        // Filter Results Info
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 16,
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                'Showing ${displayedResults.length} of ${searchResults.length} product${searchResults.length != 1 ? 's' : ''}',
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w500,
                                                  color: Colors.black54,
                                                ),
                                              ),
                                              Row(
                                                children: [
                                                  if (_hasMoreProducts)
                                                    Text(
                                                      'Page $_currentPage',
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        color: Colors.grey[600],
                                                      ),
                                                    ),
                                                  if (_hasMoreProducts &&
                                                      !_isLoadingMore)
                                                    TextButton(
                                                      onPressed:
                                                          _loadMoreProducts,
                                                      child: const Text(
                                                        'Load More',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          color: Color(
                                                            0xFFFFBF00,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(height: 16),
                                        PointProductGrid(
                                          products: displayedResults,
                                          isLoadingMore: _isLoadingMore,
                                          hasMoreProducts: _hasMoreProducts,
                                          onProductTap: (product) {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    PointProductDetails(
                                                      product: product,
                                                    ),
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                        ),
                      ],
                    ),
                  ),

                // Default state when no search has been performed
                if (!hasSearched && recentSearches.isEmpty)
                  Expanded(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.search, size: 64, color: Colors.grey[400]),
                          const SizedBox(height: 16),
                          Text(
                            'Search for products',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Enter keywords to find point products',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
