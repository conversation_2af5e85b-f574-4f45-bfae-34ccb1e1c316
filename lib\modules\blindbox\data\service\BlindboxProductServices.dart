import 'package:dio/dio.dart';
import '../model/BlindboxProductModel.dart';
import '../model/BlindboxDetailModel.dart';
import 'package:luckymall/res/utility/network/api_client.dart';

final blindboxProductServices = BlindboxProductServices();

class BlindboxProductServices {
  final Dio publicDio = ApiClient.instance.publicDio;
  final Dio authDio = ApiClient.instance.authDio;

  Future<List<BlindboxCategoryModel>> getBlindboxCategories() async {
    final url = 'https://api.luckymall.com.my/unbox/categorys';

    final response = await publicDio.get(url);
    if (response.statusCode == 200) {
      final responseData = response.data;
      final List<dynamic> categoryList = responseData['data'] ?? [];
      return categoryList
          .map((json) => BlindboxCategoryModel.fromJson(json))
          .toList();
    } else {
      throw Exception('Failed to load categories: ${response.statusCode}');
    }
  }

  Future<Map<String, dynamic>> getBlindboxProducts({
    int? limit = 10,
    int? page = 1,
    String? sortBy,
    bool? ascending,
    int? categoryId,
    String? searchQuery,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      // Build query parameters according to the API specification
      final Map<String, dynamic> queryParams = <String, dynamic>{};

      // Initialize orderItem map with explicit typing
      queryParams['orderItem'] = <String, dynamic>{
        'column': sortBy ?? 'relevance', // Default sort column
        'asc': ascending ?? true, // Default descending order
      };

      // Add other parameters
      queryParams['limit'] = limit;
      queryParams['page'] = page;

      // Add search query if specified
      if (searchQuery != null && searchQuery.trim().isNotEmpty) {
        queryParams['vo']['unboxTitle'] = searchQuery;
      }

      // Determine the appropriate sort column based on context
      if (sortBy == null) {
        if (searchQuery != null && searchQuery.trim().isNotEmpty) {
          queryParams['orderItem']['column'] =
              'latest'; // Default for search
        } else if (minPrice != null || maxPrice != null) {
          queryParams['orderItem']['column'] =
              'most_sold'; // Default for price filtering
        } else {
          queryParams['orderItem']['column'] =
              'price'; // Default for general browsing
        }
      }

      // Determine the appropriate sort order based on context
      if (ascending == null) {
        if (queryParams['orderItem']['column'] == 'salePrice') {
          queryParams['orderItem']['asc'] =
              true; // Price: low to high by default
        } else {
          queryParams['orderItem']['asc'] =
              false; // Other fields: high to low by default
        }
      } else {
        queryParams['orderItem']['asc'] = ascending;
      }

      late final Response response;
      try {
        response = await publicDio.get('/unbox', queryParameters: queryParams);
      } catch (httpError) {
        rethrow;
      }

      if (response.statusCode == 200) {
        final responseData = response.data;

        final List<dynamic> productList = responseData['data']['list'] ?? [];
        final products = productList
            .map((json) => BlindboxProductModel.fromJson(json))
            .toList();

        return {
          'products': products,
          'totalCount': products.length,
          'pageSize': responseData['data']['pageSize'] ?? 10,
          'totalPage': responseData['data']['totalPage'] ?? 1,
          'currentPage': responseData['data']['currentPage'] ?? 1,
          'originalTotalCount':
              responseData['data']['totalCount'] ??
              0, // Keep original count for reference
        };
      } else {
        throw Exception('Failed to load products: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  /// Get detailed information for a specific product by unboxId
  Future<BlindboxDetailModel> getProductDetail(int unboxId) async {
    try {
      final response = await publicDio.get('/unbox/$unboxId');

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['status'] == 200 && responseData['data'] != null) {
          return BlindboxDetailModel.fromJson(responseData['data']);
        } else {
          throw Exception(
            'Blindbox detail not found: ${responseData['message']}',
          );
        }
      } else {
        throw Exception(
          'Failed to load product detail: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }
}
