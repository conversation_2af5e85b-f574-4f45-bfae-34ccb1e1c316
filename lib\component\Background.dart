import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class Background extends StatefulWidget {
  final String path;
  final Widget child;
  final bool loop;
  final bool mute;
  final Color placeholderColor;

  const Background({
    super.key,
    required this.path,
    required this.child,
    this.loop = true,
    this.mute = true,
    this.placeholderColor = Colors.transparent,
  });

  @override
  State<Background> createState() => _BackgroundState();
}

class _BackgroundState extends State<Background> {
  late final VideoPlayerController _controller;
  bool _initialized = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.asset(widget.path)
      ..setLooping(widget.loop)
      ..setVolume(widget.mute ? 0 : 1)
      ..initialize().then((_) {
        if (!mounted) return;
        setState(() {
          _initialized = true;
        });
        _controller.play();
      });

    _controller.addListener(() {
      final value = _controller.value;
      if (value.hasError) {
        // ignore: avoid_print
        print(
          'Background video error for path: ${widget.path} -> ${value.errorDescription}',
        );
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Always paint a placeholder so you can see the area the background occupies
        Positioned.fill(child: Container(color: widget.placeholderColor)),
        if (_initialized)
          Positioned.fill(
            child: FittedBox(
              fit: BoxFit.cover,
              child: SizedBox(
                width: _controller.value.size.width,
                height: _controller.value.size.height,
                child: VideoPlayer(_controller),
              ),
            ),
          ),
        // Non-positioned child defines the Stack's size
        widget.child,
      ],
    );
  }
}
