# Blindbox Module - Architecture & Implementation Guide

This document provides a comprehensive overview of the LuckyMall Blindbox module, including its architecture, components, and implementation details.

## Table of Contents

1. [Module Overview](#module-overview)
2. [Architecture](#architecture)
3. [File Structure](#file-structure)
4. [Core Components](#core-components)
5. [State Management](#state-management)
6. [API Integration](#api-integration)
7. [Routing](#routing)
8. [Features](#features)
9. [Usage Examples](#usage-examples)
10. [Development Guidelines](#development-guidelines)

## Module Overview

The Blindbox module is a comprehensive mystery box shopping experience that allows users to:
- Browse and purchase mystery boxes by category
- Search for specific products
- View product details and reviews
- Participate in group buying
- Share unboxing experiences with the community
- Reveal prizes and manage orders

**Current Status**: Fully implemented with Riverpod state management, API integration, and comprehensive UI components.

## Architecture

The module follows a clean architecture pattern with clear separation of concerns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Views       │    │   ViewModels    │    │    Providers    │
│                 │◄──►│                 │◄──►│                 │
│  (UI Layer)     │    │ (Business Logic)│    │ (State Mgmt)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Widgets      │    │     Models      │    │    Services     │
│                 │    │                 │    │                 │
│ (Reusable UI)   │    │ (Data Models)   │    │ (API Layer)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## File Structure

```
lib/modules/blindbox/
├── Blindbox Guideline.md           # This documentation
├── data/
│   ├── model/
│   │   ├── BlindboxProductModel.dart    # Product list model
│   │   └── BlindboxDetailModel.dart     # Product detail model
│   └── service/
│       └── BlindboxProductServices.dart  # API service layer
├── providers/
│   └── blindbox_providers.dart          # Riverpod providers
├── view/
│   ├── BlindboxView.dart                 # Main community view
│   ├── BlindboxShopView.dart             # Shop/browse view
│   ├── BlindboxBuyView.dart              # Product detail view
│   ├── BlindboxSearchView.dart           # Search results view
│   ├── BlindboxGroupBuyView.dart         # Group buying view
│   ├── BlindboxRevealView.dart           # Prize reveal view
│   ├── BlindboxReviewView.dart           # Reviews view
│   ├── PostView.dart                     # Community post view
│   └── PostProfileView.dart              # User profile view
├── view-model/
│   ├── BlindboxVM.dart                   # Main view model
│   ├── BlindboxShopVM.dart               # Shop view model
│   ├── BlindboxBuyVM.dart                # Buy view model
│   ├── BlindboxSearchVM.dart             # Search view model
│   ├── BlindboxGroupBuyVM.dart           # Group buy view model
│   ├── BlindboxRevealVM.dart             # Reveal view model
│   ├── BlindboxReviewVM.dart             # Review view model
│   ├── BlindboxCategoryBarVM.dart        # Category bar view model
│   ├── PostVM.dart                       # Post view model
│   └── PostProfileViewVM.dart            # Profile view model
└── widgets/
    ├── BlindboxBanner.dart               # Main banner
    ├── BlindboxShopCard.dart             # Product card
    ├── CategoryTab.dart                  # Category tabs
    ├── SearchFilterTabs.dart             # Search filters
    ├── BlindboxFilterOverlay.dart        # Filter overlay
    ├── ProductImageSection.dart          # Product images
    ├── ProductInfoSection.dart           # Product info
    ├── ProductReviewsSection.dart        # Product reviews
    ├── ProductBottomActionBar.dart       # Action buttons
    ├── PostCard.dart                     # Community post card
    ├── CountdownTimer.dart               # Timer widget
    ├── QRCodeSection.dart                # QR code display
    ├── MembersDisplay.dart               # Group members
    ├── GroupBuySuccessContent.dart       # Success content
    ├── BlindboxBuyShimmer.dart          # Loading skeleton
    └── [Other UI components...]
```

## Core Components

### 1. Data Models

#### BlindboxProductModel
```dart
class BlindboxProductModel {
  final int unboxId;           // Unique product identifier
  final String unboxCover;     // Product image URL
  final String unboxTitle;     // Product name
  final int cardDeduction;     // Card points that can be used
  final double salePrice;      // Product price
  final double score;          // Rating (0.0 - 5.0)
  final int sold;              // Number of units sold
  final bool isCollect;        // Is favorited by user
}
```

#### BlindboxDetailModel
```dart
class BlindboxDetailModel {
  final int unboxId;           // Unique product identifier
  final List<String> unboxCover; // Multiple product images
  final String unboxTitle;     // Product name
  final int cardDeduction;     // Card points that can be used
  final String details;        // Product description
  final double salePrice;      // Product price
  final double score;          // Rating
  final int sold;              // Number of units sold
  final bool isCollect;        // Is favorited by user
}
```

#### BlindboxCategoryModel
```dart
class BlindboxCategoryModel {
  final int categoryId;        // Category identifier
  final String categoryName;   // Category display name
  final bool isAdult;          // Age restriction flag
}
```

### 2. Service Layer

#### BlindboxProductServices
The service layer handles all API communication:

- **`getBlindboxCategories()`**: Fetches available product categories
- **`getBlindboxProducts()`**: Fetches products with filtering, sorting, and pagination
- **`getProductDetail(unboxId)`**: Fetches detailed product information

**API Endpoints:**
- Categories: `GET /unbox/categorys`
- Products: `GET /unbox` (with query parameters)
- Product Detail: `GET /unbox/{unboxId}`

**Query Parameters:**
```dart
{
  'limit': 10,                    // Items per page
  'page': 1,                      // Page number
  'sortBy': 'relevance',          // Sort column
  'ascending': true,              // Sort order
  'categoryId': 1,                // Category filter
  'searchQuery': 'search term',   // Search filter
  'minPrice': 10.0,              // Price range filter
  'maxPrice': 100.0              // Price range filter
}
```

### 3. State Management (Riverpod)

The module uses Riverpod for state management with the following providers:

#### Core Providers
```dart
// Service provider
final blindboxProductServicesProvider = Provider<BlindboxProductServices>

// Categories
final blindboxCategoriesProvider = FutureProvider<List<BlindboxCategoryModel>>
final selectedCategoryIdProvider = StateProvider<int>

// Products
final blindboxProductsProvider = FutureProvider.family<Map<String, dynamic>, int>
final blindboxSearchProductsProvider = FutureProvider.family<Map<String, dynamic>, Map<String, dynamic>>
final blindboxProductsByPriceRangeProvider = FutureProvider.family<Map<String, dynamic>, Map<String, dynamic>>

// Product details
final blindboxProductDetailProvider = FutureProvider.family<BlindboxDetailModel, int>

// Search and filters
final blindboxSearchResultsProvider = StateProvider<List<BlindboxProductModel>>
final blindboxSearchQueryProvider = StateProvider<String>
final blindboxLoadingProvider = StateProvider<bool>
final blindboxFilterProvider = StateProvider<Map<String, dynamic>>

// Pagination
final blindboxPaginationProvider = StateProvider<Map<String, dynamic>>
```

## Features

### 1. Product Browsing
- **Category-based filtering**: Browse products by category (All, Travel, Stationery, Sports, etc.)
- **Price filtering**: Filter by price range with preset and custom options
- **Sorting**: Sort by relevance, latest, most sold, or price
- **Pagination**: Load more products as user scrolls

### 2. Search Functionality
- **Real-time search**: Search products by title
- **Search filters**: Apply sorting and filtering to search results
- **Search history**: Maintain search state and results

### 3. Product Details
- **Image gallery**: Multiple product images with carousel
- **Product information**: Title, price, rating, sold count, card deduction
- **Reviews**: User reviews with ratings and helpful votes
- **Purchase options**: Buy now, add to favorites

### 4. Group Buying
- **Group formation**: Create groups with minimum member requirements
- **Countdown timer**: Time-limited group formation
- **Member management**: Track group members and status
- **QR code sharing**: Easy group invitation

### 5. Community Features
- **Unboxing posts**: Share unboxing experiences
- **User profiles**: View user posts and statistics
- **Social interaction**: Like posts and view engagement

### 6. Order Management
- **Checkout integration**: Seamless purchase flow
- **Prize reveal**: Interactive prize unveiling
- **Refund options**: Request refunds with credit conversion

## Routing

The module uses GoRouter with the following route structure:

```dart
// Main blindbox tab (with bottom navigation)
GoRoute(
  path: '/blind-box',
  builder: (context, state) => const BlindboxView(),
),

// Shop view (with Riverpod ProviderScope)
GoRoute(
  path: '/blind-box/shop',
  builder: (context, state) => const riverpod.ProviderScope(
    child: BlindboxShopView(),
  ),
),

// Product detail view
GoRoute(
  path: '/blind-box/buy/:unboxId',
  builder: (context, state) => riverpod.ProviderScope(
    child: BlindboxBuyView(
      unboxId: int.parse(state.pathParameters['unboxId']!),
      product: state.extra as Map<String, dynamic>? ?? {},
    ),
  ),
),

// Search view
GoRoute(
  path: '/blind-box/search',
  builder: (context, state) => riverpod.ProviderScope(
    child: BlindboxSearchView(
      initialSearchQuery: state.extra?['initialQuery'],
      triggerOnInit: state.extra?['trigger'] ?? false,
    ),
  ),
),

// Other routes
'/blind-box/reviews'      // Product reviews
'/blind-box/reveal'       // Prize reveal
'/blind-box/group-buy'    // Group buying
'/blind-box/post/:postId' // Community post
'/blind-box/profile/:userName' // User profile
```

## Usage Examples

### 1. Watching Products in a View

```dart
class BlindboxShopView extends ConsumerStatefulWidget {
  @override
  ConsumerState<BlindboxShopView> createState() => _BlindboxShopViewState();
}

class _BlindboxShopViewState extends ConsumerState<BlindboxShopView> {
  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(blindboxCategoriesProvider);
    final selectedCategoryId = ref.watch(selectedCategoryIdProvider);
    final productsAsync = ref.watch(blindboxProductsProvider(selectedCategoryId));

    return Scaffold(
      body: Column(
        children: [
          // Categories
          categoriesAsync.when(
            data: (categories) => CategoryTabs(categories: categories),
            loading: () => CircularProgressIndicator(),
            error: (error, stack) => Text('Error: $error'),
          ),
          
          // Products
          productsAsync.when(
            data: (response) {
              final products = response['products'] as List<BlindboxProductModel>;
              return ProductGrid(products: products);
            },
            loading: () => ProductGridShimmer(),
            error: (error, stack) => ErrorWidget(error: error),
          ),
        ],
      ),
    );
  }
}
```

### 2. Searching Products

```dart
Future<void> searchProducts(String query) async {
  final searchParams = {
    'query': query,
    'limit': 20,
    'page': 1,
    'sortBy': 'relevance',
    'ascending': false,
  };
  
  try {
    final results = await ref.read(
      blindboxSearchProductsProvider(searchParams).future,
    );
    
    ref.read(blindboxSearchResultsProvider.notifier).state = 
        results['products'] as List<BlindboxProductModel>;
  } catch (e) {
    // Handle error
  }
}
```

### 3. Filtering by Price Range

```dart
Future<void> filterByPrice(double minPrice, double maxPrice) async {
  final filterParams = {
    'minPrice': minPrice,
    'maxPrice': maxPrice,
    'limit': 15,
    'page': 1,
    'sortBy': 'salePrice',
    'ascending': true,
  };
  
  try {
    final results = await ref.read(
      blindboxProductsByPriceRangeProvider(filterParams).future,
    );
    
    // Update UI with filtered results
    updateProductList(results['products']);
  } catch (e) {
    // Handle error
  }
}
```

### 4. Getting Product Details

```dart
class ProductDetailView extends ConsumerWidget {
  final int unboxId;
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productDetailAsync = ref.watch(blindboxProductDetailProvider(unboxId));
    
    return productDetailAsync.when(
      data: (productDetail) => ProductDetailContent(product: productDetail),
      loading: () => ProductDetailShimmer(),
      error: (error, stack) => ErrorWidget(error: error),
    );
  }
}
```

## Development Guidelines

### 1. State Management
- Use Riverpod providers for all state management
- Prefer `ref.watch()` for reactive UI updates
- Use `ref.read()` for one-time actions
- Keep providers focused and single-purpose

### 2. Error Handling
- Always handle loading, data, and error states
- Provide meaningful error messages to users
- Implement retry mechanisms where appropriate
- Log errors for debugging

### 3. Performance
- Implement pagination for large data sets
- Use `const` constructors where possible
- Implement proper disposal of controllers and listeners
- Cache API responses when appropriate

### 4. UI/UX
- Follow Material Design principles
- Implement responsive design for different screen sizes
- Provide loading states and skeleton screens
- Use consistent spacing and typography

### 5. Testing
- Write unit tests for view models
- Test provider state changes
- Mock API services for testing
- Test error scenarios and edge cases

### 6. Code Organization
- Keep views focused on UI logic
- Move business logic to view models
- Use providers for state management
- Create reusable widgets for common UI patterns

## API Response Structure

### Categories Response
```json
{
  "status": 200,
  "data": [
    {
      "categoryId": 1,
      "categoryName": "Travel",
      "isAdult": false
    }
  ]
}
```

### Products Response
```json
{
  "status": 200,
  "data": {
    "totalCount": 7,
    "pageSize": 10,
    "totalPage": 1,
    "currentPage": 1,
    "list": [
      {
        "unboxId": 1,
        "unboxCover": "https://...",
        "unboxTitle": "Product Name",
        "salePrice": 11.89,
        "cardDeduction": 2,
        "sold": 0,
        "score": 4.5,
        "isCollect": false
      }
    ]
  }
}
```

### Product Detail Response
```json
{
  "status": 200,
  "data": {
    "unboxId": 1,
    "unboxCover": ["https://...", "https://..."],
    "unboxTitle": "Product Name",
    "cardDeduction": 2,
    "details": "Product description...",
    "salePrice": 11.89,
    "score": 4.5,
    "sold": 0,
    "isCollect": false
  }
}
```

## Future Enhancements

1. **Offline Support**: Implement caching and offline-first architecture
2. **Real-time Updates**: WebSocket integration for live updates
3. **Advanced Filtering**: More filter options and saved filters
4. **Personalization**: AI-powered product recommendations
5. **Social Features**: Enhanced community interaction
6. **Analytics**: User behavior tracking and insights
7. **Accessibility**: Improved screen reader support
8. **Internationalization**: Multi-language support

## Troubleshooting

### Common Issues

1. **Provider Not Found**: Ensure ProviderScope wraps the view
2. **API Errors**: Check network connectivity and API endpoints
3. **State Not Updating**: Verify ref.watch() usage and provider invalidation
4. **Memory Leaks**: Properly dispose controllers and listeners
5. **Performance Issues**: Implement pagination and optimize rebuilds

### Debug Tips

- Use `ref.debugPrint()` to log provider state changes
- Check the Flutter Inspector for widget rebuilds
- Monitor network requests in DevTools
- Verify route parameters and extra data

---

This guide covers the complete implementation of the LuckyMall Blindbox module. For additional questions or clarifications, refer to the codebase or contact the development team.
