import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import '../view-model/CategoryVM.dart';
import 'PointRangeBottomSheet.dart';

class CategorySelectorWidget extends StatefulWidget {
  final Function(String)? onCategoryChanged;
  final Function(int min, int max)? onPointRangeChanged;
  final String? initialCategory;

  const CategorySelectorWidget({
    super.key,
    this.onCategoryChanged,
    this.onPointRangeChanged,
    this.initialCategory,
  });

  @override
  State<CategorySelectorWidget> createState() => _CategorySelectorWidgetState();
}

class _CategorySelectorWidgetState extends State<CategorySelectorWidget> {
  String selectedCategory = 'ALL';

  @override
  void initState() {
    super.initState();
    // Initialize selected category from parent if provided
    selectedCategory = widget.initialCategory ?? 'ALL';
    // Fetch categories when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final categoryVM = Provider.of<CategoryVM>(context, listen: false);
      categoryVM.fetchCategories();
    });
  }

  @override
  void didUpdateWidget(covariant CategorySelectorWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Sync selection when parent-provided initialCategory changes
    if (widget.initialCategory != null &&
        widget.initialCategory != oldWidget.initialCategory &&
        widget.initialCategory != selectedCategory) {
      setState(() {
        selectedCategory = widget.initialCategory!;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375;
    double scaleH(double value) => value * screenHeight / 812;
    double scaleText(double value) => value * screenWidth / 375;

    return Consumer<CategoryVM>(
      builder: (context, categoryVM, child) {
        // Build categories list with 'ALL' option first
        final List<String> categories = ['ALL'];

        if (categoryVM.categories.isNotEmpty) {
          // Add non-adult categories from API
          final nonAdultCategories = categoryVM.categories
              .where((cat) => !cat.isAdult)
              .map((cat) => cat.categoryName)
              .toList();
          categories.addAll(nonAdultCategories);
        } else if (categoryVM.isLoading) {
          // Show loading state
          return Container(
            padding: EdgeInsets.all(scaleW(16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Category',
                          style: TextStyle(
                            fontSize: scaleText(16),
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                        SizedBox(width: scaleW(8)),
                        SvgPicture.asset(
                          'assets/icons/category.svg',
                          width: scaleW(16),
                          height: scaleW(16),
                        ),
                      ],
                    ),
                    _buildPointRangeButton(scaleW, scaleH, scaleText),
                  ],
                ),
                SizedBox(height: scaleH(16)),
                const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xFFFFBF00),
                    ),
                  ),
                ),
              ],
            ),
          );
        } else if (categoryVM.errorMessage != null) {
          // Show error state with fallback categories
          categories.addAll(['Hot Goods', 'TNG Vouchers']);
        } else {
          // Fallback to default categories if API fails
          categories.addAll(['Hot Goods', 'TNG Vouchers']);
        }

        return Container(
          padding: EdgeInsets.all(scaleW(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with Category title and Point Range filter
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Category',
                        style: TextStyle(
                          fontSize: scaleText(16),
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(width: scaleW(8)),
                      SvgPicture.asset(
                        'assets/icons/category.svg',
                        width: scaleW(16),
                        height: scaleW(16),
                      ),
                    ],
                  ),
                  _buildPointRangeButton(scaleW, scaleH, scaleText),
                ],
              ),

              SizedBox(height: scaleH(16)),

              // Category buttons
              Row(
                children: categories.map((category) {
                  final isSelected = selectedCategory == category;
                  return Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(
                        right: category != categories.last ? scaleW(8.0) : 0,
                      ),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedCategory = category;
                          });
                          widget.onCategoryChanged?.call(category);
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: scaleH(12)),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? const Color(0xFFffbf00)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(scaleW(8)),
                            border: Border.all(
                              color: isSelected
                                  ? const Color(0xFFffbf00)
                                  : Colors.grey.shade500,
                              width: 1,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              category,
                              style: TextStyle(
                                fontSize: scaleText(12),
                                fontWeight: FontWeight.w600,
                                color: isSelected ? Colors.black : Colors.black,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPointRangeButton(
    double Function(double) scaleW,
    double Function(double) scaleH,
    double Function(double) scaleText,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: scaleW(16),
        vertical: scaleH(8),
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFffbf00),
        borderRadius: BorderRadius.circular(scaleW(8)),
      ),
      child: InkWell(
        onTap: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder: (context) => PointRangeBottomSheet(
              onConfirm: (min, max) {
                widget.onPointRangeChanged?.call(min, max);
              },
              onReset: () {
                // Handle reset logic if needed
              },
            ),
          );
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              'assets/icons/Filter.svg',
              width: scaleW(16),
              height: scaleW(16),
            ),
            SizedBox(width: scaleW(4)),
            Text(
              'Point Range',
              style: TextStyle(
                fontSize: scaleText(12),
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
