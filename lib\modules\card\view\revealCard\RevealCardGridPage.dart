import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../model/reveal/RevealedCard.dart';
import '../../view-model/reveal/RevealCardViewModel.dart';

class RevealCardGridPage extends StatefulWidget {
  const RevealCardGridPage({super.key});

  @override
  State<RevealCardGridPage> createState() => _RevealCardGridPageState();
}

class _RevealCardGridPageState extends State<RevealCardGridPage> {
  final ScrollController _scrollController = ScrollController();
  bool _canScrollLeft = false;
  bool _canScrollRight = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_updateArrowVisibility);
  }

  void _updateArrowVisibility() {
    setState(() {
      _canScrollLeft = _scrollController.offset > 0;
      _canScrollRight = _scrollController.offset < _scrollController.position.maxScrollExtent;
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_updateArrowVisibility);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cards = context.watch<RevealCardViewModel>().cards;

    if (cards.isEmpty) {
      return const Scaffold(
        body: Center(
          child: Text(
            "No cards to reveal",
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ),
      );
    }

    final isGrid = cards.length > 5;

    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: const Text(
          'Card Reveal',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.amber,
            letterSpacing: 1.5,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          final screenWidth = constraints.maxWidth;
          final crossAxisCount = screenWidth > 1000 ? 4 : screenWidth > 600 ? 3 : 2;

          return Padding(
            padding: const EdgeInsets.all(16),
            child: isGrid
                ? GridView.builder(
                    itemCount: cards.length,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: crossAxisCount,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 750 / 1050,
                    ),
                    itemBuilder: (_, index) => _buildCard(cards[index]),
                  )
                : Stack(
                    children: [
                      Center(
                        child: SizedBox(
                          height: constraints.maxHeight * 0.7,
                          child: ListView.separated(
                            controller: _scrollController,
                            scrollDirection: Axis.horizontal,
                            itemCount: cards.length,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemBuilder: (_, index) => AspectRatio(
                              aspectRatio: 750 / 1050,
                              child: _buildCard(cards[index]),
                            ),
                            separatorBuilder: (_, __) => const SizedBox(width: 16),
                          ),
                        ),
                      ),
                      if (_canScrollLeft)
                        Positioned(
                          left: 0,
                          top: 0,
                          bottom: 0,
                          child: Container(
                            width: 40,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [const Color.fromARGB(99, 0, 0, 0), Colors.transparent],
                              ),
                            ),
                            child: const Icon(Icons.arrow_back_ios, color: Colors.white70),
                          ),
                        ),
                      if (_canScrollRight)
                        Positioned(
                          right: 0,
                          top: 0,
                          bottom: 0,
                          child: Container(
                            width: 40,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.centerRight,
                                end: Alignment.centerLeft,
                                colors: [Colors.black.withOpacity(0.6), Colors.transparent],
                              ),
                            ),
                            child: const Icon(Icons.arrow_forward_ios, color: Colors.white70),
                          ),
                        ),
                    ],
                  ),
          );
        },
      ),
      bottomNavigationBar: SizedBox(
        height: 70,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.amber.shade600, Colors.orange.shade700],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: const [
              BoxShadow(color: Colors.black54, blurRadius: 6, offset: Offset(0, -2)),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _gameButton(
                onPressed: () => context.go('/card/purchase'),
                icon: Icons.add_circle,
                label: "Buy More",
                bgColor: Colors.black,
                fgColor: Colors.amberAccent,
              ),
              _gameButton(
                onPressed: () => context.go('/card'),
                icon: Icons.home,
                label: "Card Home",
                bgColor: Colors.white,
                fgColor: Colors.black87,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCard(RevealedCard card) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeOutBack,
      tween: Tween(begin: 0.8, end: 1.0),
      builder: (context, scale, child) {
        return Transform.scale(
          scale: scale,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: const LinearGradient(
                colors: [Color(0xFFFFF176), Color(0xFFFFC107)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black45,
                  blurRadius: 10,
                  offset: Offset(0, 6),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Image.asset(
                card.imagePath,
                fit: BoxFit.cover,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _gameButton({
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    required Color bgColor,
    required Color fgColor,
  }) {
    return SizedBox(
      width: 120,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, color: fgColor, size: 20),
        label: Text(label, style: TextStyle(color: fgColor, fontSize: 14)),
        style: ElevatedButton.styleFrom(
          backgroundColor: bgColor,
          padding: const EdgeInsets.symmetric(vertical: 10),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          elevation: 5,
          shadowColor: Colors.black87,
        ),
      ),
    );
  }
}
