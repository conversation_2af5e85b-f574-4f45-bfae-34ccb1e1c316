class UserInfoModel {
  final String? avatar; // image url
  final String nickname; // user name
  final int? id;
  final String mobile; // mobile number
  final String memberCode; // user id
  final DateTime
  birthday; // Birthday (if this attribute is not returned, it means the user has not set it)
  final int gender; // Gender (0: not set; 1: Female; 2: Male;)
  final int
  faceState; // Face verification status (-1: returned; 0: not authenticated; 1: authenticating; 10: completed;)

  UserInfoModel({
    this.avatar,
    required this.nickname,
    this.id,
    required this.mobile,
    required this.memberCode,
    required this.birthday,
    required this.gender,
    required this.faceState,
  });

  factory UserInfoModel.fromJson(Map<String, dynamic> json) {
    // Coerce and guard types coming from API
    final dynamic rawAvatar = json['avatar'];
    final dynamic rawNickname = json['nickname'];
    final dynamic rawId = json['id'];
    final dynamic rawMobile = json['mobile'];
    final dynamic rawMemberCode = json['memberCode'];
    final dynamic rawBirthday = json['birthday'];
    final dynamic rawGender = json['gender'];
    final dynamic rawFaceState = json['faceState'];

    // Parse birthday safely (expecting formats like YYYY-MM-DD)
    DateTime parsedBirthday;
    if (rawBirthday is String && rawBirthday.isNotEmpty) {
      parsedBirthday = DateTime.tryParse(rawBirthday) ?? DateTime(1970, 1, 1);
    } else {
      parsedBirthday = DateTime(1970, 1, 1);
    }

    return UserInfoModel(
      avatar: rawAvatar?.toString(),
      nickname: (rawNickname ?? '').toString(),
      id: int.tryParse((rawId ?? 0).toString()) ?? 0,
      mobile: (rawMobile ?? '').toString(),
      memberCode: (rawMemberCode ?? '').toString(),
      birthday: parsedBirthday,
      gender: int.tryParse((rawGender ?? 0).toString()) ?? 0,
      faceState: int.tryParse((rawFaceState ?? 0).toString()) ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'avatar': avatar,
      'nickname': nickname,
      'birthday': birthday.toIso8601String(),
      'gender': gender,
    };
  }
}
