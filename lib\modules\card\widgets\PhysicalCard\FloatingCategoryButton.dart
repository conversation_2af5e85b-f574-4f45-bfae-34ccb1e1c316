import 'package:flutter/material.dart';
import '../CategorySelectionSheet.dart';

class FloatingCategoryButton extends StatelessWidget {
  final Function(String category, String series) onCategorySelected;
  final String? currentSeries;
  final bool showSeriesSelection;

  const FloatingCategoryButton({
    super.key,
    required this.onCategorySelected,
    this.currentSeries,
    this.showSeriesSelection = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        builder: (_) => CategorySelectionSheet(
          onCategorySelected: onCategorySelected,
          showSeriesSelection: showSeriesSelection,
          initialSeries: currentSeries,
        ),
      ),
      child: Container(
        margin: const EdgeInsets.only(right: 0),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFFFFC107), Color(0xFFFF9800)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            bottomLeft: Radius.circular(30),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 6,
              offset: Offset(0, 4),
            )
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: const [
            Text(
              'Select Category',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(width: 8),
            Icon(Icons.arrow_forward_ios, color: Colors.white, size: 16),
          ],
        ),
      ),
    );
  }
}
