import 'package:flutter/material.dart';

class HistoryTabSelector extends StatelessWidget {
  final TabController controller;

  const HistoryTabSelector({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final double scale = (screenWidth / 390).clamp(0.9, 1.2);

    return Container(
      height: 44 * scale,
      margin: EdgeInsets.symmetric(horizontal: 16 * scale),
      padding: EdgeInsets.all(2 * scale),
      decoration: BoxDecoration(
        color: Colors.amber,
        borderRadius: BorderRadius.circular(12 * scale),
      ),
      child: Material(
        color: Colors.transparent,
        child: TabBar(
          controller: controller,
          indicator: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10 * scale),
          ),
          indicatorColor: Colors.transparent, // ✅ hides underline completely
          labelColor: Colors.black,
          unselectedLabelColor: Colors.white,
          labelStyle: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14 * scale,
          ),
          unselectedLabelStyle: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14 * scale,
          ),
          overlayColor: WidgetStateColor.transparent,
          indicatorSize: TabBarIndicatorSize.tab,
          dividerColor: Colors.transparent, // ✅ Flutter 3.13+
          tabs: const [
            Tab(text: 'Today'),
            Tab(text: 'Month'),
            Tab(text: 'Year'),
          ],
        ),
      ),
    );
  }
}
