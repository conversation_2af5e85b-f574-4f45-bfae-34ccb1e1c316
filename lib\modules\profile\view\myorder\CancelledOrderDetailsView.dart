import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';
import '../../../../data/model/OrderModel.dart';

class CancelledOrderDetailsView extends StatelessWidget {
  final OrderModel order;

  const CancelledOrderDetailsView({super.key, required this.order});

  // Responsive utility functions
  static double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  static double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black87),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: true,
        title: Text(
          'Order Details',
          style: TextStyle(
            fontSize: getResponsiveFontSize(16, context),
            fontWeight: FontWeight.w700,
            color: Colors.black,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Banner
            _buildStatusBanner(context),

            SizedBox(height: getResponsivePadding(16, context)),

            // Order Information
            _buildOrderInfo(context),

            SizedBox(height: getResponsivePadding(16, context)),

            // Address/Pickup Information
            _buildAddressInfo(context),

            SizedBox(height: getResponsivePadding(16, context)),

            // Product Details
            _buildProductDetails(context),

            SizedBox(height: getResponsivePadding(16, context)),

            // Price Summary
            _buildPriceSummary(context),

            SizedBox(height: getResponsivePadding(16, context)),

            // Payment Details
            _buildPaymentDetails(context),

            SizedBox(height: getResponsivePadding(32, context)),

            // Bottom Action Buttons
            _buildBottomActions(context),

            SizedBox(height: getResponsivePadding(32, context)),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBanner(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(getResponsivePadding(12, context)),
      color: Colors.grey.shade300,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Cancellation Completed',
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          Text(
            '2025-02-20 12:02',
            style: TextStyle(
              fontSize: getResponsiveFontSize(12, context),
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderInfo(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        children: [
          _buildDetailRow(context, 'Order Number', order.id, hasIcon: true),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildDetailRow(
            context,
            'Order Time',
            '${order.createdAt.year}-${order.createdAt.month.toString().padLeft(2, '0')}-${order.createdAt.day.toString().padLeft(2, '0')} ${order.createdAt.hour.toString().padLeft(2, '0')}:${order.createdAt.minute.toString().padLeft(2, '0')}',
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value, {
    bool hasIcon = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            color: Colors.black87,
          ),
        ),
        Row(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            if (hasIcon) ...[
              SizedBox(width: getResponsivePadding(4, context)),
              GestureDetector(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: value));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Order number copied!')),
                  );
                },
                child: Icon(
                  Icons.copy,
                  size: getResponsiveFontSize(16, context),
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildAddressInfo(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person,
                size: getResponsiveFontSize(20, context),
                color: Colors.black87,
              ),
              SizedBox(width: getResponsivePadding(8, context)),
              Text(
                'Angela',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              Text(
                '012 345 6789',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  color: const Color(0xFF3B82F6),
                ),
              ),
            ],
          ),
          SizedBox(height: getResponsivePadding(8, context)),
          Padding(
            padding: EdgeInsets.only(left: getResponsivePadding(28, context)),
            child: Text(
              '1 Blok A Tingkat, Lot 1, Jalan Indah Jaya, 90000 Sandakan, Sabah, Malaysia',
              style: TextStyle(
                fontSize: getResponsiveFontSize(12, context),
                color: Colors.grey.shade600,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductDetails(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order type badge
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: getResponsivePadding(8, context),
              vertical: getResponsivePadding(4, context),
            ),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.shopping_bag,
                  size: getResponsiveFontSize(16, context),
                  color: Colors.black54,
                ),
                SizedBox(width: getResponsivePadding(4, context)),
                Text(
                  order.type.typeDisplayName,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(12, context),
                    fontWeight: FontWeight.w600,
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: getResponsivePadding(12, context)),

          // Product items
          _buildProductItem(
            context,
            'Russell Taylor\'s 3D Visible Window Air Fryer',
            'White Large',
            'RM219.99',
            1,
            'https://example.com/air-fryer.jpg',
          ),
          SizedBox(height: getResponsivePadding(12, context)),
          _buildProductItem(
            context,
            'Russell Taylor\'s 3D Visible Window Air Fryer (T-shirt)',
            'S',
            'RM10.99',
            1,
            'https://example.com/tshirt.jpg',
          ),

          SizedBox(height: getResponsivePadding(12, context)),
          Divider(color: Colors.grey.shade200),
          SizedBox(height: getResponsivePadding(8, context)),

          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'View More',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(12, context),
                  color: Colors.grey.shade600,
                ),
              ),
              SizedBox(width: getResponsivePadding(4, context)),
              Icon(
                Icons.keyboard_arrow_down,
                size: getResponsiveFontSize(16, context),
                color: Colors.grey.shade600,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductItem(
    BuildContext context,
    String name,
    String variant,
    String price,
    int quantity,
    String imageUrl,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Product image
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: getResponsivePadding(60, context),
            height: getResponsivePadding(60, context),
            color: Colors.grey.shade100,
            child: CachedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey.shade200,
                child: const Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xFFFCD255),
                    ),
                  ),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey.shade200,
                child: Icon(Icons.image, color: Colors.grey.shade400),
              ),
            ),
          ),
        ),

        SizedBox(width: getResponsivePadding(12, context)),

        // Product Info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: getResponsivePadding(4, context)),
              Text(
                variant,
                style: TextStyle(
                  fontSize: getResponsiveFontSize(12, context),
                  color: Colors.grey.shade600,
                ),
              ),
              SizedBox(height: getResponsivePadding(8, context)),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    price,
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(14, context),
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    'x$quantity',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(12, context),
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPriceSummary(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        children: [
          _buildPriceRow(context, 'Product Subtotal Price', 'RM242.98'),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildPriceRow(context, 'Delivery Fees', 'RM10.00'),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildPriceRow(
            context,
            'New User Discount',
            '-RM44.00',
            isDiscount: true,
          ),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildPriceRow(
            context,
            'Voucher Used x1',
            '-RM8.00',
            isDiscount: true,
          ),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildPriceRow(
            context,
            'Card Discount Applied',
            '-RM0.10',
            isDiscount: true,
          ),
          SizedBox(height: getResponsivePadding(12, context)),
          Divider(color: Colors.grey.shade200),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildPriceRow(context, 'Total Payment', 'RM200.88', isTotal: true),
        ],
      ),
    );
  }

  Widget _buildPriceRow(
    BuildContext context,
    String label,
    String value, {
    bool isDiscount = false,
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            color: Colors.black87,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            fontWeight: isTotal ? FontWeight.w700 : FontWeight.w500,
            color: isDiscount
                ? Colors.red
                : isTotal
                ? Colors.black87
                : Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentDetails(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        children: [
          _buildDetailRow(context, 'Payment Type', 'Billplz Pay - FPX'),
          SizedBox(height: getResponsivePadding(8, context)),
          _buildDetailRow(context, 'Payment Time', '2025-02-20 12:00'),
        ],
      ),
    );
  }

  Widget _buildBottomActions(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Row(
        children: [
          // Help Support Button
          Expanded(
            child: SizedBox(
              height: getResponsivePadding(56, context),
              child: OutlinedButton(
                onPressed: () {
                  // TODO: Implement help support functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Help Support clicked')),
                  );
                },
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(0xFFFCD255),
                  side: BorderSide(color: const Color(0xFFFCD255), width: 2),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Help Support',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),

          SizedBox(width: getResponsivePadding(12, context)),

          // Buy Again Button
          Expanded(
            child: SizedBox(
              height: getResponsivePadding(56, context),
              child: ElevatedButton(
                onPressed: () {
                  // Navigate based on order type
                  switch (order.type) {
                    case OrderType.storeOrder:
                      context.go('/shop');
                      break;
                    case OrderType.blindBox:
                      context.go('/blind-box');
                      break;
                    case OrderType.prize:
                    case OrderType.luckyGroupBuy:
                      context.go('/lucky-draw');
                      break;
                    default:
                      context.go('/shop');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFCD255),
                  foregroundColor: Colors.black87,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Buy Again',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
