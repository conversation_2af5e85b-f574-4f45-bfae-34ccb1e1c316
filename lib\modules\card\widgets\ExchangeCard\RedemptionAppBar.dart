import 'package:flutter/material.dart';

class RedemptionAppBar extends StatelessWidget implements PreferredSizeWidget {
  const RedemptionAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final totalHeight = statusBarHeight + 70;

    return Container(
      height: totalHeight,
      padding: EdgeInsets.only(
        top: statusBarHeight,
        left: 0,
        right: 0,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color.fromARGB(83, 0, 0, 0),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Centered title
          const Align(
            alignment: Alignment.center,
            child: Text(
              'Confirm Redemption',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Back button on the left
          Positioned(
            left: 4,
            top: 10,
            bottom: 10,
            child: IconButton(
              icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
              onPressed: () => Navigator.pop(context),
              tooltip: 'Back',
            ),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 20);
}
