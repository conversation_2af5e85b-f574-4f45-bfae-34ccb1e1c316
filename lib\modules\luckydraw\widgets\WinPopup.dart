import 'package:flutter/material.dart';
import '../view/ParticipationRecordsView.dart';

class WinPopup extends StatelessWidget {
  final VoidCallback? onParticipantRecord;
  final VoidCallback? onClose;
  final String? productImageUrl;
  final double? imageHeight;
  final double? imageWidth;
  final BoxFit imageFit;

  const WinPopup({
    super.key,
    this.onParticipantRecord,
    this.onClose,
    this.productImageUrl,
    this.imageHeight,
    this.imageWidth,
    this.imageFit = BoxFit.cover,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black54,
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Top section with light green background
              Container(
                decoration: const BoxDecoration(
                  color: Color(0xFFBCFFB5), // Light green background
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Product image container
                    Container(
                      width: imageWidth ?? double.infinity,
                      height: imageHeight ?? 200,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: productImageUrl != null
                            ? Image.network(
                                productImageUrl!,
                                fit: imageFit,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: Colors.grey[200],
                                    child: const Icon(Icons.error, size: 48),
                                  );
                                },
                              )
                            : Container(
                                color: Colors.grey[200],
                                child: const Icon(Icons.image, size: 48),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
              // Bottom section with white background
              Container(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Congratulations text
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text('🎉', style: TextStyle(fontSize: 24)),
                        const SizedBox(width: 8),
                        const Text(
                          'Congratulations!',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2C3E50),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'You have win the prize!',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 24),
                    // Participant Record button
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: ElevatedButton(
                        onPressed:
                            onParticipantRecord ??
                            () {
                              // Default action if no callback provided
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const ParticipationRecordsView(),
                                ),
                              );
                            },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(
                            0xFFFFBF00,
                          ), // Yellow background
                          foregroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Participant Record',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Close button
                    GestureDetector(
                      onTap: onClose,
                      child: const Text(
                        'Close',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Helper class to show the popup
class WinPopupOverlay {
  static void show({
    required BuildContext context,
    VoidCallback? onParticipantRecord,
    VoidCallback? onClose,
    String? prizeTitle,
    String? brandName,
    String? resellerName,
    String? productImageUrl,
    double? imageHeight,
    double? imageWidth,
    BoxFit imageFit = BoxFit.cover,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return WinPopup(
          onParticipantRecord: onParticipantRecord,
          onClose: onClose ?? () => Navigator.of(context).pop(),
          productImageUrl: productImageUrl,
          imageHeight: imageHeight,
          imageWidth: imageWidth,
          imageFit: imageFit,
        );
      },
    );
  }
}
