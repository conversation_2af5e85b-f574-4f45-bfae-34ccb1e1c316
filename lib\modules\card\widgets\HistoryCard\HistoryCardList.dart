import 'package:flutter/material.dart';
import 'package:luckymall/modules/card/data/MockHistoryData.dart';
import '../../view-Model/historyCard/MockHistoryCardData.dart';
import 'HistoryCardItemRow.dart';

class HistoryCardList extends StatelessWidget {
  final String mode;
  final String sortOrder;
  final String selectedFilter;
  final DateTime? startDate;
  final DateTime? endDate;

  const HistoryCardList({
    super.key,
    required this.mode,
    required this.sortOrder,
    required this.selectedFilter,
    this.startDate,
    this.endDate,
  });

  @override
  Widget build(BuildContext context) {
    final mockDataProvider = MockHistoryCardData(mockHistoryData); // or mockMonthData
    final filteredList = mockDataProvider.getFilteredRecords(
      mode: mode,
      sortOrder: sortOrder,
      selectedFilter: selectedFilter,
      startDate: startDate,
      endDate: endDate,
    );

    if (filteredList.isEmpty) {
      final screenWidth = MediaQuery.of(context).size.width;
      final scale = (screenWidth / 390).clamp(0.9, 1.2);

      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history_edu_outlined,
              size: 80 * scale,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              "No record found",
              style: TextStyle(
                fontSize: 16 * scale,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    } 


    return ListView.builder(
      itemCount: filteredList.length,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemBuilder: (context, index) {
        final item = filteredList[index];
        return HistoryCardItemRow(item: item);
      },
    );
  }
}

