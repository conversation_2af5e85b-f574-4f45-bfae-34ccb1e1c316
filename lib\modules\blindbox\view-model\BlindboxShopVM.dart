import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../data/model/BlindboxProductModel.dart';
import '../providers/blindbox_providers.dart';

class BlindboxShopViewVM extends ConsumerStatefulWidget {
  final Widget child;

  const BlindboxShopViewVM({super.key, required this.child});

  @override
  ConsumerState<BlindboxShopViewVM> createState() => BlindboxShopViewVMState();
}

class BlindboxShopViewVMState extends ConsumerState<BlindboxShopViewVM> {
  // Search controller
  late TextEditingController searchController;
  late ScrollController scrollController;
  Map<String, dynamic>? currentFilters;
  bool isLoading = true;
  bool isLoadingMore = false;
  bool hasMoreData = true;

  // Pagination variables
  int currentPage = 1;
  int pageSize = 10;
  List<BlindboxProductModel> allProducts = [];
  List<BlindboxProductModel> displayedProducts = [];

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();
    scrollController = ScrollController();
    scrollController.addListener(_onScroll);
    _simulateLoading();
  }

  @override
  void dispose() {
    searchController.dispose();
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    super.dispose();
  }

  void navigateToSearch(BuildContext context) {
    context.push('/blind-box/search');
  }

  void _simulateLoading() async {
    await Future.delayed(const Duration(milliseconds: 1500));
    setState(() {
      isLoading = false;
    });
  }

  // Scroll listener for pagination
  void _onScroll() {
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200) {
      loadMoreData();
    }
  }

  // Load more data when scrolling near the bottom
  Future<void> loadMoreData() async {
    if (isLoadingMore || !hasMoreData) return;

    setState(() {
      isLoadingMore = true;
    });

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Check if we have more data to load
      if (displayedProducts.length < allProducts.length) {
        final nextBatch = allProducts
            .take(displayedProducts.length + pageSize)
            .toList();

        setState(() {
          displayedProducts = nextBatch;
          isLoadingMore = false;
        });
      } else {
        setState(() {
          hasMoreData = false;
          isLoadingMore = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoadingMore = false;
      });
    }
  }

  // Refresh data and reset pagination
  void refreshData() {
    setState(() {
      isLoading = true;
      currentPage = 1;
      hasMoreData = true;
      allProducts.clear();
      displayedProducts.clear();
    });

    _simulateLoading();
    // Trigger a rebuild to fetch fresh data
    final selectedCategoryId = ref.read(selectedCategoryIdProvider);
    ref.invalidate(blindboxProductsProvider(selectedCategoryId));
  }

  void applyFilters(Map<String, dynamic> filters) {
    setState(() {
      currentFilters = filters;
      // Reset pagination when filters change
      currentPage = 1;
      hasMoreData = true;
      displayedProducts.clear();
    });
  }

  List<BlindboxProductModel> filterProducts(
    List<BlindboxProductModel> products,
    Map<String, dynamic> filters,
  ) {
    List<BlindboxProductModel> filtered = List.from(products);

    // Filter by price range
    if (filters['minPrice'] != null || filters['maxPrice'] != null) {
      filtered = filtered.where((product) {
        double price = product.salePrice;
        bool minCheck =
            filters['minPrice'] == null || price >= filters['minPrice'];
        bool maxCheck =
            filters['maxPrice'] == null || price <= filters['maxPrice'];
        return minCheck && maxCheck;
      }).toList();
    }

    // Filter by category
    if (filters['category'] != null && filters['category'] != 'All') {
      // Note: You might want to add a category field to your BlindboxProductModel
      // For now, we'll skip category filtering
    }

    // Sort products
    if (filters['sortBy'] != null) {
      switch (filters['sortBy']) {
        case 'Price: Low to High':
          filtered.sort((a, b) => a.salePrice.compareTo(b.salePrice));
          break;
        case 'Price: High to Low':
          filtered.sort((a, b) => b.salePrice.compareTo(a.salePrice));
          break;
        case 'Newest':
          filtered.sort((a, b) => a.sold.compareTo(b.sold));
          break;
        case 'Popularity':
        default:
          filtered.sort((a, b) => b.sold.compareTo(a.sold));
          break;
      }
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final selectedCategoryId = ref.read(selectedCategoryIdProvider);
    final productsAsync = ref.watch(
      blindboxProductsProvider(selectedCategoryId),
    );

    return productsAsync.when(
      data: (response) {
        final products = response['products'] as List<BlindboxProductModel>;
        final filteredProducts = currentFilters != null
            ? filterProducts(products, currentFilters!)
            : products;

        // Initialize products for pagination if not already done
        if (allProducts.isEmpty) {
          allProducts = List.from(filteredProducts);
          displayedProducts = allProducts.take(pageSize).toList();
        } else if (currentFilters != null) {
          // Update products when filters change
          allProducts = List.from(filteredProducts);
          displayedProducts = allProducts.take(pageSize).toList();
        }

        // Check if we have more data
        hasMoreData = displayedProducts.length < allProducts.length;

        return widget.child;
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error: ${error.toString()}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                final selectedCategoryId = ref.read(selectedCategoryIdProvider);
                ref.refresh(blindboxProductsProvider(selectedCategoryId));
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  // Getter methods for the view to access pagination state
  List<BlindboxProductModel> get products => displayedProducts;
  bool get isInitialLoading => isLoading;
  bool get isLoadMoreLoading => isLoadingMore;
  bool get canLoadMore => hasMoreData;
  ScrollController get scrollControllerInstance => scrollController;
}
