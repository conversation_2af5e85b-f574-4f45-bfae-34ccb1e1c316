# Mega Prizes API Implementation

## Overview
This document describes the implementation of the mega prizes API integration for the Prize Reveal View in the LuckyMall Flutter app.

## API Endpoint
- **URL**: `https://api.luckymall.com.my/product/point/mega`
- **Method**: GET
- **Response**: Returns 3 mega prizes in order (1st = position 1, 2nd = position 2, 3rd = position 3)

## API Response Structure
```json
{
  "status": 200,
  "message": "success",
  "data": [
    {
      "id": 4,
      "productSkuName": "TNG 5000 Cash Voucher",
      "productSkuImage": "https://files.luckymall.my/TNG/5000.jpg",
      "usedAmount": 0,
      "totalAmount": 5500
    },
    {
      "id": 6,
      "productSkuName": "TNG 2000 Cash Voucher",
      "productSkuImage": "https://files.luckymall.my/TNG/2000.jpg",
      "usedAmount": 0,
      "totalAmount": 2200
    },
    {
      "id": 5,
      "productSkuName": "TNG 1000 Cash Voucher",
      "productSkuImage": "https://files.luckymall.my/TNG/1000.jpg",
      "usedAmount": 0,
      "totalAmount": 1100
    }
  ],
  "timestamp": 1755755223706
}
```

## Implementation Details

### 1. Data Model (`MegaPrize.dart`)
- **Fields**: Only includes fields from API response (no hardcoded fields)
- **Properties**: `id`, `productSpuId`, `productSkuId`, `productSkuName`, `productSkuImage`, `usedAmount`, `totalAmount`
- **No fallback data**: Model only contains API response fields

### 2. Service Layer (`LuckyDrawService.dart`)
- **Method**: `getMegaPrizes()`
- **API Call**: GET request to `https://api.luckymall.com.my/product/point/mega`
- **Error Handling**: Comprehensive error handling with detailed logging

### 3. Repository Layer (`LuckyDrawRepository.dart`)
- **Abstract Method**: `fetchMegaPrizes()` in base class
- **Implementation**: `MockLuckyDrawRepository` calls real API service
- **Fallback**: Returns empty list if API fails

### 4. View Model (`PrizeRevealVM.dart`)
- **State Management**: `_megaPrizes` list and `_isMegaPrizesLoading` boolean
- **Data Fetching**: `fetchMegaPrizes()` method processes API response
- **Initialization**: Fetches mega prizes during app startup
- **Refresh**: Includes mega prizes in `refreshAll()` method

### 5. View Layer (`PrizeRevealView.dart`)
- **Widget Usage**: `MegaPrizes(prizes: vm.megaPrizes)`
- **No Fallback**: Passes empty list directly to widget
- **Loading State**: Shows shimmer effect while loading

### 6. Widget (`MegaPrizes.dart`)
- **Empty State Handling**: Shows "No mega prizes for now" placeholder for each ranking
- **Responsive Design**: Adapts to different screen sizes
- **Podium Layout**: 1st, 2nd, and 3rd place positions with appropriate heights
- **Navigation**: JOIN buttons navigate to respective product details pages

## Key Features

### ✅ **API Integration**
- Real-time data fetching from `https://api.luckymall.com.my/product/point/mega`
- Proper error handling and logging
- Loading states with shimmer effects

### ✅ **No Hardcoded Data**
- Model only contains fields from API response
- No fallback to default/mock data
- Clean, API-driven implementation

### ✅ **Empty State Handling**
- Graceful display when no prizes available
- "No mega prizes for now" placeholders for each ranking
- Maintains visual structure even without data

### ✅ **Responsive Design**
- Adapts to different screen sizes
- Maintains podium proportions
- Consistent visual hierarchy

### ✅ **State Management**
- Proper loading states
- Error handling
- Refresh functionality

### ✅ **Navigation & User Experience**
- JOIN buttons navigate to product details pages
- Automatic success messages when joining prizes
- Error handling for navigation failures
- Position-based prize identification

## Usage

### Basic Implementation
```dart
// In your view
MegaPrizes(
  prizes: viewModel.megaPrizes,
  onPrizeJoin: () {
    // Optional callback for additional logic
    // Navigation is handled automatically by the widget
  },
)
```

### With Loading State
```dart
if (viewModel.isMegaPrizesLoading) {
  return const MegaPrizesShimmer();
} else {
  return MegaPrizes(prizes: viewModel.megaPrizes);
}
```

### Navigation Behavior
When a user clicks the JOIN button:
1. **Original Callback**: Executes `onPrizeJoin` if provided
2. **Navigation**: Automatically navigates to `/lucky-draw/product-detail`
3. **Data Passing**: Passes complete prize data including:
   - All API fields (id, productSpuId, productSkuId, etc.)
   - `isMegaPrize: true` flag
   - `position` (1st, 2nd, or 3rd place)
4. **User Feedback**: Shows success message with prize name
5. **Error Handling**: Graceful fallback if navigation fails

## Testing

### API Response Scenarios
1. **Success**: 3 prizes returned - displays normally with working JOIN buttons
2. **Empty**: No prizes - shows "No mega prizes for now" placeholders, JOIN buttons disabled
3. **Partial**: 1-2 prizes - shows available prizes + placeholders, only available prizes have working JOIN buttons
4. **Error**: API failure - shows placeholders (handled gracefully)

### Navigation Testing
1. **Click JOIN**: Verify navigation to product details page
2. **Data Passing**: Check that prize data is correctly passed
3. **Position Tracking**: Verify correct position (1st, 2nd, 3rd) is passed
4. **Error Scenarios**: Test with invalid navigation routes

### Manual Testing
1. Start app - mega prizes load from API
2. Pull to refresh - data refreshes
3. Click JOIN buttons - verify navigation and data passing
4. Check network - verify API calls
5. Test empty state - disconnect network to see placeholders

## Future Enhancements

### Potential Improvements
- **Caching**: Store prizes locally for offline access
- **Real-time Updates**: WebSocket integration for live updates
- **Analytics**: Track user interactions with prizes
- **A/B Testing**: Different prize display layouts
- **Personalization**: User-specific prize recommendations

### API Enhancements
- **Pagination**: Handle more than 3 prizes
- **Filtering**: Category-based prize filtering
- **Search**: Prize search functionality
- **Sorting**: Different ranking algorithms

### Navigation Enhancements
- **Deep Linking**: Direct links to specific prizes
- **History**: Track user's prize viewing history
- **Favorites**: Allow users to favorite specific prizes
- **Sharing**: Share prize links with other users

## Troubleshooting

### Common Issues
1. **No prizes displayed**: Check API endpoint and network connectivity
2. **Loading stuck**: Verify API response format and error handling
3. **Layout issues**: Check responsive design breakpoints
4. **Performance**: Monitor API response times and caching
5. **Navigation fails**: Verify route configuration and data format

### Debug Information
- Check console logs for API calls and responses
- Verify ViewModel state changes
- Monitor network requests in browser dev tools
- Test with different screen sizes
- Check navigation route configuration
- Verify data format being passed to product details page
