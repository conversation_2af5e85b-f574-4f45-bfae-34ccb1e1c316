import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../data/model/OrderModel.dart';

class OrderStatusBadge extends StatelessWidget {
  final OrderType orderType;
  final DeliveryMethod deliveryMethod;
  final OrderStatus? orderStatus;
  final RefundStatus? refundStatus;
  final double? iconSize;

  const OrderStatusBadge({
    super.key,
    required this.orderType,
    required this.deliveryMethod,
    this.orderStatus,
    this.refundStatus,
    this.iconSize,
  });

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  String _getOrderTypeIcon() {
    switch (orderType) {
      case OrderType.storeOrder:
        return 'assets/icons/nav/ShopInactive.svg';
      case OrderType.blindBox:
        return 'assets/icons/nav/BlindBoxInactive.svg';
      case OrderType.prize:
        return 'assets/icons/nav/LuckyDrawInactive.svg';
      case OrderType.luckyGroupBuy:
        return 'assets/icons/nav/ShopInactive.svg'; // Using shop icon as placeholder
      case OrderType.physicalCard:
        return 'assets/icons/nav/CardInactive.svg';
    }
  }

  Color _getDeliveryMethodColor() {
    switch (deliveryMethod) {
      case DeliveryMethod.delivery:
        return const Color(0xFF10B981); // Green for delivery
      case DeliveryMethod.pickup:
        return const Color(0xFFF59E0B); // Amber for pickup
    }
  }

  Color _getOrderStatusColor() {
    if (orderStatus == null) return Colors.grey;

    switch (orderStatus!) {
      case OrderStatus.toPay:
        return const Color(0xFFEF4444); // Red for urgent
      case OrderStatus.toShip:
        return const Color(0xFF3B82F6); // Blue for processing
      case OrderStatus.toReceive:
        return const Color(0xFF8B5CF6); // Purple for in transit
      case OrderStatus.completed:
        return const Color(0xFF10B981); // Green for completed
      case OrderStatus.cancelled:
        return const Color(0xFF6B7280); // Gray for cancelled
      case OrderStatus.returnRefund:
        return _getRefundStatusColor(); // Use specific refund status color
    }
  }

  Color _getRefundStatusColor() {
    if (refundStatus == null) return const Color(0xFFF59E0B); // Default amber

    switch (refundStatus!) {
      case RefundStatus.refundRequested:
        return const Color(0xFF3B82F6); // Blue for in progress
      case RefundStatus.refundApproved:
        return const Color(0xFF10B981); // Green for approved
      case RefundStatus.returnInProgress:
        return const Color(0xFF8B5CF6); // Purple for in progress
      case RefundStatus.productReceived:
        return const Color(0xFF059669); // Dark green for received
      case RefundStatus.refundCompleted:
      case RefundStatus.exchangeCompleted:
        return const Color(0xFF10B981); // Green for completed
      case RefundStatus.refundRejected:
        return const Color(0xFFEF4444); // Red for rejected
    }
  }

  String _getStatusDisplayText() {
    if (orderStatus == null) return '';

    if (orderStatus == OrderStatus.returnRefund && refundStatus != null) {
      return refundStatus!.displayName;
    }

    return orderStatus!.statusDisplayName;
  }

  @override
  Widget build(BuildContext context) {
    final double iconSizeValue = iconSize ?? getResponsivePadding(16, context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left side - Order type and delivery method
        Row(
          children: [
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: getResponsivePadding(8, context),
                vertical: getResponsivePadding(4, context),
              ),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300, width: 1),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    _getOrderTypeIcon(),
                    width: iconSizeValue,
                    height: iconSizeValue,
                    colorFilter: const ColorFilter.mode(
                      Colors.black54,
                      BlendMode.srcIn,
                    ),
                  ),
                  SizedBox(width: getResponsivePadding(4, context)),
                  Text(
                    orderType.typeDisplayName,
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(11, context),
                      fontWeight: FontWeight.w600,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(width: getResponsivePadding(8, context)),

            // Delivery method badge
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: getResponsivePadding(8, context),
                vertical: getResponsivePadding(4, context),
              ),
              decoration: BoxDecoration(
                color: _getDeliveryMethodColor(),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                deliveryMethod.deliveryMethodDisplayName,
                style: TextStyle(
                  fontSize: getResponsiveFontSize(11, context),
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),

        // Right side - Status badge
        if (orderStatus != null)
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: getResponsivePadding(8, context),
              vertical: getResponsivePadding(4, context),
            ),
            decoration: BoxDecoration(
              color: _getOrderStatusColor(),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _getStatusDisplayText(),
              style: TextStyle(
                fontSize: getResponsiveFontSize(11, context),
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }
}
