import 'package:flutter/material.dart';
import 'package:intl_phone_field/intl_phone_field.dart';

class AddressForm extends StatefulWidget {
  final TextEditingController recipientController;
  final TextEditingController mobileController;
  final TextEditingController postcodeController;
  final TextEditingController addressController;
  final GlobalKey<FormState> formKey;

  const AddressForm({
    super.key,
    required this.recipientController,
    required this.mobileController,
    required this.postcodeController,
    required this.addressController,
    required this.formKey,
  });

  @override
  State<AddressForm> createState() => _AddressFormState();
}

class _AddressFormState extends State<AddressForm> {
  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Recipient Name*'),
          const SizedBox(height: 4),
          TextFormField(
            controller: widget.recipientController,
            decoration: _buildInputDecoration('Please enter recipient name'),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Name is required';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          const Text('Recipient Phone Number*'),
          const SizedBox(height: 4),
          IntlPhoneField(
            controller: widget.mobileController,
            initialCountryCode: 'MY',
            showDropdownIcon: true,
            disableLengthCheck: true,
            decoration: _buildInputDecoration(
              'Please enter recipient phone number',
            ).copyWith(counterText: ''),
            keyboardType: TextInputType.phone,
            onChanged: (phone) {
              debugPrint('Phone number: ${phone.completeNumber}');
            },
          ),
          const SizedBox(height: 16),

          const Text('Postal Code*'),
          const SizedBox(height: 4),
          TextFormField(
            controller: widget.postcodeController,
            decoration: _buildInputDecoration('Please enter postal code'),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Postal code is required';
              }
              final regex = RegExp(r'^\d{5}$');
              if (!regex.hasMatch(value)) {
                return 'Invalid postal code';
              }
              final intCode = int.tryParse(value) ?? 0;
              if (intCode < 1000 || intCode > 98859) {
                return 'Invalid postal code range';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          const Text('Full Address*'),
          const SizedBox(height: 4),
          TextFormField(
            controller: widget.addressController,
            maxLines: 4,
            decoration: _buildInputDecoration('Please enter full address'),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Address is required';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  InputDecoration _buildInputDecoration(String hint) {
    return InputDecoration(
      hintText: hint,
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      fillColor: Colors.grey[200],
      filled: true,
    );
  }
}
