# Lucky Draw Category API Integration

This document describes the integration of the lucky draw product categories API endpoint.

## API Endpoint

**GET** `/product/point/categorys`

**Base URL:** `https://api.luckymall.com.my`

**Response Structure:**
```json
{
  "status": 200,
  "message": "success",
  "data": [
    {
      "categoryId": 1,
      "categoryName": "Hot Goods"
    },
    {
      "categoryId": 2,
      "categoryName": "TNG Voucher"
    },
    {
      "categoryId": 3,
      "categoryName": "Staff"
    }
  ],
  "timestamp": 1755670288684
}
```

## Implementation Files

### 1. Model
- **File:** `lib/modules/luckydraw/model/LuckyDrawCategory.dart`
- **Purpose:** Data model for lucky draw categories
- **Fields:** Maps exactly to the API response
- **Note:** `isAdult` is provided as a getter that always returns `false` for backward compatibility

### 2. Service
- **File:** `lib/modules/luckydraw/data/LuckyDrawService.dart`
- **Purpose:** Handles API calls to the categories endpoint
- **Methods:** 
  - `getCategories({bool forceRefresh = false})` - Fetch categories with caching
  - `getCategoryIdByName(String categoryName)` - Find category ID by name
  - `clearCategoriesCache()` - Clear the categories cache

### 3. Repository
- **File:** `lib/modules/luckydraw/data/LuckyDrawRepository.dart`
- **Purpose:** Abstraction layer that uses the service
- **Method:** `fetchCategories()` - Returns `List<LuckyDrawCategory>`

### 4. View Model
- **File:** `lib/modules/luckydraw/view-model/CategoryVM.dart`
- **Purpose:** Business logic and state management for categories
- **Features:** Loading states, error handling, caching, category lookups

## Usage Examples

### 1. Basic API Call

```dart
import '../data/LuckyDrawService.dart';

final service = LuckyDrawService();

try {
  final categories = await service.getCategories();
  print('Fetched ${categories.length} categories');
  
  for (final category in categories) {
    print('${category.categoryName} (ID: ${category.categoryId})');
  }
} catch (e) {
  print('Error: $e');
}
```

### 2. Using the Repository

```dart
import '../data/LuckyDrawRepository.dart';

final repository = MockLuckyDrawRepository();

try {
  final categories = await repository.fetchCategories();
  print('Repository returned ${categories.length} categories');
} catch (e) {
  print('Repository error: $e');
}
```

### 3. Using the View Model

```dart
import '../view-model/CategoryVM.dart';

final categoryVM = CategoryVM();

// Listen to changes
categoryVM.addListener(() {
  if (categoryVM.isLoading) {
    print('Loading categories...');
  } else if (categoryVM.errorMessage != null) {
    print('Error: ${categoryVM.errorMessage}');
  } else {
    print('Loaded ${categoryVM.categories.length} categories');
  }
});

// Fetch categories
await categoryVM.fetchCategories();

// Get category ID by name
final categoryId = categoryVM.getCategoryIdByName('Hot Goods');
if (categoryId != null) {
  print('Hot Goods category ID: $categoryId');
}
```

### 4. Category Filtering in Products

```dart
import '../data/LuckyDrawService.dart';

final service = LuckyDrawService();

// Get products by category name
final categoryId = await service.getCategoryIdByName('Hot Goods');
if (categoryId != null) {
  final products = await service.getLuckyDrawProducts(categoryId: categoryId);
  print('Found ${products['products'].length} products in Hot Goods category');
}
```

## Caching

The service implements automatic caching for categories:

- **Cache Duration:** 30 minutes
- **Cache Invalidation:** Automatic based on time
- **Force Refresh:** Use `getCategories(forceRefresh: true)` to bypass cache
- **Manual Clear:** Use `clearCategoriesCache()` to clear cache

## Migration from Hardcoded Categories

### Before (Hardcoded):
```dart
// Old hardcoded method
int? getCategoryId(String category) {
  switch (category.toLowerCase()) {
    case 'hot goods': return 1;
    case 'tng vouchers': return 2;
    case 'staff': return 3;
    default: return null;
  }
}
```

### After (API-based):
```dart
// New API-based method
Future<int?> getCategoryIdByName(String categoryName) async {
  final categories = await getCategories();
  final category = categories.firstWhere(
    (cat) => cat.categoryName.toLowerCase() == categoryName.toLowerCase(),
    orElse: () => LuckyDrawCategory(categoryId: -1, categoryName: ''),
  );
  return category.categoryId != -1 ? category.categoryId : null;
}
```

## Testing

Run the category API tests:

```bash
flutter test lib/modules/luckydraw/test/category_api_test.dart
```

## Error Handling

The implementation includes comprehensive error handling:

- **Network Errors:** DioException handling with user-friendly messages
- **API Errors:** Status code and message validation
- **Fallbacks:** Empty lists returned on errors to prevent app crashes
- **Logging:** Detailed logging for debugging

## Benefits

1. **Dynamic Categories:** Categories are now fetched from the server instead of being hardcoded
2. **Real-time Updates:** Category changes on the server are reflected in the app
3. **Better Performance:** Caching reduces unnecessary API calls
4. **Maintainability:** No need to update app code when categories change
5. **Scalability:** Easy to add new categories without app updates
6. **Accurate Data:** Categories now match exactly what's in your database
