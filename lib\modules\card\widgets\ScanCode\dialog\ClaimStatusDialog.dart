import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../model/scanCode/ScanCodeModel.dart';

class ClaimStatusDialog extends StatelessWidget {
  final ScanCodeModel model;

  const ClaimStatusDialog({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    final claimedAt = model.claimedAt ?? DateTime.now();
    final formattedDate = DateFormat('yyyy-MM-dd').format(claimedAt);
    final formattedTime = DateFormat('HH:mm').format(claimedAt);

    return AlertDialog(
      title: const Text('Code Already Redeemed'),
      content: RichText(
        text: TextSpan(
          style: Theme.of(context).textTheme.bodyMedium,
          children: [
            const TextSpan(text: 'The code '),
            TextSpan(
              text: model.code,
              style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.red),
            ),
            const TextSpan(text: ' was already claimed on '),
            TextSpan(
              text: '$formattedDate at $formattedTime',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const TextSpan(text: '.'),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('OK'),
        ),
      ],
    );
  }
}
