# Lucky Draw Category API Integration - Implementation Summary

## Overview

This implementation replaces the hardcoded category system in the Lucky Draw module with a dynamic API-based system that fetches categories from `https://api.luckymall.com.my/product/point/categorys`.

## What Was Implemented

### 1. **LuckyDrawCategory Model** (`lib/modules/luckydraw/model/LuckyDrawCategory.dart`)
- New data model that matches the API response structure
- Includes `categoryId`, `categoryName`, and `isAdult` fields
- Provides backward compatibility getters (`id`, `name`, `displayName`)

### 2. **Updated LuckyDrawService** (`lib/modules/luckydraw/data/LuckyDrawService.dart`)
- Added `getCategories({bool forceRefresh = false})` method
- Added `getCategoryIdByName(String categoryName)` method
- Implemented automatic caching (30-minute cache duration)
- Added `clearCategoriesCache()` method
- Deprecated the old hardcoded `getCategoryId()` method

### 3. **Updated LuckyDrawRepository** (`lib/modules/luckydraw/data/LuckyDrawRepository.dart`)
- Changed `fetchCategories()` return type from `List<Map<String, dynamic>>` to `List<LuckyDrawCategory>`
- Updated all category filtering methods to use the new API-based approach
- Replaced hardcoded category ID lookups with dynamic API calls

### 4. **New CategoryVM** (`lib/modules/luckydraw/view-model/CategoryVM.dart`)
- View model for managing category state and caching
- Provides loading states, error handling, and category lookups
- Implements local caching for better performance
- Filters out adult content by default

### 5. **Updated CategorySelectorWidget** (`lib/modules/luckydraw/widgets/CategorySelectorWidget.dart`)
- Now fetches categories from the API instead of using hardcoded values
- Shows loading state while fetching categories
- Provides fallback to default categories if API fails
- Automatically filters out adult content

### 6. **Test Files**
- `lib/modules/luckydraw/test/category_api_test.dart` - Unit tests for the API integration
- `lib/modules/luckydraw/example_usage.dart` - Example implementations showing how to use the new system

## API Response Structure

The implementation expects this API response format:

```json
{
  "status": 0,
  "message": "",
  "data": [
    {
      "categoryId": 0,
      "categoryName": "",
      "isAdult": true
    }
  ],
  "timestamp": 0
}
```

## Key Benefits

1. **Dynamic Categories**: Categories are now fetched from the server instead of being hardcoded
2. **Real-time Updates**: Category changes on the server are reflected in the app
3. **Better Performance**: Caching reduces unnecessary API calls
4. **Maintainability**: No need to update app code when categories change
5. **Scalability**: Easy to add new categories without app updates
6. **Adult Content Filtering**: Built-in support for filtering adult content

## Migration from Hardcoded System

### Before (Hardcoded):
```dart
// Old hardcoded method
int? getCategoryId(String category) {
  switch (category.toLowerCase()) {
    case 'hot goods': return 1;
    case 'tng vouchers': return 2;
    case 'staff': return 3;
    default: return null;
  }
}
```

### After (API-based):
```dart
// New API-based method
Future<int?> getCategoryIdByName(String categoryName) async {
  final categories = await getCategories();
  final category = categories.firstWhere(
    (cat) => cat.categoryName.toLowerCase() == categoryName.toLowerCase(),
    orElse: () => LuckyDrawCategory(categoryId: -1, categoryName: '', isAdult: false),
  );
  return category.categoryId != -1 ? category.categoryId : null;
}
```

## Usage Examples

### Basic API Call:
```dart
final service = LuckyDrawService();
final categories = await service.getCategories();
```

### Using the Repository:
```dart
final repository = MockLuckyDrawRepository();
final categories = await repository.fetchCategories();
```

### Using the View Model:
```dart
final categoryVM = CategoryVM();
await categoryVM.fetchCategories();
final categoryId = categoryVM.getCategoryIdByName('Electronics');
```

### Category Filtering:
```dart
// Get products by category name
final categoryId = await service.getCategoryIdByName('Electronics');
if (categoryId != null) {
  final products = await service.getLuckyDrawProducts(categoryId: categoryId);
}
```

## Caching Strategy

- **Cache Duration**: 30 minutes
- **Cache Invalidation**: Automatic based on time
- **Force Refresh**: Use `getCategories(forceRefresh: true)` to bypass cache
- **Manual Clear**: Use `clearCategoriesCache()` to clear cache

## Error Handling

The implementation includes comprehensive error handling:

- **Network Errors**: DioException handling with user-friendly messages
- **API Errors**: Status code and message validation
- **Fallbacks**: Empty lists returned on errors to prevent app crashes
- **Logging**: Detailed logging for debugging

## Testing

Run the category API tests:

```bash
flutter test lib/modules/luckydraw/test/category_api_test.dart
```

## Files Modified

1. `lib/modules/luckydraw/model/LuckyDrawCategory.dart` - **NEW**
2. `lib/modules/luckydraw/data/LuckyDrawService.dart` - **UPDATED**
3. `lib/modules/luckydraw/data/LuckyDrawRepository.dart` - **UPDATED**
4. `lib/modules/luckydraw/view-model/CategoryVM.dart` - **NEW**
5. `lib/modules/luckydraw/widgets/CategorySelectorWidget.dart` - **UPDATED**
6. `lib/modules/luckydraw/test/category_api_test.dart` - **NEW**
7. `lib/modules/luckydraw/example_usage.dart` - **NEW**
8. `lib/modules/luckydraw/README_CATEGORY_API.md` - **NEW**

## Next Steps

1. **Test the Integration**: Run the tests and verify API connectivity
2. **Update Views**: Ensure all views that use categories are properly updated
3. **Monitor Performance**: Watch for any performance issues with the new async calls
4. **Add Error Handling**: Consider adding user-facing error messages for failed API calls
5. **Optimize Caching**: Adjust cache duration based on real-world usage patterns

## Notes

- The old hardcoded `getCategoryId()` method is deprecated but kept for backward compatibility
- All category filtering now requires async operations
- The system automatically filters out adult content by default
- Fallback categories are provided if the API fails
- Comprehensive logging is added for debugging purposes
