import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:luckymall/modules/luckydraw/widgets/DrawRecordCard.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';
import 'package:luckymall/modules/luckydraw/view-model/LatestDrawRecordVM.dart';
import 'package:shimmer/shimmer.dart';

class LatestDrawRecordView extends StatelessWidget {
  const LatestDrawRecordView({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => LatestDrawRecordVM()..initialize(),
      child: Consumer<LatestDrawRecordVM>(
        builder: (context, vm, _) {
          if (vm.isLoading) {
            return Scaffold(
              appBar: const CustomAppBar(title: 'Draw Records'),
              backgroundColor: const Color(0xFFDEDEDE),
              body: ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: 16),
                itemCount: 3,
                itemBuilder: (context, index) => const Padding(
                  padding: EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                  child: DrawRecordCardShimmer(),
                ),
              ),
            );
          }

          if (vm.hasError) {
            return Scaffold(
              appBar: const CustomAppBar(title: 'Draw Records'),
              backgroundColor: const Color(0xFFDEDEDE),
              body: Center(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Failed to load draw records',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(color: Colors.red[700]),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        vm.errorMessage,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: vm.retry,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Retry'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue[600],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }

          return Scaffold(
            appBar: const CustomAppBar(title: 'Draw Records'),
            backgroundColor: const Color(0xFFDEDEDE),
            body: vm.records.isEmpty
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.all(24.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.inbox_outlined,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No draw records found',
                            style: TextStyle(fontSize: 18, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    itemCount: vm.records.length,
                    itemBuilder: (context, index) {
                      final record = vm.records[index];
                      return DrawRecordCard(
                        username: record.username,
                        productTitle: record.productTitle,
                        date: record.date,
                        time: record.time,
                        period: record.period,
                        totalShares: record.totalShares,
                        participationPoint: record.participationPoint,
                        winningName: record.winningName,
                        imageUrl: record.imageUrl,
                      );
                    },
                  ),
          );
        },
      ),
    );
  }
}

class DrawRecordCardShimmer extends StatelessWidget {
  const DrawRecordCardShimmer({super.key});
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375;
    double scaleH(double value) => value * screenHeight / 812;
    // double scaleText(double value) => value * screenWidth / 375;
    Color baseColor = Colors.grey[300]!;
    Color highlightColor = Colors.grey[100]!;
    Widget shimmerBox({
      double? width,
      double? height,
      BorderRadius? borderRadius,
      BoxShape shape = BoxShape.rectangle,
    }) {
      return Shimmer.fromColors(
        baseColor: baseColor,
        highlightColor: highlightColor,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: baseColor,
            borderRadius: shape == BoxShape.circle
                ? null
                : (borderRadius ?? BorderRadius.circular(4)),
            shape: shape,
          ),
        ),
      );
    }

    return Container(
      margin: EdgeInsets.symmetric(vertical: scaleH(6)),
      padding: EdgeInsets.all(scaleW(8)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(scaleW(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: scaleW(6),
            offset: Offset(0, scaleH(2)),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top Row: Avatar + User + Title
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              shimmerBox(
                width: scaleW(36),
                height: scaleW(36),
                shape: BoxShape.circle,
              ),
              SizedBox(width: scaleW(12)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    shimmerBox(width: scaleW(80), height: scaleH(12)),
                    SizedBox(height: scaleH(4)),
                    shimmerBox(width: scaleW(160), height: scaleH(14)),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: scaleH(16)),
          // Image and Description Grid
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              shimmerBox(
                width: scaleW(100),
                height: scaleW(100),
                borderRadius: BorderRadius.circular(scaleW(8)),
              ),
              SizedBox(width: scaleW(8)),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          shimmerBox(width: scaleW(70), height: scaleH(10)),
                          SizedBox(height: scaleH(6)),
                          shimmerBox(width: scaleW(60), height: scaleH(10)),
                          SizedBox(height: scaleH(6)),
                          shimmerBox(width: scaleW(50), height: scaleH(10)),
                        ],
                      ),
                    ),
                    SizedBox(width: scaleW(8)),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          shimmerBox(width: scaleW(80), height: scaleH(10)),
                          SizedBox(height: scaleH(6)),
                          shimmerBox(width: scaleW(70), height: scaleH(10)),
                          SizedBox(height: scaleH(6)),
                          shimmerBox(width: scaleW(100), height: scaleH(10)),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: scaleH(8)),
          // Winning Name Section
          shimmerBox(
            width: double.infinity,
            height: scaleH(28),
            borderRadius: BorderRadius.circular(scaleW(8)),
          ),
        ],
      ),
    );
  }
}
