class PurchaseCardOrder {
  final String gateway;
  final String name;
  final String phone;
  final String email;
  final int quantity;
  final double total;
  final DateTime timestamp;

  PurchaseCardOrder({
    required this.gateway,
    required this.name,
    required this.phone,
    required this.email,
    required this.quantity,
    required this.total,
    required this.timestamp,
  });

  // Optional: Convert to JSON for API or storage
  Map<String, dynamic> toJson() {
    return {
      'gateway': gateway,
      'name': name,
      'phone': phone,
      'email': email,
      'quantity': quantity,
      'total': total,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
