class LuckyDrawRecord {
  final String username;
  final String productTitle;
  final String date;
  final String time;
  final String period;
  final String totalShares;
  final String participationPoint;
  final String winningName;
  final String imageUrl;

  const LuckyDrawRecord({
    required this.username,
    required this.productTitle,
    required this.date,
    required this.time,
    required this.period,
    required this.totalShares,
    required this.participationPoint,
    required this.winningName,
    required this.imageUrl,
  });

  factory LuckyDrawRecord.fromJson(Map<String, dynamic> json) {
    return LuckyDrawRecord(
      username: json['username']?.toString() ?? '',
      productTitle: json['productTitle']?.toString() ?? '',
      date: json['date']?.toString() ?? '',
      time: json['time']?.toString() ?? '',
      period: json['period']?.toString() ?? '',
      totalShares: json['totalShares']?.toString() ?? '',
      participationPoint: json['participationPoint']?.toString() ?? '',
      winningName: json['winningName']?.toString() ?? '',
      imageUrl: json['imageUrl']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'productTitle': productTitle,
      'date': date,
      'time': time,
      'period': period,
      'totalShares': totalShares,
      'participationPoint': participationPoint,
      'winningName': winningName,
      'imageUrl': imageUrl,
    };
  }
}
