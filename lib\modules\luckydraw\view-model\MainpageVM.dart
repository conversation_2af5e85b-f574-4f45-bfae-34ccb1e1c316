import 'package:flutter/material.dart';
import '../data/LuckyDrawRepository.dart';
import '../data/LuckyDrawService.dart';
import '../data/LatestDrawRecordService.dart';
import '../model/LuckyDrawProduct.dart';
import '../model/LatestDrawSummary.dart';

class MainpageVM extends ChangeNotifier {
  final LuckyDrawRepository repository;
  final LatestDrawRecordService _latestDrawService = LatestDrawRecordService();

  MainpageVM({LuckyDrawRepository? repository})
    : repository = repository ?? MockLuckyDrawRepository();

  String selectedCategory = 'ALL';
  int minPointFilter = 0;
  int maxPointFilter = 999999;

  bool isReviewsLoading = true;
  bool isLatestDrawsLoading = true;
  bool isCommunitySharingLoading = true;
  bool isPointProductsLoading = true;

  List<LuckyDrawProduct> _allProducts = [];
  List<LuckyDrawProduct> _filteredProducts = [];
  List<LuckyDrawProduct> get allProducts => _allProducts;
  List<LuckyDrawProduct> get filteredProducts => _filteredProducts;

  // Latest draws data
  List<LatestDrawSummary> _latestDraws = [];
  List<LatestDrawSummary> get latestDraws => _latestDraws;

  Future<void> initialize() async {
    _simulateLoadingStages();
    await Future.wait([fetchProducts(), fetchLatestDraws()]);
  }

  Future<void> fetchProducts() async {
    isPointProductsLoading = true;
    notifyListeners();

    try {
      print(
        'MainpageVM: Fetching products with category: $selectedCategory, minPoints: $minPointFilter, maxPoints: $maxPointFilter',
      );

      // Test API connectivity first
      final apiTest = await luckyDrawService.testAPI();
      print('MainpageVM: API test result: $apiTest');

      // Use the new pagination method with filters
      final result = await repository.fetchProductsWithPagination(
        category: selectedCategory == 'ALL' ? null : selectedCategory,
        minPoints: minPointFilter,
        maxPoints: maxPointFilter,
        page: 1,
        limit: 50, // Fetch more products for better filtering
      );

      print('MainpageVM: API result: $result');
      print('MainpageVM: API result type: ${result.runtimeType}');
      print('MainpageVM: API result keys: ${result.keys}');

      final productsList = result['products'];
      print('MainpageVM: Products list from result: $productsList');
      print('MainpageVM: Products list type: ${productsList.runtimeType}');

      _allProducts = (productsList as List<LuckyDrawProduct>?) ?? [];
      print('MainpageVM: Products fetched: ${_allProducts.length}');
      print('MainpageVM: All products: $_allProducts');
      _applyFilters();
    } catch (e) {
      print('MainpageVM: Error fetching products: $e');
      // Fallback to basic fetch if pagination fails
      try {
        _allProducts = await repository.fetchProducts();
        print('MainpageVM: Fallback products fetched: ${_allProducts.length}');
      } catch (fallbackError) {
        print('MainpageVM: Fallback also failed: $fallbackError');
        // Create some dummy data for testing
        _allProducts = [
          LuckyDrawProduct(
            id: 1,
            productSpuId: 1001,
            productSkuId: 2001,
            productSkuName: 'Test Gaming Mouse',
            productSkuImage:
                'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46',
            usedAmount: 50,
            totalAmount: 100,
            score: 4.5,
            sold: 10,
          ),
          LuckyDrawProduct(
            id: 2,
            productSpuId: 1002,
            productSkuId: 2002,
            productSkuName: 'Test Wireless Headphones',
            productSkuImage:
                'https://images.unsplash.com/photo-1505740420928-5e560c06d30e',
            usedAmount: 75,
            totalAmount: 150,
            score: 4.8,
            sold: 25,
          ),
        ];
        print(
          'MainpageVM: Created ${_allProducts.length} dummy products for testing',
        );
      }
      _applyFilters();
    }

    isPointProductsLoading = false;
    notifyListeners();
  }

  /// Fetch latest draws from the API
  Future<void> fetchLatestDraws() async {
    isLatestDrawsLoading = true;
    notifyListeners();

    try {
      print('MainpageVM: Fetching latest draws...');
      final result = await _latestDrawService.getLatestDrawSummaries();

      final recordsList = result['records'];
      if (recordsList is List<LatestDrawSummary>) {
        _latestDraws = recordsList;
      } else {
        // Handle case where records might be in different format
        _latestDraws = [];
      }

      print('MainpageVM: Latest draws fetched: ${_latestDraws.length}');
    } catch (e) {
      print('MainpageVM: Error fetching latest draws: $e');
      _latestDraws = [];
    }

    isLatestDrawsLoading = false;
    notifyListeners();
  }

  void setCategory(String category) {
    selectedCategory = category;
    fetchProducts(); // Fetch new data with category filter
  }

  void setPointRange(int min, int max) {
    minPointFilter = min;
    maxPointFilter = max;
    fetchProducts(); // Fetch new data with point range filter
  }

  void _applyFilters() {
    _filteredProducts = _allProducts.where((product) {
      // Note: Since we removed category and pointValue from the model,
      // we'll need to implement category and point filtering through the API
      // For now, we'll show all products and implement proper filtering later
      return true;
    }).toList();
  }

  Future<void> refreshAll() async {
    isReviewsLoading = true;
    isLatestDrawsLoading = true;
    isCommunitySharingLoading = true;
    isPointProductsLoading = true;
    notifyListeners();
    await Future.wait([
      fetchProducts(),
      fetchLatestDraws(),
      Future.delayed(const Duration(milliseconds: 800)),
    ]);
    _simulateLoadingStages();
  }

  void _simulateLoadingStages() {
    Future.delayed(const Duration(milliseconds: 1500), () {
      isReviewsLoading = false;
      notifyListeners();
    });
    Future.delayed(const Duration(milliseconds: 2500), () {
      isCommunitySharingLoading = false;
      notifyListeners();
    });
  }
}
