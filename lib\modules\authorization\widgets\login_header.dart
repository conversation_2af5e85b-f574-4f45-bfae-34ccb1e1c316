import 'package:flutter/material.dart';

class LoginHeader extends StatelessWidget {
  const LoginHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Column(
      children: [
        const SizedBox(height: 40),
        // Top Illustration
        Center(
          child: Image.asset(
            'assets/images/LoginImage.png',
            width: size.width * 0.7,
            fit: BoxFit.contain,
          ),
        ),
        const SizedBox(height: 32),
      ],
    );
  }
}
