class PrizeRevealProduct {
  final int productId; // Maps to API 'id' field (period of luckydrawproduct)
  final String productName; // Maps to API 'productName' field
  final String imageUrl; // Maps to API 'productImage' field
  final String countdownSeconds; // Maps to API 'prizeDrawTime' field
  final int currentBatch; // Same as productId (period)
  final int? nextBatch; // Maps to API 'nextId' field (0 means no next batch)

  const PrizeRevealProduct({
    required this.productId,
    required this.productName,
    required this.imageUrl,
    required this.countdownSeconds,
    required this.currentBatch,
    this.nextBatch,
  });

  factory PrizeRevealProduct.fromJson(Map<String, dynamic> json) {
    return PrizeRevealProduct(
      productId: json['id'] as int? ?? 0,
      productName: json['productName']?.toString() ?? '',
      imageUrl: json['productImage']?.toString() ?? '',
      countdownSeconds: json['prizeDrawTime']?.toString() ?? '',
      currentBatch: json['id'] as int? ?? 0, // Same as productId
      nextBatch: json['nextId'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': productId,
      'productName': productName,
      'productImage': imageUrl,
      'prizeDrawTime': countdownSeconds,
      'nextId': nextBatch,
    };
  }

  PrizeRevealProduct copyWith({
    int? productId,
    String? productName,
    String? imageUrl,
    String? countdownSeconds,
    int? currentBatch,
    int? nextBatch,
  }) {
    return PrizeRevealProduct(
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      imageUrl: imageUrl ?? this.imageUrl,
      countdownSeconds: countdownSeconds ?? this.countdownSeconds,
      currentBatch: currentBatch ?? this.currentBatch,
      nextBatch: nextBatch ?? this.nextBatch,
    );
  }

  @override
  String toString() {
    return 'PrizeRevealProduct(productId: $productId, productName: $productName, countdownSeconds: $countdownSeconds)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PrizeRevealProduct && other.productId == productId;
  }

  @override
  int get hashCode => productId.hashCode;
}
