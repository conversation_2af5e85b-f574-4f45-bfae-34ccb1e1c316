import 'package:flutter/material.dart';
import '../data/LuckyDrawRepository.dart';
import '../model/LuckyDrawProduct.dart';

enum SearchFilter { relevance, latest, points }

class LuckyDrawSearchVM extends ChangeNotifier {
  final LuckyDrawRepository repository;

  LuckyDrawSearchVM({LuckyDrawRepository? repository})
    : repository = repository ?? MockLuckyDrawRepository();

  late final TextEditingController searchController;
  bool isSearching = false;
  bool hasSearched = false;
  String searchQuery = '';
  SearchFilter selectedFilter = SearchFilter.relevance;
  bool isPointsAscending = true;

  List<LuckyDrawProduct> allProducts = const [];
  List<LuckyDrawProduct> searchResults = const [];
  List<String> recentSearches = [];

  Future<void> initialize() async {
    searchController = TextEditingController();
    allProducts = await repository.fetchProducts();
    notifyListeners();
  }

  void performSearch(String query) {
    if (query.trim().isEmpty) {
      searchResults = const [];
      hasSearched = false;
      isSearching = false;
      searchQuery = '';
      notifyListeners();
      return;
    }

    isSearching = true;
    hasSearched = true;
    searchQuery = query;
    notifyListeners();

    // Use the repository search method instead of local filtering
    _performAPISearch(query);
  }

  Future<void> _performAPISearch(String query) async {
    try {
      final results = await repository.searchProducts(
        query: query,
        sortBy: _getSortByString(),
        sortOrder: isPointsAscending ? 'asc' : 'desc',
      );

      searchResults = results;
      isSearching = false;
      _applyFilterIfNeeded();
      _addRecent(query);
      notifyListeners();
    } catch (e) {
      // Fallback to local search if API fails
      searchResults = allProducts
          .where(
            (p) => p.productName.toLowerCase().contains(query.toLowerCase()),
          )
          .toList();
      isSearching = false;
      _applyFilterIfNeeded();
      _addRecent(query);
      notifyListeners();
    }
  }

  String _getSortByString() {
    switch (selectedFilter) {
      case SearchFilter.relevance:
        return 'relevance';
      case SearchFilter.latest:
        return 'created_at';
      case SearchFilter.points:
        return 'totalAmount';
      default:
        return 'relevance';
    }
  }

  void _addRecent(String query) {
    if (!recentSearches.contains(query)) {
      recentSearches = [query, ...recentSearches].take(5).toList();
    }
  }

  void clearSearch() {
    searchController.clear();
    searchQuery = '';
    searchResults = const [];
    hasSearched = false;
    isSearching = false;
    notifyListeners();
  }

  void selectFilter(SearchFilter filter) {
    if (filter == SearchFilter.points) {
      if (selectedFilter == filter) {
        isPointsAscending = !isPointsAscending;
      } else {
        isPointsAscending = true;
        selectedFilter = filter;
      }
    } else {
      selectedFilter = filter;
    }
    _applyFilterIfNeeded();
    notifyListeners();
  }

  void _applyFilterIfNeeded() {
    switch (selectedFilter) {
      case SearchFilter.relevance:
        // keep as is for demo
        break;
      case SearchFilter.latest:
        break;
      case SearchFilter.points:
        searchResults = [...searchResults]
          ..sort(
            (a, b) => isPointsAscending
                ? a.totalAmount.compareTo(b.totalAmount)
                : b.totalAmount.compareTo(a.totalAmount),
          );
        break;
    }
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }
}
