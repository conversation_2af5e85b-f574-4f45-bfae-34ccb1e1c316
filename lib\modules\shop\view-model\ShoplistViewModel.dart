import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../data/model/ShoplistModel.dart';
import '../../../data/service/ShoplistService.dart';

class ShoplistViewModel extends ChangeNotifier {
  List<ShopCategory> categories = [];
  List<ShopProduct> products = [];
  String selectedCategoryId = 'all';
  bool isLoading = true;
  String? errorMessage;
  final TextEditingController searchController = TextEditingController();
  
  // Pagination properties
  int currentPage = 1;
  int totalCount = 0;
  int pageSize = 10;
  int totalPages = 0;
  bool hasMoreProducts = false;
  bool isLoadingMore = false;

  ShoplistViewModel() {
    loadData();
  }

  Future<void> loadData() async {
    isLoading = true;
    errorMessage = null;
    currentPage = 1; // Reset pagination
    notifyListeners();
    
    try {
      final results = await Future.wait([
        shopService.fetchCategories(),
        shopService.fetchProducts(page: 1, limit: pageSize),
      ]);
      
      categories = results[0] as List<ShopCategory>;
      final productResult = results[1] as Map<String, dynamic>;
      
      products = productResult['products'] as List<ShopProduct>;
      totalCount = productResult['totalCount'] as int;
      pageSize = productResult['pageSize'] as int;
      totalPages = productResult['totalPage'] as int;
      currentPage = productResult['currentPage'] as int;
      hasMoreProducts = currentPage < totalPages;

      // Add "All" category at the beginning
      if (categories.isNotEmpty) {
        categories.insert(0, const ShopCategory(
          categoryId: 0,
          categoryName: "All",
          isSelected: true,
          categoryIcon: "",
          isAdult: false,
        ));
      }
      
      selectedCategoryId = '0'; // Set to "All" by default
    } catch (e) {
      errorMessage = "Error loading shop data: $e";
      print(errorMessage);
      categories = [];
      products = [];
      totalCount = 0;
      hasMoreProducts = false;
    }

    isLoading = false;
    notifyListeners();
  }

  Future<void> loadMoreProducts() async {
    if (isLoadingMore || !hasMoreProducts) return;

    isLoadingMore = true;
    notifyListeners();

    try {
      final categoryIdInt = int.tryParse(selectedCategoryId);
      
      Map<String, dynamic> result;
      if (categoryIdInt == 0 || categoryIdInt == null) {
        // Load more from all products
        result = await shopService.fetchProducts(
          page: currentPage + 1,
          limit: pageSize,
          searchQuery: searchController.text.trim().isEmpty ? null : searchController.text.trim(),
        );
      } else {
        // Load more from specific category
        result = await shopService.fetchProducts(
          page: currentPage + 1,
          limit: pageSize,
          categoryId: categoryIdInt,
        );
      }

      final newProducts = result['products'] as List<ShopProduct>;
      products.addAll(newProducts);
      
      currentPage = result['currentPage'] as int;
      totalPages = result['totalPage'] as int;
      hasMoreProducts = currentPage < totalPages;

    } catch (e) {
      print("Error loading more products: $e");
      errorMessage = "Failed to load more products";
    }

    isLoadingMore = false;
    notifyListeners();
  }

  Future<void> onCategoryTap(String categoryId) async {
    try {
      // Validate and parse categoryId
      final int? categoryIdInt = int.tryParse(categoryId);
      if (categoryIdInt == null) {
        print("Invalid category ID: $categoryId");
        return;
      }

      selectedCategoryId = categoryId;
      currentPage = 1; // Reset pagination
      
      // Update category selection
      categories = categories.map((cat) {
        return cat.copyWith(
          isSelected: cat.categoryId == categoryIdInt
        );
      }).toList();

      // Clear existing products and show loading
      products = [];
      isLoading = true;
      notifyListeners();

      // Fetch products by category using new service
      Map<String, dynamic> result;
      if (categoryIdInt == 0) {
        // "All" category - fetch all products
        result = await shopService.fetchProducts(page: 1, limit: pageSize);
      } else {
        // Specific category
        result = await shopService.fetchProducts(
          page: 1, 
          limit: pageSize, 
          categoryId: categoryIdInt
        );
      }
      
      products = result['products'] as List<ShopProduct>;
      totalCount = result['totalCount'] as int;
      pageSize = result['pageSize'] as int;
      totalPages = result['totalPage'] as int;
      currentPage = result['currentPage'] as int;
      hasMoreProducts = currentPage < totalPages;
      
      isLoading = false;
      notifyListeners();
    } catch (e) {
      print("Error in onCategoryTap: $e");
      errorMessage = "Failed to load category products";
      isLoading = false;
      notifyListeners();
    }
  }

  Future<void> onSearch() async {
    try {
      final query = searchController.text.trim();
      if (query.isEmpty) {
        await loadData();
        return;
      }
      
      // Reset category selection when searching
      selectedCategoryId = 'all';
      currentPage = 1; // Reset pagination
      categories = categories.map((cat) {
        return cat.copyWith(isSelected: false);
      }).toList();
      
      // Clear existing products and show loading
      products = [];
      isLoading = true;
      notifyListeners();
      
      // Search using new service
      final result = await shopService.fetchProducts(
        page: 1, 
        limit: pageSize, 
        searchQuery: query
      );
      
      products = result['products'] as List<ShopProduct>;
      totalCount = result['totalCount'] as int;
      pageSize = result['pageSize'] as int;
      totalPages = result['totalPage'] as int;
      currentPage = result['currentPage'] as int;
      hasMoreProducts = currentPage < totalPages;
      
      isLoading = false;
      notifyListeners();
    } catch (e) {
      print("Error in onSearch: $e");
      errorMessage = "Search failed";
      isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    errorMessage = null;
    notifyListeners();
  }

  void onProductTap(BuildContext context, ShopProduct product) {
    print("Product Tapped ID: ${product.productSpuId}");
    context.push('/shop/detail/${product.productSpuId}');
  }

  void onFavoriteTap(BuildContext context, ShopProduct product) {
    final index = products.indexWhere((p) => p.productSkuId == product.productSkuId);
    if (index != -1) {
      products[index] = product.copyWith(isFavorite: !product.isFavorite);
      notifyListeners();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            product.isFavorite
                ? 'Removed from favorites'
                : 'Added to favorites',
          ),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }
}
