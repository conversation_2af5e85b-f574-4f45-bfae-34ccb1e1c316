import 'package:flutter/material.dart';
import 'HistoryFilterBar.dart';
import 'HistoryDateRangePickerRow.dart';

class HistoryFilterSection extends StatelessWidget {
  final DateTime startDate;
  final DateTime endDate;
  final ValueChanged<DateTime> onStartDateChanged;
  final ValueChanged<DateTime> onEndDateChanged;
  final String sortOrder;
  final String selectedFilter;
  final ValueChanged<String> onSortOrderChanged;
  final ValueChanged<String> onFilterChanged;
  final bool isYearMode;
  final bool showDatePickers;

  const HistoryFilterSection({
    super.key,
    required this.startDate,
    required this.endDate,
    required this.onStartDateChanged,
    required this.onEndDateChanged,
    required this.sortOrder,
    required this.selectedFilter,
    required this.onSortOrderChanged,
    required this.onFilterChanged,
    required this.isYearMode,
    required this.showDatePickers,
  });

  @override
  Widget build(BuildContext context) {
    final scale = MediaQuery.of(context).size.width / 390;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16 * scale, vertical: 12 * scale),
      padding: EdgeInsets.symmetric(vertical: 12 * scale, horizontal: 16 * scale),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(14 * scale),
        boxShadow: const [
          BoxShadow(
            color: Color(0x22000000),
            blurRadius: 6,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          if (showDatePickers)
            HistoryDateRangePickerRow(
              startDate: startDate,
              endDate: endDate,
              onStartDateChanged: onStartDateChanged,
              onEndDateChanged: onEndDateChanged,
              isYearMode: isYearMode,
            ),
          HistoryFilterBar(
            sortOrder: sortOrder,
            selectedFilter: selectedFilter,
            onSortOrderChanged: onSortOrderChanged,
            onFilterChanged: onFilterChanged,
            showDatePickers: false,
            startDate: startDate,
            endDate: endDate,
            onStartTap: () {}, // no-op
            onEndTap: () {},   // no-op
          ),
        ],
      ),
    );
  }
}
