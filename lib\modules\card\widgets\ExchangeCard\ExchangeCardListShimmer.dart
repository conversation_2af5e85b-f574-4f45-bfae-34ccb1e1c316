import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ExchangeCardListShimmer extends StatelessWidget {
  const ExchangeCardListShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        color: Colors.white, // Background always white
        child: ListView.separated(
          padding: const EdgeInsets.all(16),
          itemCount: 2,
          separatorBuilder: (_, __) => const SizedBox(height: 12),
          itemBuilder: (context, index) {
            return Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white, // Force white inside card
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 5,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    // Image placeholder
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.white, // White base under shimmer
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Text placeholders
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(height: 16, color: Colors.white),
                          const SizedBox(height: 4),
                          Container(height: 14, width: 100, color: Colors.white),
                          const SizedBox(height: 2),
                          Container(height: 12, width: 150, color: Colors.white),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Button placeholder
                    Container(
                      width: 90,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}


