class BlindboxProductModel {
  final int unboxId; // id for blindbox
  final String unboxCover; // image for blindbox
  final String unboxTitle; // title for blindbox
  final int cardDeduction; // max card deduction (card that can be use to reduce price) for blindbox
  final double salePrice; // price for blindbox
  final double score; // this is rating
  final int sold; // sold count for blindbox
  final bool isCollect; // isCollect is basically isFavorite

  const BlindboxProductModel({
    required this.unboxId,
    required this.unboxCover,
    required this.unboxTitle,
    required this.salePrice,
    required this.score,
    required this.cardDeduction,
    required this.sold,
    required this.isCollect,
  });

  factory BlindboxProductModel.fromJson(Map<String, dynamic> json) {
    return BlindboxProductModel(
      unboxId: (json['unboxId'] as num).toInt(),
      unboxCover: json['unboxCover']?.toString() ?? '',
      unboxTitle: json['unboxTitle']?.toString() ?? '',
      salePrice: (json['salePrice'] as num).toDouble(),
      score: (json['score'] as num).toDouble(),
      cardDeduction: (json['cardDeduction'] as num).toInt(),
      sold: (json['sold'] as num).toInt(),
      isCollect: json['isCollect'] == true,
    );
  }
}

class BlindboxCategoryModel {
  final int categoryId;
  final String categoryName;
  final bool isAdult;

  const BlindboxCategoryModel({
    required this.categoryId,
    required this.categoryName,
    required this.isAdult,
  });

  factory BlindboxCategoryModel.fromJson(Map<String, dynamic> json) {
    return BlindboxCategoryModel(
      categoryId: (json['categoryId'] as num).toInt(),
      categoryName: json['categoryName']?.toString() ?? '',
      isAdult: json['isAdult'] ?? false,
    );
  }
}
