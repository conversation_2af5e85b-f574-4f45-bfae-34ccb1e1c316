import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShopCardSkeleton extends StatelessWidget {
  final double scale;
  const ShopCardSkeleton({super.key, this.scale = 1.0});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final cardWidth = constraints.maxWidth;
        double scale = cardWidth / 180.0;
        scale = scale.clamp(0.85, 1.5);

        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Card(
            elevation: 0,
            margin: const EdgeInsets.all(0),
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(14 * scale),
              side: BorderSide(
                color: Colors.grey.withOpacity(0.5),
                width: 1.3,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image section with favorite button
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(14 * scale),
                    topRight: Radius.circular(14 * scale),
                  ),
                  child: Container(
                    color: Colors.grey[300],
                    width: double.infinity,
                    height: 110 * scale,
                    child: Stack(
                      children: [
                        // Image placeholder
                        Positioned.fill(
                          child: Container(
                            color: Colors.grey[300],
                          ),
                        ),
                        // Favorite button placeholder
                        Positioned(
                          top: 8 * scale,
                          right: 8 * scale,
                          child: Container(
                            width: 26 * scale,
                            height: 26 * scale,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.95),
                              shape: BoxShape.circle,
                            ),
                            child: Container(
                              margin: EdgeInsets.all(5 * scale),
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Main content
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 10 * scale,
                    vertical: 10 * scale,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title placeholder
                      Container(
                        height: 16 * scale,
                        width: double.infinity,
                        color: Colors.grey[300],
                      ),
                      SizedBox(height: 4 * scale),
                      // Max savings placeholder
                      Container(
                        height: 18 * scale,
                        width: 90 * scale,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4 * scale),
                        ),
                      ),
                      SizedBox(height: 6 * scale),
                      // Price and discount row
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Container(
                            height: 18 * scale,
                            width: 60 * scale,
                            color: Colors.grey[300],
                          ),
                          SizedBox(width: 6 * scale),
                          Container(
                            height: 14 * scale,
                            width: 32 * scale,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(3 * scale),
                            ),
                          ),
                        ],
                      ),
                      // Original price placeholder
                      Padding(
                        padding: EdgeInsets.only(top: 2 * scale),
                        child: Container(
                          height: 12 * scale,
                          width: 50 * scale,
                          color: Colors.grey[300],
                        ),
                      ),
                    ],
                  ),
                ),
                // Bottom section for rating and sold
                Padding(
                  padding:
                      EdgeInsets.fromLTRB(10 * scale, 0, 10 * scale, 10 * scale),
                  child: Row(
                    children: [
                      Container(
                        width: 16 * scale,
                        height: 16 * scale,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: 4 * scale),
                      Container(
                        width: 20 * scale,
                        height: 12 * scale,
                        color: Colors.grey[300],
                      ),
                      SizedBox(width: 8 * scale),
                      Container(
                        width: 16 * scale,
                        height: 16 * scale,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          shape: BoxShape.circle,
                        ),
                      ),
                      Spacer(),
                      Container(
                        width: 40 * scale,
                        height: 12 * scale,
                        color: Colors.grey[300],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}