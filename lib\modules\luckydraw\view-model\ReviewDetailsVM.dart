import 'package:flutter/material.dart';
import '../data/service/ReviewService.dart';
import '../model/LuckyDrawProductReview.dart';

class ReviewDetailsVM extends ChangeNotifier {
  String selectedCategory = 'All';
  bool isLoading = true;

  // Keep data shape compatible with AllReviewCards widget
  List<Map<String, dynamic>> reviews = const [];

  Future<void> initialize() async {
    await fetchReviews();
  }

  void setCategory(String category) {
    selectedCategory = category;
    notifyListeners();
    // Refetch with new category filter
    fetchReviews();
  }

  /// Fetch latest reviews from API and map to the shape expected by AllReviewCards
  Future<void> fetchReviews({int? productSkuId, int? productSpuId}) async {
    isLoading = true;
    notifyListeners();

    try {
      // Map UI category to orderSource for API
      final int? orderSource = _mapCategoryToOrderSource(selectedCategory);

      final result = await reviewService.getLatestReviews(
        limit: 20,
        page: 1,
        orderSource: orderSource,
        productSkuId: productSkuId,
        productSpuId: productSpuId,
        fetchVariations: true,
      );

      final List<LuckyDrawProductReview> apiReviews =
          List<LuckyDrawProductReview>.from(result['reviews']);

      // Transform model objects to the map consumed by AllReviewCards
      reviews = apiReviews.map(_toWidgetMap).toList();
    } catch (e) {
      // On error, fallback to empty list (or keep last data)
      reviews = const [];
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  Map<String, dynamic> _toWidgetMap(LuckyDrawProductReview r) {
    return {
      'reviewerName': (r.reviewerName.isEmpty)
          ? 'Anonymous User'
          : r.reviewerName,
      'avatarUrl': r.avatarUrl, // empty when anonymous per API
      'date': r.date,
      'rating': r.rating.round().clamp(0, 5),
      'variation': r.variation,
      'reviewText': r.reviewText,
      'imageUrls': r.imageUrls,
      'helpfulCount': r.helpfulCount,
      // Keep existing category-based filter working by deriving label from orderType
      'category': _mapOrderSourceToCategoryLabel(r.orderType),
      'orderType': r.orderType,
    };
  }

  int? _mapCategoryToOrderSource(String category) {
    switch (category) {
      case 'Main Product':
        return 0; // Store
      case 'Point Product':
        return 10; // Point
      case 'Blind Box':
        return 20; // Blindbox
      case 'Lucky Group':
        return 30; // If used in your backend; fallback if not
      default:
        return null; // All
    }
  }

  String _mapOrderSourceToCategoryLabel(int orderSource) {
    switch (orderSource) {
      case 0:
        return 'Main Product';
      case 10:
        return 'Point Product';
      case 20:
        return 'Blind Box';
      case 30:
        return 'Lucky Group';
      default:
        return 'Main Product';
    }
  }
}
