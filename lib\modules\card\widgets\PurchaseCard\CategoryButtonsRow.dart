import 'package:flutter/material.dart';

class CategoryButtonsRow extends StatelessWidget {
  final VoidCallback onGuidelinesPressed;
  final VoidCallback onSelectCategoryPressed;

  const CategoryButtonsRow({
    super.key,
    required this.onGuidelinesPressed,
    required this.onSelectCategoryPressed,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final buttonWidth = (screenWidth / 2) - 24; // leave 12px margin between buttons

    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 16),
      child: Row(
        children: [
          // LEFT BUTTON: Purchase Guidelines
          _buildModernButton(
            label: "Purchase Guidelines",
            icon: Icons.arrow_back_ios_new,
            iconOnRight: false,
            backgroundColor: Colors.white,
            textColor: Colors.black87,
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(20),
              bottomRight: Radius.circular(20),
            ),
            width: buttonWidth,
            onPressed: onGuidelinesPressed,
            alignment: Alignment.centerLeft,
          ),

          const Spacer(), // Creates middle gap

          // RIGHT BUTTON: Select Category
          _buildModernButton(
            label: "Select Category",
            icon: Icons.arrow_forward_ios,
            iconOnRight: true,
            backgroundColor: Color.fromRGBO(252, 210, 85, 1),
            textColor: Colors.black87,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              bottomLeft: Radius.circular(20),
            ),
            width: buttonWidth,
            onPressed: onSelectCategoryPressed,
            alignment: Alignment.centerRight,
          ),
        ],
      ),
    );
  }

  Widget _buildModernButton({
    required String label,
    required IconData icon,
    required bool iconOnRight,
    required Color backgroundColor,
    required Color textColor,
    required BorderRadius borderRadius,
    required double width,
    required VoidCallback onPressed,
    required Alignment alignment,
  }) {
    return Align(
      alignment: alignment,
      child: Container(
        width: width,
        height: 50,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: borderRadius,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.15),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            elevation: 0,
            backgroundColor: backgroundColor,
            shape: RoundedRectangleBorder(borderRadius: borderRadius),
            padding: const EdgeInsets.symmetric(horizontal: 12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: iconOnRight
                ? [
                    Flexible(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          label,
                          style: TextStyle(
                            color: textColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),
                    Icon(icon, size: 16, color: textColor),
                  ]
                : [
                    Icon(icon, size: 16, color: textColor),
                    const SizedBox(width: 6),
                    Flexible(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          label,
                          style: TextStyle(
                            color: textColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                  ],
          ),
        ),
      ),
    );
  }
}
