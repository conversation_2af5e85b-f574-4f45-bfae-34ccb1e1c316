import 'cardPack/CategoryItem.dart';

class SeriesContent {
  final String seriesId;
  final String name;
  final String description;
  final List<String> imageUrls; // used in ImageSliderWithDots
  final String sheetImage;
  final List<CategoryItem> categories;

  SeriesContent({
    required this.seriesId,
    required this.name,
    required this.description,
    required this.imageUrls,
    required this.sheetImage,
    required this.categories,
  });

  factory SeriesContent.fromJson(Map<String, dynamic> json) {
    return SeriesContent(
      seriesId: json['seriesId'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      imageUrls: List<String>.from(json['imageUrls'] as List),
      sheetImage: json['sheetImage'] as String? ?? 'assets/images/LogoLM.png',
      categories: (json['categories'] as List)
          .map((cat) => CategoryItem.fromJson(cat))
          .toList(),
    );
  }
}