import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:luckymall/modules/luckydraw/widgets/LuckyDrawBanner.dart';
import 'package:luckymall/modules/blindbox/widgets/PostCard.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:luckymall/modules/blindbox/view/PostView.dart';

class CommunitySharingView extends StatelessWidget {
  const CommunitySharingView({super.key});

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  int getResponsiveCrossAxisCount(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 2;
    } else if (screenWidth < 900) {
      return 3;
    } else if (screenWidth < 1200) {
      return 4;
    } else {
      return 5;
    }
  }

  double getResponsiveHeaderHeight(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 150;
    } else if (screenWidth < 900) {
      return 180;
    } else {
      return 200;
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final crossAxisCount = getResponsiveCrossAxisCount(context);
    final headerHeight = getResponsiveHeaderHeight(context);

    // Hardcoded posts for demonstration
    final List<Map<String, dynamic>> posts = [
      {
        'imageUrl':
            'https://ae01.alicdn.com/kf/Se5a26ae5a1504b06b0c8e22049297c16X.jpg',
        'title': 'Awesome \nPrize!',
        'username': 'Alice',
        'likeCount': 12,
      },
      {
        'imageUrl':
            'https://hobiverse.com.vn/cdn/shop/articles/untitled_design__5__b2b4ba35611a49218a240c8999aec556.jpg?v=1716180382',
        'title': 'Lucky Draw Winner',
        'username': 'Bob',
        'likeCount': 8,
      },
      {
        'imageUrl':
            'https://images.summitmedia-digital.com/cosmo/images/2024/10/16/popular-pop-mart-blind-box-series-guide-1729059824.jpg',
        'title': 'Surprise Unboxing',
        'username': 'Charlie',
        'likeCount': 5,
      },
      {
        'imageUrl':
            'https://www.fifilandtoys.com/cdn/shop/files/20240520081227797.webp?v=1726077020',
        'title': 'Pop Mart \nBlind Box',
        'username': 'Charlie',
        'likeCount': 5,
      },
      {
        'imageUrl':
            'https://preview.redd.it/my-lucky-emma-collection-v0-ftt5oqnbzsib1.jpg?width=640&crop=smart&auto=webp&s=f5c7665ba10f1dddf6dfa27efd4c471ebdd38697',
        'title': 'My Lucky \nEmma Collection',
        'username': 'Charlie',
        'likeCount': 5,
      },
      {
        'imageUrl':
            'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRvD2JfTFjT0m8bQROLs-Btwhic1INEdSv87w&s',
        'title': 'My Lucky Collection',
        'username': 'Charlie',
        'likeCount': 5,
      },
      {
        'imageUrl':
            'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT16bvSJeQQbjU_FmUIdaIRTS4nuxJO4Px5ng&s',
        'title': 'Surprise Unboxing',
        'username': 'Charlie',
        'likeCount': 5,
      },
      {
        'imageUrl':
            'https://zillymonkey.com/cdn/shop/files/PTK_Web_DSC08715_1080x.jpg?v=1729811197',
        'title': 'Surprise Unboxing',
        'username': 'Charlie',
        'likeCount': 5,
      },
    ];

    return _CommunitySharingLoader(
      posts: posts,
      crossAxisCount: crossAxisCount,
      headerHeight: headerHeight,
      screenWidth: screenWidth,
      getResponsiveFontSize: getResponsiveFontSize,
      getResponsiveHeaderHeight: getResponsiveHeaderHeight,
    );
  }
}

class _CommunitySharingLoader extends StatefulWidget {
  final List<Map<String, dynamic>> posts;
  final int crossAxisCount;
  final double headerHeight;
  final double screenWidth;
  final double Function(double, BuildContext) getResponsiveFontSize;
  final double Function(BuildContext) getResponsiveHeaderHeight;
  const _CommunitySharingLoader({
    required this.posts,
    required this.crossAxisCount,
    required this.headerHeight,
    required this.screenWidth,
    required this.getResponsiveFontSize,
    required this.getResponsiveHeaderHeight,
  });
  @override
  State<_CommunitySharingLoader> createState() =>
      _CommunitySharingLoaderState();
}

class _CommunitySharingLoaderState extends State<_CommunitySharingLoader> {
  bool loading = true;
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) setState(() => loading = false);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Color(0xFFFCD255),
        clipBehavior: Clip.hardEdge,
        elevation: 0,
        toolbarHeight: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Gradient header (responsive height) with back button overlay
            SizedBox(
              width: double.infinity,
              height: widget.headerHeight,
              child: Stack(
                children: [
                  Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(0xFFFCD255),
                          Color(0xFFFCD255),
                          Color(0xFFFFEAAB),
                          Color(0xFFFFEAAB),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: [0.0, 0.46, 0.67, 1.0],
                      ),
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(50),
                        bottomRight: Radius.circular(50),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 8,
                    left: 0,
                    child: SafeArea(
                      minimum: const EdgeInsets.only(left: 12, top: 8),
                      child: _buildBackButton(context),
                    ),
                  ),
                ],
              ),
            ),
            // Overlap the banner, section titles, and grid with the gradient
            Transform.translate(
              offset: const Offset(0, -90),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: widget.screenWidth < 600 ? 15 : 20,
                    ),
                    child: LuckyDrawBanner(),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      left: widget.screenWidth < 600 ? 16 : 20,
                      right: widget.screenWidth < 600 ? 16 : 20,
                      top: 10,
                      bottom: 0,
                    ),
                    child: Text(
                      "Recent Lucky Charms by the Community",
                      style: TextStyle(
                        fontSize: widget.getResponsiveFontSize(16, context),
                        fontWeight: FontWeight.w700,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      left: widget.screenWidth < 600 ? 16 : 20,
                      right: widget.screenWidth < 600 ? 16 : 20,
                      top: 2,
                      bottom: 0,
                    ),
                    child: Text(
                      "See what others have won!",
                      style: TextStyle(
                        fontSize: widget.getResponsiveFontSize(13, context),
                        fontWeight: FontWeight.normal,
                        color: Colors.black54,
                      ),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.only(
                      top: 10,
                      left: widget.screenWidth < 600 ? 12 : 16,
                      right: widget.screenWidth < 600 ? 12 : 16,
                      bottom: 0,
                    ),
                    child: loading
                        ? MasonryGridView.count(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            crossAxisCount: widget.crossAxisCount,
                            mainAxisSpacing: widget.screenWidth < 600 ? 8 : 12,
                            crossAxisSpacing: widget.screenWidth < 600 ? 8 : 12,
                            itemCount: 8,
                            itemBuilder: (context, index) =>
                                const PostCardShimmer(),
                          )
                        : widget.posts.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: widget.getResponsiveFontSize(
                                    40,
                                    context,
                                  ),
                                ),
                                Image.asset(
                                  'assets/images/blind_box.png',
                                  width: widget.getResponsiveFontSize(
                                    160,
                                    context,
                                  ),
                                  height: widget.getResponsiveFontSize(
                                    160,
                                    context,
                                  ),
                                  fit: BoxFit.cover,
                                ),
                                SizedBox(
                                  height: widget.getResponsiveFontSize(
                                    32,
                                    context,
                                  ),
                                ),
                                Text(
                                  'Looks like no one has shared their luck...\nBe the first to show your prize!\nLet the fun begin!',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: widget.getResponsiveFontSize(
                                      18,
                                      context,
                                    ),
                                    color: Colors.grey,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : MasonryGridView.count(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            crossAxisCount: widget.crossAxisCount,
                            mainAxisSpacing: widget.screenWidth < 600 ? 8 : 12,
                            crossAxisSpacing: widget.screenWidth < 600 ? 8 : 12,
                            itemCount: widget.posts.length,
                            itemBuilder: (context, index) {
                              final post = widget.posts[index];
                              return PostCard(
                                imageUrl: post['imageUrl'] as String,
                                title: post['title'] as String,
                                username: post['username'] as String,
                                likeCount: post['likeCount'] as int,
                                isFavorite: false,
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          PostView(post: post),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget _buildBackButton(BuildContext context) {
  // Helper method to get responsive font size (matching BlindboxView)
  double getResponsiveFontSize(double baseSize) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  return Material(
    color: Colors.white.withValues(alpha: 0.5),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
    elevation: 0,
    child: InkWell(
      customBorder: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      onTap: () => context.go('/lucky-draw/prize-reveal'),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.arrow_back_ios_new_rounded,
              size: getResponsiveFontSize(18),
              color: Colors.black,
            ),
            const SizedBox(width: 4),
            Text(
              'Back',
              style: TextStyle(
                fontSize: getResponsiveFontSize(16),
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
