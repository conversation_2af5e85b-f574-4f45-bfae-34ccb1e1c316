import 'package:flutter_test/flutter_test.dart';
import '../data/OpenSoonProductService.dart';
import '../model/OpenSoonProduct.dart';

void main() {
  group('OpenSoonProduct API Integration Tests', () {
    test('OpenSoonProduct model should parse API response correctly', () {
      // Sample API response data
      final Map<String, dynamic> apiResponse = {
        'id': 1,
        'productSpuId': 1001,
        'productSkuId': 2001,
        'productSkuName': 'Test Product',
        'productSkuImage': 'https://example.com/image.jpg',
        'usedAmount': 19000,
        'totalAmount': 20000,
        'score': 4.8,
        'sold': 100,
      };

      // Parse the API response
      final product = OpenSoonProduct.fromJson(apiResponse);

      // Verify all fields are parsed correctly
      expect(product.id, equals(1));
      expect(product.productSpuId, equals(1001));
      expect(product.productSkuId, equals(2001));
      expect(product.productSkuName, equals('Test Product'));
      expect(product.productSkuImage, equals('https://example.com/image.jpg'));
      expect(product.usedAmount, equals(19000));
      expect(product.totalAmount, equals(20000));
      expect(product.score, equals(4.8));
      expect(product.sold, equals(100));
    });

    test('OpenSoonProduct should calculate progress percentage correctly', () {
      final product = OpenSoonProduct(
        id: 1,
        productSpuId: 1001,
        productSkuId: 2001,
        productSkuName: 'Test Product',
        productSkuImage: 'https://example.com/image.jpg',
        usedAmount: 15000,
        totalAmount: 20000,
        score: 4.5,
        sold: 50,
      );

      // Progress should be 75% (15000/20000 * 100)
      expect(product.calculatedProgressPercentage, equals(75.0));
    });

    test('OpenSoonProduct should calculate remaining points correctly', () {
      final product = OpenSoonProduct(
        id: 1,
        productSpuId: 1001,
        productSkuId: 2001,
        productSkuName: 'Test Product',
        productSkuImage: 'https://example.com/image.jpg',
        usedAmount: 15000,
        totalAmount: 20000,
        score: 4.5,
        sold: 50,
      );

      // Remaining points should be 5000 (20000 - 15000)
      expect(product.remainingPoints, equals(5000));
    });

    test('OpenSoonProduct should check if ready to open correctly', () {
      final productNotReady = OpenSoonProduct(
        id: 1,
        productSpuId: 1001,
        productSkuId: 2001,
        productSkuName: 'Test Product',
        productSkuImage: 'https://example.com/image.jpg',
        usedAmount: 15000,
        totalAmount: 20000,
        score: 4.5,
        sold: 50,
      );

      final productReady = OpenSoonProduct(
        id: 2,
        productSpuId: 1002,
        productSkuId: 2002,
        productSkuName: 'Test Product 2',
        productSkuImage: 'https://example.com/image2.jpg',
        usedAmount: 20000,
        totalAmount: 20000,
        score: 4.8,
        sold: 100,
      );

      expect(productNotReady.isReadyToOpen, equals(false));
      expect(productReady.isReadyToOpen, equals(true));
    });

    test('OpenSoonProduct should handle edge cases correctly', () {
      // Test with zero total amount
      final productZeroTotal = OpenSoonProduct(
        id: 1,
        productSpuId: 1001,
        productSkuId: 2001,
        productSkuName: 'Test Product',
        productSkuImage: 'https://example.com/image.jpg',
        usedAmount: 0,
        totalAmount: 0,
        score: 0.0,
        sold: 0,
      );

      expect(productZeroTotal.calculatedProgressPercentage, equals(0.0));
      expect(productZeroTotal.remainingPoints, equals(0));
      expect(productZeroTotal.isReadyToOpen, equals(true)); // 0 >= 0 is true

      // Test with null values in JSON (should use defaults)
      final Map<String, dynamic> jsonWithNulls = {
        'id': null,
        'productSpuId': null,
        'productSkuId': null,
        'productSkuName': null,
        'productSkuImage': null,
        'usedAmount': null,
        'totalAmount': null,
        'score': null,
        'sold': null,
      };

      final productWithDefaults = OpenSoonProduct.fromJson(jsonWithNulls);
      expect(productWithDefaults.id, equals(0));
      expect(productWithDefaults.productSpuId, equals(0));
      expect(productWithDefaults.productSkuId, equals(0));
      expect(productWithDefaults.productSkuName, equals(''));
      expect(productWithDefaults.productSkuImage, equals(''));
      expect(productWithDefaults.usedAmount, equals(0));
      expect(productWithDefaults.totalAmount, equals(0));
      expect(productWithDefaults.score, equals(0.0));
      expect(productWithDefaults.sold, equals(0));
    });
  });
}
