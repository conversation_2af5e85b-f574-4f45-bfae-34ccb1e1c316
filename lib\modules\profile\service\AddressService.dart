import 'package:luckymall/modules/profile/model/AddressModel.dart';
import 'package:dio/dio.dart';
import 'package:luckymall/res/utility/network/api_client.dart';
import 'package:flutter/foundation.dart';

final addressService = AddressService();

class AddressService {
  final Dio authDio = ApiClient.instance.authDio;

  Future<List<Addressmodel>> getAddresses() async {
    try {
      debugPrint('[AddressService] Fetching addresses from API...');

      final response = await authDio.get('/address');

      if (response.statusCode == 200) {
        final data = response.data;
        final List<dynamic> addressList = data['data']['list'];
        final List<Addressmodel> addresses =
            addressList.map((json) => Addressmodel.fromJson(json)).toList();
        debugPrint('[AddressService] Addresses fetched: $data');
        return addresses;
      } else {
        throw Exception('Error: ${response.data['message']}');
      }
    } catch (e) {
      debugPrint('[AddressService] Error fetching addresses: $e');
      throw Exception(e);
    }
  }

  Future<bool> addAddress(Addressmodel address) async {
    try {
      final response = await authDio.post('/address', data: address.toJson());
      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception(response.data['message']);
      }
    } catch (e) {
      debugPrint('[AddressService] Error adding address: $e');
      throw Exception(e);
    }
  }

  Future<bool> updateAddress(Addressmodel address) async {
    try {
      final response = await authDio.put('/address/${address.id}', data: address.toJson());
      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception(response.data['message']);
      }
    } catch (e) {
      debugPrint('[AddressService] Error updating address: $e');
      throw Exception(e);
    }
  }

  Future<bool> deleteAddress(int id) async {
    try {
      final response = await authDio.delete('/address/$id');
      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception(response.data['message']);
      }
    } catch (e) {
      debugPrint('[AddressService] Error deleting address: $e');
      throw Exception(e);
    }
  }

  Future<Addressmodel> getDefaultAddress() async {
    try {
      final response = await authDio.get('/address/default');
      if (response.statusCode == 200) {
        final data = response.data;
        final Addressmodel address = Addressmodel.fromJson(data['data']);
        debugPrint('[AddressService] Default address fetched: $data');
        return address;
      } else {
        throw Exception(response.data['message']);
      }
    } catch (e) {
      debugPrint('[AddressService] Error getting default address: $e');
      throw Exception(e);
    }
  }
}
