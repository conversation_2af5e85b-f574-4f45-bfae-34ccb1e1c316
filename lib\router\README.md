# Navigation System Documentation

## Overview

This Flutter app now uses **GoRouter** for declarative routing with the following benefits:
- ✅ Deep linking support
- ✅ Type-safe navigation
- ✅ Nested routing with selective bottom navigation
- ✅ Browser back button support
- ✅ Authentication guards
- ✅ Centralized route management

## App Shell (Bottom Navigation) System

The navigation system uses a **selective app shell** approach where only main tab pages show the bottom navigation bar, while secondary and detail pages provide a full-screen experience.

### Pages WITH Bottom Navigation (Inside ShellRoute)
- `/shop` - Main shop listing page
- `/card` - Main card page
- `/lucky-draw` - Main lucky draw page (Default/Home)
- `/blind-box` - Main blind box page
- `/profile` - Main profile page

### Pages WITHOUT Bottom Navigation (Outside ShellRoute)
- **Authentication pages**: `/login`, `/register`, `/forgot-password`, `/otp-login`
- **Detail pages**: `/shop/detail/:productId`, `/blind-box/post/:postId`
- **Action pages**: `/shop/cart`, `/card/checkout`, `/card/purchase`
- **Secondary pages**: `/profile/my-address`, `/profile/voucher`, `/lucky-draw/search`
- **Sub-feature pages**: All nested routes under main tabs

## Route Structure

### Main Routes (with bottom navigation)
- `/lucky-draw` - Lucky draw main page (Default/Home page)
- `/shop` - Shop listing page
- `/card` - Card main page
- `/blind-box` - Blind box main page
- `/profile` - User profile page

### Authentication Routes (outside main app)
- `/login` - Password login
- `/register` - User registration
- `/forgot-password` - Password recovery
- `/otp-login` - OTP verification

### Secondary Routes (no bottom navigation)

#### Shop Routes
- `/shop/cart` - Shopping cart
- `/shop/detail/:productId` - Product detail page
- `/shop/favourites` - Favourite items

#### Lucky Draw Routes
- `/lucky-draw/prize-reveal` - Prize reveal page
- `/lucky-draw/group-purchase` - Group purchase page
- `/lucky-draw/participation-records` - Participation records
- `/lucky-draw/claim-voucher` - Claim voucher page
- `/lucky-draw/winning-history` - Winning history
- `/lucky-draw/community-sharing` - Community sharing
- `/lucky-draw/search` - Product search

#### Blind Box Routes
- `/blind-box/shop` - Blind box shop
- `/blind-box/buy` - Purchase blind box
- `/blind-box/group-buy` - Group buy page
- `/blind-box/post/:postId` - Post details
- `/blind-box/search` - Search blind boxes

#### Card Routes
- `/card/pack` - Card pack page
- `/card/purchase` - Purchase cards
- `/card/physical-cards` - Physical cards
- `/card/checkout` - Checkout page

#### Profile Routes (Protected)
- `/profile/my-address` - Address management
- `/profile/add-address` - Add new address
- `/profile/update-address/:addressId` - Update address
- `/profile/notification` - Notifications
- `/profile/voucher` - Voucher management
- `/profile/my-sharing` - Sharing history

## Usage

### Basic Navigation
```dart
import 'package:go_router/go_router.dart';

// Navigate to main pages (with bottom nav)
context.go('/lucky-draw'); // Home page
context.go('/shop');
context.go('/card');

// Navigate to secondary pages (no bottom nav)
context.push('/shop/cart');
context.push('/shop/detail/123');
context.push('/profile/my-address');

// Navigate with data
context.push('/blind-box/buy', extra: {
  'isGroupBuy': true,
  'product': productData,
});

// Go back
context.pop();
```

### Using Navigation Helper
```dart
import '../router/navigation_helper.dart';

// Use helper methods for common navigation
NavigationHelper.goToMainPage(context); // Go to Lucky Draw (home)
NavigationHelper.goToShop(context);
NavigationHelper.goToShopCart(context);
NavigationHelper.goToShopDetail(context, 'product123');
NavigationHelper.goBack(context);
```

## Adding New Routes

### With Bottom Navigation (Main Tab Pages)
Add routes inside the `ShellRoute` in `app_router.dart`:

```dart
// Inside ShellRoute routes array
GoRoute(
  path: '/new-main-page',
  builder: (context, state) => const NewMainPageView(),
),
```

### Without Bottom Navigation (Secondary Pages)
Add routes outside the `ShellRoute` in `app_router.dart`:

```dart
// Outside ShellRoute, as standalone routes
GoRoute(
  path: '/new-main-page/detail/:id',
  builder: (context, state) {
    final id = state.pathParameters['id']!;
    return NewDetailView(id: id);
  },
),

GoRoute(
  path: '/new-main-page/settings',
  builder: (context, state) => const NewSettingsView(),
),
```

### Decision Guidelines
**Use App Shell (bottom nav) for:**
- Main landing pages for each tab
- Pages where users should easily switch between tabs
- Top-level category or overview pages

**Don't use App Shell for:**
- Detail pages (product details, post details)
- Form pages (login, registration, address forms)
- Action pages (cart, checkout, purchase flows)
- Settings and configuration pages
- Search and filter pages
- Any page that requires focused user attention

### Authentication
```dart
import '../router/auth_guard.dart';

// Login user
AuthService.login();

// Logout user
AuthService.logout();

// Check if logged in
bool isLoggedIn = AuthService.isLoggedIn;
```

## Route Guards

### Global Authentication Guard
- Redirects unauthenticated users to `/login`
- Redirects authenticated users away from login pages

### Profile Guard
- Ensures profile routes require authentication
- Redirects to login if accessing profile while logged out

## Migration Notes

### What Changed
1. **Removed** `lib/app.dart` - replaced with GoRouter
2. **Updated** `lib/main.dart` - now uses `MaterialApp.router`
3. **Created** `lib/app_shell.dart` - handles selective bottom navigation
4. **Restructured** routes - main pages inside ShellRoute, secondary pages outside
5. **Updated** all `Navigator.push()` calls to use `context.go()` or `context.push()`

### Benefits
- **Selective Navigation**: Bottom nav only appears where it makes sense
- **Better UX**: Full-screen focus for detail and action pages
- **Deep Linking**: URLs like `/lucky-draw/prize-reveal` work automatically
- **Browser Support**: Back button works correctly on web
- **Type Safety**: Compile-time route validation
- **State Management**: Bottom tab states are preserved when needed
- **Authentication**: Built-in route protection

## File Structure
```
lib/router/
├── app_router.dart          # Main router configuration
├── auth_guard.dart          # Authentication guards
├── navigation_helper.dart   # Navigation utility methods
└── README.md               # This documentation

lib/
├── app_shell.dart           # Bottom navigation shell
└── main.dart               # App entry point with router
```
