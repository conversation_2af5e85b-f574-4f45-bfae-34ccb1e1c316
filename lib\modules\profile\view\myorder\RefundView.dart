import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../view-model/RefundVM.dart';
import '../../../../data/model/OrderModel.dart';
import '../../widgets/OrderStatusBadge.dart';

class RefundView extends StatefulWidget {
  final OrderModel order;

  const RefundView({super.key, required this.order});

  @override
  State<RefundView> createState() => _RefundViewState();
}

class _RefundViewState extends State<RefundView> {
  late RefundVM _refundVM;
  final TextEditingController _otherReasonController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _refundVM = RefundVM(order: widget.order);
    _otherReasonController.addListener(() {
      _refundVM.updateOtherReasonText(_otherReasonController.text);
    });
  }

  @override
  void dispose() {
    _otherReasonController.dispose();
    _refundVM.dispose();
    super.dispose();
  }

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  Future<void> _pickEvidence() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        _refundVM.addEvidenceFile(File(image.path));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to pick image. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handleSubmitRefund() async {
    final success = await _refundVM.submitRefund();

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Refund request submitted successfully!'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop(true);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _refundVM.errorMessage ?? 'Failed to submit refund request',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _refundVM,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black87),
            onPressed: () => Navigator.of(context).pop(),
          ),
          centerTitle: true,
          title: Text(
            'Return / Refund',
            style: TextStyle(
              fontSize: getResponsiveFontSize(18, context),
              fontWeight: FontWeight.w700,
              color: Colors.black,
            ),
          ),
        ),
        body: Consumer<RefundVM>(
          builder: (context, vm, _) {
            return Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Warning Banner
                        _buildWarningBanner(context, vm),

                        SizedBox(height: getResponsivePadding(16, context)),

                        // Order Information
                        _buildOrderInfo(context),

                        SizedBox(height: getResponsivePadding(24, context)),

                        // Reason for Application
                        _buildReasonSection(context, vm),

                        SizedBox(height: getResponsivePadding(24, context)),

                        // Evidence Section
                        _buildEvidenceSection(context, vm),

                        SizedBox(height: getResponsivePadding(24, context)),

                        // Contact Section
                        _buildContactSection(context, vm),

                        if (vm.errorMessage != null) ...[
                          SizedBox(height: getResponsivePadding(16, context)),
                          Container(
                            margin: EdgeInsets.symmetric(
                              horizontal: getResponsivePadding(16, context),
                            ),
                            padding: EdgeInsets.all(
                              getResponsivePadding(12, context),
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.red.shade200),
                            ),
                            child: Text(
                              vm.errorMessage!,
                              style: TextStyle(
                                color: Colors.red.shade700,
                                fontSize: getResponsiveFontSize(14, context),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                // Submit Button
                _buildSubmitButton(context, vm),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildWarningBanner(BuildContext context, RefundVM vm) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(getResponsivePadding(12, context)),
      color: const Color(0xFFFCD255),
      child: Text(
        vm.eligibilityMessage,
        style: TextStyle(
          fontSize: getResponsiveFontSize(14, context),
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildOrderInfo(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order type and order number
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: getResponsivePadding(8, context),
                  vertical: getResponsivePadding(4, context),
                ),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.card_giftcard,
                      size: getResponsivePadding(16, context),
                      color: Colors.black54,
                    ),
                    SizedBox(width: getResponsivePadding(4, context)),
                    Text(
                      widget.order.type.typeDisplayName,
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(12, context),
                        fontWeight: FontWeight.w600,
                        color: Colors.black54,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              Text(
                'Order Number: ${widget.order.id}',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(12, context),
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),

          SizedBox(height: getResponsivePadding(12, context)),

          // Product info
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: getResponsivePadding(60, context),
                  height: getResponsivePadding(60, context),
                  color: Colors.grey.shade100,
                  child: widget.order.productImage.startsWith('http')
                      ? Image.network(
                          widget.order.productImage,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey.shade200,
                              child: Icon(
                                Icons.image,
                                color: Colors.grey.shade400,
                              ),
                            );
                          },
                        )
                      : Image.asset(
                          widget.order.productImage,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey.shade200,
                              child: Icon(
                                Icons.image,
                                color: Colors.grey.shade400,
                              ),
                            );
                          },
                        ),
                ),
              ),

              SizedBox(width: getResponsivePadding(12, context)),

              // Product details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.order.productName,
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: getResponsivePadding(8, context)),
                    Text(
                      'Total Points: 1000 pts',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReasonSection(BuildContext context, RefundVM vm) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reason for application*',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: getResponsivePadding(12, context)),

          // Reason buttons
          Wrap(
            spacing: getResponsivePadding(8, context),
            runSpacing: getResponsivePadding(8, context),
            children: RefundReason.values
                .where((reason) => reason != RefundReason.other)
                .map((reason) {
                  final isSelected = vm.selectedReason == reason;
                  return GestureDetector(
                    onTap: () => vm.selectReason(reason),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: getResponsivePadding(12, context),
                        vertical: getResponsivePadding(8, context),
                      ),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? const Color(0xFFFCD255)
                            : Colors.white,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: isSelected
                              ? const Color(0xFFFCD255)
                              : Colors.grey.shade300,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        reason.displayName,
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  );
                })
                .toList(),
          ),

          SizedBox(height: getResponsivePadding(12, context)),

          // Other reasons text field
          TextField(
            controller: _otherReasonController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Other reasons...',
              hintStyle: TextStyle(
                color: Colors.grey.shade500,
                fontSize: getResponsiveFontSize(14, context),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFFFCD255)),
              ),
              contentPadding: EdgeInsets.all(getResponsivePadding(12, context)),
            ),
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.black87,
            ),
            onTap: () {
              vm.selectReason(RefundReason.other);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEvidenceSection(BuildContext context, RefundVM vm) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Evidence*',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: getResponsivePadding(8, context)),
          Text(
            vm.evidenceRequirement,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.grey.shade600,
              height: 1.4,
            ),
          ),
          SizedBox(height: getResponsivePadding(16, context)),

          // Evidence upload and preview
          Row(
            children: [
              // Upload button
              GestureDetector(
                onTap: _pickEvidence,
                child: Container(
                  width: getResponsivePadding(100, context),
                  height: getResponsivePadding(100, context),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFCD255),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.camera_alt_outlined,
                        size: getResponsivePadding(32, context),
                        color: Colors.black54,
                      ),
                      SizedBox(height: getResponsivePadding(8, context)),
                      Text(
                        'Upload media',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(12, context),
                          color: Colors.black54,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Evidence previews
              if (vm.evidenceFiles.isNotEmpty) ...[
                SizedBox(width: getResponsivePadding(12, context)),
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: vm.evidenceFiles.asMap().entries.map((entry) {
                        final index = entry.key;
                        final file = entry.value;
                        return Container(
                          margin: EdgeInsets.only(
                            right: getResponsivePadding(8, context),
                          ),
                          child: Stack(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.file(
                                  file,
                                  width: getResponsivePadding(80, context),
                                  height: getResponsivePadding(80, context),
                                  fit: BoxFit.cover,
                                ),
                              ),
                              Positioned(
                                top: 4,
                                right: 4,
                                child: GestureDetector(
                                  onTap: () => vm.removeEvidenceFile(index),
                                  child: Container(
                                    padding: EdgeInsets.all(
                                      getResponsivePadding(2, context),
                                    ),
                                    decoration: const BoxDecoration(
                                      color: Colors.black54,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.close,
                                      size: getResponsivePadding(12, context),
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection(BuildContext context, RefundVM vm) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Contact',
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: getResponsivePadding(12, context)),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                vm.userPhoneNumber,
                style: TextStyle(
                  fontSize: getResponsiveFontSize(16, context),
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),

          SizedBox(height: getResponsivePadding(8, context)),

          Text(
            vm.contactInfo,
            style: TextStyle(
              fontSize: getResponsiveFontSize(12, context),
              color: Colors.grey.shade600,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton(BuildContext context, RefundVM vm) {
    return Container(
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      child: SizedBox(
        width: double.infinity,
        height: getResponsivePadding(50, context),
        child: ElevatedButton(
          onPressed: vm.isSubmitting || !vm.canSubmit
              ? null
              : _handleSubmitRefund,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFCD255),
            disabledBackgroundColor: Colors.grey.shade300,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: vm.isSubmitting
              ? SizedBox(
                  width: getResponsivePadding(20, context),
                  height: getResponsivePadding(20, context),
                  child: const CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black54),
                  ),
                )
              : Text(
                  'Submit',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(16, context),
                    fontWeight: FontWeight.w700,
                    color: Colors.black87,
                  ),
                ),
        ),
      ),
    );
  }
}
