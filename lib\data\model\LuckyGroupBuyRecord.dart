enum LuckyGroupBuyStatus { ongoing, completed, refundInProgress, failed }

class LuckyGroupBuyRecord {
  final String id;
  final String productName;
  final String productImageUrl;
  final String variation;
  final double pricePerUnit;
  final int quantity;
  final double totalPrice;
  final LuckyGroupBuyStatus status;
  final DateTime createdAt;
  final DateTime? endTime;
  final String groupId;
  final int currentMembers;
  final int requiredMembers;

  LuckyGroupBuyRecord({
    required this.id,
    required this.productName,
    required this.productImageUrl,
    required this.variation,
    required this.pricePerUnit,
    required this.quantity,
    required this.totalPrice,
    required this.status,
    required this.createdAt,
    this.endTime,
    required this.groupId,
    required this.currentMembers,
    required this.requiredMembers,
  });

  factory LuckyGroupBuyRecord.fromJson(Map<String, dynamic> json) {
    return LuckyGroupBuyRecord(
      id: json['id'] as String,
      productName: json['productName'] as String,
      productImageUrl: json['productImageUrl'] as String,
      variation: json['variation'] as String,
      pricePerUnit: (json['pricePerUnit'] as num).toDouble(),
      quantity: json['quantity'] as int,
      totalPrice: (json['totalPrice'] as num).toDouble(),
      status: LuckyGroupBuyStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => LuckyGroupBuyStatus.ongoing,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      endTime: json['endTime'] != null
          ? DateTime.parse(json['endTime'] as String)
          : null,
      groupId: json['groupId'] as String,
      currentMembers: json['currentMembers'] as int,
      requiredMembers: json['requiredMembers'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productName': productName,
      'productImageUrl': productImageUrl,
      'variation': variation,
      'pricePerUnit': pricePerUnit,
      'quantity': quantity,
      'totalPrice': totalPrice,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'groupId': groupId,
      'currentMembers': currentMembers,
      'requiredMembers': requiredMembers,
    };
  }

  String getFormattedDate() {
    return '${createdAt.year}/${createdAt.month.toString().padLeft(2, '0')}/${createdAt.day.toString().padLeft(2, '0')}';
  }

  String getStatusDisplayName() {
    switch (status) {
      case LuckyGroupBuyStatus.ongoing:
        return 'Ongoing';
      case LuckyGroupBuyStatus.completed:
        return 'Completed';
      case LuckyGroupBuyStatus.refundInProgress:
        return 'Refund in Progress';
      case LuckyGroupBuyStatus.failed:
        return 'Failed';
    }
  }

  LuckyGroupBuyRecord copyWith({
    String? id,
    String? productName,
    String? productImageUrl,
    String? variation,
    double? pricePerUnit,
    int? quantity,
    double? totalPrice,
    LuckyGroupBuyStatus? status,
    DateTime? createdAt,
    DateTime? endTime,
    String? groupId,
    int? currentMembers,
    int? requiredMembers,
  }) {
    return LuckyGroupBuyRecord(
      id: id ?? this.id,
      productName: productName ?? this.productName,
      productImageUrl: productImageUrl ?? this.productImageUrl,
      variation: variation ?? this.variation,
      pricePerUnit: pricePerUnit ?? this.pricePerUnit,
      quantity: quantity ?? this.quantity,
      totalPrice: totalPrice ?? this.totalPrice,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      endTime: endTime ?? this.endTime,
      groupId: groupId ?? this.groupId,
      currentMembers: currentMembers ?? this.currentMembers,
      requiredMembers: requiredMembers ?? this.requiredMembers,
    );
  }
}
