import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/AddressForm.dart';
import '../widgets/SubmitButton.dart';
import '../service/AddressService.dart';
import '../model/AddressModel.dart';

class AddAddressView extends StatefulWidget {
  const AddAddressView({super.key});

  @override
  State<AddAddressView> createState() => _AddAddressViewState();
}

class _AddAddressViewState extends State<AddAddressView> {
  bool isDefault = false;
  final _formKey = GlobalKey<FormState>();
  final _recipientController = TextEditingController();
  final _mobileController = TextEditingController();
  final _postcodeController = TextEditingController();
  final _detailedAddressController = TextEditingController();
  final AddressService _addressService = AddressService();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _recipientController.dispose();
    _mobileController.dispose();
    _postcodeController.dispose();
    _detailedAddressController.dispose();
    super.dispose();
  }

  Future<void> _submitAddress() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final address = Addressmodel(
        id: 0, // Will be assigned by the server
        country: 60, // Malaysia default
        recipient: _recipientController.text.trim(),
        mobile: _mobileController.text.trim(),
        postcode: int.parse(_postcodeController.text.trim()),
        detailedAddress: _detailedAddressController.text.trim(),
        isDefault: isDefault,
      );

      // Use the service to add the address
      final success = await _addressService.addAddress(address);

      if (success) {
        // Navigate back to address list
        if (mounted) {
          context.pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding address: ${e.toString()}'),
            backgroundColor: Colors.amber,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        title: const Text(
          'Add New Address',
          style: TextStyle(color: Colors.black),
        ),
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            AddressForm(
              formKey: _formKey,
              recipientController: _recipientController,
              mobileController: _mobileController,
              postcodeController: _postcodeController,
              addressController: _detailedAddressController,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: isDefault,
                  onChanged: (value) {
                    setState(() {
                      isDefault = value ?? false;
                    });
                  },
                ),
                const Text('Set as Default Address'),
              ],
            ),
            const SizedBox(height: 24),
            SubmitButton(onSubmit: _submitAddress),
          ],
        ),
      ),
    );
  }
}
