import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../model/cardPack/CategoryItem.dart';

class CardCategoryBottomNav extends StatelessWidget {
  final List<CategoryItem> categories;
  final String selectedCategory;
  final ValueChanged<String> onCategorySelected;

  const CardCategoryBottomNav({
    super.key,
    required this.categories,
    required this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    final bool isTablet = screenWidth > 600;
    final double iconSize = isTablet ? screenWidth * 0.05 : screenWidth * 0.08;
    final double circleSize = isTablet ? screenWidth * 0.1 : screenWidth * 0.14;
    final double fontSize = isTablet ? screenWidth * 0.018 : screenWidth * 0.027;
    final double navBarHeight = isTablet ? screenWidth * 0.15 : screenWidth * 0.25;

    Widget buildCategoryItem(CategoryItem cat) {
      final bool isSelected = cat.key == selectedCategory;
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.015),
        child: InkWell(
          borderRadius: BorderRadius.circular(circleSize),
          splashColor: Colors.amber.withOpacity(0.3),
          onTap: () => onCategorySelected(cat.key),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: circleSize,
                height: circleSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected ? Colors.amber : Colors.grey.shade300,
                ),
                child: Center(
                  child: SvgPicture.asset(
                    isSelected ? cat.activeIcon : cat.icon, // FIXED: use icon for inactive
                    width: iconSize,
                    height: iconSize,
                    colorFilter: ColorFilter.mode(
                      isSelected ? Colors.white : Colors.grey,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              SizedBox(height: screenWidth * 0.01),
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  cat.key[0].toUpperCase() + cat.key.substring(1),
                  style: TextStyle(
                    fontSize: fontSize,
                    color: isSelected ? Colors.amber : Colors.grey,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      height: navBarHeight,
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -3),
          ),
        ],
      ),
      child: SafeArea(
        child: categories.length <= 5
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: categories.map(buildCategoryItem).toList(),
              )
            : SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.02,
                  vertical: screenWidth * 0.025,
                ),
                child: Row(
                  children: categories.map(buildCategoryItem).toList(),
                ),
              ),
      ),
    );
  }
}
