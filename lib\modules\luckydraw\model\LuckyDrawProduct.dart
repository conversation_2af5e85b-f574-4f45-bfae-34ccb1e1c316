class LuckyDrawProduct {
  final int id;
  final int productSpuId;
  final int productSkuId;
  final String productSkuName;
  final String productSkuImage;
  final int usedAmount;
  final int totalAmount;
  final double score;
  final int sold;

  const LuckyDrawProduct({
    required this.id,
    required this.productSpuId,
    required this.productSkuId,
    required this.productSkuName,
    required this.productSkuImage,
    required this.usedAmount,
    required this.totalAmount,
    required this.score,
    required this.sold,
  });

  factory LuckyDrawProduct.fromJson(Map<String, dynamic> json) {
    return LuckyDrawProduct(
      id: (json['id'] as num?)?.toInt() ?? 0,
      productSpuId: (json['productSpuId'] as num?)?.toInt() ?? 0,
      productSkuId: (json['productSkuId'] as num?)?.toInt() ?? 0,
      productSkuName: json['productSkuName']?.toString() ?? '',
      productSkuImage: json['productSkuImage']?.toString() ?? '',
      usedAmount: (json['usedAmount'] as num?)?.toInt() ?? 0,
      totalAmount: (json['totalAmount'] as num?)?.toInt() ?? 0,
      score: (json['score'] as num?)?.toDouble() ?? 0.0,
      sold: (json['sold'] as num?)?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productSpuId': productSpuId,
      'productSkuId': productSkuId,
      'productSkuName': productSkuName,
      'productSkuImage': productSkuImage,
      'usedAmount': usedAmount,
      'totalAmount': totalAmount,
      'score': score,
      'sold': sold,
      // Legacy field names for backward compatibility
      'productName': productSkuName,
      'imageUrl': productSkuImage,
      'currentParticipants': usedAmount,
      'maxParticipants': totalAmount,
      'rating': score,
      'claimsRemaining': sold,
    };
  }

  // Legacy getters for backward compatibility
  String get productName => productSkuName;
  String get imageUrl => productSkuImage;
  int get currentParticipants => usedAmount;
  int get maxParticipants => totalAmount;
  double get rating => score;
  int get claimsRemaining => sold;

  LuckyDrawProduct copyWith({
    int? id,
    int? productSpuId,
    int? productSkuId,
    String? productSkuName,
    String? productSkuImage,
    int? usedAmount,
    int? totalAmount,
    double? score,
    int? sold,
  }) {
    return LuckyDrawProduct(
      id: id ?? this.id,
      productSpuId: productSpuId ?? this.productSpuId,
      productSkuId: productSkuId ?? this.productSkuId,
      productSkuName: productSkuName ?? this.productSkuName,
      productSkuImage: productSkuImage ?? this.productSkuImage,
      usedAmount: usedAmount ?? this.usedAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      score: score ?? this.score,
      sold: sold ?? this.sold,
    );
  }

  @override
  String toString() {
    return 'LuckyDrawProduct(id: $id, productSkuName: $productSkuName, score: $score, sold: $sold)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LuckyDrawProduct && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
