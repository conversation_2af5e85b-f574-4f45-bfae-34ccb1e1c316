import 'package:flutter/material.dart';
import '../../model/historyCard/HistoryCardUsedItem.dart';

class HistoryItemGrid extends StatefulWidget {
  final List<HistoryCardUsedItem> cardsUsed;

  const HistoryItemGrid({super.key, required this.cardsUsed});

  @override
  State<HistoryItemGrid> createState() => _HistoryItemGridState();
}

class _HistoryItemGridState extends State<HistoryItemGrid> {
  bool expanded = false;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scale = screenWidth / 390;
    const crossAxisCount = 3;
    final itemsToShow = expanded ? widget.cardsUsed : widget.cardsUsed.take(6).toList();

    return Column(
      children: [
        GridView.builder(
          itemCount: itemsToShow.length,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            childAspectRatio: 0.75,
          ),
          itemBuilder: (context, index) {
            final card = itemsToShow[index];

            return Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10 * scale),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 6,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10 * scale),
                    child: Image.asset(
                      card.imageUrl,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                  ),
                ),
                if (card.quantity > 1)
                  Positioned(
                    bottom:2,
                    right: 2,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 12 * scale, vertical: 2 * scale),
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(10 * scale),
                          bottomRight: Radius.circular(10 * scale),
                        ),
                        border: Border.all(color: Colors.black54, width: 1 * scale),
                      ),
                      child: Text(
                        '${card.quantity}',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 11 * scale,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
        if (!expanded && widget.cardsUsed.length > 6)
          TextButton(
            onPressed: () => setState(() => expanded = true),
            child: const Text("View More"),
          ),
      ],
    );
  }
}
