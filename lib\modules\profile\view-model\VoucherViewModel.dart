import 'package:flutter/material.dart';
import '../model/VoucherModel.dart';

class VoucherViewModel extends ChangeNotifier {
  List<Voucher> _vouchers = [];
  bool _isLoading = true;

  List<Voucher> get vouchers => _vouchers;
  bool get isLoading => _isLoading;

  VoucherViewModel() {
    fetchVouchers();
  }

  void fetchVouchers() async {
    await Future.delayed(const Duration(seconds: 2)); // simulate API

    _vouchers = [
      Voucher(amount: "RM10", minSpend: "RM30", validDate: "20/12/2025", status: VoucherStatus.active),
      Voucher(amount: "RM15", minSpend: "RM50", validDate: "25/12/2025", status: VoucherStatus.active),
      Voucher(amount: "RM8", minSpend: "RM20", validDate: "15/12/2025", status: VoucherStatus.used),
      Voucher(amount: "RM5", minSpend: "RM15", validDate: "10/12/2025", status: VoucherStatus.expired),
    ];

    _isLoading = false;
    notifyListeners();
  }

  List<Voucher> filterByStatus(VoucherStatus? status) {
    if (status == null) return _vouchers;
    return _vouchers.where((v) => v.status == status).toList();
  }
}
