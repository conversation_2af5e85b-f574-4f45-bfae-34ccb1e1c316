import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../model/LuckyDrawProductReview.dart';

class ReviewCard extends StatelessWidget {
  final String reviewerName;
  final String avatarUrl;
  final String date;
  final int rating;
  final String variation;
  final String reviewText;
  final List<String> imageUrls;
  final int orderType; // Add this new field

  const ReviewCard({
    super.key,
    required this.reviewerName,
    required this.avatarUrl,
    required this.date,
    required this.rating,
    required this.variation,
    required this.reviewText,
    required this.imageUrls,
    required this.orderType, // Add this to constructor
  });

  // Convenience constructor that accepts LuckyDrawProductReview
  ReviewCard.fromReview({super.key, required LuckyDrawProductReview review})
    : reviewerName = review.reviewerName,
      avatarUrl = review.avatarUrl,
      date = review.date,
      rating = review.rating.toInt(),
      variation = review.variation,
      reviewText = review.reviewText,
      imageUrls = review.imageUrls,
      orderType = review.orderType;

  // Helper method to get order type text
  String _getOrderTypeText() {
    switch (orderType) {
      case 0:
        return 'Store Order';
      case 10:
        return 'Point Order';
      case 20:
        return 'Blindbox Order';
      default:
        return 'Store Order';
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 335,
      child: Stack(
        children: [
          // Main card content with extra right padding for the image
          Container(
            margin: const EdgeInsets.symmetric(
              horizontal: 6,
            ), // Reduced margins
            padding: EdgeInsets.only(
              top: 12, // Reduced from 16
              left: 12, // Reduced from 16
              right: imageUrls.isNotEmpty ? 70 : 12, // Reduced right padding
              bottom: 12, // Reduced from 16
            ),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFFFFFF), Color(0x80FFBF00)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(12), // Reduced from 15
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.06),
                  blurRadius: 6, // Reduced from 8
                  offset: const Offset(0, 3), // Reduced from 4
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    const Icon(
                      Icons.shopping_bag_outlined,
                      size: 18,
                    ), // Reduced from 22
                    const SizedBox(width: 6), // Reduced from 8
                    Text(
                      _getOrderTypeText(), // Use dynamic text instead of hardcoded
                      style: const TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 13, // Reduced from 15
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 15), // Reduced from 15
                // Reviewer and Date
                Row(
                  children: [
                    CircleAvatar(
                      radius: 12, // Reduced from 15
                      backgroundImage: avatarUrl.isNotEmpty
                          ? NetworkImage(avatarUrl)
                          : null,
                      backgroundColor: Colors.grey[300],
                      child: avatarUrl.isEmpty
                          ? const Icon(
                              Icons.person,
                              size: 16,
                              color: Colors.grey,
                            )
                          : null,
                    ),
                    const SizedBox(width: 8), // Reduced from 10
                    Expanded(
                      child: Text(
                        reviewerName.isNotEmpty ? reviewerName : 'Anonymous',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 11, // Reduced from 12
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ), // Reduced from 10
                    Text(
                      date,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 10, // Reduced from 12
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4), // Added small spacing
                // Rating
                Row(
                  children: List.generate(
                    5,
                    (index) => Icon(
                      Icons.star,
                      color: index < rating ? Colors.amber : Colors.grey[300],
                      size: 14, // Reduced from 18
                    ),
                  ),
                ),
                const SizedBox(height: 4), // Reduced from 6
                // Variation
                Text(
                  'Variation: $variation',
                  style: const TextStyle(
                    color: Colors.brown,
                    fontWeight: FontWeight.w600,
                    fontSize: 10, // Reduced from 12
                  ),
                ),
                const SizedBox(height: 4), // Reduced from 5
                // Review Text
                Text(
                  reviewText,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    fontSize: 10, // Reduced from 12
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          // Full-width divider, positioned below the header
          Positioned(
            left: 24, // Adjusted for new padding
            right: 24, // Adjusted for new padding
            top: 38, // Adjusted for new header height
            child: Container(height: 1, color: Colors.black),
          ),
          // Overlay image in top-right
          if (imageUrls.isNotEmpty)
            Positioned(
              top: 55, // Adjusted for new layout
              right: 12, // Reduced from 16
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8), // Reduced from 12
                child: CachedNetworkImage(
                  imageUrl: imageUrls.first,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    width: 60,
                    height: 60,
                    color: Colors.grey[200],
                    child: const Center(
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    width: 60,
                    height: 60,
                    color: Colors.grey[200],
                    child: const Icon(
                      Icons.broken_image,
                      color: Colors.grey,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
