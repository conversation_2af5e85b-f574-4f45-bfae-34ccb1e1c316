import 'package:flutter/material.dart';
import '../../model/historyCard/HistoryCardItem.dart';
import 'InfoRow.dart';

class HistoryRecordInfoRows extends StatelessWidget {
  final HistoryCardItem item;

  const HistoryRecordInfoRows({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final List<Widget> rows = [];

    // PURCHASE or PHYSICAL (Delivery)
    if (item.type == 'Purchase Card' || 
       (item.type == 'Physical Card' && item.orderMethod == 'Delivery')) {
      if (item.paymentMethod != null) {
        rows.add(InfoRow(label: 'Payment Method', value: item.paymentMethod!));
      }
      if (item.paymentUsed != null) {
        rows.add(InfoRow(label: 'Payment Used', value: item.paymentUsed!));
      }
    }

    // DATE (all types)
    rows.add(InfoRow(label: 'Date', value: formatDateTime(item.timestamp)));

    // STATUS (all types)
    rows.add(InfoRow(label: 'Status', value: item.status));

    // DETAIL (all types)
    rows.add(InfoRow(label: 'Detail', value: item.detail));

    // DISCOUNT USED
    if (item.type == 'Discount Used' && item.productName != null) {
      rows.add(InfoRow(label: 'Product', value: item.productName!));
    }

    // TRANSFER CARD
    if (item.type == 'Transfer Card' && item.transferType != null) {
      rows.add(InfoRow(label: 'Type of Transfer', value: item.transferType!));
    }

    // PHYSICAL CARD
    if (item.type == 'Physical Card' && item.orderMethod != null) {
      rows.add(InfoRow(label: 'Order Method', value: item.orderMethod!));
    }

    // GIFT
    if (item.type == 'Gift' && item.giftSource != null) {
      rows.add(InfoRow(label: 'Gifts', value: item.giftSource!));
    }

    return Column(
      children: rows
      .expand((row) => [row, const Divider()])
      .toList(),
    );
  }

  String formatDateTime(DateTime dt) {
    return "${dt.day.toString().padLeft(2, '0')} "
           "${_monthName(dt.month)} ${dt.year} "
           "${dt.hour.toString().padLeft(2, '0')}:"
           "${dt.minute.toString().padLeft(2, '0')}";
  }

  String _monthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }
}
