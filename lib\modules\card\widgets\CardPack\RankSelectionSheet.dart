import 'package:flutter/material.dart';

class RankSelectionSheet extends StatelessWidget {
  final String? selectedRank;
  final ValueChanged<String?> onRankSelected;

  const RankSelectionSheet({
    super.key,
    required this.selectedRank,
    required this.onRankSelected,
  });

  final ranks = const ['All', 'R', 'SR', 'SSR', 'UR', 'King', 'Queen'];

  final Map<String, Color> rankColors = const {
    'R': Colors.grey,
    'SR': Colors.blueAccent,
    'SSR': Colors.deepPurpleAccent,
    'UR': Colors.orangeAccent,
    'King': Colors.redAccent,
    'Queen': Colors.pinkAccent,
    'All': Colors.black87,
  };

  /// ✅ Build icons depending on rank type
  Widget buildRankIcon(String rank, bool isSelected, Color color) {
    if (rank == 'All') {
      // unlimited ∞ icon
      return Icon(
        Icons.all_inclusive,
        size: 22,
        color: isSelected ? color : Colors.grey.shade400,
      );
    }

    int starCount = 0;
    bool outlined = false;

    if (rank == 'King' || rank == 'Queen') {
      starCount = 5;
      outlined = true;
    } else {
      if (rank == "R") starCount = 1;
      if (rank == "SR") starCount = 2;
      if (rank == "SSR") starCount = 3;
      if (rank == "UR") starCount = 4;
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(starCount, (i) {
        return Icon(
          outlined ? Icons.star_border : Icons.star,
          size: 18,
          color: isSelected ? color : Colors.grey.shade400,
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      width: double.infinity,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // drag handle
            Container(
              height: 5,
              width: 40,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey.shade400,
                borderRadius: BorderRadius.circular(12),
              ),
            ),

            Text(
              'Select Card Rank',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w800,
                color: Colors.amber.shade600,
                letterSpacing: 1.1,
              ),
            ),

            const SizedBox(height: 20),

            Wrap(
              spacing: 10,
              runSpacing: 10,
              alignment: WrapAlignment.center,
              children: ranks.map((rank) {
                final isSelected = selectedRank == rank ||
                    (rank == 'All' && selectedRank == null);
                final color = rankColors[rank]!;

                return GestureDetector(
                  onTap: () => onRankSelected(rank == 'All' ? null : rank),
                  child: AnimatedScale(
                    scale: isSelected ? 1.05 : 1.0,
                    duration: const Duration(milliseconds: 200),
                    curve: Curves.easeOutBack,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      curve: Curves.easeOutCubic,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 14, vertical: 10),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? color.withOpacity(0.15)
                            : Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected ? color : Colors.grey.shade300,
                          width: isSelected ? 2 : 1,
                        ),
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: color.withOpacity(0.3),
                                  blurRadius: 8,
                                  spreadRadius: 1,
                                  offset: const Offset(0, 3),
                                )
                              ]
                            : [],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          buildRankIcon(rank, isSelected, color),
                          const SizedBox(width: 6),
                          AnimatedDefaultTextStyle(
                            duration: const Duration(milliseconds: 200),
                            style: TextStyle(
                              color: isSelected ? color : Colors.black87,
                              fontSize: 13,
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.w500,
                            ),
                            child: Text(rank),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}
