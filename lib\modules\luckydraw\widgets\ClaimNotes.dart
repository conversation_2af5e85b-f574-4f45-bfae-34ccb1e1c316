import 'package:flutter/material.dart';

class ClaimNotes extends StatelessWidget {
  const ClaimNotes({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375; // base width 375
    double scaleH(double value) =>
        value * screenHeight / 812; // base height 812
    double scaleText(double value) => value * screenWidth / 375;

    return Container(
      // width: double.infinity, // Removed to align with other cards
      padding: EdgeInsets.symmetric(
        horizontal: scaleW(10),
        vertical: scaleH(15),
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(scaleW(5)),
      ),
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: 'Notes:',
              style: TextStyle(
                color: Color(0xFFD90019),
                fontSize: scaleText(14),
                fontWeight: FontWeight.bold,
              ),
            ),
            TextSpan(
              text:
                  ' User can choose to claim the voucher as TNG Voucher or directly credited to <PERSON> Mall E-Wallet.',
              style: TextStyle(
                color: Color(0xFFD90019),
                fontSize: scaleText(12),
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
