import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../model/scanCode/ScanCodeModel.dart';

class AlreadyClaimedDialog extends StatelessWidget {
  final ScanCodeModel model;

  const AlreadyClaimedDialog({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    final claimedDate = model.claimedAt != null
        ? DateFormat('dd/MM/yyyy HH:mm').format(model.claimedAt!)
        : 'Unknown';

    final infoStyle = const TextStyle(fontSize: 16, color: Colors.white70);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.amberAccent, width: 3),
          boxShadow: const [
            BoxShadow(
              color: Color.fromARGB(78, 255, 214, 64),
              blurRadius: 12,
              spreadRadius: 2,
            ),
          ],
          gradient: const LinearGradient(
            colors: [Colors.black87, Colors.black54],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.verified, size: 60, color: Colors.amberAccent),
            const SizedBox(height: 12),
            const Text(
              'Already Claimed!',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.amberAccent,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'This code was claimed before.',
              style: TextStyle(fontSize: 16, color: Colors.grey.shade300),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.qr_code_2, color: Colors.white70),
                const SizedBox(width: 8),
                Expanded(
                  child: SelectableText(model.code, style: infoStyle),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                const Icon(Icons.calendar_today, color: Colors.white70),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(claimedDate, style: infoStyle),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                elevation: 5,
              ),
              onPressed: () => Navigator.pop(context),
              child: const Text('OK', style: TextStyle(fontWeight: FontWeight.bold)),
            ),
          ],
        ),
      ),
    );
  }
}
