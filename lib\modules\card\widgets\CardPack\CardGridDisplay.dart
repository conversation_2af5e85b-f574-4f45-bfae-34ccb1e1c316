import 'package:flutter/material.dart';
import '../../model/cardPack/CardItem.dart';
import '../loadings/CardPack/ShimmerCardItem.dart';

class CardGridDisplay extends StatelessWidget {
  final List<CardItem> cards;
  final String category;
  final bool showArchived;
  final bool isLoading;

  const CardGridDisplay({
    super.key,
    required this.cards,
    required this.category,
    required this.showArchived,
    required this.isLoading,
  });

  int _getCrossAxisCount(double width) {
    if (width < 600) return 2; // phones
    if (width < 900) return 3; // phablets / small tablets
    return 4; // tablets and up
  }

  double _getChildAspectRatio(double width) {
    if (width < 600) return 3 / 4; // taller cards on small screens
    if (width < 900) return 4 / 5;
    return 1; // almost square cards on big tablets
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final crossAxisCount = _getCrossAxisCount(screenWidth);
    final childAspectRatio = _getChildAspectRatio(screenWidth);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: GridView.builder(
        itemCount: isLoading ? 6 : cards.length,
        padding: const EdgeInsets.only(bottom: 20),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          mainAxisSpacing: 16,
          crossAxisSpacing: 12,
          childAspectRatio: childAspectRatio,
        ),
        itemBuilder: (context, index) {
          if (isLoading) {
            return const ShimmerCardItem();
          }

          final card = cards[index];

          return GestureDetector(
            onTap: () {},
            child: Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                    image: DecorationImage(
                      image: AssetImage(card.imagePath),
                      fit: BoxFit.cover,
                      colorFilter: card.isOwned
                          ? null
                          : ColorFilter.mode(
                              Colors.black.withOpacity(0.6), BlendMode.darken),
                    ),
                  ),
                ),
                if (!card.isOwned)
                  const Positioned.fill(
                    child: Center(
                      child: Icon(Icons.lock, color: Colors.white70, size: 30),
                    ),
                  ),
                Positioned(
                  bottom: 12,
                  left: 12,
                  right: 12,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        card.name,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        card.category.toUpperCase(),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
                if (card.isOwned)
                  Positioned(
                    bottom: 5,
                    right: 5,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: screenWidth * 0.025,
                        vertical: screenWidth * 0.012,
                      ),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFFFFD54F), Color(0xFFFFB300)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          bottomRight: Radius.circular(6),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Color.fromARGB(33, 255, 193, 7),
                            blurRadius: 6,
                            offset: Offset(1, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        '${card.quantity}',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: screenWidth * 0.03,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.3,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
