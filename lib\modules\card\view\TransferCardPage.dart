import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/TransferCard/TransferCardAppBar.dart';
import '../widgets/TransferCard/TransferAccountIdDialog.dart';
import '../widgets/TransferCard/TransferBottomBar.dart';
import '../widgets/TransferCard/TransferCategoryNav.dart';
import '../widgets/PhysicalCard/PhysicalCardRowItem.dart';
import '../widgets/PhysicalCard/FloatingCategoryButton.dart';
import '../widgets/loadings/PhysicalCard/PhysicalCardListShimmer.dart';
import '../widgets/CardPack/EmptyState.dart';
import '../view-Model/transferCard/TransferCardController.dart';
import '../widgets/TransferCard/TransferResultDialog.dart';
import '../view-Model/cardPack/CardCategoryViewModel.dart';

class TransferCardPage extends StatelessWidget {
  const TransferCardPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = context.watch<TransferCardController>();
    final isLoading = controller.isFetching;
    final isEmpty = !isLoading && controller.currentVisibleCards.isEmpty;
    final categoryVM = context.watch<CardCategoryViewModel>();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const TransferCardAppBar(),
      body: SafeArea(
        child: Stack(
          children: [
            Positioned.fill(
              child: Column(
                children: [
                  const SizedBox(height: 12),
                  TransferCategoryNav(
                    selectedCategory: controller.currentCategory,
                    onCategorySelected: controller.onCategoryChanged,
                    selectedCountMap: controller.selectedMap,
                    userCards: controller.allCards,
                  ),
                  const SizedBox(height: 12),
                  Expanded(
                    child: isLoading
                        ? const PhysicalCardListShimmer()
                        : isEmpty
                            ? const EmptyState()
                            : ListView.builder(
                                padding: const EdgeInsets.only(bottom: 100),
                                itemCount: controller.currentVisibleCards.length,
                                itemBuilder: (context, index) {
                                  final card = controller.currentVisibleCards[index];
                                  final isSelected = controller.selectedMap.containsKey(card.id);
                                  final quantity = controller.selectedMap[card.id] ?? 1;
                                  return PhysicalCardRowItem(
                                    card: card,
                                    isSelected: isSelected,
                                    selectedQuantity: quantity,
                                    onToggle: (val) =>
                                        controller.onToggleCardSelection(card.id, val),
                                    onQuantityChanged: (q) =>
                                        controller.onUpdateQuantity(card.id, q, context),
                                  );
                                },
                              ),
                  ),
                ],
              ),
            ),
            Positioned(
              bottom: 80,
              right: 0,
              child: FloatingCategoryButton(
                onCategorySelected: (category, series) {
                  controller.onSeriesAndCategoryChanged(series, category);
                },
                currentSeries: controller.currentSeries,
                showSeriesSelection: true,
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: TransferBottomBar(
        isGroupSelected: controller.groupSelected,
        onToggleGroup: controller.onToggleGroup,
        totalSelected: controller.totalSelected,
        isLoading: controller.isTransferring, // ✅ NEW
        onConfirm: () async {
          if (!controller.hasSelected) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text("Please select at least one card."),
                backgroundColor: Colors.redAccent,
              ),
            );
            return;
          }

          if (controller.totalSelected > 10) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text("Maximum of 10 cards allowed."),
                backgroundColor: Colors.redAccent,
              ),
            );
            return;
          }

          final accountId = await showDialog<String>(
            context: context,
            builder: (_) => const TransferAccountIdDialog(),
          );

          if (accountId == null || accountId.isEmpty) return;

          final isSuccess = await controller.transferCards(accountId);

          showDialog(
            context: context,
            builder: (_) => TransferResultDialog(
              isSuccess: isSuccess,
              accountId: accountId,
            ),
          );
        },
      ),
    );
  }
}
