import 'dart:convert';
import 'package:dio/dio.dart';
import '../model/ShopDetailModel.dart';
import 'package:luckymall/res/utility/network/api_client.dart';

final shopDetailService = ShopDetailService();

class ShopDetailService {
  final Dio publicDio = ApiClient.instance.publicDio;
  final Dio authDio = ApiClient.instance.authDio;

  static const String baseUrl = "https://api.luckymall.com.my";

  Future<ShopDetailModel?> fetchProductDetail(String productId) async {
    try {
      final url = '$baseUrl/product/$productId';
      
      print("🔍 Fetching product detail from: $url");
      print("🔍 Product ID: $productId");
      
      final response = await publicDio.get(url);
      
      print("📡 Response status: ${response.statusCode}");
      print("📡 Response data: ${response.data}");
      
      if (response.statusCode == 200) {
        final responseData = response.data;
        
        if (responseData["status"] == 200 && responseData["data"] != null) {
          return ShopDetailModel.fromApiJson(responseData["data"]);
        } else {
          print("⚠️ Product not found. Status: ${responseData["status"]}, Message: ${responseData["message"]}");
          return null;
        }
      } else if (response.statusCode == 404) {
        print("⚠️ Product not found: $productId");
        return null;
      } else {
        throw Exception("Failed to load product details: HTTP ${response.statusCode}");
      }
    } on DioException catch (e) {
      print("🚨 DioException details:");
      print("   Type: ${e.type}");
      print("   Message: ${e.message}");
      print("   Status Code: ${e.response?.statusCode}");
      print("   Response Data: ${e.response?.data}");
      print("   Request URL: ${e.requestOptions.uri}");
      
      if (e.response?.statusCode == 404) {
        print("⚠️ Product not found: $productId");
        return null;
      } else if (e.response?.statusCode == 500) {
        print("🚨 Server Error 500 - Check if product ID format is correct");
        print("🚨 Product ID received: '$productId' (length: ${productId.length})");
        
        // Try to check if the product ID is valid format
        if (productId.isEmpty) {
          throw Exception('Product ID is empty');
        }
        
        // Check if product ID is numeric (adjust based on your API requirements)
        final numericId = int.tryParse(productId);
        if (numericId == null) {
          throw Exception('Invalid product ID format: $productId (must be numeric)');
        }
        
        throw Exception('Server error (500): The server failed to process the request for product $productId');
      }
      
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print("🚨 Unexpected error: $e");
      throw Exception('Unexpected error: $e');
    }
  }

  // Update the reviews method with better error handling too
  Future<ProductReviewResponse> fetchProductReviews({
    required String productSpuId,
    String? productSkuId,
    int? score,
    int orderSource = 0,
    String? targetId,
    int limit = 10,
    int page = 1,
  }) async {
    try {
      print("🔍 Fetching reviews for product: $productSpuId");
      
      // Build query parameters
      final Map<String, dynamic> queryParams = <String, dynamic>{
        'productSpuId': productSpuId,
        'orderSource': orderSource,
        'limit': limit,
        'page': page,
      };
      
      // Add optional parameters
      if (productSkuId != null) queryParams['productSkuId'] = productSkuId;
      if (score != null) queryParams['score'] = score;
      if (targetId != null) queryParams['targetId'] = targetId;
      
      print("🔍 Query params: $queryParams");
      
      const url = '$baseUrl/order/comment';
      final response = await publicDio.get(url, queryParameters: queryParams);

      print("📡 Reviews response status: ${response.statusCode}");
      
      if (response.statusCode == 200) {
        final responseData = response.data;
        return ProductReviewResponse.fromApiJson(responseData);
      } else {
        throw Exception("Failed to load reviews: HTTP ${response.statusCode}");
      }
    } on DioException catch (e) {
      print("🚨 Reviews DioException: ${e.message}");
      if (e.response?.statusCode == 500) {
        print("🚨 Server Error 500 for reviews - Product may not exist or have reviews");
        // Return empty reviews instead of throwing error
        return ProductReviewResponse.empty();
      }
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print("🚨 Reviews error: $e");
      throw Exception('Unexpected error: $e');
    }
  }

  // Simplified method for basic usage
  Future<List<ProductReview>> fetchProductReviewsSimple(String productId, {int page = 1, int limit = 10}) async {
    try {
      final response = await fetchProductReviews(
        productSpuId: productId,
        page: page,
        limit: limit,
      );
      return response.data.list;
    } catch (e) {
      print("Error fetching reviews: $e");
      return [];
    }
  }

  // Add product to favorites
  Future<bool> addToFavorites(String productId) async {
    try {
      final url = '$baseUrl/product/$productId/favorite';
      final response = await authDio.post(url); // Use authDio for authenticated requests
      
      if (response.statusCode == 200) {
        final responseData = response.data;
        return responseData["status"] == 200;
      }
      return false;
    } on DioException catch (e) {
      print("Error adding to favorites: ${e.message}");
      return false;
    } catch (e) {
      print("Error adding to favorites: $e");
      return false;
    }
  }

  // Remove product from favorites
  Future<bool> removeFromFavorites(String productId) async {
    try {
      final url = '$baseUrl/product/$productId/favorite';
      final response = await authDio.delete(url); // Use authDio for authenticated requests
      
      if (response.statusCode == 200) {
        final responseData = response.data;
        return responseData["status"] == 200;
      }
      return false;
    } on DioException catch (e) {
      print("Error removing from favorites: ${e.message}");
      return false;
    } catch (e) {
      print("Error removing from favorites: $e");
      return false;
    }
  }
}