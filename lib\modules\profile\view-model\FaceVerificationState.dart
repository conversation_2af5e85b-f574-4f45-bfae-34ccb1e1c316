import 'package:flutter/foundation.dart';

/// Represents the face verification status for the current user
enum FaceVerificationStatus { notVerified, pendingApproval, verified }

/// Simple global state holder using ValueNotifier so UI can reactively update
class FaceVerificationStateManager {
  FaceVerificationStateManager._();

  static final FaceVerificationStateManager instance =
      FaceVerificationStateManager._();

  final ValueNotifier<FaceVerificationStatus> statusNotifier =
      ValueNotifier<FaceVerificationStatus>(FaceVerificationStatus.notVerified);

  void setNotVerified() {
    statusNotifier.value = FaceVerificationStatus.notVerified;
  }

  void setPendingApproval() {
    statusNotifier.value = FaceVerificationStatus.pendingApproval;
  }

  void setVerified() {
    statusNotifier.value = FaceVerificationStatus.verified;
  }
}
