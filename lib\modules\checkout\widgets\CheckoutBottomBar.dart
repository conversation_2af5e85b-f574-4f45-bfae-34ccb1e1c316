import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import '../view-model/CheckoutVM.dart';
import '../data/service/CheckoutService.dart';
import '../data/model/PaymentModel.dart';
import '../data/model/CheckoutModel.dart';

class CheckoutBottomBar extends StatelessWidget {
  final VoidCallback? onSuccess;

  const CheckoutBottomBar({super.key, this.onSuccess});

  @override
  Widget build(BuildContext context) {
    return Consumer<CheckoutVM>(
      builder: (context, viewModel, child) {
        final productData = viewModel.productData;
        debugPrint('🔍 CheckoutBottomBar building');
        debugPrint('📤 ProductData: $productData');
        debugPrint('📤 CheckoutType: ${viewModel.checkoutType}');
        debugPrint('📤 DeliveryType: ${viewModel.deliveryType}');

        final total = productData != null
            ? _calculateTotal(productData, viewModel)
            : 0.0;

        debugPrint('📤 Calculated total: $total');

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: const BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                blurRadius: 10,
                color: Colors.black26,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Show error message if any
                if (viewModel.errorMessage != null) ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red.shade600,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            viewModel.errorMessage!,
                            style: TextStyle(
                              color: Colors.red.shade700,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () => viewModel.clearError(),
                          icon: Icon(
                            Icons.close,
                            color: Colors.red.shade600,
                            size: 20,
                          ),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ],
                    ),
                  ),
                ],

                Row(
                  children: [
                    // Total display
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Total',
                            style: TextStyle(fontSize: 14, color: Colors.grey),
                          ),
                          Text(
                            'RM${total.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Pay button
                    SizedBox(
                      width: 120,
                      height: 52,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFFFB800),
                          foregroundColor: Colors.black,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                          disabledBackgroundColor: Colors.grey.shade300,
                          disabledForegroundColor: Colors.grey.shade600,
                        ),
                        onPressed: viewModel.isLoading || !viewModel.isFormValid
                            ? null
                            : viewModel.checkoutType == CheckoutType.blindbox
                            ? () => _handleSubmitBlindbox(context, viewModel)
                            : () => _handleSubmitShop(context, viewModel),
                        child: viewModel.isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.black,
                                  ),
                                ),
                              )
                            : Text(
                                _getButtonText(viewModel.checkoutType),
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getButtonText(CheckoutType checkoutType) {
    switch (checkoutType) {
      case CheckoutType.shop:
      case CheckoutType.blindbox:
      case CheckoutType.physicalCard:
        return 'Pay Now';
      case CheckoutType.luckydraw:
        return 'Claim';
    }
  }

  double _calculateTotal(
    Map<String, dynamic> productData,
    CheckoutVM viewModel,
  ) {
    double total = productData['price'] ?? 0.0;

    // Subtract product discount
    if (productData['discount'] != null) {
      total -= productData['discount'];
    }

    // Subtract voucher discount
    if (viewModel.selectedVoucher != null) {
      total -= viewModel.voucherDiscount;
    }

    // Add delivery fee if delivery is selected
    if (viewModel.deliveryType == DeliveryType.delivery &&
        productData['deliveryFee'] != null) {
      total += productData['deliveryFee'];
    }

    return total;
  }

  void _handleSubmitShop(BuildContext context, CheckoutVM viewModel) async {
    try {
      // Validate form before submission
      if (!viewModel.isFormValid) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Please fill in all required fields'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // Validate required data
      if (viewModel.selectedAddress == null ||
          viewModel.selectedPaymentGateway == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Please select address and payment method'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      final productData = viewModel.productData;
      if (productData == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Product data not available'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Additional validation for address ID
      if (viewModel.selectedAddress!.id == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Selected address has no ID'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Debug: Print data to identify null values
      debugPrint('🔍 Product data: $productData');
      debugPrint('🔍 Selected address ID: ${viewModel.selectedAddress!.id}');
      debugPrint(
        '🔍 Selected payment gateway: ${viewModel.selectedPaymentGateway}',
      );
      debugPrint(
        '🔍 productSpuId: ${productData['productSpuId']} (type: ${productData['productSpuId'].runtimeType})',
      );
      debugPrint(
        '🔍 productSkuId: ${productData['productSkuId']} (type: ${productData['productSkuId'].runtimeType})',
      );
      debugPrint(
        '🔍 quantity: ${productData['quantity']} (type: ${productData['quantity'].runtimeType})',
      );
      debugPrint(
        '🔍 stock: ${productData['stock']} (type: ${productData['stock'].runtimeType})',
      );

      // Safely convert potential null values to integers
      final int productSpuId = (productData['productSpuId'] is int)
          ? productData['productSpuId']
          : int.tryParse(productData['productSpuId']?.toString() ?? '0') ?? 0;

      final int productSkuId = (productData['productSkuId'] is int)
          ? productData['productSkuId']
          : int.tryParse(productData['productSkuId']?.toString() ?? '0') ?? 0;

      final int quantity = (productData['quantity'] is int)
          ? productData['quantity']
          : int.tryParse(productData['quantity']?.toString() ?? '1') ?? 1;

      final int stock = (productData['stock'] is int)
          ? productData['stock']
          : int.tryParse(productData['stock']?.toString() ?? '0') ?? 0;

      // Create order model
      final toOrderModel = ToOrderModel(
        addressId: viewModel.selectedAddress!.id!,
        products: [
          Products(
            productSpuId: productSpuId,
            productSkuId: productSkuId,
            productSkuImage: productData['productSkuImage'] ?? '',
            productSkuName:
                productData['productSkuName'] ?? (productData['title'] ?? ''),
            productSkuPropertys:
                productData['productSkuPropertys'] ??
                (productData['variant'] ?? ''),
            quantity: quantity,
            salePrice: (productData['salePrice'] ?? productData['price'] ?? 0)
                .toDouble(),
            totalSalePrice:
                (productData['totalSalePrice'] ?? productData['price'] ?? 0)
                    .toDouble(),
            cardDeduction: (productData['cardDeduction'] ?? 0).toDouble(),
            totalCardDeduction: (productData['totalCardDeduction'] ?? 0)
                .toDouble(),
            useCardDeduction: (productData['useCardDeduction'] ?? 0).toDouble(),
            payableAmount:
                (productData['payableAmount'] ?? productData['price'] ?? 0)
                    .toDouble(),
            stock: stock,
          ),
        ],
        userNote: viewModel.remarksController.text.trim().isNotEmpty
            ? viewModel.remarksController.text.trim()
            : null,
        couponId: _getCouponId(viewModel.selectedVoucher),
      );

      // Map payment gateway name to correct API gateway value
      String gatewayValue = _mapPaymentGateway(
        viewModel.selectedPaymentGateway!,
      );
      debugPrint('🔍 Mapped gateway value: $gatewayValue');

      // Debug: Print order model details
      debugPrint('🔍 ToOrderModel - addressId: ${toOrderModel.addressId}');
      debugPrint(
        '🔍 ToOrderModel - products count: ${toOrderModel.products.length}',
      );
      debugPrint('🔍 ToOrderModel - userNote: ${toOrderModel.userNote}');
      debugPrint('🔍 ToOrderModel - couponId: ${toOrderModel.couponId}');
      debugPrint(
        '🔍 User details - name: ${viewModel.nameController.text.trim()}',
      );
      debugPrint(
        '🔍 User details - email: ${viewModel.emailController.text.trim()}',
      );
      debugPrint(
        '🔍 User details - phone: ${viewModel.phoneController.text.trim()}',
      );

      // Call CheckoutService createOrder directly
      final paymentUrl = await CheckoutService().createOrder(
        toOrderModel,
        gatewayValue,
        viewModel.nameController.text.trim(),
        viewModel.emailController.text.trim(),
        viewModel.phoneController.text.trim(),
      );

      debugPrint('🔍 Payment URL received: $paymentUrl');

      if (context.mounted) {
        if (paymentUrl.isNotEmpty) {
          // Open payment URL in browser
          _launchPaymentUrl(paymentUrl);

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Order placed successfully! Redirecting to payment...',
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );

          // Call onSuccess callback if provided
          if (onSuccess != null) {
            onSuccess!();
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Payment URL not received'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Safely extracts coupon ID from selected voucher
  int? _getCouponId(Map<String, dynamic>? selectedVoucher) {
    if (selectedVoucher == null || selectedVoucher['id'] == null) {
      return null;
    }

    try {
      final id = selectedVoucher['id'];
      if (id is int) {
        return id;
      } else if (id is String) {
        return int.tryParse(id);
      } else {
        return int.tryParse(id.toString());
      }
    } catch (e) {
      debugPrint('Error parsing coupon ID: $e');
      return null;
    }
  }

  /// Maps payment gateway display name to API gateway key
  String _mapPaymentGateway(String gatewayName) {
    switch (gatewayName) {
      case 'Billplz':
        return 'billplz';
      case 'Touch \'n Go':
        return 'tng';
      case 'Stripe':
        return 'stripe';
      case 'iPay88':
        return 'ipay88';
      case 'Lucky Mall':
        return 'cash';
      default:
        // If it's already the gateway key, return as is
        return gatewayName.toLowerCase();
    }
  }

  /// Opens the payment URL in the default browser
  Future<void> _launchPaymentUrl(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('Could not launch $url');
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }

  void _handleSubmitBlindbox(BuildContext context, CheckoutVM viewModel) async {
    try {
      final success = await viewModel.submitOrder();

      if (context.mounted && success) {
        // For luckydraw, navigate to ClaimProductView instead of showing dialog
        if (viewModel.checkoutType == CheckoutType.luckydraw) {
          context.go('/lucky-draw/claim-product');
          return;
        }

        // Show success dialog for other checkout types
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 80),
                  const SizedBox(height: 16),
                  Text(
                    _getSuccessMessage(viewModel.checkoutType),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getSuccessSubtitle(viewModel.checkoutType),
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (onSuccess != null) {
                      onSuccess!();
                    } else {
                      Navigator.of(context).pop(); // Go back to previous screen
                    }
                  },
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getSuccessMessage(CheckoutType checkoutType) {
    switch (checkoutType) {
      case CheckoutType.shop:
        return 'Order Placed Successfully!';
      case CheckoutType.blindbox:
        return 'BlindBox Purchased!';
      case CheckoutType.luckydraw:
        return 'Prize Claimed Successfully!';
      case CheckoutType.physicalCard:
        return 'Physical Card Order Placed!';
    }
  }

  String _getSuccessSubtitle(CheckoutType checkoutType) {
    switch (checkoutType) {
      case CheckoutType.shop:
        return 'Thank you for your purchase. We\'ll process your order shortly.';
      case CheckoutType.blindbox:
        return 'Your mystery box will be prepared and shipped soon!';
      case CheckoutType.luckydraw:
        return 'Congratulations! Your prize will be delivered according to your preferences.';
      case CheckoutType.physicalCard:
        return 'Your physical card will be prepared and shipped soon!';
    }
  }
}
