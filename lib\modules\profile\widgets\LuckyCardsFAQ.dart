import 'package:flutter/material.dart';

class LuckyCardsFAQPage extends StatelessWidget {
  const LuckyCardsFAQPage({super.key});

  final List<Map<String, String>> faqs = const [
    {
      'question': 'What are Lucky Cards?',
      'answer': 'Lucky Cards are digital reward cards you can collect during special promotions or campaigns.'
    },
    {
      'question': 'How can I earn Lucky Cards?',
      'answer': 'You can earn Lucky Cards by completing certain tasks, like making purchases or referring friends.'
    },
    {
      'question': 'Do Lucky Cards expire?',
      'answer': 'Yes, each card has an expiration date. Please check your card details in the app.'
    },
    {
      'question': 'Can I trade or sell Lucky Cards?',
      'answer': 'No, Lucky Cards are non-transferable and cannot be sold or traded.'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        iconTheme: const IconThemeData(color: Colors.black),
        title: const Text(
          'Lucky Cards FAQ',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        centerTitle: true,
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: faqs.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final faq = faqs[index];
          return Card(
            elevation: 1,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ExpansionTile(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              title: Row(
                children: [
                  const Icon(Icons.help_outline, color: Color.fromARGB(255, 13, 13, 13)),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      faq['question']!,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
              children: [
                Text(
                  faq['answer']!,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
