import 'package:flutter/material.dart';
import '../InfoDialog/PhysicalCardDialog.dart';

class PhysicalCardAppBar extends StatelessWidget implements PreferredSizeWidget {
  const PhysicalCardAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final totalHeight = statusBarHeight + 70;

    return Container(
      height: totalHeight,
      padding: EdgeInsets.only(
        top: statusBarHeight + 10,
        left: 16,
        right: 16,
        bottom: 12,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color.fromARGB(83, 0, 0, 0),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button (no circle)
          IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
            onPressed: () => Navigator.pop(context),
            tooltip: 'Back',
          ),

          // Title
          const Text(
            'Select Physical Card',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: Colors.black,
            ),
          ),

          // Info button (no circle)
          IconButton(
            icon: const Icon(Icons.info_outline, color: Colors.black),
            onPressed: () {
              showDialog(
                context: context, 
                builder: (_) => PhysicalCardDialog(),
              );
            },
            tooltip: 'Info',
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 20);
}
