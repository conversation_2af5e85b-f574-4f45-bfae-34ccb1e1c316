import '../model/ShopCartModel.dart';

class ShopCartService {
  static final List<ShopCartItem> _cartItems = [
    ShopCartItem(
      id: "1",
      image: "https://img.lazcdn.com/g/ff/kf/S35ed6742ef0d46e9868125e6337f68d4y.jpg_720x720q80.jpg",
      title: "<PERSON><PERSON><PERSON> & Jamie <PERSON>'s Classic All-In-One Pot With Lid - 30cm",
      variation: "Default",
      price: 369.00,
      quantity: 1,
      selected: false,
    ),
  ];

  static Future<List<ShopCartItem>> fetchCartItems() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return List<ShopCartItem>.from(_cartItems);
  }

  static Future<void> updateCartItem(ShopCartItem item) async {
    final index = _cartItems.indexWhere((e) => e.id == item.id);
    if (index != -1) _cartItems[index] = item;
  }

  static Future<void> selectAll(bool selected) async {
    for (var item in _cartItems) {
      item.selected = selected;
    }
  }
}