import 'package:flutter/material.dart';

class ProductRatingSection extends StatelessWidget {
  final double rating;
  final VoidCallback? onViewAllPressed;

  const ProductRatingSection({
    super.key,
    required this.rating,
    this.onViewAllPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Rating and View All row
          Row(
            children: [
              // Rating with stars
              Row(
                children: [
                  Text(
                    rating.toString(),
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < rating.floor()
                            ? Icons.star
                            : (index < rating && rating % 1 != 0)
                            ? Icons.star_half
                            : Icons.star_border,
                        color: Colors.amber[700],
                        size: 20,
                      );
                    }),
                  ),
                ],
              ),
              const Spacer(),
              // View All button
              GestureDetector(
                onTap: onViewAllPressed,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFC107),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: const Text(
                    'View All',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Product Ratings & Reviews text
          const Text(
            'Product Ratings & Reviews',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}
