import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../data/service/BlindboxProductServices.dart';
import '../data/model/BlindboxProductModel.dart';
import '../data/model/BlindboxDetailModel.dart';
import '../view-model/BlindboxCategoryBarVM.dart';

// Service provider
final blindboxProductServicesProvider = Provider<BlindboxProductServices>((
  ref,
) {
  return blindboxProductServices;
});

// Categories provider (from API)
final blindboxCategoriesProvider = FutureProvider<List<BlindboxCategoryModel>>((
  ref,
) async {
  final service = ref.read(blindboxProductServicesProvider);
  return await service.getBlindboxCategories();
});

// Selected category state (0 = All, other values = category ID)
final selectedCategoryIdProvider = StateProvider<int>((ref) => 0);

// Main products provider that handles all cases with a single method
// This provider automatically handles category switching and caches results
// to prevent unnecessary API calls when switching between categories
final blindboxProductsProvider =
    FutureProvider.family<Map<String, dynamic>, int>((ref, categoryId) async {
      final service = ref.read(blindboxProductServicesProvider);

      try {
        if (categoryId == 0) {
          // Fetch all products (no category filter)
          return await service.getBlindboxProducts();
        } else {
          // Fetch products by specific category
          return await service.getBlindboxProducts(categoryId: categoryId);
        }
      } catch (e) {
        rethrow;
      }
    });

// Search products provider
final blindboxSearchProductsProvider =
    FutureProvider.family<Map<String, dynamic>, Map<String, dynamic>>((
      ref,
      params,
    ) async {
      final query = params['query'] ?? '';
      if (query.trim().isEmpty) {
        return {
          'products': <BlindboxProductModel>[],
          'totalCount': 0,
          'pageSize': 10,
          'totalPage': 0,
          'currentPage': 1,
        };
      }

      final service = ref.read(blindboxProductServicesProvider);

      try {
        return await service.getBlindboxProducts(
          limit: (params['limit'] as int?) ?? 10,
          page: (params['page'] as int?) ?? 1,
          sortBy: params['sortBy'] as String?,
          ascending: params['ascending'] as bool?,
          searchQuery: query,
        );
      } catch (e) {
        rethrow;
      }
    });

// Products by price range provider
final blindboxProductsByPriceRangeProvider =
    FutureProvider.family<Map<String, dynamic>, Map<String, dynamic>>((
      ref,
      params,
    ) async {
      final service = ref.read(blindboxProductServicesProvider);

      try {
        return await service.getBlindboxProducts(
          minPrice: (params['minPrice'] as num?)?.toDouble() ?? 0.0,
          maxPrice: (params['maxPrice'] as num?)?.toDouble() ?? double.infinity,
          limit: (params['limit'] as int?) ?? 10,
          page: (params['page'] as int?) ?? 1,
          sortBy: params['sortBy'] as String? ?? 'salePrice',
          ascending: params['ascending'] as bool? ?? true,
        );
      } catch (e) {
        rethrow;
      }
    });

// Provider for getting product detail by unboxId
final blindboxProductDetailProvider =
    FutureProvider.family<BlindboxDetailModel, int>((ref, unboxId) async {
      final service = ref.read(blindboxProductServicesProvider);
      return await service.getProductDetail(unboxId);
    });

// Category provider
final blindboxCategoryProvider = ChangeNotifierProvider<BlindboxCategoryBarVM>((
  ref,
) {
  return BlindboxCategoryBarVM();
});

// Search results provider
final blindboxSearchResultsProvider = StateProvider<List<BlindboxProductModel>>(
  (ref) => [],
);

// Search query provider
final blindboxSearchQueryProvider = StateProvider<String>((ref) => '');

// Loading state provider
final blindboxLoadingProvider = StateProvider<bool>((ref) => false);

// Pagination state provider
final blindboxPaginationProvider = StateProvider<Map<String, dynamic>>(
  (ref) => {'page': 1, 'limit': 10, 'hasMore': true},
);

// Filter state provider
final blindboxFilterProvider = StateProvider<Map<String, dynamic>>(
  (ref) => {
    'category': 'all',
    'minPrice': null,
    'maxPrice': null,
    'sortBy': 'created_at',
    'ascending': false,
  },
);
