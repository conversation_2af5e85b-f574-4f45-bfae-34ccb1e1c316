import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class SwitchLanguageView extends StatefulWidget {
  const SwitchLanguageView({super.key});

  @override
  State<SwitchLanguageView> createState() => _SwitchLanguageViewState();
}

class _SwitchLanguageViewState extends State<SwitchLanguageView> {
  final List<String> languages = [
    'English',
    'Bahasa Melayu',
    'Simplified Chinese',
  
  ];

  int selectedIndex = 0; // Default to English

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        centerTitle: true,
        title: const Text(
          'Switch Language',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      backgroundColor: Colors.white,
      body: Column(
        children: [
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: List.generate(languages.length, (index) {
                final isSelected = selectedIndex == index;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(16),
                    onTap: () {
                      setState(() {
                        selectedIndex = index;
                      });
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      curve: Curves.ease,
                      decoration: BoxDecoration(
                        color: isSelected ? const Color(0xFFFFF3D6) : const Color(0xFFF6F6F6),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: isSelected ? const Color(0xFFFFC700) : Colors.transparent,
                          width: 2,
                        ),
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: const Color(0xFFFFC700).withOpacity(0.15),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ]
                            : [],
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                        leading: Icon(
                          isSelected ? Icons.language : Icons.language_outlined,
                          color: isSelected ? const Color(0xFFFF9900) : Colors.grey,
                          size: 28,
                        ),
                        title: Text(
                          languages[index],
                          style: TextStyle(
                            color: isSelected ? const Color(0xFFFF9900) : Colors.black,
                            fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                            fontSize: 18,
                          ),
                        ),
                        trailing: isSelected
                            ? Container(
                                decoration: BoxDecoration(
                                  color: const Color(0xFFFFC700),
                                  shape: BoxShape.circle,
                                ),
                                padding: const EdgeInsets.all(4),
                                child: const Icon(Icons.check, color: Colors.white, size: 20),
                              )
                            : null,
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),
          const Spacer(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
            child: SizedBox(
              width: double.infinity,
              height: 54,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFFC700),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(14),
                  ),
                  elevation: 0,
                ),
                onPressed: () {
                  // TODO: Implement language change logic
                  context.pop();
                },
                child: const Text(
                  'Confirm',
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w700,
                    fontSize: 20,
                  ),
                ),
                ),
            ),
          ),
        ],
      ),
    );
  }
}