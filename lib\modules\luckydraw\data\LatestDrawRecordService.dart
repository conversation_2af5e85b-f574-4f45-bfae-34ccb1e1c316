import 'package:dio/dio.dart';
import '../model/LatestDrawSummary.dart';
import '../model/LuckyDrawRecord.dart';

final latestDrawRecordService = LatestDrawRecordService();

class LatestDrawRecordService {
  final Dio dio = Dio(
    BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
    ),
  );

  /// Get latest draw summaries for main page display
  /// Endpoint: GET /product/point/latest
  ///
  /// Returns: Map with records list and pagination info
  Future<Map<String, dynamic>> getLatestDrawSummaries() async {
    try {
      print('LatestDrawRecordService: getLatestDrawSummaries called');

      final response = await dio.get(
        'https://api.luckymall.com.my/product/point/latest',
      );

      print(
        'LatestDrawRecordService: API response status: ${response.statusCode}',
      );
      print('LatestDrawRecordService: API response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData['status'] == 200) {
          final List<dynamic> recordList = responseData['data']['list'] ?? [];
          print('LatestDrawRecordService: Record list from API: $recordList');

          final records = recordList
              .map((json) => _mapApiResponseToLatestDrawSummary(json))
              .toList();

          print('LatestDrawRecordService: Mapped records: ${records.length}');

          return {
            'records': records,
            'totalCount': responseData['data']['totalCount'] ?? 0,
            'pageSize': responseData['data']['pageSize'] ?? 10,
            'totalPage': responseData['data']['totalPage'] ?? 1,
            'currentPage': responseData['data']['currentPage'] ?? 1,
          };
        } else {
          throw Exception('API Error: ${responseData['message']}');
        }
      } else {
        throw Exception(
          'Failed to load latest draw records: HTTP ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      print('LatestDrawRecordService: DioException: ${e.message}');
      print('LatestDrawRecordService: DioException type: ${e.type}');

      String errorMessage = 'Network error';
      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = 'Connection timeout';
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Response timeout';
      } else if (e.type == DioExceptionType.badResponse) {
        errorMessage = 'Server error: ${e.response?.statusCode}';
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'Connection failed';
      }

      throw Exception('$errorMessage: ${e.message}');
    } catch (e) {
      print('LatestDrawRecordService: Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  /// Get latest draw records for detailed view (LatestDrawRecordView)
  /// Endpoint: GET /product/point/latest
  ///
  /// Returns: Map with records list and pagination info
  Future<Map<String, dynamic>> getLatestDrawRecords() async {
    try {
      print('LatestDrawRecordService: getLatestDrawRecords called');

      final response = await dio.get(
        'https://api.luckymall.com.my/product/point/latest',
      );

      print(
        'LatestDrawRecordService: API response status: ${response.statusCode}',
      );
      print('LatestDrawRecordService: API response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData['status'] == 200) {
          final List<dynamic> recordList = responseData['data']['list'] ?? [];
          print('LatestDrawRecordService: Record list from API: $recordList');

          final records = recordList
              .map((json) => _mapApiResponseToLuckyDrawRecord(json))
              .toList();

          print('LatestDrawRecordService: Mapped records: ${records.length}');

          return {
            'records': records,
            'totalCount': responseData['data']['totalCount'] ?? 0,
            'pageSize': responseData['data']['pageSize'] ?? 10,
            'totalPage': responseData['data']['totalPage'] ?? 1,
            'currentPage': responseData['data']['currentPage'] ?? 1,
          };
        } else {
          throw Exception('API Error: ${responseData['message']}');
        }
      } else {
        throw Exception(
          'Failed to load latest draw records: HTTP ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      print('LatestDrawRecordService: DioException: ${e.message}');
      print('LatestDrawRecordService: DioException type: ${e.type}');

      String errorMessage = 'Network error';
      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = 'Connection timeout';
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Response timeout';
      } else if (e.type == DioExceptionType.badResponse) {
        errorMessage = 'Server error: ${e.response?.statusCode}';
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'Connection failed';
      }

      throw Exception('$errorMessage: ${e.message}');
    } catch (e) {
      print('LatestDrawRecordService: Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  /// Get winning history for a specific product
  /// Endpoint: GET /product/point/history/{goodsPointId}
  ///
  /// Query params: limit, page (optional)
  /// Returns: Map with records list and pagination info
  Future<Map<String, dynamic>> getProductWinningHistory({
    required int goodsPointId,
    int? limit,
    int? page,
  }) async {
    try {
      print(
        'LatestDrawRecordService: getProductWinningHistory called for id: ' +
            goodsPointId.toString(),
      );

      final response = await dio.get(
        'https://api.luckymall.com.my/product/point/history/' +
            goodsPointId.toString(),
        queryParameters: {
          if (limit != null) 'limit': limit,
          if (page != null) 'page': page,
        },
      );

      print(
        'LatestDrawRecordService: API response status: ' +
            (response.statusCode?.toString() ?? ''),
      );
      print(
        'LatestDrawRecordService: API response data: ' +
            response.data.toString(),
      );

      if (response.statusCode == 200) {
        final responseData = response.data;

        // Some environments use status 0, others 200; treat both as success
        final dynamic status = responseData['status'];
        if (status == 0 || status == 200) {
          final List<dynamic> recordList = responseData['data']?['list'] ?? [];
          final records = recordList
              .map(
                (json) => _mapApiResponseToLuckyDrawRecord(
                  json as Map<String, dynamic>,
                ),
              )
              .toList();

          return {
            'records': records,
            'totalCount': responseData['data']?['totalCount'] ?? 0,
            'pageSize': responseData['data']?['pageSize'] ?? (limit ?? 10),
            'totalPage': responseData['data']?['totalPage'] ?? 1,
            'currentPage': responseData['data']?['currentPage'] ?? (page ?? 1),
          };
        } else {
          throw Exception(
            'API Error: ' + (responseData['message']?.toString() ?? 'Unknown'),
          );
        }
      } else {
        throw Exception(
          'Failed to load product winning history: HTTP ' +
              (response.statusCode?.toString() ?? ''),
        );
      }
    } on DioException catch (e) {
      print('LatestDrawRecordService: DioException: ' + (e.message ?? ''));
      print('LatestDrawRecordService: DioException type: ' + e.type.toString());

      String errorMessage = 'Network error';
      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = 'Connection timeout';
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Response timeout';
      } else if (e.type == DioExceptionType.badResponse) {
        errorMessage =
            'Server error: ' + (e.response?.statusCode?.toString() ?? '');
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'Connection failed';
      }

      throw Exception(errorMessage + ': ' + (e.message ?? ''));
    } catch (e) {
      print('LatestDrawRecordService: Unexpected error: ' + e.toString());
      throw Exception('Unexpected error: ' + e.toString());
    }
  }

  /// Map API response to LatestDrawSummary model for main page
  /// Mapping according to user requirements:
  /// itemName = productName
  /// imageUrl = productImage
  /// winningId = memberCode (from user object)
  /// dateTime = createdTime
  /// winnerName = nickname (from user object)
  /// avatarUrl = avatar (from user object)
  LatestDrawSummary _mapApiResponseToLatestDrawSummary(
    Map<String, dynamic> json,
  ) {
    // Handle user object that might be null (as in the second record)
    String nickname = '';
    String avatar = '';
    String memberCode = '';
    if (json['user'] != null && json['user'] is Map<String, dynamic>) {
      nickname = json['user']['nickname']?.toString() ?? '';
      avatar = json['user']['avatar']?.toString() ?? '';
      memberCode = json['user']['memberCode']?.toString() ?? '';
    }

    return LatestDrawSummary(
      itemName: json['productName']?.toString() ?? '',
      imageUrl: json['productImage']?.toString() ?? '',
      winningId: memberCode,
      dateTime: json['createdTime']?.toString() ?? '',
      winnerName: nickname,
      avatarUrl: avatar,
    );
  }

  /// Map API response to LuckyDrawRecord model for detailed view
  /// This maps to the existing LuckyDrawRecord model structure
  LuckyDrawRecord _mapApiResponseToLuckyDrawRecord(Map<String, dynamic> json) {
    final String createdTime = json['createdTime']?.toString() ?? '';

    // Handle user object that might be null (as in the second record)
    String nickname = '';
    if (json['user'] != null && json['user'] is Map<String, dynamic>) {
      nickname = json['user']['nickname']?.toString() ?? '';
    }

    // Parse date and time from createdTime
    String date = '';
    String time = '';

    if (createdTime.isNotEmpty) {
      try {
        final DateTime dateTime = DateTime.parse(createdTime);
        date =
            '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
        time =
            '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } catch (e) {
        print('LatestDrawRecordService: Error parsing date: $e');
        date = createdTime;
        time = createdTime;
      }
    }

    return LuckyDrawRecord(
      username: nickname,
      productTitle: json['productName']?.toString() ?? '',
      date: date,
      time: time,
      period: json['period']?.toString() ?? '',
      totalShares: json['totalAmount']?.toString() ?? '',
      participationPoint: json['amount']?.toString() ?? '',
      winningName: nickname,
      imageUrl: json['productImage']?.toString() ?? '',
    );
  }
}
