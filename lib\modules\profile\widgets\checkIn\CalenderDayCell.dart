import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../model/CheckingModel.dart';
import '../../view-model/CheckInViewModel.dart';

class CalendarDayCell extends StatefulWidget {
  final CheckInDay day;
  const CalendarDayCell({super.key, required this.day});
  
  @override
  State<CalendarDayCell> createState() => _CalendarDayCellState();
}

class _CalendarDayCellState extends State<CalendarDayCell>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnim;
  late Animation<double> _scaleAnim;
  bool _hasAnimated = false;
  
  Widget _buildRotatedStamp() {
    return Transform.rotate(
      angle: 20 * pi / 180,
      child: Image.asset(
        'assets/images/Checked.png',
        height: 40,
        fit: BoxFit.contain,
      ),
    );
  }
  
  @override
  void didUpdateWidget(covariant CalendarDayCell oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the day has changed (navigated to a new month), reset animation state
    if (widget.day.date != oldWidget.day.date) {
      _controller.reset();
      _hasAnimated = false;
    }
  }
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnim = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );

    _scaleAnim = Tween<double>(begin: 1.8, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
  }

  void _onClaim(CheckInViewModel vm) {
    final today = DateUtils.dateOnly(DateTime.now());
    final isSameMonth = widget.day.date.year == today.year &&
                        widget.day.date.month == today.month;
    final isToday = DateUtils.isSameDay(widget.day.date, today);

    if (isToday && isSameMonth && !vm.isDayClaimed(widget.day.date)) {
      vm.claimDay(widget.day.date);
      _controller.forward(from: 0.0);
      _hasAnimated = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    final vm = Provider.of<CheckInViewModel>(context);
    final claimed = vm.isDayClaimed(widget.day.date);
    final today = DateUtils.dateOnly(DateTime.now());
    final isToday = DateUtils.isSameDay(today, widget.day.date);

    return GestureDetector(
        onTap: () => _onClaim(vm),
        child: Container(
          width: 55,
          height: 55,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: isToday ? Colors.blue : Colors.grey[300]!,
              width: isToday ? 2 : 1,
            ),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Stamp image (behind everything)
              if (claimed)
                (_hasAnimated)
                ? ScaleTransition(
                    scale: _scaleAnim,
                    child: _buildRotatedStamp(),
                  )
                : _buildRotatedStamp(),

              // Day number and +1 (on top)
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${widget.day.date.day}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: claimed ? Colors.green[800] : Colors.black87,
                    ),
                  ),

                  const SizedBox(height: 2),

                  // +1 animation (visible only if not yet claimed)
                  if (!claimed)
                    FadeTransition(
                      opacity: _fadeAnim,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.amber.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          '+1',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.black87,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }


}
