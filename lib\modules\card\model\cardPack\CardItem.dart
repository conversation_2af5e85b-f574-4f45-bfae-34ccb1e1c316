class CardItem {
  final String id;
  final String name;
  final String category;
  final String series;
  final String imagePath; // local for now, URL later
  final String rank;
  final int quantity;
  final bool isOwned;

  const CardItem({
    required this.id,
    required this.name,
    required this.category,
    required this.series,
    required this.imagePath,
    required this.rank,
    this.quantity = 1,
    this.isOwned = false,
  });

  // Add copyWith method for immutable updates
  CardItem copyWith({
    String? id,
    String? name,
    String? category,
    String? series,
    String? imagePath,
    String? rank,
    int? quantity,
    bool? isOwned,
  }) {
    return CardItem(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      series: series ?? this.series,
      imagePath: imagePath ?? this.imagePath,
      rank: rank ?? this.rank,
      quantity: quantity ?? this.quantity,
      isOwned: isOwned ?? this.isOwned,
    );
  }

  // Add fromJson factory for serialization
  factory CardItem.fromJson(Map<String, dynamic> json) {
    return CardItem(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      series: json['series'] as String,
      imagePath: json['imagePath'] as String,
      rank: json['rank'] as String,
      quantity: json['quantity'] as int? ?? 1,
      isOwned: json['isOwned'] as bool? ?? false,
    );
  }

  // Add toJson method for serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'series': series,
      'imagePath': imagePath,
      'rank': rank,
      'quantity': quantity,
      'isOwned': isOwned,
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CardItem &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          category == other.category &&
          series == other.series &&
          imagePath == other.imagePath &&
          rank == other.rank &&
          quantity == other.quantity &&
          isOwned == other.isOwned;

  @override
  int get hashCode =>
      id.hashCode ^
      name.hashCode ^
      category.hashCode ^
      series.hashCode ^
      imagePath.hashCode ^
      rank.hashCode ^
      quantity.hashCode ^
      isOwned.hashCode;
}