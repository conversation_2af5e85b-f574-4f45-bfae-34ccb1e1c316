import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import '../../modules/nav/tabItem.dart'; // to access TabItem enum

class BottomNavBar extends StatefulWidget {
  final TabItem currentTab;
  final ValueChanged<TabItem> onSelectTab;

  const BottomNavBar({
    super.key,
    required this.currentTab,
    required this.onSelectTab,
  });

  @override
  State<BottomNavBar> createState() => _BottomNavBarState();
}

class _BottomNavBarState extends State<BottomNavBar>
    with TickerProviderStateMixin {
  final Map<TabItem, bool> _hoverStates = {
    for (var tab in TabItem.values) tab: false,
  };

  final Map<TabItem, AnimationController> _animationControllers = {};

  @override
  void initState() {
    super.initState();
    // Initialize animation controllers for each tab
    for (var tab in TabItem.values) {
      _animationControllers[tab] = AnimationController(
        duration: const Duration(milliseconds: 1000),
        vsync: this,
      );
    }

    // Play animation for the initially selected tab
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationControllers[widget.currentTab]?.forward();
    });
  }

  @override
  void dispose() {
    // Dispose all animation controllers
    for (var controller in _animationControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant BottomNavBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the current tab changed externally (not through tap), update animation
    if (widget.currentTab != oldWidget.currentTab) {
      // Reset all animations and play the current tab
      for (var tab in TabItem.values) {
        _animationControllers[tab]?.reset();
        if (tab == widget.currentTab) {
          _animationControllers[tab]?.forward();
        }
      }
    }
  }

  void _onNavItemTapped(int index) {
    final selected = TabItem.values[index];

    // Reset all animations to beginning and start the selected one
    for (var tab in TabItem.values) {
      _animationControllers[tab]?.reset();
      if (tab == selected) {
        _animationControllers[tab]?.forward();
      }
    }

    widget.onSelectTab(selected);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border(top: BorderSide(color: Colors.grey.shade200, width: 1.0))),
        child:BottomNavigationBar(
      elevation: 0,
      backgroundColor: Colors.white,
      type: BottomNavigationBarType.fixed,
      currentIndex: TabItem.values.indexOf(widget.currentTab),
      onTap: _onNavItemTapped,
      selectedItemColor: Colors.amber,
      unselectedItemColor: const Color.fromRGBO(153, 153, 153, 1),
      items: TabItem.values.map((tab) {
        final assetBase = tab.assetName;

        final String lottieV2Path = 'assets/lottie/${assetBase}V2.json';

        return BottomNavigationBarItem(
          label: assetBase[0].toUpperCase() + assetBase.substring(1),
          icon: MouseRegion(
            onEnter: (_) => setState(() => _hoverStates[tab] = true),
            onExit: (_) => setState(() => _hoverStates[tab] = false),
            child: Lottie.asset(
              lottieV2Path,
              width: 30,
              height: 30,
              controller: _animationControllers[tab],
              repeat: false,
              errorBuilder: (context, error, stackTrace) =>
                  const Icon(Icons.error, color: Colors.red, size: 28),
            ),
          ),
        );
      }).toList(),
    ));
  }
}
