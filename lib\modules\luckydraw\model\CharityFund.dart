class CharityFund {
  final int totalAmount;
  final int lastUpdated;

  const CharityFund({required this.totalAmount, required this.lastUpdated});

  factory CharityFund.fromJson(Map<String, dynamic> json) {
    return CharityFund(
      totalAmount: (json['data'] as num?)?.toInt() ?? 0,
      lastUpdated: (json['timestamp'] as num?)?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {'data': totalAmount, 'timestamp': lastUpdated};
  }

  // Get formatted date string from timestamp
  String get formattedLastUpdated {
    if (lastUpdated == 0) return '';
    final date = DateTime.fromMillisecondsSinceEpoch(lastUpdated);
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }

  @override
  String toString() {
    return 'CharityFund(totalAmount: $totalAmount, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CharityFund &&
        other.totalAmount == totalAmount &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode => totalAmount.hashCode ^ lastUpdated.hashCode;
}
