import 'package:flutter/material.dart';
import '../data/LuckyDrawRepository.dart';
import '../model/ParticipationRecord.dart';

enum ClaimStatus { idle, claiming, success, failed }

class ClaimVoucherVM extends ChangeNotifier {
  final LuckyDrawRepository repository;

  ClaimVoucherVM({LuckyDrawRepository? repository})
    : repository = repository ?? MockLuckyDrawRepository();

  ClaimStatus claimStatus = ClaimStatus.idle;
  String? errorMessage;
  String? successMessage;

  // Claim details
  String? participationId;
  ParticipationRecord? participationRecord;

  // Delivery address for normal prizes
  Map<String, String> deliveryAddress = {
    'fullName': '',
    'phoneNumber': '',
    'address': '',
    'city': '',
    'state': '',
    'postalCode': '',
    'country': 'Malaysia',
  };

  bool get isLoading => claimStatus == ClaimStatus.claiming;
  bool get isSuccess => claimStatus == ClaimStatus.success;
  bool get isFailed => claimStatus == ClaimStatus.failed;

  void initialize(String participationId, ParticipationRecord record) {
    this.participationId = participationId;
    participationRecord = record;
    notifyListeners();
  }

  Future<void> claimPrize({Map<String, String>? address}) async {
    if (participationId == null || participationRecord == null) {
      errorMessage = 'Invalid participation record';
      claimStatus = ClaimStatus.failed;
      notifyListeners();
      return;
    }

    claimStatus = ClaimStatus.claiming;
    errorMessage = null;
    successMessage = null;
    notifyListeners();

    try {
      final claimType = participationRecord!.prizeType == PrizeType.tngVoucher
          ? 'tngVoucher'
          : 'normal';

      await repository.claimPrize(
        participationId: participationId!,
        claimType: claimType,
        deliveryAddress: address ?? deliveryAddress,
      );

      claimStatus = ClaimStatus.success;
      successMessage = claimType == 'tngVoucher'
          ? 'TNG Voucher claimed successfully! Check your E-Wallet.'
          : 'Prize claim submitted successfully! You will receive a tracking number soon.';
    } catch (e) {
      claimStatus = ClaimStatus.failed;
      errorMessage = _getErrorMessage(e.toString());
    }

    notifyListeners();
  }

  String _getErrorMessage(String error) {
    if (error.contains('PRIZE_ALREADY_CLAIMED')) {
      return 'This prize has already been claimed.';
    } else if (error.contains('INVALID_PARTICIPATION')) {
      return 'Invalid participation record.';
    } else if (error.contains('NETWORK')) {
      return 'Network error. Please check your connection and try again.';
    } else {
      return 'Failed to claim prize. Please try again.';
    }
  }

  void updateDeliveryAddress(Map<String, String> address) {
    deliveryAddress = Map.from(address);
    notifyListeners();
  }

  void updateAddressField(String field, String value) {
    deliveryAddress[field] = value;
    notifyListeners();
  }

  bool validateDeliveryAddress() {
    final requiredFields = [
      'fullName',
      'phoneNumber',
      'address',
      'city',
      'state',
      'postalCode',
    ];

    for (String field in requiredFields) {
      if (deliveryAddress[field]?.trim().isEmpty ?? true) {
        return false;
      }
    }

    // Validate phone number format (basic validation)
    final phoneNumber = deliveryAddress['phoneNumber']!;
    if (!RegExp(
      r'^(\+?6?01[02-46-9])[0-9]{7,8}$',
    ).hasMatch(phoneNumber.replaceAll(RegExp(r'[\s-]'), ''))) {
      return false;
    }

    // Validate postal code format for Malaysia
    final postalCode = deliveryAddress['postalCode']!;
    if (!RegExp(r'^\d{5}$').hasMatch(postalCode)) {
      return false;
    }

    return true;
  }

  List<String> getValidationErrors() {
    List<String> errors = [];

    if (deliveryAddress['fullName']?.trim().isEmpty ?? true) {
      errors.add('Full name is required');
    }

    if (deliveryAddress['phoneNumber']?.trim().isEmpty ?? true) {
      errors.add('Phone number is required');
    } else {
      final phoneNumber = deliveryAddress['phoneNumber']!;
      if (!RegExp(
        r'^(\+?6?01[02-46-9])[0-9]{7,8}$',
      ).hasMatch(phoneNumber.replaceAll(RegExp(r'[\s-]'), ''))) {
        errors.add('Invalid Malaysian phone number format');
      }
    }

    if (deliveryAddress['address']?.trim().isEmpty ?? true) {
      errors.add('Address is required');
    }

    if (deliveryAddress['city']?.trim().isEmpty ?? true) {
      errors.add('City is required');
    }

    if (deliveryAddress['state']?.trim().isEmpty ?? true) {
      errors.add('State is required');
    }

    if (deliveryAddress['postalCode']?.trim().isEmpty ?? true) {
      errors.add('Postal code is required');
    } else {
      final postalCode = deliveryAddress['postalCode']!;
      if (!RegExp(r'^\d{5}$').hasMatch(postalCode)) {
        errors.add('Postal code must be 5 digits');
      }
    }

    return errors;
  }

  void resetStatus() {
    claimStatus = ClaimStatus.idle;
    errorMessage = null;
    successMessage = null;
    notifyListeners();
  }

  void clearError() {
    errorMessage = null;
    if (claimStatus == ClaimStatus.failed) {
      claimStatus = ClaimStatus.idle;
    }
    notifyListeners();
  }
}
