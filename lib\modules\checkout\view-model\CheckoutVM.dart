import 'package:flutter/material.dart';
import 'package:luckymall/modules/checkout/data/service/CheckoutService.dart';
import 'package:luckymall/modules/checkout/data/model/CheckoutModel.dart';
import 'package:luckymall/modules/checkout/data/model/PaymentModel.dart';
import 'package:luckymall/modules/profile/model/AddressModel.dart';

enum CheckoutType { shop, blindbox, luckydraw, physicalCard }

enum DeliveryType { delivery, pickup }

class CheckoutVM extends ChangeNotifier {
  // Checkout configuration
  final CheckoutType checkoutType;
  DeliveryType _deliveryType = DeliveryType.delivery;
  DeliveryType get deliveryType => _deliveryType;

  // Payment state
  String? _selectedPaymentGateway;
  String? get selectedPaymentGateway => _selectedPaymentGateway;

  // Voucher state
  Map<String, dynamic>? _selectedVoucher;
  Map<String, dynamic>? get selectedVoucher => _selectedVoucher;

  // Address state
  Addressmodel? _selectedAddress;
  Addressmodel? get selectedAddress => _selectedAddress;

  // Form controllers
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController remarksController = TextEditingController();

  // Product information
  Map<String, dynamic>? _productData;
  Map<String, dynamic>? get productData => _productData;

  // Checkout data from API
  CheckoutReceiveModel? _checkoutData;
  CheckoutReceiveModel? get checkoutData => _checkoutData;

  // Loading state
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // Error state
  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  // Payment gateways (only showing available ones for now)
  final List<Map<String, dynamic>> paymentGateways = [
    {
      'gateway': 'billplz',
      'name': 'Billplz',
      'iconSelected': 'assets/icons/checkOutCard/billPlz2.jpg',
      'iconUnselected': 'assets/icons/checkOutCard/billPlz.jpg',
    },
    {
      'gateway': 'tng',
      'name': 'Touch \'n Go',
      'iconSelected': 'assets/icons/checkOutCard/tngLogo.png',
      'iconUnselected': 'assets/icons/checkOutCard/tngLogo2.png',
    },
    {
      'gateway': 'stripe',
      'name': 'Stripe',
      'iconSelected': 'assets/icons/checkOutCard/stripe2.jpg',
      'iconUnselected': 'assets/icons/checkOutCard/stripe.jpg',
    },
    {
      'gateway': 'ipay88',
      'name': 'iPay88',
      'iconSelected': 'assets/icons/checkOutCard/ipay882.jpg',
      'iconUnselected': 'assets/icons/checkOutCard/ipay88.jpg',
    },
    {
      'gateway': 'cash',
      'name': 'Lucky Mall',
      'iconSelected': 'assets/icons/checkOutCard/lmWallet2.jpg',
      'iconUnselected': 'assets/icons/checkOutCard/lmWallet.jpg',
    },
  ];

  CheckoutVM({required this.checkoutType}) {
    // For blindbox, always set to delivery (no pickup option)
    if (checkoutType == CheckoutType.blindbox) {
      _deliveryType = DeliveryType.delivery;
    }

    // For physical card, always set to delivery (no pickup option)
    if (checkoutType == CheckoutType.physicalCard) {
      _deliveryType = DeliveryType.delivery;
    }
  }

  // Delivery type methods
  void setDeliveryType(DeliveryType type) {
    // Don't allow pickup for blindbox
    if (checkoutType == CheckoutType.blindbox && type == DeliveryType.pickup) {
      return;
    }
    _deliveryType = type;
    notifyListeners();
  }

  // Payment methods
  void selectPaymentGateway(String gateway) {
    _selectedPaymentGateway = gateway;
    notifyListeners();
  }

  // Voucher methods
  void selectVoucher(Map<String, dynamic>? voucher) {
    _selectedVoucher = voucher;
    notifyListeners();
  }

  void clearVoucher() {
    _selectedVoucher = null;
    notifyListeners();
  }

  // Address methods
  void selectAddress(Addressmodel address) {
    _selectedAddress = address;
    notifyListeners();

    // Refresh checkout data with the new address
    if (_productData != null) {
      getCheckoutData();
    }
  }

  void clearAddress() {
    _selectedAddress = null;
    notifyListeners();
  }

  // Product data
  void setProductData(Map<String, dynamic> data) {
    _productData = data;
    notifyListeners();
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Form validation methods
  bool get isFormValid {
    // For luckydraw, no validation needed since no billing info required
    if (checkoutType == CheckoutType.luckydraw) {
      return true;
    }

    // Basic validation for other checkout types
    bool isValid = true;

    // Check if name is not empty
    if (nameController.text.trim().isEmpty) {
      isValid = false;
    }

    // Check if phone is not empty
    if (phoneController.text.trim().isEmpty) {
      isValid = false;
    }

    // Check if email is not empty and has valid format
    if (emailController.text.trim().isEmpty ||
        !_isValidEmail(emailController.text.trim())) {
      isValid = false;
    }

    // Check if payment gateway is selected (for paid checkouts)
    if (requiresPayment && _selectedPaymentGateway == null) {
      isValid = false;
    }

    // Check if address is selected (for delivery)
    if (_deliveryType == DeliveryType.delivery && _selectedAddress == null) {
      isValid = false;
    }

    return isValid;
  }

  // Email validation helper
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // Get validation errors for specific fields
  String? get nameError {
    if (nameController.text.trim().isEmpty) {
      return 'Name is required';
    }
    return null;
  }

  String? get phoneError {
    if (phoneController.text.trim().isEmpty) {
      return 'Phone number is required';
    }
    return null;
  }

  String? get emailError {
    if (emailController.text.trim().isEmpty) {
      return 'Email is required';
    }
    if (!_isValidEmail(emailController.text.trim())) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? get addressError {
    if (_deliveryType == DeliveryType.delivery && _selectedAddress == null) {
      return 'Please select a delivery address';
    }
    return null;
  }

  String? get paymentGatewayError {
    if (requiresPayment && _selectedPaymentGateway == null) {
      return 'Please select a payment method';
    }
    return null;
  }

  // Checkout configuration getters
  bool get showDeliveryOption =>
      checkoutType == CheckoutType.shop ||
      checkoutType == CheckoutType.luckydraw ||
      checkoutType == CheckoutType.physicalCard;

  bool get showPaymentMethods =>
      checkoutType == CheckoutType.shop ||
      checkoutType == CheckoutType.blindbox ||
      checkoutType == CheckoutType.physicalCard;

  bool get requiresPayment =>
      checkoutType == CheckoutType.shop ||
      checkoutType == CheckoutType.blindbox ||
      checkoutType == CheckoutType.physicalCard;

  bool get showVoucherOption =>
      checkoutType == CheckoutType.shop ||
      checkoutType == CheckoutType.blindbox;

  // Get voucher discount amount
  double get voucherDiscount {
    if (_selectedVoucher != null && _selectedVoucher!['amount'] != null) {
      return _selectedVoucher!['amount'].toDouble();
    }
    return 0.0;
  }

  // Get voucher description
  String get voucherDescription {
    if (_selectedVoucher != null && _selectedVoucher!['description'] != null) {
      return _selectedVoucher!['description'];
    }
    return '';
  }

  // Check if voucher can be applied
  bool canApplyVoucher(Map<String, dynamic> voucher) {
    if (voucher['minSpend'] == null) return false;
    final orderAmount =
        (_productData?['price'] ?? 0.0) - (_productData?['discount'] ?? 0.0);
    return orderAmount >= voucher['minSpend'];
  }

  // Get available vouchers (filtered by minimum spend)
  List<Map<String, dynamic>> getAvailableVouchers(
    List<Map<String, dynamic>> allVouchers,
  ) {
    if (_productData == null) return [];
    final orderAmount =
        (_productData!['price'] ?? 0.0) - (_productData!['discount'] ?? 0.0);
    return allVouchers
        .where(
          (voucher) =>
              voucher['minSpend'] != null && orderAmount >= voucher['minSpend'],
        )
        .toList();
  }

  // API Integration Methods

  // Get checkout data from API
  Future<CheckoutReceiveModel?> getCheckoutData() async {
    if (_productData == null) {
      return null;
    }

    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Create checkout send model
      final checkoutSendModel = CheckoutSendModel(
        products: [
          Products(
            productSpuId: _productData!['productSpuId'] ?? 0,
            productSkuId: _productData!['productSkuId'] ?? 0,
            productSkuImage: _productData!['productSkuImage'] ?? '',
            productSkuName: _productData!['productSkuName'] ?? '',
            productSkuPropertys: _productData!['productSkuPropertys'] ?? '',
            quantity: _productData!['quantity'] ?? 1,
            salePrice: (_productData!['salePrice'] ?? 0).toDouble(),
            totalSalePrice: (_productData!['totalSalePrice'] ?? 0).toDouble(),
            cardDeduction: (_productData!['cardDeduction'] ?? 0).toDouble(),
            totalCardDeduction: (_productData!['totalCardDeduction'] ?? 0)
                .toDouble(),
            useCardDeduction: (_productData!['useCardDeduction'] ?? 0)
                .toDouble(),
            payableAmount: (_productData!['payableAmount'] ?? 0).toDouble(),
            stock: _productData!['stock'] ?? 0,
          ),
        ],
        // Add address if selected
        addressId: _selectedAddress?.id,
        // Add voucher if selected
        couponId: _selectedVoucher?['id'] != null
            ? int.tryParse(_selectedVoucher!['id'].toString())
            : null,
      );

      final result = await checkoutService.getCheckoutData(
        checkoutSendModel,
        _selectedAddress?.id,
      );

      _isLoading = false;
      notifyListeners();

      _checkoutData = result; // Store the result

      // Map API model into productData for UI display compatibility
      if (result.list.isNotEmpty) {
        final first = result.list.first;
        _productData = {
          'title': first.productSkuName,
          'imageUrl': first.productSkuImage,
          'variant': first.productSkuPropertys,
          'price': first.salePrice,
          'quantity': first.quantity,
          'discount': (first.totalSalePrice - first.payableAmount) > 0
              ? (first.totalSalePrice - first.payableAmount)
              : 0.0,
          'deliveryFee': result.deliveryFee,
          // Preserve IDs if present from original input
          'productSpuId': first.productSpuId,
          'productSkuId': first.productSkuId,
        };
      }
      // Auto-set address from API response only if no address is currently selected
      if (_selectedAddress == null) {
        _selectedAddress = result.address;
        notifyListeners();
      }
      return result;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to get checkout data: $e';
      notifyListeners();
      return null;
    }
  }

  // Create order and get payment URL
  Future<String?> createOrder() async {
    if (_selectedAddress == null || _selectedPaymentGateway == null) {
      _errorMessage = 'Please select address and payment method';
      notifyListeners();
      return null;
    }

    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Create order model
      final toOrderModel = ToOrderModel(
        addressId: _selectedAddress!.id!,
        products: [
          Products(
            productSpuId: _productData!['productSpuId'] ?? 0,
            productSkuId: _productData!['productSkuId'] ?? 0,
            productSkuImage: _productData!['productSkuImage'] ?? '',
            productSkuName:
                _productData!['productSkuName'] ??
                (_productData!['title'] ?? ''),
            productSkuPropertys:
                _productData!['productSkuPropertys'] ??
                (_productData!['variant'] ?? ''),
            quantity: _productData!['quantity'] ?? 1,
            salePrice:
                (_productData!['salePrice'] ?? _productData!['price'] ?? 0)
                    .toDouble(),
            totalSalePrice:
                (_productData!['totalSalePrice'] ?? _productData!['price'] ?? 0)
                    .toDouble(),
            cardDeduction: (_productData!['cardDeduction'] ?? 0).toDouble(),
            totalCardDeduction: (_productData!['totalCardDeduction'] ?? 0)
                .toDouble(),
            useCardDeduction: (_productData!['useCardDeduction'] ?? 0)
                .toDouble(),
            payableAmount:
                (_productData!['payableAmount'] ?? _productData!['price'] ?? 0)
                    .toDouble(),
            stock: _productData!['stock'] ?? 0,
          ),
        ],
        userNote: remarksController.text.trim().isNotEmpty
            ? remarksController.text.trim()
            : null,
        // Add voucher if selected
        couponId: _selectedVoucher?['id'] != null
            ? int.tryParse(_selectedVoucher!['id'].toString())
            : null,
      );

      final paymentUrl = await checkoutService.createOrder(
        toOrderModel,
        _selectedPaymentGateway!,
        nameController.text.trim(),
        emailController.text.trim(),
        phoneController.text.trim(),
      );

      _isLoading = false;
      notifyListeners();

      return paymentUrl;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to create order: $e';
      notifyListeners();
      return null;
    }
  }

  // Submission method
  Future<bool> submitOrder() async {
    try {
      // Get checkout data first
      final checkoutData = await getCheckoutData();
      if (checkoutData == null) {
        return false;
      }

      // Create order and get payment URL
      final paymentUrl = await createOrder();
      if (paymentUrl == null) {
        return false;
      }

      // TODO: Handle payment URL (open webview, redirect, etc.)
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    phoneController.dispose();
    emailController.dispose();
    remarksController.dispose();
    super.dispose();
  }
}
