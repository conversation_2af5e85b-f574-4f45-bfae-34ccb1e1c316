import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/AddressForm.dart';
import '../widgets/SubmitButton.dart';
import '../service/AddressService.dart';
import '../model/AddressModel.dart';

class UpdateAddressView extends StatefulWidget {
  final Map<String, dynamic> address;

  const UpdateAddressView({super.key, required this.address});

  @override
  State<UpdateAddressView> createState() => _UpdateAddressViewState();
}

class _UpdateAddressViewState extends State<UpdateAddressView> {
  late TextEditingController _recipientController;
  late TextEditingController _mobileController;
  late TextEditingController _postcodeController;
  late TextEditingController _detailedAddressController;
  late bool _isDefault;
  late Addressmodel _address;
  final _formKey = GlobalKey<FormState>();
  final AddressService _addressService = AddressService();
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _initializeAddress();
  }

  void _initializeAddress() {
    // Convert the passed address data to Addressmodel
    _address = Addressmodel(
      id: widget.address['id'] ?? 0,
      country: widget.address['country'] ?? 60,
      recipient: widget.address['recipient'] ?? '',
      mobile: widget.address['mobile'] ?? '',
      postcode: widget.address['postcode'] ?? 0,
      detailedAddress: widget.address['detailedAddress'] ?? '',
      isDefault: widget.address['isDefault'] ?? false,
    );

    _recipientController = TextEditingController(text: _address.recipient);
    _mobileController = TextEditingController(text: _address.mobile);
    _postcodeController = TextEditingController(
      text: _address.postcode.toString(),
    );
    _detailedAddressController = TextEditingController(
      text: _address.detailedAddress,
    );
    _isDefault = _address.isDefault;
  }

  @override
  void dispose() {
    _recipientController.dispose();
    _mobileController.dispose();
    _postcodeController.dispose();
    _detailedAddressController.dispose();
    super.dispose();
  }

  Future<void> _submitAddress() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final updatedAddress = Addressmodel(
        id: _address.id,
        country: _address.country,
        recipient: _recipientController.text.trim(),
        mobile: _mobileController.text.trim(),
        postcode: int.parse(_postcodeController.text.trim()),
        detailedAddress: _detailedAddressController.text.trim(),
        isDefault: _isDefault,
      );

      // Use the service to update the address
      final success = await _addressService.updateAddress(updatedAddress);

      if (success) {
        // Navigate back to address list
        if (mounted) {
          context.pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating address: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        title: const Text(
          'Update Address',
          style: TextStyle(color: Colors.black),
        ),
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            AddressForm(
              formKey: _formKey,
              recipientController: _recipientController,
              mobileController: _mobileController,
              postcodeController: _postcodeController,
              addressController: _detailedAddressController,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: _isDefault,
                  onChanged: (value) {
                    setState(() {
                      _isDefault = value ?? false;
                    });
                  },
                ),
                const Text('Set as Default Address'),
              ],
            ),
            const SizedBox(height: 24),
            SubmitButton(onSubmit: _submitAddress),
          ],
        ),
      ),
    );
  }
}
