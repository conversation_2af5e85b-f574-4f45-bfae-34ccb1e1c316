import 'AddressModel.dart';

class MyOrderModel {
  // for list of order
  int? id; // Order ID
  int?
  source; // Order source (0: mall order; 1: card mailing; 10: points mall order; 30: group purchase order)
  int? orderType; // Order type (0: Express delivery; 10: Pickup)
  double? totalPrice; // Total order amount
  int?
  state; // Order status (-1: Cancelled; 0: Waiting for payment; 10: Waiting for shipment; 20: Waiting for receipt; 100: Completed;)
  bool? isAppraise; // Whether to evaluate
  String? expiredTime; // Expiration time
  String? createdTime; // creation time
  List<OrderItem>? orderItems;

  MyOrderModel({
    this.id,
    this.source,
    this.orderType,
    this.totalPrice,
    this.state,
    this.isAppraise,
    this.expiredTime,
    this.createdTime,
    this.orderItems,
  });

  factory MyOrderModel.fromJson(Map<String, dynamic> json) {
    return MyOrderModel(
      id: json['id'] != null ? int.tryParse(json['id'].toString()) : null,
      source: json['source'] != null
          ? int.tryParse(json['source'].toString())
          : null,
      orderType: json['orderType'] != null
          ? int.tryParse(json['orderType'].toString())
          : null,
      totalPrice: (json['totalPrice'] as num?)?.toDouble(),
      state: json['state'] != null
          ? int.tryParse(json['state'].toString())
          : null,
      isAppraise: json['isAppraise'] == true,
      expiredTime: json['expiredTime']?.toString(),
      createdTime: json['createdTime']?.toString(),
      orderItems: json['list'] != null
          ? (json['list'] as List<dynamic>)
                .map((item) => OrderItem.fromJson(item))
                .toList()
          : null,
    );
  }
}

class OrderItem {
  // item model
  int? id;
  int? productSpuId;
  int? cardId;
  String? productSkuImage;
  String? productSkuName;
  int? quantity;
  double? unitPrice;
  double? discountPrice;
  double?
  payableAmount; // Total sales amount of goods (total sales price of goods - member discount price - discount amount)

  OrderItem({
    this.id,
    this.productSpuId,
    this.cardId,
    this.productSkuImage,
    this.productSkuName,
    this.quantity,
    this.unitPrice,
    this.discountPrice,
    this.payableAmount,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      id: json['id'] != null ? int.tryParse(json['id'].toString()) : null,
      productSpuId: json['productSpuId'] != null
          ? int.tryParse(json['productSpuId'].toString())
          : null,
      cardId: json['cardId'] != null
          ? int.tryParse(json['cardId'].toString())
          : null,
      productSkuImage: json['productSkuImage']?.toString(),
      productSkuName: json['productSkuName']?.toString(),
      quantity: json['quantity'] != null
          ? int.tryParse(json['quantity'].toString())
          : null,
      unitPrice: (json['unitPrice'] as num?)?.toDouble(),
      discountPrice: (json['discountPrice'] as num?)?.toDouble(),
      payableAmount: (json['payableAmount'] as num?)?.toDouble(),
    );
  }
}

class MyOrderDetail {
  // detail of order
  int? id;
  int? orderType;
  int? source;
  String? orderSn;
  double? deliveryFee;
  String? logisticCode;
  double? cardDeduction;
  double? totalPrice;
  int?
  state; // 订单状态(-10:已退款; -1：已取消; 0:待支付; 10:待发货; 20:待收货; 100:已完成;) //please translate this
  DateTime? expiredTime;
  DateTime? completeTime;
  DateTime? createdTime;
  DateTime? payTime;
  List<OrderItem>? orderItems;
  String? userNote;
  Addressmodel? address;

  MyOrderDetail({
    this.id,
    this.orderType,
    this.source,
    this.orderSn,
    this.deliveryFee,
    this.logisticCode,
    this.cardDeduction,
    this.totalPrice,
    this.state,
    this.expiredTime,
    this.completeTime,
    this.createdTime,
    this.payTime,
    this.orderItems,
    this.userNote,
    this.address,
  });

  factory MyOrderDetail.fromJson(Map<String, dynamic> json) {
    return MyOrderDetail(
      id: json['id'] != null ? int.tryParse(json['id'].toString()) : null,
      orderType: json['orderType'] != null
          ? int.tryParse(json['orderType'].toString())
          : null,
      source: json['source'] != null
          ? int.tryParse(json['source'].toString())
          : null,
      orderSn: json['orderSn']?.toString(),
      deliveryFee: (json['deliveryFee'] as num?)?.toDouble(),
      logisticCode: json['logisticCode']?.toString(),
      cardDeduction: (json['cardDeduction'] as num?)?.toDouble(),
      totalPrice: (json['totalPrice'] as num?)?.toDouble(),
      state: json['state'] != null
          ? int.tryParse(json['state'].toString())
          : null,
      expiredTime: json['expiredTime'] != null
          ? DateTime.tryParse(json['expiredTime'])
          : null,
      completeTime: json['completeTime'] != null
          ? DateTime.tryParse(json['completeTime'])
          : null,
      createdTime: json['createdTime'] != null
          ? DateTime.tryParse(json['createdTime'])
          : null,
      payTime: json['payTime'] != null
          ? DateTime.tryParse(json['payTime'])
          : null,
      orderItems: json['list'] != null
          ? (json['list'] as List<dynamic>)
                .map((item) => OrderItem.fromJson(item))
                .toList()
          : null,
      userNote: json['userNote']?.toString(),
      address: json['address'] != null
          ? Addressmodel.fromJson(json['address'])
          : null,
    );
  }
}

class MyOrderTrack {
  // for track specific order?? idk
  DateTime? acceptTime;
  String?
  state; // not sure what is the function of this one, probably others as well
  String? entrySiteCode;
  String? nextSiteCode;
  double? longitude;
  double? latitude;
  String? employeePhone;
  String? operatorPhone;
  String? operatorName;
  String? sigPicUrl;

  MyOrderTrack({
    this.acceptTime,
    this.state,
    this.entrySiteCode,
    this.nextSiteCode,
    this.longitude,
    this.latitude,
    this.employeePhone,
    this.operatorPhone,
    this.operatorName,
    this.sigPicUrl,
  });

  factory MyOrderTrack.fromJson(Map<String, dynamic> json) {
    return MyOrderTrack(
      acceptTime: json['acceptTime'] != null
          ? DateTime.tryParse(json['acceptTime'])
          : null,
      state: json['state']?.toString(),
      entrySiteCode: json['entrySiteCode']?.toString(),
      nextSiteCode: json['nextSiteCode']?.toString(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      latitude: (json['latitude'] as num?)?.toDouble(),
      employeePhone: json['employeePhone']?.toString(),
      operatorPhone: json['operatorPhone']?.toString(),
      operatorName: json['operatorName']?.toString(),
      sigPicUrl: json['sigPicUrl']?.toString(),
    );
  }
}
