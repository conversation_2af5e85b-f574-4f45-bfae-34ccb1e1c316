import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:luckymall/modules/card/model/purchaseCard/PurchaseCardOrder.dart';
import 'package:luckymall/modules/card/model/reveal/RevealCard.dart';
import 'package:luckymall/modules/profile/model/AddressModel.dart';
import 'auth_guard.dart';
import 'package:provider/provider.dart';

// Main tab views
import '../modules/shop/view/ShoplistView.dart';
import '../modules/card/view/CardPage.dart';
import '../modules/luckydraw/view/MainpageView.dart';
import '../modules/blindbox/view/BlindboxView.dart';
import '../modules/profile/view/UserProfileView.dart' as profile;

// Lucky Draw sub-views
import '../modules/luckydraw/view/PrizeRevealView.dart';
import '../modules/luckydraw/view/GroupPurchaseView.dart';
import '../modules/luckydraw/view/ParticipationRecordsView.dart';
import '../modules/luckydraw/view/ClaimVoucherView.dart';
import '../modules/luckydraw/view/ClaimVoucherEWalletView.dart';
import '../modules/luckydraw/view/WinningHistoryView.dart';
import '../modules/luckydraw/view/CommunitySharingView.dart';
import '../modules/luckydraw/view/LatestDrawRecordView.dart';
import '../modules/luckydraw/view/ReviewDetailsView.dart';
import '../modules/luckydraw/view/ProductSearchPage.dart';
import '../modules/luckydraw/widgets/PointProductDetails.dart';
import '../modules/luckydraw/view/ClaimVoucherTngView.dart';
import '../modules/luckydraw/view/ClaimProductView.dart';
import '../modules/luckydraw/model/LuckyDrawProduct.dart';

// Blindbox sub-views
import '../modules/blindbox/view/BlindboxShopView.dart';
import '../modules/blindbox/view/BlindboxBuyView.dart';
import '../modules/blindbox/view/BlindboxGroupBuyView.dart';
import '../modules/blindbox/view/PostView.dart';
import '../modules/blindbox/view/PostProfileView.dart';
import '../modules/blindbox/view/BlindboxSearchView.dart';
import '../modules/blindbox/view/BlindboxRevealView.dart';
import '../modules/blindbox/view/BlindboxReviewView.dart';

// Card sub-views
import '../modules/card/view/CardPackPage.dart';
import '../modules/card/view/PurchaseCardPage.dart';
import '../modules/card/view/checkOutCard/CheckOutPage.dart';
import '../modules/card/view/historyCard/HistoryCardPage.dart';
import '../modules/card/view/historyCard/HistoryCardDetailPage.dart';
import '../modules/card/view/physicalCard/SelectPhysicalCardPage.dart';
import '../modules/card/model/historyCard/HistoryCardItem.dart';
import '../modules/card/widgets/MessagePurchase/PurchaseResultPage.dart';
import '../modules/card/view/TransferCardPage.dart';
import '../modules/card/view-Model/transferCard/TransferCardController.dart';
import '../modules/card/view-Model/cardPack/MockCards.dart'; // for allCardCatalog
import '../modules/card/view/ExchangeCardPage.dart';
import '../modules/card/view/redemption/ConfirmRedemptionPage.dart';
import '../modules/card/widgets/ExchangeCard/RedemptionResultPage.dart';
import '../modules/card/view/revealCard/RevealCardGridPage.dart';
import '../modules/card/view/revealCard/RevealFlowWrapper.dart';
import '../modules/card/view/scanCode/ScanCodePage.dart';
import '../modules/card/view/scanCode/ScanCardAnimationPage.dart';
import '../modules/card/view/scanCode/ScanCardRevealPage.dart';
import '../modules/card/view-Model/scanCode/RevealsScanCardViewModel.dart';
import '../modules/card/model/exchangeCard/ExchangeCardModel.dart' as model;
import '../modules/card/view-Model/purchaseCard/PurchaseCardViewModel.dart';
import '../modules/card/view-Model/cardPack/CardCategoryViewModel.dart';
import '../modules/card/services/SeriesService.dart';

// Checkout
import '../modules/checkout/view/CheckoutView.dart';
import '../modules/checkout/view-model/CheckoutVM.dart';
import '../modules/checkout/view/SelectVoucherView.dart';
import '../modules/checkout/view/SelectAddressView.dart';

// Shop sub-views
import '../modules/shop/view/ShopCartView.dart';
import '../modules/shop/view/ShopDetailView.dart';
import '../modules/favourite/view/FavouriteView.dart';

// Profile sub-views
import '../modules/profile/view/AddAddressView.dart';
import '../modules/profile/view/AddressView.dart';
import '../modules/profile/view/UpdateAddress.dart';
import '../modules/profile/view/NotificationView.dart';
import '../modules/profile/view/VoucherView.dart';
import '../modules/profile/view/MySharingView.dart';
import '../modules/profile/view/UserPostView.dart';
import '../modules/profile/view/myorder/MyOrderView.dart';
import '../modules/profile/view/SharePostView.dart';
import '../modules/profile/view/myorder/RefundView.dart';
import '../modules/profile/view/myorder/RefundStatusView.dart';
import '../modules/profile/view/myorder/ToReceiveView.dart';
import '../modules/profile/view/myorder/ToShipView.dart';
import '../modules/profile/view/myorder/CompletedOrderDetailsView.dart';
import '../modules/profile/view/myorder/CancelledOrderDetailsView.dart';
import '../modules/profile/view/myorder/ShippingInformationView.dart';
import '../data/model/OrderModel.dart';
import '../modules/profile/view/FaceVerificationView.dart';
import '../modules/profile/view/MyReview/ReviewPageView.dart';
import '../modules/profile/view/checkIn/CheckInPage.dart';
import '../modules/profile/view/UserProfileInfoView.dart';
import '../modules/profile/view/EditUserProfileView.dart';
import '../modules/profile/view/Settings/ProfileSettingsView.dart';
import '../modules/profile/view/Settings/ChangePasswordView.dart';
import '../modules/profile/view/Settings/DeactivateAccountView.dart';
import '../modules/profile/view/Settings/SwitchLanguageView.dart';
import '../modules/profile/view/Settings/TermConditionsView.dart';
import '../modules/profile/view/Settings/PrivacyPolicyView.dart';
import '../modules/profile/view/Settings/ReturnView.dart';
import '../modules/profile/view/Settings/MerchantPolicyView.dart';
import '../modules/profile/view/Settings/FAQViews.dart';
import '../modules/profile/view/Settings/AboutUsView.dart';
import '../modules/profile/view/BalanceView.dart';
import '../modules/profile/view/WithdrawView.dart';
import '../modules/profile/view/ChooseBackgroundView.dart';
import '../modules/profile/view/LuckyGroupBuyView.dart';
import '../modules/profile/view/inviteFriend/InviteFriendPage.dart';

// Authorization views
import '../modules/authorization/view/PasswordLoginView.dart';
import '../modules/authorization/view/ForgotPassword.dart';
import '../modules/authorization/view/RegisterView.dart';
import '../modules/authorization/view/OtpLoginView.dart';

// App shell for bottom navigation
import '../app_shell.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/blind-box/shop',
    redirect: authGuard,
    routes: [
      // Authentication routes (outside shell - no bottom nav)
      GoRoute(
        path: '/login',
        builder: (context, state) => const PasswordLoginView(),
      ),
      GoRoute(
        path: '/forgot-password',
        builder: (context, state) => const ForgotPasswordView(),
      ),
      GoRoute(
        path: '/register',
        builder: (context, state) => const RegisterView(),
      ),
      GoRoute(
        path: '/otp-login',
        builder: (context, state) => const OtpLoginView(),
      ),

      // Secondary pages (outside shell - no bottom nav)
      GoRoute(
        path: '/shop/cart',
        builder: (context, state) => const ShopCartView(),
      ),
      GoRoute(
        path: '/shop/detail/:productId',
        builder: (context, state) {
          final productId = state.pathParameters['productId']!;
          return ShopDetailView(productId: productId);
        },
      ),
      GoRoute(
        path: '/lucky-draw/claim-product',
        builder: (context, state) => const ClaimProductView(),
      ),
      GoRoute(
        path: '/shop/favourites',
        builder: (context, state) => const FavouriteView(),
      ),

      // Card sub-pages (outside shell - no bottom nav)
      GoRoute(
        path: '/card/pack',
        builder: (context, state) => const CardPackPage(),
      ),
      GoRoute(
        path: '/card/purchase',
        builder: (context, state) {
          final cardCategoryVM = Provider.of<CardCategoryViewModel>(
            context,
            listen: false,
          );
          return ChangeNotifierProvider(
            create: (_) => PurchaseCardViewModel(
              cardCategoryViewModel: cardCategoryVM,
              seriesService: SeriesService(), // 👈 required param
            ),
            child: const PurchaseCardPage(),
          );
        },
      ),
      GoRoute(
        path: '/card/physical-card',
        builder: (context, state) => const SelectPhysicalCardPage(),
      ),
      GoRoute(
        path: '/card/checkout',
        name: 'checkout',
        builder: (context, state) {
          final data = state.extra as Map<String, dynamic>?;

          final quantity = data?['quantity'] as int? ?? 1;
          final total = data?['total'] as double? ?? 0.0;

          return CheckoutPage(quantity: quantity, total: total);
        },
      ),
      GoRoute(
        path: '/purchase/result',
        name: 'purchase_result',
        builder: (context, state) {
          final resultData = state.extra as Map<String, dynamic>;
          return PurchaseResultPage(
            resultData: resultData['result'],
            order: resultData['order'],
          );
        },
      ),
      GoRoute(
        path: '/card/history-card',
        builder: (context, state) => const HistoryCardPage(),
      ),
      GoRoute(
        path: '/card/history-card/detail',
        name: 'historyCardDetail',
        builder: (context, state) {
          final record = state.extra as HistoryCardItem;
          return HistoryCardDetailPage(record: record);
        },
      ),
      GoRoute(
        path: '/card/transfer',
        builder: (context, state) {
          final cardCategoryVM = Provider.of<CardCategoryViewModel>(
            context,
            listen: false,
          );
          return MultiProvider(
            providers: [
              ChangeNotifierProvider<TransferCardController>(
                create: (_) => TransferCardController(
                  categoryVM:
                      cardCategoryVM, // Changed from cardCategoryViewModel to categoryVM
                  allCards: allCardCatalog,
                ),
              ),
            ],
            child: const TransferCardPage(),
          );
        },
      ),
      GoRoute(
        path: '/exchange',
        builder: (context, state) => const ExchangeCardPage(),
      ),
      GoRoute(
        path: '/confirm-redemption',
        builder: (context, state) {
          final data = state.extra! as Map<String, dynamic>;
          return ConfirmRedemptionPage(
            modelData: data['model'] as model.ExchangeCardModel,
            selectedCardIds: (data['selectedCardIds'] as List<String>?) ?? [],
            totalRequired: data['totalRequired'] as int,
            selectionType: data['selectionType'] as model.SelectionType,
          );
        },
      ),
      GoRoute(
        path: '/redemption-result',
        builder: (context, state) => const RedemptionResultPage(
          isSuccess: true,
          message: "Sample success",
        ),
      ),
      GoRoute(
        path: '/card/reveal-animation',
        builder: (context, state) {
          final order = state.extra as PurchaseCardOrder;
          return RevealFlowWrapper(order: order); // <- PROVIDER STARTS HERE
        },
      ),
      GoRoute(
        path: '/card/reveal-view',
        builder: (context, state) {
          return const RevealCardGridPage(); // <- PROVIDER STILL AVAILABLE
        },
      ),
      GoRoute(
        path: '/card/scan-code',
        builder: (context, state) {
          return ChangeNotifierProvider(
            create: (_) => RevealsScanCardViewModel(),
            child: const ScanCodePage(),
          );
        },
      ),
      GoRoute(
        path: '/scan/animation',
        builder: (context, state) {
          final cards = state.extra as List<RevealCard>;
          return ScanCardAnimationPage(cards: cards);
        },
      ),
      GoRoute(
        path: '/scan/reveal',
        builder: (context, state) {
          final cards = state.extra as List<RevealCard>;
          return ScanCardRevealPage(cards: cards);
        },
      ),

      // Checkout (outside shell - no bottom nav)
      GoRoute(
        path: '/checkout',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          final checkoutType = extra?['checkoutType'] as CheckoutType;
          final productData = extra?['productData'] as Map<String, dynamic>?;
          final onSuccess = extra?['onSuccess'] as VoidCallback?;

          return CheckoutView(
            checkoutType: checkoutType,
            productData: productData,
            onSuccess: onSuccess,
          );
        },
      ),

      // Select Voucher (outside shell - no bottom nav)
      GoRoute(
        path: '/checkout/select-voucher',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          final orderAmount = extra?['orderAmount'] as double? ?? 0.0;
          final onVoucherSelected =
              extra?['onVoucherSelected'] as Function(Map<String, dynamic>?)?;

          return SelectVoucherView(
            orderAmount: orderAmount,
            onVoucherSelected: onVoucherSelected,
          );
        },
      ),

      // Select Address (outside shell - no bottom nav)
      GoRoute(
        path: '/checkout/select-address',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          final onAddressSelected =
              extra?['onAddressSelected'] as Function(Addressmodel)?;

          return SelectAddressView(onAddressSelected: onAddressSelected);
        },
      ),

      // Lucky Draw sub-pages (outside shell - no bottom nav)
      GoRoute(
        path: '/lucky-draw/group-purchase',
        builder: (context, state) => const GroupPurchaseView(),
      ),
      GoRoute(
        path: '/lucky-draw/participation-records',
        builder: (context, state) => const ParticipationRecordsView(),
      ),
      GoRoute(
        path: '/lucky-draw/claim-voucher',
        builder: (context, state) => const ClaimVoucherView(),
      ),
      GoRoute(
        path: '/lucky-draw/claim-voucher-ewallet',
        builder: (context, state) => const ClaimVoucherEWallet(),
      ),
      GoRoute(
        path: '/lucky-draw/claim-voucher-tng',
        builder: (context, state) => const ClaimVoucherTng(),
      ),

      GoRoute(
        path: '/lucky-draw/winning-history',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          final goodsPointId = (extra?['goodsPointId'] as num?)?.toInt();
          return WinningHistoryView(goodsPointId: goodsPointId);
        },
      ),
      GoRoute(
        path: '/lucky-draw/community-sharing',
        builder: (context, state) => const CommunitySharingView(),
      ),
      GoRoute(
        path: '/lucky-draw/draw-records',
        builder: (context, state) => const LatestDrawRecordView(),
      ),
      GoRoute(
        path: '/lucky-draw/review-details',
        builder: (context, state) => const ReviewDetailsView(),
      ),
      GoRoute(
        path: '/lucky-draw/product-detail',
        builder: (context, state) {
          final product = state.extra as Map<String, dynamic>? ?? {};
          return PointProductDetails(product: product);
        },
      ),
      GoRoute(
        path: '/lucky-draw/search',
        builder: (context, state) {
          final allProducts = state.extra as List<LuckyDrawProduct>? ?? [];
          // Convert LuckyDrawProduct list to Map<String, dynamic> list for backward compatibility
          final convertedProducts = allProducts
              .map((product) => product.toJson())
              .toList();
          return ProductSearchPage(allProducts: convertedProducts);
        },
      ),

      // Blind Box sub-pages (outside shell - no bottom nav)
      GoRoute(
        path: '/blind-box/buy/:unboxId',
        builder: (context, state) {
          // Extract productSpuId from URL path parameters for deep linking
          final unboxIdStr = state.pathParameters['unboxId']!;
          final unboxId = int.tryParse(unboxIdStr);

          if (unboxId == null) {
            // Handle invalid productSpuId
            return const Scaffold(
              body: Center(child: Text('Invalid blindbox ID')),
            );
          }

          // Get additional data from extra if provided (for regular navigation)
          final extra = state.extra as Map<String, dynamic>?;
          final isGroupBuy = extra?['isGroupBuy'] ?? false;
          final additionalProductData =
              extra?['product'] as Map<String, dynamic>? ?? {};

          // Create product data with productSpuId from URL
          final productData = {
            'unboxId': unboxId,
            ...additionalProductData, // Merge any additional data
          };

          return riverpod.ProviderScope(
            child: BlindboxBuyView(
              isGroupBuy: isGroupBuy,
              product: productData,
              unboxId: unboxId, // Pass unboxId directly for deep linking
            ),
          );
        },
      ),
      GoRoute(
        path: '/blind-box/reviews',
        builder: (context, state) => const BlindboxReviewView(),
      ),
      GoRoute(
        path: '/blind-box/reveal',
        builder: (context, state) {
          final orderInfo = state.extra as Map<String, dynamic>? ?? {};
          return BlindboxRevealView(orderInfo: orderInfo);
        },
      ),
      GoRoute(
        path: '/blind-box/group-buy',
        builder: (context, state) => const BlindboxGroupBuyView(),
      ),
      GoRoute(
        path: '/blind-box/post/:postId',
        builder: (context, state) {
          final post = state.extra as Map<String, dynamic>? ?? {};
          return PostView(post: post);
        },
      ),
      GoRoute(
        path: '/blind-box/profile/:userName',
        builder: (context, state) {
          final userName = state.pathParameters['userName']!;
          return PostProfileView(userName: userName);
        },
      ),
      GoRoute(
        path: '/blind-box/search',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          final initialQuery = extra?['initialQuery'] as String?;
          final trigger = extra?['trigger'] as bool? ?? false;
          return riverpod.ProviderScope(
            child: BlindboxSearchView(
              initialSearchQuery: initialQuery,
              triggerOnInit: trigger,
            ),
          );
        },
      ),

      // Profile sub-pages (outside shell - no bottom nav)
      GoRoute(
        path: '/profile/add-address',
        builder: (context, state) {
          return const AddAddressView();
        },
      ),
      GoRoute(
        path: '/profile/my-address',
        builder: (context, state) {
          return const AddressView();
        },
      ),
      GoRoute(
        path: '/profile/my-address-view',
        builder: (context, state) {
          return const AddressView();
        },
      ),
      GoRoute(
        path: '/profile/update-address/:id',
        name: 'update-address',
        builder: (context, state) {
          final address = state.extra as Map<String, dynamic>? ?? {};
          return UpdateAddressView(address: address);
        },
      ),
      GoRoute(
        path: '/profile/notification',
        builder: (context, state) => const NotificationView(),
      ),
      GoRoute(
        path: '/profile/voucher',
        builder: (context, state) => const VoucherView(),
      ),
      GoRoute(
        path: '/profile/my-sharing',
        builder: (context, state) => const MySharingView(),
      ),
      GoRoute(
        path: '/profile/post/:title',
        builder: (context, state) {
          final post = state.extra as Map<String, dynamic>? ?? {};
          return UserPostView(post: post);
        },
      ),
      GoRoute(
        path: '/profile/my-order',
        builder: (context, state) => const MyOrderView(),
      ),
      GoRoute(
        path: '/profile/share-post',
        builder: (context, state) {
          final orderType = state.extra as Map<String, dynamic>?;
          return SharePostView(
            orderType: orderType?['orderType'] as String?,
            orderId: orderType?['orderId'] as String?,
          );
        },
      ),
      GoRoute(
        path: '/profile/refund',
        builder: (context, state) {
          final order = state.extra as OrderModel;
          return RefundView(order: order);
        },
      ),
      GoRoute(
        path: '/profile/refund-status',
        builder: (context, state) {
          final order = state.extra as OrderModel;
          return RefundStatusView(order: order);
        },
      ),
      GoRoute(
        path: '/profile/completed-order-details',
        builder: (context, state) {
          final order = state.extra as OrderModel;
          return CompletedOrderDetailsView(order: order);
        },
      ),
      GoRoute(
        path: '/profile/cancelled-order-details',
        builder: (context, state) {
          final order = state.extra as OrderModel;
          return CancelledOrderDetailsView(order: order);
        },
      ),
      GoRoute(
        path: '/profile/shipping-information',
        builder: (context, state) {
          final order = state.extra as OrderModel;
          return ShippingInformationView(order: order);
        },
      ),
      GoRoute(
        path: '/profile/to-receive',
        builder: (context, state) {
          final order = state.extra as OrderModel;
          return ToReceiveView(order: order);
        },
      ),
      GoRoute(
        path: '/profile/to-ship',
        builder: (context, state) {
          final order = state.extra as OrderModel;
          return ToShipView(order: order);
        },
      ),
      GoRoute(
        path: '/profile/face-verification',
        builder: (context, state) => const FaceVerificationView(),
      ),
      GoRoute(
        path: '/profile/my-review',
        builder: (context, state) => const ReviewPageView(),
      ),
      GoRoute(
        path: '/profile/checkin',
        name: 'checkin',
        builder: (context, state) => const CheckInPage(),
      ),
      GoRoute(
        path: '/profile/view-profile',
        builder: (context, state) => const UserProfileInfoView(),
      ),
      GoRoute(
        path: '/profile/edit-profile',
        builder: (context, state) => const EditUserProfileView(),
      ),
      GoRoute(
        path: '/profile/settings',
        builder: (context, state) => const ProfileSettingsView(),
      ),
      GoRoute(
        path: '/profile/change-password',
        builder: (context, state) => const ChangePasswordView(),
      ),
      GoRoute(
        path: '/profile/deactivate-account',
        builder: (context, state) => const DeactivateAccountView(),
      ),
      GoRoute(
        path: '/profile/switch-language',
        builder: (context, state) => const SwitchLanguageView(),
      ),
      GoRoute(
        path: '/profile/terms-and-conditions',
        builder: (context, state) => const TermsAndConditionView(),
      ),
      GoRoute(
        path: '/profile/privacy-policy',
        builder: (context, state) => const PrivacyPolicyview(),
      ),
      GoRoute(
        path: '/profile/return-policy',
        builder: (context, state) => const ReturnView(),
      ),
      GoRoute(
        path: '/profile/merchant-policy',
        builder: (context, state) => const MerchantPolicyview(),
      ),
      GoRoute(
        path: '/profile/faq',
        builder: (context, state) => const FaqView(),
      ),
      GoRoute(
        path: '/profile/about-us',
        builder: (context, state) => const AboutUsView(),
      ),
      GoRoute(
        path: '/profile/balance',
        builder: (context, state) => const BalanceView(),
      ),
      GoRoute(
        path: '/profile/withdraw',
        builder: (context, state) => const WithdrawView(),
      ),
      GoRoute(
        path: '/profile/choose-background',
        builder: (context, state) => const ChooseBackgroundView(),
      ),
      GoRoute(
        path: '/profile/lucky-group-buy',
        builder: (context, state) => const LuckyGroupBuyView(),
      ),
      GoRoute(
        path: '/profile/invite-friends',
        name: 'inviteFriends',
        builder: (context, state) => const InviteFriendPage(),
      ),

      // In your router configuration (usually in main.dart or app_router.dart)
      GoRoute(
        path: '/shop/detail/:productId', // Make sure this matches
        builder: (context, state) {
          final productId = state.pathParameters['productId']!;
          return ShopDetailView(productId: productId);
        },
      ),

      // App shell with bottom navigation, add the page here if the code need
      // bottom navigation bar
      ShellRoute(
        builder: (context, state, child) => AppShell(child: child),
        routes: [
          // Main Shop tab (with bottom nav)
          GoRoute(
            path: '/shop',
            builder: (context, state) => const ShoplistView(),
          ),

          // Main Card tab (with bottom nav)
          GoRoute(path: '/card', builder: (context, state) => const CardPage()),

          // Main Lucky Draw tab (with bottom nav)
          GoRoute(
            path: '/lucky-draw',
            builder: (context, state) => const MainpageView(),
          ),

          GoRoute(
            path: '/lucky-draw/prize-reveal',
            builder: (context, state) => const PrizeRevealView(),
          ),

          // Main Blind Box tab (with bottom nav)
          GoRoute(
            path: '/blind-box',
            builder: (context, state) => const BlindboxView(),
          ),

          GoRoute(
            // Homepage
            path: '/',
            builder: (context, state) =>
                const riverpod.ProviderScope(child: BlindboxShopView()),
          ),

          GoRoute(
            path: '/blind-box/shop',
            builder: (context, state) =>
                const riverpod.ProviderScope(child: BlindboxShopView()),
          ),

          // Main Profile tab (with bottom nav)
          GoRoute(
            path: '/profile',
            // redirect: profileGuard,
            builder: (context, state) => const profile.UserProfileView(),
          ),
        ],
      ),
    ],
  );
}
