# Charity Fund API Integration

This document describes the API integration for the charity fund counter in the Lucky Draw module.

## API Endpoint

- **URL**: `http://192.168.0.47:1222/system/charitable/fund`
- **Method**: GET
- **Response Format**: JSON

## API Response Structure

```json
{
  "status": 200,
  "message": "success",
  "data": 4819,
  "timestamp": 1755829222602
}
```

### Response Fields

- `status`: HTTP status code (200 for success)
- `message`: Response message ("success" for successful requests)
- `data`: The total charity fund amount (integer)
- `timestamp`: Unix timestamp in milliseconds when the data was last updated

## Implementation Files

### 1. Model (`CharityFund.dart`)

The `CharityFund` model class that represents the charity fund data:

```dart
class CharityFund {
  final int totalAmount;    // Maps to API response 'data' field
  final int lastUpdated;    // Maps to API response 'timestamp' field
  
  // Constructor and methods...
}
```

**Key Features:**
- Maps API response fields correctly (`data` → `totalAmount`, `timestamp` → `lastUpdated`)
- Provides formatted date string via `formattedLastUpdated` getter
- Handles null values gracefully with default values

### 2. Service (`CharityFundService.dart`)

The service layer that handles API communication:

```dart
class CharityFundService {
  Future<CharityFund> getCharityFund() async { ... }
  void clearCache() { ... }
  CharityFund? getCachedCharityFund() { ... }
  bool get isCacheValid { ... }
}
```

**Key Features:**
- Makes HTTP GET requests to the charity fund endpoint
- Implements caching (5-minute cache duration)
- Handles network errors and API errors
- Provides cache management methods

### 3. Repository (`CharityFundRepository.dart`)

The repository layer that abstracts data access:

```dart
class CharityFundRepository {
  Future<CharityFund> getCharityFund() async { ... }
  CharityFund? getCachedCharityFund() { ... }
  void clearCache() { ... }
  bool get isCacheValid { ... }
}
```

**Key Features:**
- Provides clean interface for data access
- Handles service layer errors
- Exposes cache management functionality

### 4. View Model (`CharityFundVM.dart`)

The view model that manages state and business logic:

```dart
class CharityFundVM extends ChangeNotifier {
  CharityFund? get charityFund { ... }
  bool get isLoading { ... }
  String? get error { ... }
  String get formattedAmount { ... }
  String get formattedLastUpdated { ... }
  
  Future<void> fetchCharityFund() async { ... }
  Future<void> refreshCharityFund() async { ... }
  void initialize() { ... }
}
```

**Key Features:**
- Manages loading states, errors, and data
- Provides formatted data for UI display
- Handles data initialization and refresh
- Notifies listeners of state changes

### 5. Widget (`CharityCounter.dart`)

The UI widget that displays the charity fund counter:

```dart
class CharityCounter extends StatefulWidget {
  // Displays charity fund amount in a digital counter format
  // Shows loading states, error handling, and last updated info
}
```

**Key Features:**
- Displays charity fund amount in digital counter format
- Shows loading state (`---`)
- Shows error state (`ERR`) with refresh button
- Displays last updated timestamp
- Handles API errors gracefully

## Usage

### Basic Usage

```dart
// The CharityCounter widget automatically fetches and displays data
CharityCounter()
```

### Manual Control

```dart
// Create and manage the view model manually
final charityFundVM = CharityFundVM();

// Initialize and fetch data
charityFundVM.initialize();

// Refresh data
await charityFundVM.refreshCharityFund();

// Access data
final amount = charityFundVM.formattedAmount;
final lastUpdated = charityFundVM.formattedLastUpdated;
```

### Error Handling

The implementation includes comprehensive error handling:

- Network errors are caught and displayed
- API errors (non-200 status) are handled
- Users can retry failed requests
- Graceful fallbacks for missing data

### Caching

- Data is cached for 5 minutes to reduce API calls
- Cache can be manually cleared for fresh data
- Offline scenarios can use cached data if available

## Testing

Run the charity fund tests:

```bash
flutter test lib/modules/luckydraw/test/charity_fund_test.dart
```

## Dependencies

- `dio`: For HTTP requests
- `provider`: For state management
- `flutter`: Core Flutter framework

## Notes

- The API endpoint uses HTTP (not HTTPS) - ensure this is acceptable for production
- Cache duration is set to 5 minutes - adjust based on data update frequency
- Error states are displayed with user-friendly messages and retry options
- The widget automatically handles its own lifecycle and data fetching
