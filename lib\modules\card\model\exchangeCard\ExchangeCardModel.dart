enum CardRank { r, sr, ssr, sssr, ur }
enum SelectionType { auto, selfSelect }

class ExchangeCardModel {
  final String id;
  final String title;
  final String type; // e.g., "TNG", "Voucher"
  final String imageUrl;
  final String bwImageUrl;
  final bool isNetworkImage;
  final String requirementDetail;
  final List<String> requiredCardIds;
  final CardRank rank;           // NEW
  final SelectionType selectionType; // NEW

  ExchangeCardModel({
    required this.id,
    required this.title,
    required this.type,
    required this.imageUrl,
    required this.bwImageUrl,
    required this.isNetworkImage,
    required this.requirementDetail,
    required this.requiredCardIds,
    required this.rank,
    required this.selectionType,
  });
}
