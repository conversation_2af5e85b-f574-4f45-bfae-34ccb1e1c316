import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class PostVM {
  static double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  static double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  static double getResponsiveImageHeight(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 320;
    } else if (screenWidth < 900) {
      return 400;
    } else {
      return 500;
    }
  }

  static double getResponsiveAvatarRadius(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 20;
    } else if (screenWidth < 900) {
      return 25;
    } else {
      return 30;
    }
  }

  static double getResponsiveProductImageHeight(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 80;
    } else if (screenWidth < 900) {
      return 100;
    } else {
      return 120;
    }
  }

  // Sample product data for demonstration
  static Map<String, dynamic> getSampleProduct() {
    return {
      'unboxId': '1',
      'title': 'Mystery Box Elite',
      'price': 'RM 45.00',
      'imageUrl':
          'https://oss.luckymymall.com/3238aef0019fb92e208adb997431f2ac2337a7baab70f472d0a49a314b29fde9.jpg',
      'imageUrls': [
        'https://oss.luckymymall.com/3238aef0019fb92e208adb997431f2ac2337a7baab70f472d0a49a314b29fde9.jpg',
      ],
      'soldCount': 156,
      'rating': 4.8,
      'pointsOffer': 'Use 45 card points',
      'variation': 'Random Selection',
      'description':
          'This is a premium mystery box containing various high-quality items. Each box is carefully curated to provide a unique unboxing experience. Perfect for gifts or personal enjoyment.',
      'isAvailable': true,
      'category': 'Mystery Box',
      'salePrice': 45.0,
      'cardDeduction': 45,
      'isCollect': false,
    };
  }

  // Handle product tap action
  static void onProductTap(BuildContext context, Map<String, dynamic> product) {
    if (!product['isAvailable']) {
      // Show sold out message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${product['title']} is currently sold out'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    } else {
      // Navigate to product details
      context.push(
        '/blind-box/buy/1',
        extra: {
          'product': product,
          'isGroupBuy': product['isGroupBuy'] ?? false,
        },
      );
    }
  }
}
