import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class RedemptionCategoryNav extends StatelessWidget {
  final String selectedCategory;
  final ValueChanged<String> onCategorySelected;
  final Map<String, int> categoryCounts; // ✅ New

  const RedemptionCategoryNav({
    super.key,
    required this.selectedCategory,
    required this.onCategorySelected,
    required this.categoryCounts, // ✅ New
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scale = (screenWidth / 390.0).clamp(0.9, 1.1);

    final categories = [
      {
        'key': 'royal',
        'label': 'Royal',
        'icon': 'assets/icons/cardCategory/King01.svg',
        'activeIcon': 'assets/icons/cardCategory/King02.svg',
      },
      {
        'key': 'knight',
        'label': 'Knight',
        'icon': 'assets/icons/cardCategory/Knight01.svg',
        'activeIcon': 'assets/icons/cardCategory/Knight02.svg',
      },
      {
        'key': 'wizard',
        'label': 'Wizard',
        'icon': 'assets/icons/cardCategory/Wizard01.svg',
        'activeIcon': 'assets/icons/cardCategory/Wizard02.svg',
      },
      {
        'key': 'smith',
        'label': 'Smith',
        'icon': 'assets/icons/cardCategory/Smith01.svg',
        'activeIcon': 'assets/icons/cardCategory/Smith02.svg',
      },
      {
        'key': 'elf',
        'label': 'Elf',
        'icon': 'assets/icons/cardCategory/Elf01.svg',
        'activeIcon': 'assets/icons/cardCategory/Elf02.svg',
      },
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8 * scale, vertical: 10 * scale),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(2, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: categories.map((cat) {
          final String catKey = cat['key']!;
          final String label = cat['label']!;
          final bool isSelected = selectedCategory == catKey;
          final int count = categoryCounts[catKey] ?? 0;

          return GestureDetector(
            onTap: () => onCategorySelected(catKey),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  alignment: Alignment.topRight,
                  children: [
                    Container(
                      padding: EdgeInsets.all(10 * scale),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected ? Colors.amber : Colors.grey[300],
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: const Color.fromARGB(73, 255, 193, 7),
                                  blurRadius: 8,
                                  offset: const Offset(0, 3),
                                ),
                              ]
                            : [],
                      ),
                      child: SvgPicture.asset(
                        isSelected ? cat['activeIcon']! : cat['icon']!,
                        width: isSelected ? 30 * scale : 26 * scale,
                        height: isSelected ? 30 * scale : 26 * scale,
                        colorFilter: ColorFilter.mode(
                          isSelected ? Colors.white : Colors.grey,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    if (count > 0)
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '$count',
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                SizedBox(height: 4 * scale),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12 * scale,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? Colors.amber : Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}
