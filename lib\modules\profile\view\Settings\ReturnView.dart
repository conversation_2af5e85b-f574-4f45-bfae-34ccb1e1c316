import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class ReturnView extends StatelessWidget {
  const ReturnView({super.key});

  @override
  Widget build(BuildContext context) {
    // Randomly generated return and refund policy sections
    final List<_TermsSection> sections = [
      _TermsSection(
        title: "Introduction",
        content:
            "This Return and Refund Policy explains how Lucky Mall handles returns, exchanges, and refunds for purchases made through our platform. Please review this policy before making a purchase.",
      ),
      _TermsSection(
        title: "1. Return Eligibility",
        content:
            "Items may be eligible for return if they are defective, damaged, or not as described. To be eligible, items must be unused and in the original packaging.",
      ),
      _TermsSection(
        title: "2. Return Process",
        content:
            "To initiate a return, contact our customer support within 7 days of receiving your order. Provide your order number, reason for return, and photos if applicable.",
      ),
      _TermsSection(
        title: "3. Non-Returnable Items",
        content:
            "Certain items such as perishable goods, personal care products, and gift cards are not eligible for return or refund.",
      ),
      _TermsSection(
        title: "4. Refunds",
        content:
            "Once your return is received and inspected, we will notify you of the approval or rejection of your refund. Approved refunds will be processed to your original payment method within 7-14 business days.",
      ),
      _TermsSection(
        title: "5. Exchanges",
        content:
            "If you need to exchange an item for the same product, contact our support team. Exchanges are subject to product availability.",
      ),
      _TermsSection(
        title: "6. Shipping Costs",
        content:
            "Return shipping costs are the responsibility of the customer unless the item is defective or incorrect. Shipping costs are non-refundable.",
      ),
      _TermsSection(
        title: "7. Late or Missing Refunds",
        content:
            "If you haven’t received a refund yet, first check your bank account again. Then contact your credit card company and bank. If you still have not received your refund, please contact us.",
      ),
      _TermsSection(
        title: "8. Policy Changes",
        content:
            "Lucky Mall reserves the right to update or modify this Return and Refund Policy at any time. Changes will be posted on this page.",
      ),
      _TermsSection(
        title: "9. Contact Us",
        content:
            "For questions about returns or refunds, please contact our support <NAME_EMAIL>.",
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        centerTitle: true,
        title: const Text(
          'Return and Refund Policy',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Lucky Mall Return and Refund Policy",
              style: TextStyle(fontWeight: FontWeight.w700, fontSize: 18),
            ),
            const SizedBox(height: 18),
            ...sections.map(
              (section) => Padding(
                padding: const EdgeInsets.only(bottom: 18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      section.title,
                      style: TextStyle(
                        fontWeight: section.title == "Introduction"
                            ? FontWeight.w700
                            : FontWeight.w600,
                        fontSize: section.title == "Introduction" ? 16 : 15,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      section.content,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TermsSection {
  final String title;
  final String content;
  _TermsSection({required this.title, required this.content});
}