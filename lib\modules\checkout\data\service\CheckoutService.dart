import 'package:luckymall/modules/checkout/data/model/CheckoutModel.dart';
import 'package:luckymall/modules/checkout/data/model/PaymentModel.dart';
import 'package:dio/dio.dart';
import 'package:luckymall/modules/profile/model/AddressModel.dart';
import 'package:luckymall/modules/profile/service/AddressService.dart';
import 'package:luckymall/res/utility/network/api_client.dart';
import 'package:flutter/foundation.dart';

final checkoutService = CheckoutService();

class CheckoutService {
  final Dio authDio = ApiClient.instance.authDio;
  final AddressService addressService = AddressService();
  // this is for getting all the data that needed to display in the checkout page
  // specifically for the shop product
  Future<CheckoutReceiveModel> getCheckoutData(
    CheckoutSendModel checkoutSendModel,
    int? addressId,
  ) async {
    try {
      // Use the provided addressId if available, otherwise try to get default address
      if (addressId != null) {
        checkoutSendModel.addressId = addressId;
      } else if (checkoutSendModel.addressId == null) {
        try {
          final Addressmodel address = await addressService.getDefaultAddress();
          checkoutSendModel.addressId = address.id;
        } catch (e) {
          // Continue without address - let the API handle this
        }
      }

      final response = await authDio.post(
        '/order/checkout',
        data: checkoutSendModel.toJson(checkoutSendModel.addressId),
      );

      debugPrint('📥 Response status: ${response.statusCode}');
      debugPrint('📥 Response data: ${response.data}');

      if (response.statusCode == 200) {
        // API returns wrapper { status, message, data: {...} }
        final wrapper = response.data;
        final data = wrapper is Map<String, dynamic>
            ? (wrapper['data'] as Map<String, dynamic>?)
            : null;

        if (data == null) {
          throw Exception('Invalid response payload');
        }

        final CheckoutReceiveModel checkoutReceiveModel =
            CheckoutReceiveModel.fromJson(data);

        return checkoutReceiveModel;
      } else {
        throw Exception(response.data['message']);
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  Future<String> createOrder(
    ToOrderModel toOrderModel,
    String gateway,
    String name,
    String email,
    String mobile,
  ) async {
    try {
      final response = await authDio.post(
        '/order/created',
        data: toOrderModel.toJson(toOrderModel.addressId),
      );

      debugPrint('📥 Order creation response status: ${response.statusCode}');
      debugPrint('📥 Order creation response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseWrapper = response.data;
        final data = responseWrapper['data'] as Map<String, dynamic>?;

        if (data == null) {
          throw Exception('Invalid response: missing data field');
        }

        final int orderId = data['orderId'] as int;
        String paymentUrl = '';

        switch (gateway) {
          case 'cash': // lucky mall wallet (confirm with backend)
            try {
              final cashResponse = await authDio.get(
                '/pay/order/cash/$orderId',
              );
              debugPrint(
                '📥 Cash payment response: ${cashResponse.statusCode}',
              );
              debugPrint(
                '📥 Lucky Mall Wallet payment data: ${cashResponse.data}',
              );

              if (cashResponse.statusCode == 200) {
                final cashData = cashResponse.data;
                final cashUrl =
                    cashData is Map<String, dynamic> && cashData['data'] != null
                    ? cashData['data'].toString()
                    : cashData.toString();
                paymentUrl = cashUrl;
              } else {
                final errorMsg = cashResponse.data is Map<String, dynamic>
                    ? cashResponse.data['message']
                    : 'Lucky Mall Wallet payment failed';
                throw Exception(errorMsg ?? 'Lucky Mall Wallet payment failed');
              }
            } catch (e) {
              throw Exception('Cash payment error: $e');
            }
          case 'ipay88':
            try {
              final ipay88Response = await authDio.post(
                '/pay/order/ipay88/$orderId',
                data: {'name': name, 'email': email, 'mobile': mobile},
              );
              debugPrint('📥 iPay88 response: ${ipay88Response.statusCode}');
              debugPrint('📥 iPay88 data: ${ipay88Response.data}');

              if (ipay88Response.statusCode == 200) {
                final ipay88Data = ipay88Response.data;
                final ipay88Url =
                    ipay88Data is Map<String, dynamic> &&
                        ipay88Data['data'] != null
                    ? ipay88Data['data'].toString()
                    : ipay88Data.toString();
                paymentUrl = ipay88Url;
              } else {
                final errorMsg = ipay88Response.data is Map<String, dynamic>
                    ? ipay88Response.data['message']
                    : 'iPay88 payment failed';
                throw Exception(errorMsg ?? 'iPay88 payment failed');
              }
            } catch (e) {
              throw Exception('iPay88 payment error: $e');
            }
          case 'tng':
            try {
              final tngResponse = await authDio.post(
                '/pay/order/tng/$orderId',
                data: {'name': name, 'email': email, 'mobile': mobile},
              );
              debugPrint('📥 TNG response: ${tngResponse.statusCode}');
              debugPrint('📥 TNG data: ${tngResponse.data}');

              if (tngResponse.statusCode == 200) {
                final tngData = tngResponse.data;
                final tngUrl =
                    tngData is Map<String, dynamic> && tngData['data'] != null
                    ? tngData['data'].toString()
                    : tngData.toString();
                paymentUrl = tngUrl;
              } else {
                final errorMsg = tngResponse.data is Map<String, dynamic>
                    ? tngResponse.data['message']
                    : 'TNG payment failed';
                throw Exception(errorMsg ?? 'TNG payment failed');
              }
            } catch (e) {
              throw Exception('TNG payment error: $e');
            }
          case 'stripe':
            try {
              final stripeResponse = await authDio.get(
                '/pay/order/stripe/$orderId',
              );
              debugPrint('📥 Stripe response: ${stripeResponse.statusCode}');
              debugPrint('📥 Stripe data: ${stripeResponse.data}');

              if (stripeResponse.statusCode == 200) {
                final stripeData = stripeResponse.data;
                final stripeUrl =
                    stripeData is Map<String, dynamic> &&
                        stripeData['data'] != null
                    ? stripeData['data'].toString()
                    : stripeData.toString();
                paymentUrl = stripeUrl;
              } else {
                final errorMsg = stripeResponse.data is Map<String, dynamic>
                    ? stripeResponse.data['message']
                    : 'Stripe payment failed';
                throw Exception(errorMsg ?? 'Stripe payment failed');
              }
            } catch (e) {
              throw Exception('Stripe payment error: $e');
            }
          case 'billplz':
            try {
              final billplzResponse = await authDio.post(
                '/pay/order/billplz/$orderId',
                data: {'name': name, 'email': email, 'mobile': mobile},
              );
              debugPrint('📥 Billplz response: ${billplzResponse.statusCode}');
              debugPrint('📥 Billplz data: ${billplzResponse.data}');

              if (billplzResponse.statusCode == 200) {
                final billplzData = billplzResponse.data;
                final billplzUrl =
                    billplzData is Map<String, dynamic> &&
                        billplzData['data'] != null
                    ? billplzData['data'].toString()
                    : billplzData.toString();
                paymentUrl = billplzUrl;
              } else {
                final errorMsg = billplzResponse.data is Map<String, dynamic>
                    ? billplzResponse.data['message']
                    : 'Billplz payment failed';
                throw Exception(errorMsg ?? 'Billplz payment failed');
              }
            } catch (e) {
              throw Exception('Billplz payment error: $e');
            }
          default:
            throw Exception('Invalid gateway: $gateway');
        }

        if (paymentUrl.isEmpty) {
          throw Exception('Payment URL is empty for gateway: $gateway');
        }

        return paymentUrl;
      } else {
        final errorMsg = response.data is Map<String, dynamic>
            ? response.data['message']
            : 'Unknown error';
        throw Exception(
          'Order creation failed: ${errorMsg ?? 'Unknown error'}',
        );
      }
    } catch (e) {
      // Re-throw the exception without wrapping it again
      rethrow;
    }
  }
}
