import 'package:flutter/material.dart';

class OtpField extends StatefulWidget {
  final TextEditingController? controller;
  final String? hintText;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final VoidCallback? onSendCode;

  const OtpField({
    super.key,
    this.controller,
    this.hintText,
    this.onChanged,
    this.validator,
    this.onSendCode,
  });

  @override
  State<OtpField> createState() => _OtpFieldState();
}

class _OtpFieldState extends State<OtpField> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // OTP Label
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'OTP Code',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 13),
            ),
          ),
        ),
        const SizedBox(height: 8),
        // OTP Field
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Container(
            height: 48, // Set a fixed height for consistency
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.black),
            ),
            child: TextField(
              controller: widget.controller,
              keyboardType: TextInputType.number,
              maxLength: 6,
              onChanged: widget.onChanged,
              style: const TextStyle(fontSize: 13),
              decoration: InputDecoration(
                border: InputBorder.none,
                counterText: '', // Hide character counter
                hintText: widget.hintText ?? 'Enter 6-digit OTP',
                hintStyle: const TextStyle(color: Colors.grey),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 12,
                ),
                suffixIcon: TextButton(
                  child: const Text(
                    'Send code',
                    style: TextStyle(color: Color(0xFFD90019)),
                  ),
                  onPressed: widget.onSendCode,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
