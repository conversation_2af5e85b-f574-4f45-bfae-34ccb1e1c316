import 'package:flutter/material.dart';
import '../../model/exchangeCard/ExchangeCardModel.dart';

class InsufficientCardDialog extends StatelessWidget {
  final ExchangeCardModel model;
  final VoidCallback onConfirm;

  const InsufficientCardDialog({
    super.key,
    required this.model,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(7),
      ),
      title: Center(
        child: Text(
          "Insufficient Cards",
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
      ),
      content: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: screenWidth < 350 ? screenWidth * 0.9 : 350,
        ),
        child: Text(
          "You don’t have all the required cards to redeem \"${model.title}\".\n\n"
          "Would you like to view your cards that meet the requirement?",
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 14),
        ),
      ),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text("Cancel"),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(7),
                ),
              ),
              onPressed: () {
                Navigator.pop(context);
                onConfirm();
              },
              child: const Text(
                "View My Cards",
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
