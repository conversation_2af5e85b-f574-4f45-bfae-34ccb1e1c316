import 'package:flutter/material.dart';

class EmptyInvitePlaceholder extends StatelessWidget {
  const EmptyInvitePlaceholder({super.key});

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.width > 600;
    final iconSize = isTablet ? 100.0 : 72.0;
    final textStyle = Theme.of(context).textTheme.titleMedium?.copyWith(color: Colors.black54);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 48),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFFFFFBEA),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.amber.shade100),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.group_add_outlined, size: iconSize, color: Colors.amber[600]),
            const SizedBox(height: 20),
            Text("No invited friends yet", style: textStyle),
            const SizedBox(height: 8),
            Text(
              "Start sharing your referral code to invite friends and earn rewards!",
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.black45),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
