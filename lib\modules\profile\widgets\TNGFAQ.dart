import 'package:flutter/material.dart';

class TNGFAQPage extends StatelessWidget {
  const TNGFAQPage({super.key});

  final List<Map<String, String>> faqs = const [
    {
      'question': 'What is Touch \'n Go (TNG)?',
      'answer':
          'Touch \'n Go (TNG) is a cashless payment system widely used in Malaysia for tolls, transport, parking, and more.'
    },
    {
      'question': 'Can I link my TNG card to this app?',
      'answer':
          'Currently, direct linking to a physical TNG card is not supported. However, you may use TNG eWallet where available.'
    },
    {
      'question': 'How do I top up my TNG eWallet?',
      'answer':
          'You can top up your TNG eWallet using online banking, debit/credit cards, or through selected merchants.'
    },
    {
      'question': 'Is it safe to use TNG eWallet?',
      'answer':
          'Yes, TNG eWallet uses encryption and authentication measures to keep your transactions secure.'
    },
    {
      'question': 'Can I get a refund from my TNG card?',
      'answer':
          'Refunds for physical TNG cards are managed by TNG itself. Please visit the official Touch \'n Go website or contact their customer service.'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        iconTheme: const IconThemeData(color: Colors.black),
        centerTitle: true,
        title: const Text(
          'TNG FAQ',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: faqs.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final faq = faqs[index];
          return Card(
            elevation: 1,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ExpansionTile(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              title: Row(
                children: [
                  const Icon(Icons.account_balance_wallet_outlined, color: Colors.black),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      faq['question']!,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
              children: [
                Text(
                  faq['answer']!,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
