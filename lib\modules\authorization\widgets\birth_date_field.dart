import 'package:flutter/material.dart';

class BirthDateField extends StatefulWidget {
  final TextEditingController? controller;
  final String? hintText;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final bool isRequired;

  const BirthDateField({
    super.key,
    this.controller,
    this.hintText,
    this.onChanged,
    this.validator,
    this.isRequired = true,
  });

  /// Static method to calculate user's age based on birth date
  static int? calculateAge(String birthDate) {
    try {
      final parts = birthDate.split('-');
      if (parts.length != 3) return null;

      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final day = int.parse(parts[2]);

      final birthDateTime = DateTime(year, month, day);
      final now = DateTime.now();

      final int age = now.year - birthDateTime.year;
      final bool isBirthdayPassed =
          now.month > birthDateTime.month ||
          (now.month == birthDateTime.month && now.day >= birthDateTime.day);
      final int actualAge = isBirthdayPassed ? age : age - 1;

      return actualAge;
    } catch (e) {
      return null;
    }
  }

  /// Static method to check if a user is 18+ based on birth date
  static bool isUser18Plus(String birthDate) {
    final age = calculateAge(birthDate);
    return age != null && age >= 18;
  }

  /// Utility method to get age-based product access level
  /// This can be used to determine what products a user can see
  static String getProductAccessLevel(String birthDate) {
    final age = calculateAge(birthDate);
    if (age == null) return 'Unknown';

    if (age >= 21) return 'Full Access (21+)';
    if (age >= 18) return 'Adult Access (18+)';
    if (age >= 13) return 'Teen Access (13+)';
    return 'Child Access (Under 13)';
  }

  @override
  State<BirthDateField> createState() => _BirthDateFieldState();
}

class _BirthDateFieldState extends State<BirthDateField> {
  String? _selectedDate;

  @override
  void initState() {
    super.initState();
    if (widget.controller?.text.isNotEmpty == true) {
      _selectedDate = widget.controller!.text;
    }
  }

  Future<void> _selectDate() async {
    final DateTime now = DateTime.now();
    final DateTime initialDate = DateTime(now.year - 18, now.month, now.day);
    final DateTime firstDate = DateTime(1900);
    final DateTime lastDate = now;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.black,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      final String formattedDate =
          "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";

      setState(() {
        _selectedDate = formattedDate;
      });

      widget.controller?.text = formattedDate;
      if (widget.onChanged != null) {
        widget.onChanged!(formattedDate);
      }
    }
  }

  String _getDisplayText() {
    if (_selectedDate != null) {
      final parts = _selectedDate!.split('-');
      if (parts.length == 3) {
        final year = parts[0];
        final month = parts[1];
        final day = parts[2];
        return '$day/$month/$year';
      }
    }
    return widget.hintText ?? 'Please select your birth date';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Birth Date Label
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                Text(
                  'Birth Date',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 13,
                  ),
                ),
                if (widget.isRequired)
                  const Text(
                    ' *',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                      fontSize: 13,
                    ),
                  ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),
        // Birth Date Field
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Container(
            width: double.infinity,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.black),
            ),
            child: InkWell(
              onTap: _selectDate,
              borderRadius: BorderRadius.circular(10),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 12,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        _getDisplayText(),
                        style: TextStyle(
                          fontSize: 13,
                          color: _selectedDate != null
                              ? Colors.black
                              : Colors.grey,
                        ),
                      ),
                    ),
                    const Icon(
                      Icons.calendar_today,
                      color: Colors.grey,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
