import 'package:flutter/material.dart';

class ScanCodeButton extends StatefulWidget {
  final VoidCallback onTap;

  const ScanCodeButton({super.key, required this.onTap});

  @override
  State<ScanCodeButton> createState() => _ScanCodeButtonState();
}

class _ScanCodeButtonState extends State<ScanCodeButton>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
      lowerBound: 0.95,
      upperBound: 1.0,
    );

    _scaleAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(_) => _controller.reverse();
  void _onTapUp(_) => _controller.forward();

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final double width = constraints.maxWidth;
        final double iconSize = width * 1.0;
        final double iconOffset = iconSize / 2.5;
        final double fontSize = width * 0.12;
        final double verticalPadding = width * 0.05;

        return MouseRegion(
          onEnter: (_) => setState(() => _isHovered = true),
          onExit: (_) => setState(() => _isHovered = false),
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: GestureDetector(
              onTapDown: _onTapDown,
              onTapUp: _onTapUp,
              onTapCancel: () => _controller.forward(),
              onTap: widget.onTap,
              child: Stack(
                clipBehavior: Clip.none,
                alignment: Alignment.topCenter,
                children: [
                  // Main button
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: EdgeInsets.only(
                      top: iconOffset + verticalPadding * 0.3,
                      bottom: verticalPadding,
                      left: verticalPadding,
                      right: verticalPadding,
                    ),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: _isHovered
                            ? [Colors.amber.shade300, Colors.white]
                            : [Colors.amber, Colors.white],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: const Color.fromARGB(71, 0, 0, 0),
                          blurRadius: 6,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        'Scan Code',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: fontSize.clamp(12, 18),
                          color: Colors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),

                  // Floating icon
                  Positioned(
                    top: -iconOffset,
                    child: Container(
                      width: iconSize,
                      height: iconSize,
                      padding: EdgeInsets.all(iconSize * 0.18),
                      child: Image.asset(
                        'assets/images/scan.png',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
