import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

class GroupBuySuccessContent extends StatelessWidget {
  final String groupId;
  final String productName;
  final String productImageUrl;
  final int membersInvited;
  final double rewardAmount;
  final VoidCallback? onMyOrderPressed;
  final VoidCallback? onViewWalletPressed;

  const GroupBuySuccessContent({
    super.key,
    required this.groupId,
    required this.productName,
    required this.productImageUrl,
    required this.membersInvited,
    required this.rewardAmount,
    this.onMyOrderPressed,
    this.onViewWalletPressed,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Group ID
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: getResponsivePadding(12, context),
            vertical: getResponsivePadding(6, context),
          ),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            'Group ID : $groupId',
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        SizedBox(height: getResponsivePadding(20, context)),

        // Success message
        Text(
          'You have successfully won and ordered a',
          style: TextStyle(
            fontSize: getResponsiveFontSize(16, context),
            color: Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),

        // Product name
        Text(
          productName,
          style: TextStyle(
            fontSize: getResponsiveFontSize(18, context),
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: getResponsivePadding(20, context)),

        // Product image
        Container(
          width: getResponsiveFontSize(120, context),
          height: getResponsiveFontSize(120, context),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: CachedNetworkImage(
              imageUrl: productImageUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(color: Colors.grey[300]),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[200],
                child: Icon(
                  Icons.image,
                  color: Colors.grey[600],
                  size: getResponsiveFontSize(40, context),
                ),
              ),
            ),
          ),
        ),

        SizedBox(height: getResponsivePadding(20, context)),

        // Track it now button
        Container(
          width: double.infinity,
          margin: EdgeInsets.symmetric(
            horizontal: getResponsivePadding(40, context),
          ),
          child: ElevatedButton(
            onPressed: onMyOrderPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFCD255),
              foregroundColor: Colors.black,
              padding: EdgeInsets.symmetric(
                vertical: getResponsivePadding(12, context),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: Text(
              'My Order',
              style: TextStyle(
                fontSize: getResponsiveFontSize(16, context),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),

        SizedBox(height: getResponsivePadding(20, context)),

        // Reward section
        Container(
          padding: EdgeInsets.all(getResponsivePadding(16, context)),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Column(
            children: [
              // Celebration icon and text
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.celebration,
                    color: const Color(0xFFFCD255),
                    size: getResponsiveFontSize(24, context),
                  ),
                  SizedBox(width: getResponsivePadding(8, context)),
                  Text(
                    'You have invited $membersInvited members !',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(16, context),
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),

              SizedBox(height: getResponsivePadding(12, context)),

              // Reward amount
              Text(
                'A total of RM${rewardAmount.toStringAsFixed(2)} has been credited to your',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                'Lucky Mall Wallet !',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: getResponsivePadding(16, context)),

              // View wallet button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: onViewWalletPressed,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFFFCD255),
                    side: const BorderSide(color: Color(0xFFFCD255)),
                    padding: EdgeInsets.symmetric(
                      vertical: getResponsivePadding(12, context),
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  child: Text(
                    'View Wallet',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(16, context),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
