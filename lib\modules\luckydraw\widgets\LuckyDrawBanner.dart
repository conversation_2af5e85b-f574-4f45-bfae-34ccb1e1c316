import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class LuckyDrawBanner extends StatelessWidget {
  const LuckyDrawBanner({super.key});

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  // Helper method to get responsive banner height
  double getResponsiveBannerHeight(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return 180; // Small devices
    } else if (screenWidth < 900) {
      return 200; // Medium devices
    } else {
      return 230; // Large devices
    }
  }

  // Helper method to get responsive image size
  double getResponsiveImageSize(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return 100; // Small devices
    } else if (screenWidth < 900) {
      return 120; // Medium devices
    } else {
      return 140; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    final bannerHeight = getResponsiveBannerHeight(context);
    final imageSize = getResponsiveImageSize(context);

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(15, context),
        vertical: getResponsivePadding(1, context),
      ),
      height: bannerHeight,
      decoration: BoxDecoration(
        color: const Color(0xFF1B1464), // deep blue
        borderRadius: BorderRadius.circular(5),
      ),
      child: Row(
        children: [
          // Left Side Text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Lucky Draw Store',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(20, context),
                    fontWeight: FontWeight.w900,
                    color: Colors.white,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: getResponsivePadding(8, context)),
                Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFFFFE9A3), Color(0xFFFFC54D)],
                    ),
                    borderRadius: BorderRadius.circular(999),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.18),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: getResponsivePadding(16, context),
                    vertical: getResponsivePadding(10, context),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        size: getResponsiveFontSize(18, context),
                        color: const Color(0xFF1B1464),
                      ),
                      SizedBox(width: getResponsivePadding(10, context)),
                      Flexible(
                        child: Text(
                          'Turn your points into amazing rewards!',
                          style: TextStyle(
                            fontSize: getResponsiveFontSize(13, context),
                            fontWeight: FontWeight.w900,
                            color: const Color(0xFF1B1464),
                            letterSpacing: 0.2,
                            height: 1.2,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: getResponsivePadding(12, context)),
                CustomVisitStoreButton(),
              ],
            ),
          ),

          // Right Side Image (placeholder)
          SizedBox(width: getResponsivePadding(15, context)),
          ClipRRect(
            borderRadius: BorderRadius.circular(15),
            child: Image.asset(
              'assets/images/point_store.png',
              height: imageSize,
              width: imageSize,
              fit: BoxFit.cover,
            ),
          ),
        ],
      ),
    );
  }
}

class CustomVisitStoreButton extends StatelessWidget {
  const CustomVisitStoreButton({super.key});

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.go('/lucky-draw/prize-reveal');
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFFFFE9A3), Color(0xFFFFC54D)],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(
          horizontal: getResponsivePadding(12, context),
          vertical: getResponsivePadding(7, context),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Visit Store',
              style: TextStyle(
                fontSize: getResponsiveFontSize(14.5, context),
                fontWeight: FontWeight.w800,
                color: const Color(0xFF1B1464),
                letterSpacing: 0.2,
              ),
            ),
            SizedBox(width: getResponsivePadding(12, context)),
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.7),
                  width: 1.2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.15),
                    blurRadius: 6,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              padding: EdgeInsets.all(getResponsivePadding(5, context)),
              child: Icon(
                Icons.arrow_forward,
                size: getResponsiveFontSize(18, context),
                color: const Color(0xFF1B1464),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
