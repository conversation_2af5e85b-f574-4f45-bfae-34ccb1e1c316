import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-Model/purchaseCard/PurchaseCardViewModel.dart';
import '../widgets/PurchaseCard/BottomBar.dart';
import '../widgets/PurchaseCard/CategoryButtonsRow.dart';
import '../widgets/PurchaseCard/CategoryDescriptionBox.dart';
import '../widgets/PurchaseCard/ImageSliderWithDots.dart';
import '../widgets/PurchaseCard/PriceAndQuantitySection.dart';
import '../widgets/InfoDialog/PurchaseGuidelineDialog.dart';
import '../widgets/CategorySelectionSheet.dart';
import '../view-Model/cardPack/CardCategoryViewModel.dart';
import 'package:go_router/go_router.dart';
import 'package:shimmer/shimmer.dart';

class PurchaseCardPage extends StatefulWidget {
  const PurchaseCardPage({super.key});

  @override
  State<PurchaseCardPage> createState() => _PurchaseCardPageState();
}

class _PurchaseCardPageState extends State<PurchaseCardPage> {
  int quantity = 5;
  final double unitPrice = 1.00;
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final vm = Provider.of<PurchaseCardViewModel>(context, listen: false);
      await vm.fetchPurchaseCardData();
      if (mounted) {
        setState(() {
          quantity = vm.card?.minQuantity ?? 1; // ✅ init from series rules
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final vm = Provider.of<PurchaseCardViewModel>(context);
    double total = quantity * unitPrice;

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 70,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new),
          onPressed: () => context.go('/card'),
        ),
        title: const Text(
          'Card Collection - Surprise Attack',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w700),
        ),
        backgroundColor: Colors.white,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {},
          ),
        ],
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          bool isTablet = constraints.maxWidth > 600;

          return SingleChildScrollView(
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.stretch,
    children: [
      vm.isLoading
          ? _buildShimmer(height: isTablet ? 400 : 334)
          : ImageSliderWithDots(
              imageUrls: vm.card?.imageUrls ?? [],
              currentIndex: currentIndex,
              onPageChanged: (i) => setState(() => currentIndex = i),
            ),

      vm.isLoading
          ? _buildShimmer(height: isTablet ? 120 : 90)
          : Padding(
              padding: const EdgeInsets.symmetric(horizontal: 1),
              child: CategoryDescriptionBox(
                category: vm.card?.seriesName ?? '',
                description: vm.card?.description ?? '',
              ),
            ),

      const SizedBox(height: 12),

      // Price & quantity should fill width
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: PriceAndQuantitySection(
          quantity: quantity,
          onQuantityChanged: (q) => setState(() => quantity = q),
          unitPrice: unitPrice,
          minQuantity: vm.card?.minQuantity ?? 1,
          stepQuantity: vm.card?.stepQuantity ?? 1,
        ),
      ),

      const SizedBox(height: 16),

      // Buttons flush left/right
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 0),
        child: CategoryButtonsRow(
          onGuidelinesPressed: () {
            showDialog(
              context: context,
              builder: (context) => const PurchaseGuidelineDialog(),
            );
          },
          onSelectCategoryPressed: () async {
            final catVM = Provider.of<CardCategoryViewModel>(context, listen: false);
            if (catVM.seriesList.isEmpty) {
              await catVM.loadSeriesData();
            }
            // Defensive: select first series if not selected
            if (catVM.selectedSeries == null && catVM.seriesList.isNotEmpty) {
              catVM.selectSeries(catVM.seriesList.first.name);
            }
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.white,
              builder: (_) => CategorySelectionSheet(
                onCategorySelected: (category, series) async {
                  // here category='' and series='fantasy' or 'zodiac'
                  vm.updateSeriesAndCategory(series, category);
                  if (mounted) {
                    setState(() {
                      quantity = vm.card?.minQuantity ?? 1; // Reset quantity based on new series rules
                    });
                  }
                },
                showSeriesSelection: true,  // ✅ series mode
                initialSeries: catVM.getCurrentSeriesName(),
              ),
            );
          },
        ),
      ),

      const SizedBox(height: 8),
    ],
  ),
);
        },
      ),
      bottomNavigationBar: BottomBar(
        totalPrice: total,
        onBuyNowPressed: () {
          context.goNamed(
            'checkout',
            extra: {
              'quantity': quantity,
              'total': total,
            },
          );
        },
      ),
    );
  }

  Widget _buildShimmer({required double height}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        height: height,
        width: double.infinity,
        color: Colors.white,
      ),
    );
  }
}
