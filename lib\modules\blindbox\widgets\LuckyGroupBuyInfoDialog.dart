import 'package:flutter/material.dart';

class LuckyGroupBuyInfoDialog extends StatelessWidget {
  const LuckyGroupBuyInfoDialog({super.key});

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.85, 1.3);
    return baseSize * scaleFactor;
  }

  @override
  Widget build(BuildContext context) {
    final items = [
      'Lorem ipsum dolor sit amet consectetur adipiscing elit quisque faucibus ex sapien',
      'Vitae pellentesque sem placerat in id cursus mi pretium tellus duis',
      'Convallis tempus leo eu aenean sed diam urna tempor pulvinar vivamus fringilla lacus nec metus bibendum egestas iaculis',
      'Massa nisl malesuada lacinia integer nunc posuere.',
    ];
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(getResponsiveFontSize(32, context)),
      ),
      insetPadding: EdgeInsets.symmetric(
        horizontal: getResponsiveFontSize(24, context),
        vertical: getResponsiveFontSize(32, context),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: getResponsiveFontSize(24, context),
          vertical: getResponsiveFontSize(24, context),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Center(
                    child: Text(
                      'What is Lucky Group Buy',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: getResponsiveFontSize(24, context),
                      ),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Icon(
                    Icons.close,
                    size: getResponsiveFontSize(28, context),
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            SizedBox(height: getResponsiveFontSize(16, context)),
            Divider(thickness: 1, color: Colors.grey[300]),
            SizedBox(height: getResponsiveFontSize(16, context)),
            ...List.generate(
              items.length,
              (i) => Padding(
                padding: EdgeInsets.only(
                  bottom: getResponsiveFontSize(16, context),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${i + 1}. ',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(18, context),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        items[i],
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(18, context),
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
