class ScanCodeModel {
  final String code;
  final DateTime? claimedAt;
  final bool isClaimed;

  ScanCodeModel({
    required this.code,
    this.claimedAt,
    this.isClaimed = false,
  });

  factory ScanCodeModel.fromJson(Map<String, dynamic> json) {
    return ScanCodeModel(
      code: json['code'],
      claimedAt: json['claimedAt'] != null
          ? DateTime.parse(json['claimedAt'])
          : null,
      isClaimed: json['isClaimed'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'claimedAt': claimedAt?.toIso8601String(),
      'isClaimed': isClaimed,
    };
  }
}
