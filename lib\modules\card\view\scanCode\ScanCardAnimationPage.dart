import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:go_router/go_router.dart';
import '../../model/reveal/RevealCard.dart';

class ScanCardAnimationPage extends StatefulWidget {
  final List<RevealCard> cards; // List of card identifiers or names

  const ScanCardAnimationPage({super.key, required this.cards});

  @override
  State<ScanCardAnimationPage> createState() => _ScanCardAnimationPageState();
}

class _ScanCardAnimationPageState extends State<ScanCardAnimationPage>
    with SingleTickerProviderStateMixin {
  late VideoPlayerController _controller;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _controller = VideoPlayerController.asset('assets/videos/animation.mp4')
      ..initialize().then((_) {
        setState(() {});
        _controller.play();
      });

    _controller.addListener(() {
      if (_controller.value.isInitialized &&
          _controller.value.position >= _controller.value.duration &&
          mounted) {
        _goToRevealPage();
      }
    });

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _goToRevealPage() {
    if (!mounted) return;

    _controller.pause();
    _animationController.stop();

    context.pushReplacement('/scan/reveal', extra: widget.cards);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Fullscreen video background
          Positioned.fill(
            child: _controller.value.isInitialized
                ? FittedBox(
                    fit: BoxFit.cover,
                    child: SizedBox(
                      width: _controller.value.size.width,
                      height: _controller.value.size.height,
                      child: VideoPlayer(_controller),
                    ),
                  )
                : const Center(child: CircularProgressIndicator()),
          ),

          // Skip animation button
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: ElevatedButton(
              onPressed: _goToRevealPage,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color.fromARGB(206, 255, 193, 7),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                elevation: 6,
              ),
              child: const Text(
                'Skip Animation',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),

          // Centered "CLICK TO REVEAL" glowing button
          Center(
            child: FadeTransition(
              opacity: Tween(begin: 0.5, end: 1.0).animate(_animationController),
              child: GestureDetector(
                onTap: _goToRevealPage,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100),
                    gradient: const LinearGradient(
                      colors: [Color(0xFFFFF176), Color(0xFFFFC107)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color.fromARGB(178, 255, 214, 64),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: const Text(
                    "CLICK TO REVEAL",
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                      letterSpacing: 2,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
