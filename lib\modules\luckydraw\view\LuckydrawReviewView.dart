import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../view-model/LuckydrawReviewVM.dart';

class LuckydrawReviewView extends StatelessWidget {
  const LuckydrawReviewView({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => LuckydrawReviewVM(),
      child: Consumer<LuckydrawReviewVM>(
        builder: (context, vm, _) {
          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              centerTitle: true,
              elevation: 0,
              backgroundColor: Colors.white,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
                onPressed: () => context.pop(),
              ),
              title: const Text(
                'Rating and Reviews',
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
            ),
            body: Column(
              children: [
                _buildTabs(context, vm),
                const Divider(
                  height: 1,
                  thickness: 1,
                  color: Color(0xFFF0F0F0),
                ),
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    itemCount: vm.filteredReviews.length,
                    itemBuilder: (context, index) {
                      final review = vm.filteredReviews[index];
                      return Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        padding: const EdgeInsets.all(14),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: const Color(0xFFEDEDED)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 16,
                                  backgroundColor: Colors.grey[300],
                                  child: Text(
                                    review.userName[0],
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        review.userName,
                                        style: const TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      const SizedBox(height: 2),
                                      Row(
                                        children: [
                                          ...List.generate(
                                            5,
                                            (i) => Icon(
                                              i < review.rating.round()
                                                  ? Icons.star
                                                  : Icons.star_border,
                                              color: Colors.amber,
                                              size: 14,
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            review.date,
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Variation: ${review.variation}',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              review.reviewText,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.black87,
                              ),
                            ),
                            // Show review images if available
                            if (review.imageUrls != null &&
                                review.imageUrls!.isNotEmpty) ...[
                              const SizedBox(height: 12),
                              SizedBox(
                                height: 80,
                                child: ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  itemCount: review.imageUrls!.length,
                                  itemBuilder: (context, imageIndex) {
                                    final imageUrl =
                                        review.imageUrls![imageIndex];
                                    return Container(
                                      margin: const EdgeInsets.only(right: 8),
                                      width: 80,
                                      height: 80,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: Colors.grey[300]!,
                                          width: 1,
                                        ),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(8),
                                        child: Image.network(
                                          imageUrl,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) {
                                                return Container(
                                                  color: Colors.grey[200],
                                                  child: Icon(
                                                    Icons.image_not_supported,
                                                    color: Colors.grey,
                                                    size: 24,
                                                  ),
                                                );
                                              },
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                            const SizedBox(height: 10),
                            Row(
                              children: [
                                const Icon(
                                  Icons.thumb_up_alt_outlined,
                                  size: 16,
                                  color: Colors.grey,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  'Helpful (${review.helpfulCount})',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTabs(BuildContext context, LuckydrawReviewVM vm) {
    final TextStyle unselected = const TextStyle(
      fontWeight: FontWeight.w500,
      color: Colors.black87,
    );

    Widget pill({
      required Widget child,
      required bool isActive,
      VoidCallback? onTap,
    }) {
      return GestureDetector(
        onTap: onTap,
        child: Container(
          height: 36,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isActive ? const Color(0xFFFFB800) : Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: const Color(0xFFEDEDED)),
          ),
          child: DefaultTextStyle(
            style: isActive
                ? const TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w700,
                  )
                : unselected,
            child: child,
          ),
        ),
      );
    }

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            pill(
              isActive: vm.selectedTab == ReviewTab.all,
              onTap: () => vm.setTab(ReviewTab.all),
              child: Row(
                children: [
                  const Text('All'),
                  const SizedBox(width: 6),
                  Container(
                    width: 22,
                    height: 18,
                    decoration: BoxDecoration(
                      color: vm.selectedTab == ReviewTab.all
                          ? Colors.black
                          : const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      vm.allCount.toString(),
                      style: TextStyle(
                        color: vm.selectedTab == ReviewTab.all
                            ? Colors.white
                            : Colors.black,
                        fontSize: 12,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            pill(
              isActive: vm.selectedTab == ReviewTab.withMedia,
              onTap: () => vm.setTab(ReviewTab.withMedia),
              child: Row(
                children: [
                  const Text('With Media'),
                  const SizedBox(width: 6),
                  Text('(${vm.withMediaCount})'),
                ],
              ),
            ),
            const SizedBox(width: 8),
            pill(
              isActive: vm.selectedTab == ReviewTab.stars,
              onTap: () => vm.setTab(ReviewTab.stars),
              child: Row(
                children: [
                  const Text('Stars'),
                  const SizedBox(width: 6),
                  DropdownButton<int?>(
                    value: vm.selectedStars,
                    underline: const SizedBox.shrink(),
                    icon: Icon(
                      Icons.keyboard_arrow_down_rounded,
                      size: 18,
                      color: vm.selectedTab == ReviewTab.stars
                          ? Colors.black
                          : Colors.white,
                    ),
                    dropdownColor: Colors.white,
                    items: const [
                      DropdownMenuItem<int?>(value: null, child: Text('(All)')),
                      DropdownMenuItem<int?>(value: 5, child: Text('5')),
                      DropdownMenuItem<int?>(value: 4, child: Text('4')),
                      DropdownMenuItem<int?>(value: 3, child: Text('3')),
                      DropdownMenuItem<int?>(value: 2, child: Text('2')),
                      DropdownMenuItem<int?>(value: 1, child: Text('1')),
                    ],
                    onChanged: (v) => vm.setStars(v),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            pill(
              isActive: vm.selectedTab == ReviewTab.variation,
              onTap: () => vm.setTab(ReviewTab.variation),
              child: Row(
                children: [
                  const Text('Variation'),
                  const SizedBox(width: 6),
                  DropdownButton<String?>(
                    value: vm.selectedVariation,
                    underline: const SizedBox.shrink(),
                    icon: Icon(
                      Icons.keyboard_arrow_down_rounded,
                      size: 18,
                      color: vm.selectedTab == ReviewTab.variation
                          ? Colors.black
                          : Colors.white,
                    ),
                    dropdownColor: Colors.white,
                    items: const [
                      DropdownMenuItem<String?>(
                        value: null,
                        child: Text('(All)'),
                      ),
                      DropdownMenuItem<String?>(
                        value: 'Black',
                        child: Text('Black'),
                      ),
                      DropdownMenuItem<String?>(
                        value: 'White',
                        child: Text('White'),
                      ),
                    ],
                    onChanged: (v) => vm.setVariation(v),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
