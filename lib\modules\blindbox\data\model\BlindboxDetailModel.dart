class BlindboxDetailModel {
  final int unboxId; // id for blindbox
  final List<String> unboxCover; // image for blindbox
  final String unboxTitle; // title for blindbox
  final int cardDeduction; // max card deduction (card that can be use to reduce price) for blindbox
  final String details; // details for blindbox
  final double salePrice; // price for blindbox
  final double score; // rating for blindbox
  final int sold; // sold count for blindbox
  final bool isCollect; // isCollect is basically isFavorite

  BlindboxDetailModel({
    required this.unboxId,
    required this.unboxCover,
    required this.unboxTitle,
    required this.cardDeduction,
    required this.details,
    required this.salePrice,
    required this.score,
    required this.sold,
    required this.isCollect,
  });

  factory BlindboxDetailModel.fromJson(Map<String, dynamic> json) {
    return BlindboxDetailModel(
      unboxId: json['unboxId'],
      unboxCover: List<String>.from(json['unboxCover']),
      unboxTitle: json['unboxTitle'],
      cardDeduction: json['cardDeduction'],
      details: json['details'],
      salePrice: json['salePrice'],
      score: json['score'],
      sold: json['sold'],
      isCollect: json['isCollect'],
    );
  }
}
