import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class HistoryDateRangePickerRow extends StatelessWidget {
  final DateTime startDate;
  final DateTime endDate;
  final ValueChanged<DateTime> onStartDateChanged;
  final ValueChanged<DateTime> onEndDateChanged;
  final bool isYearMode;

  const HistoryDateRangePickerRow({
    super.key,
    required this.startDate,
    required this.endDate,
    required this.onStartDateChanged,
    required this.onEndDateChanged,
    required this.isYearMode,
  });

  @override
  Widget build(BuildContext context) {
    final scale = MediaQuery.of(context).size.width / 390;
    final format = isYearMode
        ? DateFormat('yyyy')
        : DateFormat.yMMM(); // Example: Mac 2025

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16 * scale, vertical: 10 * scale),
      child: Row(
        children: [
          Expanded(
            child: _buildPickerField(
              context,
              label: 'Start Date',
              date: startDate,
              onTap: () => _showCustomPicker(context, startDate, onStartDateChanged),
              formatter: format,
              scale: scale,
            ),
          ),
          SizedBox(width: 16 * scale),
          Expanded(
            child: _buildPickerField(
              context,
              label: 'End Date',
              date: endDate,
              onTap: () => _showCustomPicker(context, endDate, onEndDateChanged),
              formatter: format,
              scale: scale,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPickerField(
    BuildContext context, {
    required String label,
    required DateTime date,
    required VoidCallback onTap,
    required DateFormat formatter,
    required double scale,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12 * scale,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: EdgeInsets.only(bottom: 8 * scale),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.shade400,
                  width: 1.4,
                ),
              ),
            ),
            child: Text(
              formatter.format(date),
              style: TextStyle(
                fontSize: 14 * scale,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showCustomPicker(
    BuildContext context,
    DateTime initialDate,
    ValueChanged<DateTime> onSelected,
  ) async {
    if (isYearMode) {
      final pickedYear = await showDialog<int>(
        context: context,
        builder: (context) => YearPickerDialog(initialYear: initialDate.year),
      );
      if (pickedYear != null) {
        onSelected(DateTime(pickedYear));
      }
    } else {
      final picked = await showMonthYearPicker(context, initialDate);
      if (picked != null) {
        onSelected(DateTime(picked.year, picked.month));
      }
    }
  }
}

// Custom month/year picker dialog 
Future<DateTime?> showMonthYearPicker(BuildContext context, DateTime initialDate) async {
  int selectedYear = initialDate.year;
  int selectedMonth = initialDate.month;

  final screenWidth = MediaQuery.of(context).size.width;
  final scale = screenWidth / 390;

  return await showDialog<DateTime>(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: const Text('Select Month & Year'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Year Dropdown on Top
            DropdownButtonFormField<int>(
              value: selectedYear,
              decoration: InputDecoration(
                labelText: 'Year',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8 * scale)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12 * scale),
              ),
              items: List.generate(10, (i) {
                int year = DateTime.now().year - i;
                return DropdownMenuItem(value: year, child: Text('$year'));
              }),
              onChanged: (val) {
                if (val != null) selectedYear = val;
              },
            ),
            const SizedBox(height: 12),

            // Month Dropdown below
            DropdownButtonFormField<int>(
              value: selectedMonth,
              decoration: InputDecoration(
                labelText: 'Month',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8 * scale)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12 * scale),
              ),
              items: List.generate(12, (index) {
                return DropdownMenuItem(
                  value: index + 1,
                  child: Text(DateFormat.MMMM().format(DateTime(0, index + 1))),
                );
              }),
              onChanged: (val) {
                if (val != null) selectedMonth = val;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text("Cancel"),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context, DateTime(selectedYear, selectedMonth));
            },
            child: const Text("OK"),
          ),
        ],
      );
    },
  );
}



class YearPickerDialog extends StatelessWidget {
  final int initialYear;

  const YearPickerDialog({super.key, required this.initialYear});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text("Select Year"),
      content: SizedBox(
        width: double.maxFinite,
        height: 300,
        child: YearPicker(
          selectedDate: DateTime(initialYear),
          onChanged: (picked) => Navigator.pop(context, picked.year),
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
        ),
      ),
    );
  }
}

