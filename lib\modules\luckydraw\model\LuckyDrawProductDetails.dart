class LuckyDrawProductDetails {
  final int id;
  final int productSpuId;
  final int productSkuId;
  final String productSkuName;
  final String productSkuImage;
  final String productSkuPropertys;
  final String details;
  final int usedAmount;
  final int totalAmount;

  const LuckyDrawProductDetails({
    required this.id,
    required this.productSpuId,
    required this.productSkuId,
    required this.productSkuName,
    required this.productSkuImage,
    required this.productSkuPropertys,
    required this.details,
    required this.usedAmount,
    required this.totalAmount,
  });

  factory LuckyDrawProductDetails.fromJson(Map<String, dynamic> json) {
    return LuckyDrawProductDetails(
      id: (json['id'] as num?)?.toInt() ?? 0,
      productSpuId: (json['productSpuId'] as num?)?.toInt() ?? 0,
      productSkuId: (json['productSkuId'] as num?)?.toInt() ?? 0,
      productSkuName: json['productSkuName']?.toString() ?? '',
      productSkuImage: json['productSkuImage']?.toString() ?? '',
      productSkuPropertys: json['productSkuPropertys']?.toString() ?? '',
      details: json['details']?.toString() ?? '',
      usedAmount: (json['usedAmount'] as num?)?.toInt() ?? 0,
      totalAmount: (json['totalAmount'] as num?)?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productSpuId': productSpuId,
      'productSkuId': productSkuId,
      'productSkuName': productSkuName,
      'productSkuImage': productSkuImage,
      'productSkuPropertys': productSkuPropertys,
      'details': details,
      'usedAmount': usedAmount,
      'totalAmount': totalAmount,
    };
  }

  // Convenience getters for backward compatibility
  String get productName => productSkuName;
  String get imageUrl => productSkuImage;
  int get currentParticipants => usedAmount;
  int get maxParticipants => totalAmount;
  int get remainingSlots => totalAmount - usedAmount;
  double get participationRate =>
      totalAmount > 0 ? (usedAmount / totalAmount) * 100 : 0.0;

  LuckyDrawProductDetails copyWith({
    int? id,
    int? productSpuId,
    int? productSkuId,
    String? productSkuName,
    String? productSkuImage,
    String? productSkuPropertys,
    String? details,
    int? usedAmount,
    int? totalAmount,
  }) {
    return LuckyDrawProductDetails(
      id: id ?? this.id,
      productSpuId: productSpuId ?? this.productSpuId,
      productSkuId: productSkuId ?? this.productSkuId,
      productSkuName: productSkuName ?? this.productSkuName,
      productSkuImage: productSkuImage ?? this.productSkuImage,
      productSkuPropertys: productSkuPropertys ?? this.productSkuPropertys,
      details: details ?? this.details,
      usedAmount: usedAmount ?? this.usedAmount,
      totalAmount: totalAmount ?? this.totalAmount,
    );
  }

  @override
  String toString() {
    return 'LuckyDrawProductDetails(id: $id, productSkuName: $productSkuName, usedAmount: $usedAmount, totalAmount: $totalAmount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LuckyDrawProductDetails && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// API Response wrapper for product details
class LuckyDrawProductDetailsResponse {
  final int status;
  final String message;
  final LuckyDrawProductDetails? data;
  final int timestamp;

  const LuckyDrawProductDetailsResponse({
    required this.status,
    required this.message,
    this.data,
    required this.timestamp,
  });

  factory LuckyDrawProductDetailsResponse.fromJson(Map<String, dynamic> json) {
    return LuckyDrawProductDetailsResponse(
      status: (json['status'] as num?)?.toInt() ?? 0,
      message: json['message']?.toString() ?? '',
      data: json['data'] != null
          ? LuckyDrawProductDetails.fromJson(json['data'])
          : null,
      timestamp: (json['timestamp'] as num?)?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
      'timestamp': timestamp,
    };
  }

  bool get isSuccess => status == 0;
  bool get hasData => data != null;

  @override
  String toString() {
    return 'LuckyDrawProductDetailsResponse(status: $status, message: $message, hasData: $hasData)';
  }
}
