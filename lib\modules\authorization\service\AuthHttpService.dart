import 'package:dio/dio.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:luckymall/res/utility/network/api_client.dart';

class AuthHttpService {
  AuthHttpService._internal();
  static final AuthHttpService instance = AuthHttpService._internal();

  final Dio _dio = ApiClient.instance.publicDio;

  /// Send verification code to user's mobile number.
  /// Returns the raw response map from the API without mapping to a model.
  Future<Map<String, dynamic>> sendCode({
    required int country,
    required String mobile,
  }) async {
    try {
      debugPrint(
        '[AuthHttpService] sendCode payload => country: $country, mobile: $mobile',
      );
      final response = await _dio.post(
        '/user/code',
        data: {'country': country, 'mobile': mobile},
      );

      final data = response.data;
      if (data is Map<String, dynamic>) {
        return data;
      }
      return {
        'status': -1,
        'message': 'Unexpected response format',
        'data': {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } on DioException catch (e) {
      return {
        'status': -1,
        'message': e.response?.data is Map<String, dynamic>
            ? (e.response!.data['message']?.toString() ?? 'Network error')
            : (e.message ?? 'Network error'),
        'data': e.response?.data is Map<String, dynamic>
            ? (e.response!.data['data'] ?? {})
            : {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } catch (e) {
      return {
        'status': -1,
        'message': e.toString(),
        'data': {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    }
  }

  /// Register user and return raw response. Expects `data` to be JWT token.
  Future<Map<String, dynamic>> register({
    required int country,
    required String mobile,
    required String code,
    required String password,
    String? invitationCode,
  }) async {
    try {
      debugPrint(
        '[AuthHttpService] register payload => country: $country, mobile: $mobile, code: $code, invitationCode: ${invitationCode ?? ''}',
      );
      final response = await _dio.post(
        '/user/register',
        data: {
          'country': country,
          'mobile': mobile,
          'code': code,
          'invitationCode': invitationCode ?? '',
          'password': password,
        },
      );
      final data = response.data;
      if (data is Map<String, dynamic>) {
        return data;
      }
      return {
        'status': -1,
        'message': 'Unexpected response format',
        'data': {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } on DioException catch (e) {
      return {
        'status': -1,
        'message': e.response?.data is Map<String, dynamic>
            ? (e.response!.data['message']?.toString() ?? 'Network error')
            : (e.message ?? 'Network error'),
        'data': e.response?.data is Map<String, dynamic>
            ? (e.response!.data['data'] ?? {})
            : {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } catch (e) {
      return {
        'status': -1,
        'message': e.toString(),
        'data': {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    }
  }

  /// Login user with password. Expects `data` to be JWT token (string).
  Future<Map<String, dynamic>> login({
    required int country,
    required String mobile,
    required String password,
  }) async {
    try {
      debugPrint(
        '[AuthHttpService] login payload => country: $country, mobile: $mobile',
      );
      final response = await _dio.post(
        '/user/login',
        data: {'country': country, 'mobile': mobile, 'password': password},
      );
      final data = response.data;
      if (data is Map<String, dynamic>) {
        return data;
      }
      if (data is String) {
        try {
          final decoded = jsonDecode(data);
          if (decoded is Map<String, dynamic>) {
            return decoded;
          }
        } catch (_) {}
      }
      return {
        'status': -1,
        'message': 'Unexpected response format',
        'data': {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } on DioException catch (e) {
      return {
        'status': -1,
        'message': e.response?.data is Map<String, dynamic>
            ? (e.response!.data['message']?.toString() ?? 'Network error')
            : (e.message ?? 'Network error'),
        'data': e.response?.data is Map<String, dynamic>
            ? (e.response!.data['data'] ?? {})
            : {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } catch (e) {
      return {
        'status': -1,
        'message': e.toString(),
        'data': {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    }
  }

  /// Login user with OTP code.
  /// Endpoint: https://api.luckymall.com.my/user/login/code
  /// Body: { 'country': number, 'mobile': string, 'code': string }
  /// Returns raw response map similar to other auth methods.
  Future<Map<String, dynamic>> loginWithCode({
    required int country,
    required String mobile,
    required String code,
  }) async {
    try {
      debugPrint(
        '[AuthHttpService] loginWithCode payload => country: $country, mobile: $mobile, code: $code',
      );
      final response = await _dio.post(
        '/user/login/code',
        data: {'country': country, 'mobile': mobile, 'code': code},
      );
      final data = response.data;
      if (data is Map<String, dynamic>) {
        return data;
      }
      if (data is String) {
        try {
          final decoded = jsonDecode(data);
          if (decoded is Map<String, dynamic>) {
            return decoded;
          }
        } catch (_) {}
      }
      return {
        'status': -1,
        'message': 'Unexpected response format',
        'data': {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } on DioException catch (e) {
      return {
        'status': -1,
        'message': e.response?.data is Map<String, dynamic>
            ? (e.response!.data['message']?.toString() ?? 'Network error')
            : (e.message ?? 'Network error'),
        'data': e.response?.data is Map<String, dynamic>
            ? (e.response!.data['data'] ?? {})
            : {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } catch (e) {
      return {
        'status': -1,
        'message': e.toString(),
        'data': {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    }
  }

  /// Forgot password - reset password with verification code.
  /// Endpoint: https://api.luckymall.com.my/user/forgot/password
  /// Body: { 'country': number, 'mobile': string, 'code': string, 'password': string }
  /// Returns raw response map similar to other auth methods.
  Future<Map<String, dynamic>> forgotPassword({
    required int country,
    required String mobile,
    required String code,
    required String password,
  }) async {
    try {
      debugPrint(
        '[AuthHttpService] forgotPassword payload => country: $country, mobile: $mobile, code: $code',
      );
      final response = await _dio.post(
        '/user/forgot/password',
        data: {
          'country': country,
          'mobile': mobile,
          'code': code,
          'password': password,
        },
      );
      final data = response.data;
      if (data is Map<String, dynamic>) {
        return data;
      }
      return {
        'status': -1,
        'message': 'Unexpected response format',
        'data': {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } on DioException catch (e) {
      return {
        'status': -1,
        'message': e.response?.data is Map<String, dynamic>
            ? (e.response!.data['message']?.toString() ?? 'Network error')
            : (e.message ?? 'Network error'),
        'data': e.response?.data is Map<String, dynamic>
            ? (e.response!.data['data'] ?? {})
            : {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    } catch (e) {
      return {
        'status': -1,
        'message': e.toString(),
        'data': {},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    }
  }
}
