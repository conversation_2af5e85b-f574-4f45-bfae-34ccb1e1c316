import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/CheckoutVM.dart';
import '../view/CheckoutView.dart';

class AddressSection extends StatelessWidget {
  const AddressSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CheckoutVM>(
      builder: (context, viewModel, child) {
        // Show address section for delivery OR pickup for luckydraw
        if (viewModel.deliveryType != DeliveryType.delivery &&
            !(viewModel.checkoutType == CheckoutType.luckydraw &&
                viewModel.deliveryType == DeliveryType.pickup)) {
          return const SizedBox.shrink();
        }
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(color: Colors.white),
          child: GestureDetector(
            onTap: () => _navigateToAddressSelection(context, viewModel),
            child: Row(
              children: [
                Icon(Icons.location_on, color: Colors.black, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            viewModel.deliveryType == DeliveryType.delivery
                                ? (viewModel.selectedAddress?.recipient ??
                                      'Select Address')
                                : 'Lucky Mall',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            viewModel.deliveryType == DeliveryType.delivery
                                ? (viewModel.selectedAddress?.mobile ??
                                      'No Phone')
                                : 'Pickup Location',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        viewModel.deliveryType == DeliveryType.delivery
                            ? (viewModel.selectedAddress?.detailedAddress ??
                                  'Tap to select delivery address')
                            : 'Lucky Mall Store, Ground Floor, Shopping Complex, Kuala Lumpur, Malaysia',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      // Show validation error if any
                      if (viewModel.addressError != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          viewModel.addressError!,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  void _navigateToAddressSelection(BuildContext context, CheckoutVM viewModel) {
    // Only allow address selection for delivery
    if (viewModel.deliveryType != DeliveryType.delivery) {
      return;
    }

    CheckoutHelper.navigateToAddressSelection(
      context,
      onAddressSelected: (address) {
        viewModel.selectAddress(address);
      },
    );
  }
}
