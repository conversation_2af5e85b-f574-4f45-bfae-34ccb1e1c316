import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:luckymall/modules/authorization/service/AuthHttpService.dart';
import 'package:luckymall/router/auth_guard.dart';
import 'package:luckymall/modules/authorization/widgets/login_header.dart';
import 'package:luckymall/modules/authorization/widgets/phone_number_field.dart';
import 'package:luckymall/modules/authorization/widgets/password_field.dart';
import 'package:luckymall/modules/authorization/widgets/login_actions_password.dart';
import 'package:luckymall/modules/authorization/widgets/login_button.dart';
import 'package:luckymall/modules/authorization/widgets/register_prompt.dart';

class PasswordLoginView extends StatefulWidget {
  const PasswordLoginView({super.key});

  @override
  State<PasswordLoginView> createState() => _PasswordLoginViewState();
}

class _PasswordLoginViewState extends State<PasswordLoginView> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isLoading = false;
  String _phoneComplete = '';
  int _countryCode =
      60; // Default MY (kept for consistency if needed elsewhere)

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    final String mobile = _phoneComplete.trim();
    final String password = _passwordController.text.trim();

    if (mobile.isEmpty || password.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter phone and password')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final response = await AuthHttpService.instance.login(
      country: _countryCode,
      mobile: mobile,
      password: password,
    );
    // ignore: avoid_print
    print('[PasswordLoginView] login response => ' + response.toString());

    int status = -1;
    final dynamic statusRaw = response['status'];
    if (statusRaw is int) {
      status = statusRaw;
    } else if (statusRaw is String) {
      status = int.tryParse(statusRaw) ?? -1;
    }
    final String message = response['message']?.toString() ?? '';

    final bool isSuccess = status == 0 || status == 200;

    if (isSuccess) {
      final dynamic dataField = response['data'];
      String? token;
      if (dataField is String) {
        token = dataField;
      } else if (dataField is Map<String, dynamic>) {
        token =
            (dataField['token'] ??
                    dataField['jwt'] ??
                    dataField['jwtToken'] ??
                    dataField['accessToken'])
                ?.toString();
      } else {
        token = dataField?.toString();
      }
      await AuthService.setJwtToken(token, response['timestamp']);
      await AuthService.loginSimple();
      if (!mounted) return;
      context.go('/blind-box/shop');
    } else {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message.isEmpty ? 'Login failed' : message)),
      );
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _handleForgotPassword() {
    // Navigate to forgot password screen
    context.push('/forgot-password');
  }

  void _handleRegister() {
    // Navigate to register screen
    context.go('/register');
  }

  //

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFFFFFFF), Color(0xFFFCD255)],
          ),
        ),
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height,
            ),
            child: IntrinsicHeight(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const LoginHeader(),

                  // Phone Number Field
                  PhoneNumberField(
                    controller: _phoneController,
                    onChanged: (value) {
                      _phoneComplete = value;
                    },
                    onCountryCodeChanged: (dial) {
                      final normalized = dial.replaceAll('+', '');
                      final parsed = int.tryParse(normalized);
                      if (parsed != null) {
                        _countryCode = parsed;
                      }
                    },
                  ),
                  const SizedBox(height: 20),

                  // Password Field
                  PasswordField(
                    controller: _passwordController,
                    onChanged: (value) {},
                  ),
                  const SizedBox(height: 12),

                  // Login Actions (Forgot Password, OTP Login)
                  LoginActions(onForgotPassword: _handleForgotPassword),
                  const SizedBox(height: 12),

                  const SizedBox.shrink(),

                  const SizedBox(height: 12),

                  // Login Button
                  LoginButton(
                    onPressed: _isLoading ? null : _handleLogin,
                    isLoading: _isLoading,
                  ),

                  const SizedBox(height: 24),

                  // Register Prompt
                  RegisterPrompt(onRegisterTap: _handleRegister),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
