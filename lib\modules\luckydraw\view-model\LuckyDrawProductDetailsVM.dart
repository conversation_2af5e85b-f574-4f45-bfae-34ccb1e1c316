import 'package:flutter/foundation.dart';
import '../data/LuckyDrawProductDetailsService.dart';
import '../model/LuckyDrawProductDetails.dart';

enum ProductDetailsLoadingState { idle, loading, success, error }

class LuckyDrawProductDetailsVM extends ChangeNotifier {
  final LuckyDrawProductDetailsService _service =
      luckyDrawProductDetailsService;

  ProductDetailsLoadingState _loadingState = ProductDetailsLoadingState.idle;
  LuckyDrawProductDetails? _productDetails;
  String? _errorMessage;
  int? _currentProductId;

  // Getters
  ProductDetailsLoadingState get loadingState => _loadingState;
  LuckyDrawProductDetails? get productDetails => _productDetails;
  String? get errorMessage => _errorMessage;
  int? get currentProductId => _currentProductId;

  bool get isLoading => _loadingState == ProductDetailsLoadingState.loading;
  bool get hasData => _productDetails != null;
  bool get hasError => _loadingState == ProductDetailsLoadingState.error;

  /// Load product details by ID
  Future<void> loadProductDetails(int productId) async {
    if (!_service.isValidProductId(productId)) {
      _setError('Invalid product ID: $productId');
      return;
    }

    if (_currentProductId == productId && _productDetails != null) {
      // Already loaded this product, no need to reload
      return;
    }

    _setLoading();
    _currentProductId = productId;

    try {
      final response = await _service.getProductDetails(productId);

      if (response.isSuccess && response.hasData) {
        _setSuccess(response.data!);
      } else {
        _setError('Failed to load product details: ${response.message}');
      }
    } catch (e) {
      _setError('Error loading product details: $e');
    }
  }

  /// Load product details with retry logic
  Future<void> loadProductDetailsWithRetry(
    int productId, {
    int maxRetries = 3,
  }) async {
    if (!_service.isValidProductId(productId)) {
      _setError('Invalid product ID: $productId');
      return;
    }

    _setLoading();
    _currentProductId = productId;

    try {
      final response = await _service.getProductDetailsWithRetry(
        productId,
        maxRetries: maxRetries,
      );

      if (response.isSuccess && response.hasData) {
        _setSuccess(response.data!);
      } else {
        _setError('Failed to load product details: ${response.message}');
      }
    } catch (e) {
      _setError('Error loading product details after retries: $e');
    }
  }

  /// Refresh current product details
  Future<void> refresh() async {
    if (_currentProductId != null) {
      await loadProductDetails(_currentProductId!);
    }
  }

  /// Clear current data and reset state
  void clear() {
    _loadingState = ProductDetailsLoadingState.idle;
    _productDetails = null;
    _errorMessage = null;
    _currentProductId = null;
    notifyListeners();
  }

  /// Get remaining participation slots
  int get remainingSlots {
    if (_productDetails == null) return 0;
    return _productDetails!.remainingSlots;
  }

  /// Get participation rate as percentage
  double get participationRate {
    if (_productDetails == null) return 0.0;
    return _productDetails!.participationRate;
  }

  /// Check if product is fully booked
  bool get isFullyBooked {
    if (_productDetails == null) return false;
    return _productDetails!.usedAmount >= _productDetails!.totalAmount;
  }

  /// Check if product has available slots
  bool get hasAvailableSlots {
    if (_productDetails == null) return false;
    return _productDetails!.usedAmount < _productDetails!.totalAmount;
  }

  /// Get formatted participation text
  String get participationText {
    if (_productDetails == null) return '';

    final used = _productDetails!.usedAmount;
    final total = _productDetails!.totalAmount;
    final remaining = total - used;

    if (remaining <= 0) {
      return 'Fully Booked';
    } else if (remaining == 1) {
      return '1 slot remaining';
    } else {
      return '$remaining slots remaining';
    }
  }

  // Private methods for state management
  void _setLoading() {
    _loadingState = ProductDetailsLoadingState.loading;
    _errorMessage = null;
    notifyListeners();
  }

  void _setSuccess(LuckyDrawProductDetails details) {
    _loadingState = ProductDetailsLoadingState.success;
    _productDetails = details;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String message) {
    _loadingState = ProductDetailsLoadingState.error;
    _errorMessage = message;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
