// lib/widgets/invitedFriend/InvitedFriendList.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../view-model/InviteFriendViewModel.dart';
import 'EmptyInvitePlaceholder.dart';

class InvitedFriendList extends StatelessWidget {
  final ScrollController scrollController;
  const InvitedFriendList({super.key, required this.scrollController});

  @override
  Widget build(BuildContext context) {
    final vm = context.watch<InviteFriendViewModel>();
    final friends = vm.invitedFriends;

    return friends.isEmpty
        ? const EmptyInvitePlaceholder()
        : NotificationListener<ScrollNotification>(
            onNotification: (scrollInfo) {
              if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
                vm.fetchFriends();
              }
              return false;
            },
            child: ListView.separated(
              controller: scrollController,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: friends.length,
              separatorBuilder: (_, __) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final friend = friends[index];
                return ListTile(
                  title: Text(friend.name),
                  trailing: Text(
                    "${friend.invitedAt.hour}:${friend.invitedAt.minute.toString().padLeft(2, '0')}",
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                );
              },
            ),
          );
  }
}
