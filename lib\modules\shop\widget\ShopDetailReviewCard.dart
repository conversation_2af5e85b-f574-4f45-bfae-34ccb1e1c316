import 'package:flutter/material.dart';
import '../../../data/model/ShopDetailModel.dart';

class ShopDetailReviewCard extends StatelessWidget {
  final ProductReview review;
  const ShopDetailReviewCard({super.key, required this.review});

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double baseFontSize = screenWidth > 800 ? 18 : 14;
    final double smallFontSize = screenWidth > 800 ? 15 : 12;

    return Container(
      margin: const EdgeInsets.only(bottom: 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row 1: Avatar, Name, Date
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: Colors.grey,
                backgroundImage: review.avatar.isNotEmpty
                    ? NetworkImage(review.avatar)
                    : null,
                child: review.avatar.isEmpty
                    ? const Icon(Icons.person, size: 18, color: Colors.white)
                    : null,
              ),
              const SizedBox(width: 8),
              Text(
                review.isAnonymous
                    ? 'Anonymous'
                    : (review.nickname.isNotEmpty
                        ? review.nickname
                        : 'User'),
                style: TextStyle(
                    fontWeight: FontWeight.w600, fontSize: smallFontSize),
              ),
              const Spacer(),
              Text(
                _formatDate(review.createdTime),
                style: TextStyle(color: Colors.grey, fontSize: smallFontSize),
              ),
            ],
          ),
          const SizedBox(height: 6),
          // Row 2: Stars
          Row(
            children: [
              Row(
                children: List.generate(
                  5,
                  (index) => Icon(
                    index < review.score ? Icons.star : Icons.star_border,
                    color: Colors.amber[700],
                    size: 16,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${review.score.toStringAsFixed(1)} stars',
                style: TextStyle(fontSize: smallFontSize, color: Colors.grey),
              ),
            ],
          ),
          const SizedBox(height: 4),
          // Row 3: Product info (if available)
          if (review.product.productSkuId > 0)
            Text(
              'Product SKU: ${review.product.productSkuId}',
              style: TextStyle(fontSize: smallFontSize, color: Colors.grey),
            ),
          const SizedBox(height: 6),
          // Row 4: Comment
          if (review.content.isNotEmpty)
            Text(
              review.content,
              style: TextStyle(fontSize: baseFontSize, color: Colors.black87),
            ),
          // Row 5: Pictures (if any)
          if (review.pictures.isNotEmpty) ...[
            const SizedBox(height: 8),
            SizedBox(
              height: 60,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: review.pictures.length,
                itemBuilder: (context, index) => Container(
                  width: 60,
                  height: 60,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: Image.network(
                      review.pictures[index],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        color: Colors.grey[200],
                        child: const Icon(Icons.image, color: Colors.grey),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatDate(String dateString) {
    try {
      if (dateString.isEmpty) return '';
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString; // Return original if parsing fails
    }
  }
}