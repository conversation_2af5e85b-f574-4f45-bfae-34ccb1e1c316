import 'package:flutter/material.dart';

class RulesInfoDialog extends StatelessWidget {
  const RulesInfoDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Scan / Code Rules',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '- Make sure the QR code is clear and not blurry.\n'
              '- Code must be 8 digits long.\n'
              '- Each code can only be redeemed once.\n'
              '- Internet connection is required to validate.\n'
              '- If scan fails, try entering the code manually.',
              style: TextStyle(fontSize: 14),
            ),
            const Sized<PERSON><PERSON>(height: 20),
            <PERSON><PERSON>(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
