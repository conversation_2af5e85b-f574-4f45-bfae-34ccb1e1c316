import 'package:flutter/material.dart';
import '../data/LuckyDrawRepository.dart';
import '../model/LuckyDrawRecord.dart';

class LatestDrawRecordVM extends ChangeNotifier {
  final LuckyDrawRepository repository;

  LatestDrawRecordVM({LuckyDrawRepository? repository})
    : repository = repository ?? MockLuckyDrawRepository();

  bool isLoading = true;
  bool hasError = false;
  String errorMessage = '';
  List<LuckyDrawRecord> records = const [];

  Future<void> initialize() async {
    try {
      isLoading = true;
      hasError = false;
      errorMessage = '';
      notifyListeners();

      records = await repository.fetchLatestRecords();

      isLoading = false;
      notifyListeners();
    } catch (e) {
      isLoading = false;
      hasError = true;
      errorMessage = e.toString();
      print('LatestDrawRecordVM: Error initializing: $e');
      notifyListeners();
    }
  }

  Future<void> retry() async {
    await initialize();
  }
}
