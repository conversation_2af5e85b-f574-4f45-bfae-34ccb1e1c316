import 'package:dio/dio.dart';
import '../model/CharityFund.dart';

final charityFundService = CharityFundService();

class CharityFundService {
  final Dio dio = Dio();

  // Cache for charity fund to avoid repeated API calls
  CharityFund? _cachedCharityFund;
  DateTime? _lastCacheTime;
  static const Duration _cacheValidDuration = Duration(
    minutes: 5,
  ); // Cache for 5 minutes

  Future<CharityFund> getCharityFund() async {
    try {
      // Check if we have valid cached data
      if (_cachedCharityFund != null && _lastCacheTime != null) {
        final timeSinceLastCache = DateTime.now().difference(_lastCacheTime!);
        if (timeSinceLastCache < _cacheValidDuration) {
          print('Service: Returning cached charity fund data');
          return _cachedCharityFund!;
        }
      }

      print('Service: Fetching charity fund data from API');
      print(
        'Service: Making API call to: http://192.168.0.47:1222/system/charitable/fund',
      );

      final response = await dio.get(
        'http://192.168.0.47:1222/system/charitable/fund',
      );

      print('Service: API response status: ${response.statusCode}');
      print('Service: API response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        print('Service: Response data status: ${responseData['status']}');
        print('Service: Response data message: ${responseData['message']}');

        if (responseData['status'] == 200) {
          final charityFund = CharityFund.fromJson(responseData);

          // Cache the data
          _cachedCharityFund = charityFund;
          _lastCacheTime = DateTime.now();

          print('Service: Parsed charity fund: $charityFund');
          return charityFund;
        } else {
          throw Exception('API Error: ${responseData['message']}');
        }
      } else {
        throw Exception('Failed to load charity fund: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('Service: DioException: ${e.message}');
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print('Service: Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  // Force refresh by clearing cache
  void clearCache() {
    _cachedCharityFund = null;
    _lastCacheTime = null;
    print('Service: Charity fund cache cleared');
  }

  // Get cached data if available (for offline scenarios)
  CharityFund? getCachedCharityFund() {
    return _cachedCharityFund;
  }

  // Check if cache is valid
  bool get isCacheValid {
    if (_cachedCharityFund == null || _lastCacheTime == null) return false;
    final timeSinceLastCache = DateTime.now().difference(_lastCacheTime!);
    return timeSinceLastCache < _cacheValidDuration;
  }
}
