import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../widgets/CustomAppBar.dart';
import '../widgets/AddressCard.dart';
import '../service/AddressService.dart';
import '../model/AddressModel.dart';

class AddressView extends StatefulWidget {
  const AddressView({super.key});

  @override
  State<AddressView> createState() => _AddressViewState();
}

class _AddressViewState extends State<AddressView> {
  final AddressService _addressService = AddressService();
  List<Addressmodel> _addresses = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchAddresses();
  }

  Future<void> _fetchAddresses() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final addresses = await _addressService.getAddresses();
      setState(() {
        _addresses = addresses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteAddress(int addressId) async {
    try {
      final success = await _addressService.deleteAddress(addressId);
      if (success) {
        // Refresh the addresses list
        await _fetchAddresses();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Address deleted successfully'),
              backgroundColor: Colors.amber,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting address: ${e.toString()}'),
            backgroundColor: Colors.amber,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Manage Addresses',
        onAdd: () async {
          // Navigate to add address and refresh when returning
          await context.push('/profile/add-address');
          // Refresh addresses when returning from add address
          _fetchAddresses();
        },
      ),
      body: RefreshIndicator(onRefresh: _fetchAddresses, child: _buildBody()),
      backgroundColor: Colors.white,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Error loading addresses',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchAddresses,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_addresses.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              'assets/icons/Empty Address.svg',
              width: 120,
              height: 120,
              colorFilter: const ColorFilter.mode(
                Color(0xFF939598),
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'No addresses found',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(12),
      itemCount: _addresses.length,
      itemBuilder: (context, index) {
        final address = _addresses[index];
        return AddressCard(
          address: address,
          onTap: () async {
            // Navigate to update address and refresh when returning
            await context.push(
              '/profile/update-address/${address.id}',
              extra: {
                'id': address.id,
                'recipient': address.recipient,
                'mobile': address.mobile,
                'postcode': address.postcode,
                'detailedAddress': address.detailedAddress,
                'isDefault': address.isDefault,
                'country': address.country,
              },
            );
            // Refresh addresses when returning from update address
            _fetchAddresses();
          },
          onDelete: () => _deleteAddress(address.id!),
        );
      },
    );
  }
}
