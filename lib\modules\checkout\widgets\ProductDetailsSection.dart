import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/CheckoutVM.dart';
import '../view/CheckoutView.dart';


class ProductDetailsSection extends StatelessWidget {
  const ProductDetailsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CheckoutVM>(
      builder: (context, viewModel, child) {
        final productData = viewModel.productData;
        debugPrint('🔍 ProductDetailsSection building');
        debugPrint('📤 ViewModel productData: $productData');
        debugPrint('📤 ViewModel checkoutType: ${viewModel.checkoutType}');

        if (productData == null) {
          debugPrint(
            '❌ ProductDetailsSection: productData is null, returning SizedBox.shrink',
          );
          return const SizedBox.shrink();
        }

        debugPrint(
          '✅ ProductDetailsSection: productData found, rendering content',
        );
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(color: Colors.white),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Info Row
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Image
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      width: 70,
                      height: 70,
                      color: Colors.grey[200],
                      child: productData['imageUrl'] != null
                          ? Image.network(
                              productData['imageUrl'],
                              width: 70,
                              height: 70,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey[400],
                                );
                              },
                            )
                          : Icon(
                              Icons.shopping_bag,
                              color: Colors.grey[400],
                              size: 30,
                            ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Product Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          productData['title'] ?? 'Product',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: Colors.black,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        if (productData['variant'] != null)
                          Text(
                            productData['variant'],
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        const SizedBox(height: 8),
                        Text(
                          'RM${(productData['price'] ?? 0.0).toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Quantity
                  Text(
                    'x${productData['quantity'] ?? 1}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Voucher and Card sections (hidden for luckydraw and physicalCard)
              if (viewModel.checkoutType != CheckoutType.luckydraw &&
                  viewModel.checkoutType != CheckoutType.physicalCard) ...[
                // Voucher Row
                GestureDetector(
                  onTap: viewModel.showVoucherOption
                      ? () {
                          debugPrint(
                            'Voucher row tapped! showVoucherOption: ${viewModel.showVoucherOption}',
                          );
                          debugPrint(
                            'Checkout type: ${viewModel.checkoutType}',
                          );
                          _navigateToVoucherSelection(context, viewModel);
                        }
                      : () {
                          debugPrint(
                            'Voucher row tapped but showVoucherOption is false',
                          );
                          debugPrint(
                            'Checkout type: ${viewModel.checkoutType}',
                          );
                        },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: [
                        Icon(
                          Icons.local_offer_outlined,
                          color: Colors.red,
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Voucher',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const Spacer(),
                        if (viewModel.selectedVoucher != null) ...[
                          Text(
                            viewModel.voucherDescription,
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 8),
                        ] else ...[
                          const Text(
                            'Select Voucher',
                            style: TextStyle(color: Colors.grey, fontSize: 14),
                          ),
                        ],
                        const SizedBox(width: 4),
                        Icon(
                          Icons.arrow_forward_ios,
                          color: Colors.grey,
                          size: 14,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 12),

                // Card Row
                Row(
                  children: [
                    Container(
                      width: 18,
                      height: 18,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(9),
                      ),
                      child: const Icon(
                        Icons.credit_card,
                        color: Colors.white,
                        size: 12,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Card',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Text(
                        '?',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const Spacer(),
                    const Text(
                      'Select Card',
                      style: TextStyle(color: Colors.grey, fontSize: 14),
                    ),
                    const SizedBox(width: 4),
                    Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 14),
                  ],
                ),

                const SizedBox(height: 8),

                // Max discount text
                Text(
                  'Max RM10 discount with cards',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  void _navigateToVoucherSelection(BuildContext context, CheckoutVM viewModel) {
    final productData = viewModel.productData;
    if (productData != null) {
      final orderAmount =
          (productData['price'] ?? 0.0) - (productData['discount'] ?? 0.0);

      // Debug print to verify the navigation is being called
      debugPrint(
        'Navigating to voucher selection with order amount: $orderAmount',
      );

      CheckoutHelper.navigateToVoucherSelection(
        context,
        orderAmount: orderAmount,
        onVoucherSelected: (voucher) {
          debugPrint('Voucher selected: $voucher');
          viewModel.selectVoucher(voucher);
        },
      );
    } else {
      debugPrint('Product data is null, cannot navigate to voucher selection');
    }
  }
}
