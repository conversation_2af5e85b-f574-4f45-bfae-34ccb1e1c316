import 'package:flutter/material.dart';

class ReviewTextSection extends StatelessWidget {
  final String reviewText;
  final int maxCharacters;
  final Function(String) onReviewTextChanged;

  const ReviewTextSection({
    super.key,
    required this.reviewText,
    required this.maxCharacters,
    required this.onReviewTextChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Your Review',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: TextField(
            controller: TextEditingController(text: reviewText),
            onChanged: onReviewTextChanged,
            maxLines: 6,
            maxLength: maxCharacters,
            decoration: InputDecoration(
              hintText: 'Share your experience...',
              hintStyle: TextStyle(color: Colors.grey[400], fontSize: 16),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              counterText: '${reviewText.length}/$maxCharacters',
              counterStyle: TextStyle(color: Colors.grey[500], fontSize: 12),
            ),
            style: const TextStyle(fontSize: 16, color: Colors.black87),
          ),
        ),
      ],
    );
  }
}
