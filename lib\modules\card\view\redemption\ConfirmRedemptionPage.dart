import 'package:flutter/material.dart';
import '../../model/exchangeCard/ExchangeCardModel.dart' as model;
import '../../view-Model/exchangeCard/ConfirmRedemptionViewModel.dart';
import '../../view-Model/cardPack/CardPackViewModel.dart';
import '../../model/cardPack/CardItem.dart';
import '../../widgets/ExchangeCard/RedemptionCardGrid.dart';
import '../../widgets/ExchangeCard/RedemptionBottomBar.dart';
import '../../widgets/ExchangeCard/RedemptionAppBar.dart';
import '../../widgets/ExchangeCard/RedemptionCardShimmer.dart';
import '../../widgets/ExchangeCard/EmptyRedemptionState.dart';
import '../../widgets/ExchangeCard/RedemptionResultPage.dart';
import '../../widgets/ExchangeCard/RedemptionCategoryNav.dart';
import '../../view-Model/cardPack/MockCards.dart';

class ConfirmRedemptionPage extends StatefulWidget {
  final model.ExchangeCardModel modelData;

  /// Pre-chosen ids from the previous page (used for Self-select flow)
  final List<String> selectedCardIds;

  /// Total required for this exchange (can also be read from model)
  final int totalRequired;

  /// NEW: which behaviour to use in this screen
  final model.SelectionType selectionType;

  const ConfirmRedemptionPage({
    super.key,
    required this.modelData,
    required this.selectedCardIds,
    required this.totalRequired,
    required this.selectionType,
  });

  @override
  State<ConfirmRedemptionPage> createState() => _ConfirmRedemptionPageState();
}

class _ConfirmRedemptionPageState extends State<ConfirmRedemptionPage> {
  late final ConfirmRedemptionViewModel viewModel;

  Map<String, int> _getCategoryCounts() {
    final ownedIds = viewModel.getOwnedCardIds().toSet();
    final requiredIds = widget.modelData.requiredCardIds.toSet();

    final filtered = allCardCatalog.where(
      (card) => requiredIds.contains(card.id) && ownedIds.contains(card.id),
    );

    final Map<String, int> counts = {};
    for (final card in filtered) {
      counts[card.category] = (counts[card.category] ?? 0) + 1;
    }
    return counts;
  }

  @override
  void initState() {
    super.initState();
    final cardPackVM = CardPackViewModel();

    viewModel = ConfirmRedemptionViewModel(
      cardPackViewModel: cardPackVM,
      exchangeModel: widget.modelData,
      selectionType: widget.selectionType,
      initialUserSelection: widget.selectedCardIds.toSet(), // respected only in Self-select
    );

    viewModel.init();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const RedemptionAppBar(),
      body: Column(
        children: [
          const SizedBox(height: 12),

          // Category nav (available counts)
          ValueListenableBuilder<String>(
            valueListenable: viewModel.selectedCategory,
            builder: (_, category, __) {
              return RedemptionCategoryNav(
                selectedCategory: category,
                onCategorySelected: viewModel.onCategorySelected,
                categoryCounts: _getCategoryCounts(),
              );
            },
          ),
          const SizedBox(height: 8),

          // Grid / Shimmer / Empty
          Expanded(
            child: ValueListenableBuilder<bool>(
              valueListenable: viewModel.isLoading,
              builder: (_, isLoading, __) {
                if (isLoading) return const RedemptionCardShimmer();

                return ValueListenableBuilder<List<CardItem>>(
                  valueListenable: viewModel.displayableCards,
                  builder: (_, cards, __) {
                    if (cards.isEmpty) return const EmptyRedemptionState();

                    // Rebuild grid when user selections change
                    return ValueListenableBuilder<Set<String>>(
                      valueListenable: viewModel.userSelectedCardIds,
                      builder: (_, __selected, ____) {
                        return RedemptionCardGrid(
                          cardIds: cards.map((e) => e.id).toList(),
                          ownedCardIds: viewModel.getOwnedCardIds(),
                          isCardSelected: viewModel.isCardSelected,
                          isAutoSelected: viewModel.isCardAutoSelected,
                          onCardTapped: viewModel.toggleCardSelection,
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),

          // Bottom bar (shows selected/required; updates when user toggles)
          ValueListenableBuilder<Set<String>>(
            valueListenable: viewModel.userSelectedCardIds,
            builder: (_, __sel, __) {
              return RedemptionBottomBar(
                selectedCount: viewModel.selectedCount,
                totalRequired: widget.totalRequired, // or viewModel.totalRequired
                isEligible: viewModel.isEligible,
                onConfirm: _showConfirmationDialog,
              );
            },
          ),
        ],
      ),
    );
  }

  void _showConfirmationDialog() {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text("Confirm Redemption"),
        content: const Text("Are you sure you want to redeem this reward?"),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text("Cancel"),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (_) => const RedemptionResultPage(
                    isSuccess: true,
                    message: "You’ve successfully redeemed TNG Reload RM10!",
                  ),
                ),
              );
            },
            child: const Text("Confirm"),
          ),
        ],
      ),
    );
  }
}
