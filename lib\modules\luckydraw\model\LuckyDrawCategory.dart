class LuckyDrawCategory {
  final int categoryId;
  final String categoryName;

  LuckyDrawCategory({required this.categoryId, required this.categoryName});

  factory LuckyDrawCategory.fromJson(Map<String, dynamic> json) {
    return LuckyDrawCategory(
      categoryId: json['categoryId'] ?? 0,
      categoryName: json['categoryName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'categoryId': categoryId, 'categoryName': categoryName};
  }

  // Helper getter for display name (can be customized later)
  String get displayName => categoryName;

  // Helper getter for backward compatibility with existing code
  String get id => categoryId.toString();
  String get name => categoryName;

  // Since the API doesn't provide isAdult, we'll assume all categories are safe
  bool get isAdult => false;

  @override
  String toString() {
    return 'LuckyDrawCategory(categoryId: $categoryId, categoryName: $categoryName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LuckyDrawCategory && other.categoryId == categoryId;
  }

  @override
  int get hashCode => categoryId.hashCode;
}
