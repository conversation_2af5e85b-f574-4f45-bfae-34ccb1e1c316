import 'package:flutter/material.dart';
import '../../model/cardPack/CardItem.dart';
import '../../model/exchangeCard/ExchangeCardModel.dart' as model;
import '../cardPack/CardPackViewModel.dart';
import '../cardPack/MockCards.dart';

class ConfirmRedemptionViewModel {
  final CardPackViewModel cardPackViewModel;
  final model.ExchangeCardModel exchangeModel;

  /// Selection behaviour for this session
  final model.SelectionType selectionType;

  /// Preselected ids when coming from "Self-select" flow (optional)
  final Set<String> initialUserSelection;

  ConfirmRedemptionViewModel({
    required this.cardPackViewModel,
    required this.exchangeModel,
    required this.selectionType,
    Set<String>? initialUserSelection,
  }) : initialUserSelection = initialUserSelection ?? {};

  // Loading/UI
  final ValueNotifier<bool> isLoading = ValueNotifier(true);
  final ValueNotifier<String> selectedCategory = ValueNotifier('royal');
  final ValueNotifier<List<CardItem>> displayableCards = ValueNotifier([]);

  // Selections
  final ValueNotifier<Set<String>> userSelectedCardIds = ValueNotifier({});
  final Set<String> autoSelectedCardIds = {};

  int get totalRequired => exchangeModel.requiredCardIds.length;

  Future<void> init() async {
    await cardPackViewModel.loadOwnedCards();

    final owned = cardPackViewModel.getOwnedCardIds().toSet();
    final requiredIds = exchangeModel.requiredCardIds.toSet();
    final eligible = owned.intersection(requiredIds);

    if (selectionType == model.SelectionType.auto) {
      // Auto mode: lock whatever you already own & is required
      autoSelectedCardIds
        ..clear()
        ..addAll(eligible);
      userSelectedCardIds.value = {}; // No manual selection allowed
    } else {
      // Self-select mode: start from preselected (if any)
      autoSelectedCardIds.clear(); // No locked cards in self-select
      userSelectedCardIds.value = initialUserSelection.intersection(eligible);
    }

    _applyFilter(selectedCategory.value);
    isLoading.value = false;
  }

  void onCategorySelected(String category) {
    selectedCategory.value = category;
    _applyFilter(category);
  }

  void _applyFilter(String category) {
    final requiredIds = exchangeModel.requiredCardIds.toSet();
    final filtered = allCardCatalog.where((card) {
      return card.category == category && requiredIds.contains(card.id);
    }).toList();
    displayableCards.value = filtered;
  }

  /// Only works in self-select mode
  void toggleCardSelection(String cardId) {
    if (selectionType == model.SelectionType.auto) return; // Fully locked
    if (autoSelectedCardIds.contains(cardId)) return; // Locked card in self-select

    final current = {...userSelectedCardIds.value};
    if (current.contains(cardId)) {
      current.remove(cardId);
    } else {
      current.add(cardId);
    }
    userSelectedCardIds.value = current;
  }

  bool isCardSelected(String cardId) {
    if (selectionType == model.SelectionType.auto) {
      // In auto mode, only auto-selected cards count
      return autoSelectedCardIds.contains(cardId);
    }
    // In self-select, both auto-locked (if any) and user-selected count
    return autoSelectedCardIds.contains(cardId) ||
           userSelectedCardIds.value.contains(cardId);
  }

  bool isCardAutoSelected(String cardId) {
    return autoSelectedCardIds.contains(cardId);
  }

  int get selectedCount =>
      autoSelectedCardIds.length + userSelectedCardIds.value.length;

  bool get isEligible => selectedCount >= totalRequired;

  List<String> getOwnedCardIds() => cardPackViewModel.getOwnedCardIds();

  int availableCountInCategory(String category) {
    final owned = getOwnedCardIds().toSet();
    final req = exchangeModel.requiredCardIds.toSet();
    return allCardCatalog
        .where((c) =>
            c.category == category &&
            req.contains(c.id) &&
            owned.contains(c.id))
        .length;
  }

  int selectedCountInCategory(String category) {
    final selectedAll = {...autoSelectedCardIds, ...userSelectedCardIds.value};
    return allCardCatalog
        .where((c) => c.category == category && selectedAll.contains(c.id))
        .length;
  }
}
