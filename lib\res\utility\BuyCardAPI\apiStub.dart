class PaymentRequest {
  final String gateway;
  final String name;
  final String phone;
  final String email;
  final int quantity;
  final double total;

  PaymentRequest({
    required this.gateway,
    required this.name,
    required this.phone,
    required this.email,
    required this.quantity,
    required this.total,
  });

  Map<String, dynamic> toJson() {
    return {
      'gateway': gateway,
      'name': name,
      'phone': phone,
      'email': email,
      'quantity': quantity,
      'total': total.toStringAsFixed(2),
    };
  }
}

class PaymentApiStub {
  static Future<void> submit(PaymentRequest request) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Print request to console (replace this with actual API call later)
    print('Mock API Request: ${request.toJson()}');

    // You can simulate success/failure if needed
    // throw Exception("Mock error"); // Uncomment to test error handling
  }
}
