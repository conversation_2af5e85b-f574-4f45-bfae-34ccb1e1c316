import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

class PostCard extends StatelessWidget {
  const PostCard({
    super.key,
    required this.imageUrl,
    required this.title,
    required this.username,
    required this.likeCount,
    required this.onTap,
    this.isFavorite = false,
    this.onFavorite,
  });
  final String imageUrl;
  final String title;
  final String username;
  final int likeCount;
  final VoidCallback onTap;
  final bool isFavorite;
  final VoidCallback? onFavorite;

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  // Helper method to get responsive image height
  double getResponsiveImageHeight(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return 150; // Small devices
    } else if (screenWidth < 900) {
      return 180; // Medium devices
    } else {
      return 200; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    final imageHeight = getResponsiveImageHeight(context);

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
      elevation: 0,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5),
          border: Border.all(color: Color(0xFFE8E8E8)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Section (dynamic height) with navigation
            GestureDetector(
              onTap: onTap,
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(5),
                  topRight: Radius.circular(5),
                ),
                child: CachedNetworkImage(
                  imageUrl: imageUrl,
                  width: double.infinity,
                  placeholder: (context, url) => Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      width: double.infinity,
                      height: imageHeight,
                      color: Colors.grey[300]!,
                    ),
                  ),
                  errorWidget: (context, url, error) => const Icon(Icons.error),
                ),
              ),
            ),

            Padding(
              padding: EdgeInsets.fromLTRB(
                getResponsivePadding(12, context),
                getResponsivePadding(12, context),
                getResponsivePadding(12, context),
                getResponsivePadding(4, context),
              ),
              child: Text(
                title,
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: getResponsivePadding(12, context),
                vertical: getResponsivePadding(8, context),
              ),
              child: Row(
                children: [
                  // Placeholder for Avatar
                  CircleAvatar(
                    radius: getResponsiveFontSize(10, context),
                    backgroundColor: Colors.grey,
                    child: Icon(
                      Icons.person,
                      size: getResponsiveFontSize(15, context),
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(width: getResponsivePadding(8, context)),

                  // Username
                  Text(
                    username,
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(14, context),
                    ),
                  ),
                  const Spacer(),

                  // Like Count and Icon
                  Row(
                    children: [
                      Text(
                        likeCount.toString(),
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                        ),
                      ),
                      SizedBox(width: getResponsivePadding(4, context)),
                      GestureDetector(
                        onTap: onFavorite,
                        child: Icon(
                          isFavorite
                              ? Icons.thumb_up
                              : Icons.thumb_up_alt_outlined,
                          color: isFavorite ? Colors.red : Colors.grey,
                          size: getResponsiveFontSize(15, context),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PostCardShimmer extends StatelessWidget {
  const PostCardShimmer({super.key});

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  // Helper method to get responsive image height
  double getResponsiveImageHeight(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return 100; // Small devices
    } else if (screenWidth < 900) {
      return 120; // Medium devices
    } else {
      return 140; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    final imageHeight = getResponsiveImageHeight(context);

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
      elevation: 0,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5),
          border: Border.all(color: Color(0xFFE8E8E8)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image shimmer
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(5),
                topRight: Radius.circular(5),
              ),
              child: Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: double.infinity,
                  height: imageHeight,
                  color: Colors.grey[300]!,
                ),
              ),
            ),
            SizedBox(height: getResponsivePadding(12, context)),
            // Title shimmer
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: getResponsivePadding(12, context),
              ),
              child: Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: double.infinity,
                  height: 16,
                  color: Colors.grey[300]!,
                ),
              ),
            ),
            SizedBox(height: getResponsivePadding(16, context)),
            // Row shimmer (avatar, username, like)
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: getResponsivePadding(12, context),
                vertical: getResponsivePadding(8, context),
              ),
              child: Row(
                children: [
                  // Avatar shimmer
                  Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.grey[300]!,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  SizedBox(width: getResponsivePadding(8, context)),
                  // Username shimmer
                  Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      width: 50,
                      height: 12,
                      color: Colors.grey[300]!,
                    ),
                  ),
                  const Spacer(),
                  // Like shimmer
                  Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      width: 30,
                      height: 12,
                      color: Colors.grey[300]!,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
