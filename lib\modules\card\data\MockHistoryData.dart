import '../model/historyCard/HistoryCardItem.dart';
import '../model/historyCard/HistoryCardUsedItem.dart';

final List<HistoryCardItem> mockHistoryData = [
  HistoryCardItem(
    timestamp: DateTime(2025, 7, 24, 10, 20),
    type: 'Transfer Card',
    detail: 'Transfer to <PERSON>',
    status: 'completed',
    amount: -1.00,
    transferType: 'Sent',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'LovelyKnight',
        name: '<PERSON><PERSON> Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/LovelyKnight.png',
        quantity: 1,
      ),
    ],
  ),
  HistoryCardItem(
    timestamp: DateTime(2025, 7, 24, 9, 10),
    type: 'Gift',
    detail: 'Event Gift Card',
    status: 'completed',
    amount: 1.00,
    giftSource: 'Admin',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'King',
        name: 'King',
        imageUrl: 'assets/images/card/cardType/fantasy/royals/king.png',
        quantity: 1,
      ),
    ],
  ),
  HistoryCardItem(
    timestamp: DateTime(2025, 7, 24, 8, 30),
    type: 'Purchase Card',
    detail: 'Purchase 10 Cards',
    status: 'completed',
    amount: 10.00,
    paymentMethod: 'BillPlz',
    paymentUsed: 'TnG Wallet',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'ColdSweatKnight',
        name: 'Cold Sweat Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/ColdSweatKnight.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/CoolKnight.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/LovelyKnight.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/CryingKnight.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/King.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/ThinkingKnight.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/LaughingKnight.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/YummyKnight.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/WinkingKnight.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/YummyKnight.png',
        quantity: 1,
      ),
    ],
  ),
  // Today
  HistoryCardItem(
    timestamp: DateTime(2025, 7, 25, 19, 48),
    type: 'Purchase Card',
    detail: 'Purchase Card Detail',
    status: 'cancel',
    amount: -5.00,
    paymentMethod: 'BillPlz',
    paymentUsed: 'Bank Transfer (Maybank)',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'HappyKnight',
        name: 'Happy Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/HappyKnight.png',
        quantity: 2,
      ),
    ],
  ),
  HistoryCardItem(
    timestamp: DateTime(2025, 7, 25, 13, 9),
    type: 'Physical Card',
    detail: 'Physical Card Detail',
    status: 'pickup',
    amount: 3.18,
    paymentMethod: 'ipay88',
    orderMethod: 'Pickup',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'ColdSweatKnight',
        name: 'Cold Sweat Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/ColdSweatKnight.png',
        quantity: 2,
      ),
    ],
  ),
  HistoryCardItem(
    timestamp: DateTime(2025, 7, 25, 5, 18),
    type: 'Gift',
    detail: 'Gift Detail',
    status: 'shipping',
    amount: -3.59,
    giftSource: 'Event: Lucky Spin',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'King',
        name: 'King',
        imageUrl: 'assets/images/card/cardType/fantasy/royals/king.png',
        quantity: 2,
      ),
    ],
  ),

  // This Month
  HistoryCardItem(
    timestamp: DateTime(2025, 7, 15, 4, 42),
    type: 'Gift',
    detail: 'Gift Detail',
    status: 'pickup',
    amount: 9.96,
    giftSource: 'Event: Daily Login',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/CoolKnight.png',
        quantity: 2,
      ),
    ],
  ),
  HistoryCardItem(
    timestamp: DateTime(2025, 7, 19, 14, 49),
    type: 'Discount Used',
    detail: 'Discount Used Detail',
    status: 'shipping',
    amount: 2.23,
    productName: 'Promo Voucher',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'HappyKnight',
        name: 'Happy Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/HappyKnight.png',
        quantity: 1,
      ),
    ],
  ),
  HistoryCardItem(
    timestamp: DateTime(2025, 7, 12, 22, 44),
    type: 'Physical Card',
    detail: 'Physical Card Detail',
    status: 'cancel',
    amount: -7.37,
    paymentMethod: 'ipay88',
    orderMethod: 'Pickup',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'King',
        name: 'King',
        imageUrl: 'assets/images/card/cardType/fantasy/royals/king.png',
        quantity: 2,
      ),
      HistoryCardUsedItem(
        id: 'ColdSweatKnight',
        name: 'Cold Sweat Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/ColdSweatKnight.png',
        quantity: 1,
      ),
    ],
  ),

  // Earlier This Year
  HistoryCardItem(
    timestamp: DateTime(2025, 5, 30, 18, 36),
    type: 'Discount Used',
    detail: 'Discount Used Detail',
    status: 'cancel',
    amount: 9.64,
    productName: 'Promo Voucher',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/CoolKnight.png',
        quantity: 2,
      ),
    ],
  ),
  HistoryCardItem(
    timestamp: DateTime(2025, 4, 24, 6, 43),
    type: 'Discount Used',
    detail: 'Discount Used Detail',
    status: 'pickup',
    amount: -7.52,
    productName: 'Promo Voucher',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'HappyKnight',
        name: 'Happy Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/HappyKnight.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'King',
        name: 'King',
        imageUrl: 'assets/images/card/cardType/fantasy/royals/king.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'ColdSweatKnight',
        name: 'Cold Sweat Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/ColdSweatKnight.png',
        quantity: 1,
      ),
    ],
  ),
  HistoryCardItem(
    timestamp: DateTime(2025, 3, 9, 20, 21),
    type: 'Discount Used',
    detail: 'Discount Used Detail',
    status: 'cancel',
    amount: -5.54,
    productName: 'Lucky Mall Shirt',
    cardsUsed: [
      HistoryCardUsedItem(
        id: 'LovelyKnight',
        name: 'Lovely Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/LovelyKnight.png',
        quantity: 2,
      ),
      HistoryCardUsedItem(
        id: 'King',
        name: 'King',
        imageUrl: 'assets/images/card/cardType/fantasy/royals/king.png',
        quantity: 1,
      ),
      HistoryCardUsedItem(
        id: 'CoolKnight',
        name: 'Cool Knight',
        imageUrl: 'assets/images/card/cardType/fantasy/knight/CoolKnight.png',
        quantity: 1,
      ),
    ],
  ),
];
