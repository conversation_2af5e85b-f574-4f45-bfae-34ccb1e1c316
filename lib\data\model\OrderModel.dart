enum OrderType { storeOrder, blindBox, prize, luckyGroupBuy, physicalCard }

enum OrderStatus {
  toPay,
  toShip,
  toReceive,
  completed,
  cancelled,
  returnRefund,
}

enum RefundStatus {
  // Initial states
  refundRequested, // "In Progress..." - reviewing request
  refundApproved, // "Approved" - request approved, awaiting return
  // Return process
  returnInProgress, // "Return In Progress..." - package on its way back
  productReceived, // "We have received the product"
  // Final states
  refundCompleted, // "Completed" - points/money refunded
  exchangeCompleted, // "Completed" - new product sent
  refundRejected, // "Rejected" - request declined
}

enum PurchaseType {
  appPurchase, // Can be repurchased
  qrRedemption, // One-time redemption, cannot repurchase
}

enum DeliveryMethod { delivery, pickup }

class OrderModel {
  final String id;
  final OrderType type;
  final OrderStatus status;
  final RefundStatus? refundStatus;
  final PurchaseType purchaseType;
  final DeliveryMethod deliveryMethod;
  final String productName;
  final String productImage;
  final double subtotal;
  final double total;
  final int quantity;
  final DateTime createdAt;
  final DateTime? paymentDueDate;
  final String? trackingNumber;
  final String? refundRequestId;
  final String? refundReason;
  final List<String> refundEvidence;
  final Map<String, dynamic>
  metadata; // Additional data for different order types

  OrderModel({
    required this.id,
    required this.type,
    required this.status,
    this.refundStatus,
    this.purchaseType = PurchaseType.appPurchase,
    required this.deliveryMethod,
    required this.productName,
    required this.productImage,
    required this.subtotal,
    required this.total,
    required this.quantity,
    required this.createdAt,
    this.paymentDueDate,
    this.trackingNumber,
    this.refundRequestId,
    this.refundReason,
    this.refundEvidence = const [],
    this.metadata = const {},
  });

  // Helper methods
  String get typeDisplayName {
    switch (type) {
      case OrderType.storeOrder:
        return 'Store Order';
      case OrderType.blindBox:
        return 'Blind Box';
      case OrderType.prize:
        return 'Prize';
      case OrderType.luckyGroupBuy:
        return 'Lucky Group Buy';
      case OrderType.physicalCard:
        return 'Physical Card';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case OrderStatus.toPay:
        return 'To Pay';
      case OrderStatus.toShip:
        return 'To Ship';
      case OrderStatus.toReceive:
        return 'To Receive';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.returnRefund:
        return refundStatusDisplayName ?? 'Refund';
    }
  }

  String? get refundStatusDisplayName {
    if (refundStatus == null) return null;

    switch (refundStatus!) {
      case RefundStatus.refundRequested:
        return 'In Progress...';
      case RefundStatus.refundApproved:
        return 'Approved';
      case RefundStatus.returnInProgress:
        return 'Return In Progress...';
      case RefundStatus.productReceived:
        return 'Product Received';
      case RefundStatus.refundCompleted:
        return 'Completed';
      case RefundStatus.exchangeCompleted:
        return 'Completed';
      case RefundStatus.refundRejected:
        return 'Rejected';
    }
  }

  String get purchaseTypeDisplayName {
    switch (purchaseType) {
      case PurchaseType.appPurchase:
        return 'App Purchase';
      case PurchaseType.qrRedemption:
        return 'QR Redemption';
    }
  }

  String get deliveryMethodDisplayName {
    switch (deliveryMethod) {
      case DeliveryMethod.delivery:
        return 'Delivery';
      case DeliveryMethod.pickup:
        return 'Pick Up';
    }
  }

  bool get canCancel =>
      status == OrderStatus.toPay || status == OrderStatus.toShip;
  bool get canRate =>
      status == OrderStatus.completed && type == OrderType.storeOrder;
  bool get canShare =>
      (type == OrderType.blindBox || type == OrderType.prize) &&
      status == OrderStatus.completed;
  bool get canTrack =>
      status == OrderStatus.toShip || status == OrderStatus.toReceive;
  bool get canBuyAgain =>
      purchaseType == PurchaseType.appPurchase &&
      (type == OrderType.storeOrder || type == OrderType.blindBox);
  bool get canRequestRefund =>
      status == OrderStatus.completed &&
      status != OrderStatus.returnRefund &&
      type != OrderType.prize; // Prizes typically can't be refunded

  // Get appropriate button text based on order type
  String get actionButtonText {
    switch (type) {
      case OrderType.storeOrder:
      case OrderType.blindBox:
      case OrderType.physicalCard:
        return 'Buy';
      case OrderType.prize:
      case OrderType.luckyGroupBuy:
        return 'Join';
    }
  }
}

// Extension methods for better display handling
extension OrderTypeExtension on OrderType {
  String get typeDisplayName {
    switch (this) {
      case OrderType.storeOrder:
        return 'Store Order';
      case OrderType.blindBox:
        return 'Blind Box';
      case OrderType.prize:
        return 'Prize';
      case OrderType.luckyGroupBuy:
        return 'Lucky Group Buy';
      case OrderType.physicalCard:
        return 'Physical Card';
    }
  }
}

extension DeliveryMethodExtension on DeliveryMethod {
  String get deliveryMethodDisplayName {
    switch (this) {
      case DeliveryMethod.delivery:
        return 'Delivery';
      case DeliveryMethod.pickup:
        return 'Pick Up';
    }
  }
}

extension OrderStatusExtension on OrderStatus {
  String get statusDisplayName {
    switch (this) {
      case OrderStatus.toPay:
        return 'To Pay';
      case OrderStatus.toShip:
        return 'To Ship';
      case OrderStatus.toReceive:
        return 'To Receive';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.returnRefund:
        return 'Refund';
    }
  }
}

extension RefundStatusExtension on RefundStatus {
  String get displayName {
    switch (this) {
      case RefundStatus.refundRequested:
        return 'In Progress...';
      case RefundStatus.refundApproved:
        return 'Approved';
      case RefundStatus.returnInProgress:
        return 'Return In Progress...';
      case RefundStatus.productReceived:
        return 'Product Received';
      case RefundStatus.refundCompleted:
        return 'Completed';
      case RefundStatus.exchangeCompleted:
        return 'Completed';
      case RefundStatus.refundRejected:
        return 'Rejected';
    }
  }

  String get description {
    switch (this) {
      case RefundStatus.refundRequested:
        return 'We are reviewing your return/refund request. Hang tight\n\nEstimated response time: 1-2 business days.';
      case RefundStatus.refundApproved:
        return 'Your request is approved. Please return the product to Lucky Mall promptly. Thank you.';
      case RefundStatus.returnInProgress:
        return 'The package is on its way back to Lucky Mall...';
      case RefundStatus.productReceived:
        return 'We have received the product.\n\nNew product will be sent to you within 7 business days.';
      case RefundStatus.refundCompleted:
        return 'Credits Refund completed.\nCheck "Lucky Mall Wallet" for your updated balance.';
      case RefundStatus.exchangeCompleted:
        return 'Return and Exchange completed.\nThank you for your Patience.';
      case RefundStatus.refundRejected:
        return 'Your request was declined. Contact support if needed.';
    }
  }
}

extension PurchaseTypeExtension on PurchaseType {
  String get displayName {
    switch (this) {
      case PurchaseType.appPurchase:
        return 'App Purchase';
      case PurchaseType.qrRedemption:
        return 'QR Redemption';
    }
  }
}
