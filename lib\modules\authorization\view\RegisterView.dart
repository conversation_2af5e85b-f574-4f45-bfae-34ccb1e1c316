import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:luckymall/router/auth_guard.dart';
import 'package:luckymall/modules/authorization/widgets/login_header.dart';
import 'package:luckymall/modules/authorization/widgets/phone_number_field.dart';
import 'package:luckymall/modules/authorization/widgets/verification_code_field.dart';
import 'package:luckymall/modules/authorization/widgets/password_field.dart';
import 'package:luckymall/modules/authorization/widgets/birth_date_field.dart';
import 'package:luckymall/modules/authorization/widgets/referral_code_field.dart';
import 'package:luckymall/modules/authorization/widgets/login_button.dart';

class RegisterView extends StatefulWidget {
  const RegisterView({super.key});

  @override
  State<RegisterView> createState() => _RegisterViewState();
}

class _RegisterViewState extends State<RegisterView> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _verificationController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _birthDateController = TextEditingController();
  final TextEditingController _referralController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _verificationController.dispose();
    _passwordController.dispose();
    _birthDateController.dispose();
    _referralController.dispose();
    super.dispose();
  }

  Future<void> _handleSendVerificationCode() async {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Verification code sent')));
  }

  Future<void> _handleRegister() async {
    setState(() {
      _isLoading = true;
    });
    await Future.delayed(const Duration(milliseconds: 500));
    await AuthService.loginSimple();
    if (!mounted) return;
    context.go('/blind-box/shop');
    setState(() {
      _isLoading = false;
    });
  }

  //

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFFFFFFF), Color(0xFFFCD255)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const LoginHeader(),
                const SizedBox(height: 10),

                // Phone Number Field
                PhoneNumberField(
                  controller: _phoneController,
                  hintText: 'Please enter your phone number',
                  onChanged: (value) {},
                ),
                const SizedBox(height: 20),

                // Verification Code Field
                VerificationCodeField(
                  controller: _verificationController,
                  onChanged: (value) {},
                  onSendCode: _handleSendVerificationCode,
                ),
                const SizedBox(height: 10),

                // Password Field
                PasswordField(
                  controller: _passwordController,
                  hintText: 'Please enter your password',
                  onChanged: (value) {},
                ),
                const SizedBox(height: 10),

                // Birth Date Field - Required for age verification and product access
                BirthDateField(
                  controller: _birthDateController,
                  hintText: 'Please select your birth date',
                  onChanged: (value) {},
                ),
                const SizedBox(height: 10),

                // Referral Code Field
                ReferralCodeField(
                  controller: _referralController,
                  onChanged: (value) {},
                ),

                const SizedBox.shrink(),

                const SizedBox(height: 20),

                // Register Button
                LoginButton(
                  text: 'Register',
                  onPressed: _isLoading ? null : _handleRegister,
                  isLoading: _isLoading,
                ),

                const SizedBox(height: 10),

                // Login Link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Already have an account? ',
                      style: TextStyle(color: Colors.black, fontSize: 16),
                    ),
                    GestureDetector(
                      onTap: () {
                        context.go('/login');
                      },
                      child: const Text(
                        'Log in',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
