import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import '../../blindbox/widgets/PostCard.dart';

// Sample posts with structure to match PostProfileView
final posts = [
  {
    "id": "1",
    "imageUrl":
        "https://down-my.img.susercontent.com/file/sg-11134201-7rd4j-lvmr60yl4xybd5",
    "title": "Cute dolls!!!",
    "likeCount": 230,
    "username": "<PERSON>",
    "isFavorite": true,
    "images": [
      "https://down-my.img.susercontent.com/file/sg-11134201-7rd4j-lvmr60yl4xybd5",
      "https://preview.redd.it/skintific-water-gel-ceramide-toner-first-impression-v0-r8u75rmvvs3c1.jpeg?auto=webp&s=16435fbc1a0c6db0a41b8547ffa5a129a6881130",
    ],
  },
  {
    "id": "2",
    "imageUrl":
        "https://preview.redd.it/skintific-water-gel-ceramide-toner-first-impression-v0-r8u75rmvvs3c1.jpeg?auto=webp&s=16435fbc1a0c6db0a41b8547ffa5a129a6881130",
    "title": "I've been waiting for this",
    "likeCount": 180,
    "username": "Angela",
    "isFavorite": false,
    "images": [
      "https://preview.redd.it/skintific-water-gel-ceramide-toner-first-impression-v0-r8u75rmvvs3c1.jpeg?auto=webp&s=16435fbc1a0c6db0a41b8547ffa5a129a6881130",
    ],
  },
  {
    "id": "3",
    "imageUrl":
        "https://img4.dhresource.com/webp/m/0x0/f3/albu/km/y/15/7816b33c-fca1-42fe-a411-c4c1b46b50a9.jpg",
    "title": "VENTIIII!!!",
    "likeCount": 312,
    "username": "Angela",
    "isFavorite": false,
    "images": [
      "https://img4.dhresource.com/webp/m/0x0/f3/albu/km/y/15/7816b33c-fca1-42fe-a411-c4c1b46b50a9.jpg",
      "https://cdn.shopify.com/s/files/1/0537/1177/files/NaPacks_Sling_Bag_Gear_Review_Ultralight_Fanny_Pack_Made_of_Challenge_Ultra_Backpacking_Thru-Hiking_GGG_Garage_Grown_Gear.jpg?v=1681326192",
    ],
  },
  {
    "id": "4",
    "imageUrl":
        "https://cdn.shopify.com/s/files/1/0537/1177/files/NaPacks_Sling_Bag_Gear_Review_Ultralight_Fanny_Pack_Made_of_Challenge_Ultra_Backpacking_Thru-Hiking_GGG_Garage_Grown_Gear.jpg?v=1681326192",
    "title": "Comftyyy",
    "likeCount": 90,
    "username": "Angela",
    "isFavorite": false,
    "images": [
      "https://cdn.shopify.com/s/files/1/0537/1177/files/NaPacks_Sling_Bag_Gear_Review_Ultralight_Fanny_Pack_Made_of_Challenge_Thru-Hiking_GGG_Garage_Grown_Gear.jpg?v=1681326192",
    ],
  },
  {
    "id": "5",
    "imageUrl": "https://i.ebayimg.com/images/g/upIAAOSw3sJak0DU/s-l1200.jpg",
    "title": "WORTH IT",
    "likeCount": 450,
    "username": "Angela",
    "isFavorite": false,
    "images": [
      "https://i.ebayimg.com/images/g/upIAAOSw3sJak0DU/s-l1200.jpg",
      "https://xcdn.next.co.uk/common/items/default/default/itemimages/3_4Ratio/product/lge/732297s.jpg",
    ],
  },
  {
    "id": "6",
    "imageUrl":
        "https://xcdn.next.co.uk/common/items/default/default/itemimages/3_4Ratio/product/lge/732297s.jpg",
    "title": "Nice vase for my flower",
    "likeCount": 207,
    "username": "Angela",
    "isFavorite": false,
    "images": [
      "https://xcdn.next.co.uk/common/items/default/default/itemimages/3_4Ratio/product/lge/732297s.jpg",
    ],
  },
];

class MySharingView extends StatefulWidget {
  const MySharingView({super.key});

  @override
  State<MySharingView> createState() => _MySharingViewState();
}

class _MySharingViewState extends State<MySharingView> {
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _simulateLoading();
  }

  void _simulateLoading() async {
    await Future.delayed(const Duration(milliseconds: 1500));
    isLoading = false;
    setState(() {});
  }

  // Helper method to get responsive font size (matching PostProfileViewVM)
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding (matching PostProfileViewVM)
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  // Helper method to get responsive cross axis count (matching PostProfileViewVM)
  int getResponsiveCrossAxisCount(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 2;
    } else if (screenWidth < 900) {
      return 3;
    } else {
      return 4;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Color(0xFFFFEAAB),
        elevation: 0,
        toolbarHeight: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Yellow background with profile section (matching PostProfileView)
            Stack(
              children: [
                Container(
                  width: double.infinity,
                  height: 150,
                  decoration: const BoxDecoration(
                    color: Color(0xFFFFEAAB),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                  ),
                ),
                Positioned(
                  top: 10,
                  left: 16,
                  child: GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.8),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        color: Colors.black,
                        size: getResponsiveFontSize(20, context),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Column(
                    children: [
                      SvgPicture.asset(
                        'assets/icons/profiles/EmptyProfile.svg',
                        width: 70,
                        height: 70,
                        colorFilter: const ColorFilter.mode(
                          Colors.black,
                          BlendMode.srcIn,
                        ),
                      ),
                      SizedBox(height: getResponsivePadding(8, context)),
                      Text(
                        "Angela",
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(16, context),
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: getResponsivePadding(12, context)),
                    ],
                  ),
                ),
              ],
            ),

            // Stats section (matching PostProfileView)
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: getResponsivePadding(40, context),
                vertical: getResponsivePadding(24, context),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    children: [
                      Text(
                        "1020",
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(20, context),
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: getResponsivePadding(4, context)),
                      Text(
                        "Blind Box",
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Text(
                        "10",
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(20, context),
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: getResponsivePadding(4, context)),
                      Text(
                        "Card",
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(14, context),
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Post Grid or Loading State (matching PostProfileView)
            Container(
              padding: EdgeInsets.fromLTRB(
                getResponsivePadding(12, context),
                getResponsivePadding(8, context),
                getResponsivePadding(12, context),
                getResponsivePadding(24, context),
              ),
              child: isLoading
                  ? MasonryGridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: getResponsiveCrossAxisCount(context),
                      mainAxisSpacing: getResponsivePadding(8, context),
                      crossAxisSpacing: getResponsivePadding(8, context),
                      itemCount: 6,
                      itemBuilder: (context, index) {
                        return const PostCardShimmer();
                      },
                    )
                  : posts.isEmpty
                  ? Padding(
                      padding: EdgeInsets.symmetric(
                        vertical: getResponsivePadding(40, context),
                      ),
                      child: Center(
                        child: Column(
                          children: [
                            Icon(
                              Icons.photo_library_outlined,
                              size: getResponsiveFontSize(48, context),
                              color: Colors.grey[400],
                            ),
                            SizedBox(height: getResponsivePadding(16, context)),
                            Text(
                              "No post yet 💤",
                              style: TextStyle(
                                fontSize: getResponsiveFontSize(16, context),
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : MasonryGridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: getResponsiveCrossAxisCount(context),
                      mainAxisSpacing: getResponsivePadding(8, context),
                      crossAxisSpacing: getResponsivePadding(8, context),
                      itemCount: posts.length,
                      itemBuilder: (context, index) {
                        final post = posts[index];
                        return PostCard(
                          username: post['username'] as String,
                          isFavorite: (post['isFavorite'] as bool?) ?? false,
                          imageUrl: post['imageUrl'] as String,
                          title: post['title'] as String,
                          likeCount: post['likeCount'] as int,
                          onTap: () {
                            context.push(
                              '/profile/post/${post['id']}',
                              extra: {
                                ...post,
                                'images': post['images'] ?? [post['imageUrl']],
                              },
                            );
                          },
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
