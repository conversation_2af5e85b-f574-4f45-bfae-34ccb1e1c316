import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class PointProductCard extends StatelessWidget {
  final String productName;
  final String imageUrl;
  final int pointValue;
  final double rating;
  final int claimsRemaining;
  final int currentParticipants;
  final int maxParticipants;
  final VoidCallback? onTap;

  const PointProductCard({
    super.key,
    required this.productName,
    required this.imageUrl,
    required this.pointValue,
    required this.rating,
    required this.claimsRemaining,
    required this.currentParticipants,
    required this.maxParticipants,
    this.onTap,
  });

  // Calculate progress for the participation bar
  double _calculateProgress() {
    // For lucky draw products, we want to show progress towards completion
    // Based on the API response structure:
    // - usedAmount: how many people have joined the draw
    // - totalAmount: total points required to finish the lucky draw
    // - sold: items claimed/won

    // TODO: Adjust this calculation based on your business logic
    // You need to specify:
    // 1. How many points each participant contributes
    // 2. What the progress bar should represent

    if (maxParticipants > 0) {
      // Option 1: Progress based on participants (assuming each contributes 1 point)
      // final progress = currentParticipants / maxParticipants;

      // Option 2: Progress based on points collected (assuming each contributes pointValue)
      // final pointsCollected = currentParticipants * pointValue;
      // final progress = pointsCollected / maxParticipants;

      // Option 3: Simple visual indicator (always show some progress)
      final progress = currentParticipants > 0 ? 0.3 : 0.05;

      return progress.clamp(0.0, 1.0);
    } else {
      return 0.05;
    }
  }

  // Factory constructor for creating from new API model
  factory PointProductCard.fromLuckyDrawProduct({
    required Map<String, dynamic> product,
    VoidCallback? onTap,
  }) {
    return PointProductCard(
      productName: product['productSkuName'] ?? '',
      imageUrl: product['productSkuImage'] ?? '',
      pointValue: product['totalAmount'] ?? 0,
      rating: (product['score'] ?? 0.0).toDouble(),
      claimsRemaining: product['sold'] ?? 0,
      currentParticipants: product['usedAmount'] ?? 0,
      maxParticipants: product['totalAmount'] ?? 0,
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Responsive sizing with better constraints
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Improved scaling functions with constraints
    double scaleW(double size) =>
        (size * screenWidth / 375).clamp(size * 0.8, size * 1.5);
    double scaleH(double size) =>
        (size * screenHeight / 812).clamp(size * 0.8, size * 1.5);
    double scaleText(double size) =>
        (size * screenWidth / 375).clamp(size * 0.7, size * 1.3);

    // Responsive card width calculation
    final isTablet = screenWidth > 600;
    final crossAxisCount = isTablet ? 3 : 2;
    final horizontalPadding = scaleW(16);
    final spacing = scaleW(12);
    final cardWidth =
        (screenWidth -
            (horizontalPadding * 2) -
            (spacing * (crossAxisCount - 1))) /
        crossAxisCount;

    // Ensure minimum and maximum card width
    final finalCardWidth = cardWidth.clamp(140.0, 200.0);
    // Make image square and ensure it fits within card width
    final imageSize = finalCardWidth;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: finalCardWidth,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(scaleW(12)),
          border: Border.all(
            color: Colors.grey.withOpacity(0.3),
            width: scaleW(1.5).clamp(1.0, 2.0),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              offset: Offset(0, scaleH(2)),
              blurRadius: scaleW(8).clamp(4, 12),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image (full width)
            Container(
              height: imageSize,
              width: double.infinity,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(scaleW(12)),
                  topRight: Radius.circular(scaleW(12)),
                ),
                color: Colors.grey[100],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(scaleW(12)),
                  topRight: Radius.circular(scaleW(12)),
                ),
                child: CachedNetworkImage(
                  imageUrl: imageUrl,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: imageSize,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[100],
                    child: Center(
                      child: SizedBox(
                        width: scaleW(24).clamp(20, 30),
                        height: scaleW(24).clamp(20, 30),
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Color(0xFFFFBF00),
                        ),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.grey[400],
                      size: scaleW(40).clamp(30, 50),
                    ),
                  ),
                  memCacheWidth: 400,
                  memCacheHeight: 400,
                ),
              ),
            ),

            // Product Details
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(scaleW(8).clamp(6, 12)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Product Name
                    Text(
                      productName,
                      style: TextStyle(
                        fontSize: scaleText(10).clamp(8, 14),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                        height: 1.1,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // Bottom section with points and rating
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Points and Join Now button row
                        Row(
                          children: [
                            // Points
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: scaleW(5).clamp(4, 8),
                                vertical: scaleH(2).clamp(1, 4),
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFFBF00),
                                borderRadius: BorderRadius.circular(
                                  scaleW(3).clamp(2, 5),
                                ),
                              ),
                              child: Text(
                                '$pointValue Pts',
                                style: TextStyle(
                                  fontSize: scaleText(10).clamp(8, 12),
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                            ),
                            const Spacer(),
                            // Join Now button
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: scaleW(8).clamp(6, 12),
                                vertical: scaleH(4).clamp(3, 6),
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFFBF00),
                                borderRadius: BorderRadius.circular(
                                  scaleW(4).clamp(3, 6),
                                ),
                                border: Border.all(
                                  color: const Color(0xFFFFBF00),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                'Join Now',
                                style: TextStyle(
                                  fontSize: scaleText(9).clamp(7, 11),
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: scaleH(4).clamp(2, 6)),

                        // Participation Progress Bar
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: scaleH(2).clamp(1, 3)),
                            // Progress bar
                            Container(
                              height: scaleH(3).clamp(2, 5),
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(
                                  scaleH(1.5).clamp(1, 3),
                                ),
                              ),
                              child: FractionallySizedBox(
                                alignment: Alignment.centerLeft,
                                widthFactor: _calculateProgress(),
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Color(0xFFFFBF00), // Yellow
                                        Color(0xFFFFD54F), // Light yellow
                                      ],
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                    ),
                                    borderRadius: BorderRadius.circular(
                                      scaleH(1.5).clamp(1, 3),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            // Participation status text
                            SizedBox(height: scaleH(2).clamp(1, 3)),
                            Text(
                              currentParticipants > 0
                                  ? '$currentParticipants people joined'
                                  : 'Be the first to join!',
                              style: TextStyle(
                                fontSize: scaleText(8).clamp(6, 10),
                                color: currentParticipants > 0
                                    ? Colors.green[600]
                                    : Colors.orange[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),

                            // 'Join now' button
                          ],
                        ),
                        SizedBox(height: scaleH(15).clamp(10, 20)),

                        // Rating and Claims
                        Row(
                          children: [
                            // Rating
                            Row(
                              children: [
                                Text(
                                  rating.toString(),
                                  style: TextStyle(
                                    fontSize: scaleText(10).clamp(8, 12),
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                ),
                                SizedBox(width: scaleW(1).clamp(1, 2)),
                                Icon(
                                  Icons.star,
                                  size: scaleW(11).clamp(9, 14),
                                  color: const Color(0xFFFFBF00),
                                ),
                              ],
                            ),
                            const Spacer(),

                            // Claims Remaining
                            Flexible(
                              child: Text(
                                '$claimsRemaining Claim',
                                style: TextStyle(
                                  fontSize: scaleText(10).clamp(8, 12),
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black54,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Grid widget to display multiple point product cards
class PointProductGrid extends StatelessWidget {
  final List<Map<String, dynamic>> products;
  final Function(Map<String, dynamic>)? onProductTap;
  final bool isLoadingMore;
  final bool hasMoreProducts;

  const PointProductGrid({
    super.key,
    required this.products,
    this.onProductTap,
    this.isLoadingMore = false,
    this.hasMoreProducts = true,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    print('PointProductGrid: Building grid with ${products.length} products');
    print('PointProductGrid: Products data: $products');

    // Improved scaling functions
    double scaleW(double size) =>
        (size * screenWidth / 375).clamp(size * 0.8, size * 1.5);
    double scaleH(double size) =>
        (size * screenHeight / 812).clamp(size * 0.8, size * 1.5);

    // Responsive grid configuration
    int crossAxisCount;
    double childAspectRatio;

    if (screenWidth > 900) {
      // Large tablets/desktop
      crossAxisCount = 4;
      childAspectRatio = 0.7; // was 0.85
    } else if (screenWidth > 600) {
      // Tablets
      crossAxisCount = 3;
      childAspectRatio = 0.65; // was 0.8
    } else if (screenWidth > 400) {
      // Large phones
      crossAxisCount = 2;
      childAspectRatio = 0.6; // was 0.75
    } else {
      // Small phones
      crossAxisCount = 2;
      childAspectRatio = 0.55; // was 0.7
    }

    return Column(
      children: [
        GridView.builder(
          shrinkWrap: true,
          physics: const ClampingScrollPhysics(),
          padding: EdgeInsets.all(scaleW(16).clamp(12, 20)),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: scaleW(12).clamp(8, 16),
            mainAxisSpacing: scaleH(12).clamp(8, 16),
            childAspectRatio: childAspectRatio,
          ),
          itemCount: products.length,
          itemBuilder: (context, index) {
            final product = products[index];
            print('PointProductGrid: Building product $index: $product');
            return PointProductCard(
              productName: product['productSkuName'] ?? '',
              imageUrl: product['productSkuImage'] ?? '',
              pointValue: product['totalAmount'] ?? 0,
              rating: (product['score'] ?? 0.0).toDouble(),
              claimsRemaining: product['sold'] ?? 0,
              currentParticipants: product['usedAmount'] ?? 0,
              maxParticipants: product['totalAmount'] ?? 100,
              onTap: () => onProductTap?.call(product),
            );
          },
        ),
        // Loading indicator for pagination
        if (isLoadingMore)
          Container(
            padding: EdgeInsets.symmetric(vertical: scaleH(16)),
            child: const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFFBF00)),
              ),
            ),
          ),
        // End of list indicator
        if (!hasMoreProducts && products.isNotEmpty)
          Container(
            padding: EdgeInsets.symmetric(vertical: scaleH(16)),
            child: Center(
              child: Text(
                'You\'ve reached the end of the list',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
