import 'package:flutter/material.dart';

class BlindboxVM extends ChangeNotifier {
  bool isLoading = true;
  final List<Map<String, dynamic>> posts = [
    {
      "id": "1",
      "images": [
        "https://down-my.img.susercontent.com/file/sg-11134201-7rd4j-lvmr60yl4xybd5",
      ],
      "title": "Cute dolls!!!",
      "username": "<PERSON><PERSON>",
      "likeCount": 230,
      "isFavorite": true,
    },
    {
      "id": "2",
      "images": [
        "https://preview.redd.it/skintific-water-gel-ceramide-toner-first-impression-v0-r8u75rmvvs3c1.jpeg?auto=webp&s=16435fbc1a0c6db0a41b8547ffa5a129a6881130",
      ],
      "title": "I've been waiting for this",
      "username": "<PERSON>",
      "likeCount": 180,
      "isFavorite": false,
    },
    {
      "id": "3",
      "images": [
        "https://img4.dhresource.com/webp/m/0x0/f3/albu/km/y/15/7816b33c-fca1-42fe-a411-c4c1b46b50a9.jpg",
      ],
      "title": "VENTIIII!!!",
      "username": "Maya",
      "likeCount": 312,
      "isFavorite": false,
    },
    {
      "id": "4",
      "images": [
        "https://cdn.shopify.com/s/files/1/0537/1177/files/NaPacks_Sling_Bag_Gear_Review_Ultralight_Fanny_Pack_Made_of_Challenge_Ultra_Backpacking_Thru-Hiking_GGG_Garage_Grown_Gear.jpg?v=1681326192",
      ],
      "title": "Comftyyy",
      "username": "Zane",
      "likeCount": 90,
      "isFavorite": false,
    },
    {
      "id": "5",
      "images": ["https://i.ebayimg.com/images/g/upIAAOSw3sJak0DU/s-l1200.jpg"],
      "title": "WORTH IT",
      "username": "Alina",
      "likeCount": 450,
      "isFavorite": false,
    },
    {
      "id": "6",
      "images": [
        "https://xcdn.next.co.uk/common/items/default/default/itemimages/3_4Ratio/product/lge/732297s.jpg",
      ],
      "title": "Nice vase for my flower",
      "username": "Chris",
      "likeCount": 207,
      "isFavorite": false,
    },
  ];

  BlindboxVM() {
    _simulateLoading();
  }

  void _simulateLoading() async {
    await Future.delayed(const Duration(milliseconds: 1500));
    isLoading = false;
    notifyListeners();
  }
}
