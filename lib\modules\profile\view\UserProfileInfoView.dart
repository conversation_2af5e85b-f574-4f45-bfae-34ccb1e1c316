import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../view-model/UserProfileVM.dart';
import '../model/UserInfoModel.dart';
import 'EditUserProfileView.dart';
import '../widgets/UserProfileInfoShimmer.dart';

class UserProfileInfoView extends StatelessWidget {
  const UserProfileInfoView({super.key});

  static const Color gold = Color(0xFFFFBF00);

  @override
  Widget build(BuildContext context) {
    final vm = Provider.of<UserProfileVM>(context);
    final UserInfoModel? user = vm.userInfo;

    if (user == null) {
      return const UserProfileInfoShimmer();
    }

    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(top: 60),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Picture with Floating Camera Icon
                    Center(
                      child: Stack(
                        clipBehavior: Clip.none,
                        children: [
                          _buildProfileAvatar(user),
                          Positioned(
                            right: -4,
                            bottom: -4,
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white,
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // User ID
                    Center(
                      child: Text(
                        "ID: ${user.memberCode ?? 'N/A'}",
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                    ),

                    // Card with user info
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Card(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _infoTile("Username", user.nickname ?? 'Not set'),
                            _divider(),
                            _infoTile("Gender", _getGenderText(user.gender)),
                            _divider(),
                            _infoTile(
                              "Birth Date",
                              _getBirthDateText(user.birthday),
                            ),
                            _divider(),
                            _infoTile("Phone Number", user.mobile ?? 'Not set'),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Edit Button
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: ElevatedButton(
                        onPressed: () async {
                          await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const EditUserProfileView(),
                            ),
                          );
                          // Shared VM auto-updates, no flicker
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: gold,
                          foregroundColor: Colors.black,
                          minimumSize: const Size(double.infinity, 48),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text("Edit Profile"),
                      ),
                    ),

                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),

            // 🔹 Back Button (non-functional yet)
            Positioned(
              top: 10,
              left: 10,
              child: IconButton(
                onPressed: () {
                  context.pop();
                },
                style: IconButton.styleFrom(
                  backgroundColor: Colors.white,
                  padding: const EdgeInsets.all(8),
                ),
                icon: const Icon(Icons.arrow_back, color: Colors.black),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileAvatar(UserInfoModel user) {
    if (user.avatar == null || user.avatar!.isEmpty) {
      return Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ClipOval(
          child: Transform.scale(
            scale: 1.3,
            child: SvgPicture.asset(
              'assets/icons/profiles/EmptyProfile.svg',
              fit: BoxFit.contain,
              colorFilter: const ColorFilter.mode(
                Colors.black,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      );
    } else if (user.avatar!.startsWith('http')) {
      return CircleAvatar(
        radius: 50,
        backgroundImage: NetworkImage(user.avatar!),
        onBackgroundImageError: (exception, stackTrace) {
          // Fallback to default if network image fails
        },
      );
    } else {
      return CircleAvatar(
        radius: 50,
        backgroundImage: AssetImage(user.avatar!),
      );
    }
  }

  String _getGenderText(int? gender) {
    switch (gender) {
      case 1:
        return 'Female';
      case 2:
        return 'Male';
      default:
        return 'Not set';
    }
  }

  String _getBirthDateText(DateTime? birthday) {
    if (birthday == null) {
      return 'Not set';
    }
    return '${birthday.year}-${birthday.month.toString().padLeft(2, '0')}-${birthday.day.toString().padLeft(2, '0')}';
  }

  Widget _infoTile(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(color: Colors.black87, fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _divider() {
    return Divider(
      height: 1,
      color: Colors.grey.shade300,
      indent: 16,
      endIndent: 16,
    );
  }
}
