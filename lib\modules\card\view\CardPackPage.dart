import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/CardPack/AppBar.dart';
import '../widgets/CardPack/CardGridDisplay.dart';
import '../widgets/CardPack/CategoryBottomNav.dart';
import '../widgets/CardPack/EmptyState.dart';
import '../widgets/CardPack/TopContainer.dart';
import '../model/cardPack/CardItem.dart';
import '../view-Model/cardPack/MockCards.dart';
import '../widgets/CategorySelectionSheet.dart';
import '../widgets/CardPack/RankSelectionSheet.dart';
import '../view-Model/cardPack/CardCategoryViewModel.dart';

class CardPackPage extends StatefulWidget {
  const CardPackPage({super.key});

  @override
  State<CardPackPage> createState() => _CardPackPageState();
}

class _CardPackPageState extends State<CardPackPage> {
  bool showArchived = false;
  List<CardItem> allCards = allCardCatalog;
  String currentMainCategory = 'fantasy';
  String? selectedRank; // null means all ranks
  int selectedTab = 0;
  bool isLoading = true;
  //Declare Card Category  view model
  late final CardCategoryViewModel categoryVM;

  int get totalCollectedQuantity =>
      allCards.where((c) => c.isOwned).fold(0, (sum, c) => sum + c.quantity);

  void _onToggleArchived(bool value) {
    setState(() {
      showArchived = value;
    });
  }

  void _onRankSelected(String? rank) {
    setState(() {
      selectedRank = rank;
    });
  }

  // Add this method to handle category selection
  void onCategorySelected(String category, String series) {
    setState(() {
      currentMainCategory = series;
      categoryVM.selectCategory(category);
    });
  }

  @override
  void initState() {
    super.initState();

    // Initialize CategoryViewModel
    categoryVM = CardCategoryViewModel();
    categoryVM.addListener(() {
      setState(() {}); // rebuild on changes
    });
    
    // Load initial data
    _initializeData();
  }

  // Add this method to handle async initialization
  Future<void> _initializeData() async {
    try {
      await categoryVM.loadSeriesData(); // Changed from loadSeries to loadSeriesData
      categoryVM.selectSeries(currentMainCategory);
      
      // Simulate loading delay
      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error initializing data: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    categoryVM.dispose(); // Clean up
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const PreferredSize(
        preferredSize: Size.fromHeight(70),
        child: CardPackAppBar(),
      ),
      body: SafeArea(
        child: Column(
          children: [
            const SizedBox(height: 8),
            TopContainer(
              onPhysicalCardsTap: () => context.push('/card/physical-card'),
              onHistoryTap: () => context.push('/card/history-card'),
              onSelectCategory: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (_) => CategorySelectionSheet(
                    onCategorySelected: onCategorySelected,
                    showSeriesSelection: true,
                    initialSeries: currentMainCategory,
                  ),
                );
              },
              cardCount: totalCollectedQuantity,
              showArchived: showArchived,
              onToggleArchived: _onToggleArchived,
            ),
            const SizedBox(height: 8),
            Expanded(
              child: visibleCards.isEmpty
                  ? const Center(child: EmptyState())
                  : CardGridDisplay(
                      cards: visibleCards,
                      category: categoryVM.selectedCategory,
                      showArchived: showArchived,
                      isLoading: isLoading,
                    ),
            ),
          ],
        ),
      ),
       bottomNavigationBar: CardCategoryBottomNav(
        selectedCategory: categoryVM.selectedCategory,
        categories: categoryVM.categories,
        onCategorySelected: (key) => categoryVM.selectCategory(key),
      ),

      // New floating filter button
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(right: 0, bottom: 12),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(50),
            splashColor: const Color.fromARGB(157, 255, 172, 64),
            highlightColor: const Color.fromARGB(174, 255, 153, 0),
            onTap: () {
              showModalBottomSheet(
                context: context,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                ),
                builder: (context) => RankSelectionSheet(
                  selectedRank: selectedRank,
                  onRankSelected: (rank) {
                    Navigator.pop(context);
                    _onRankSelected(rank);
                  },
                ),
              );
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 250),
              height: 62,
              width: 62,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: const LinearGradient(
                  colors: [Color(0xFFFFC107), Color(0xFFFF5722)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.orange.withOpacity(0.6),
                    blurRadius: 16,
                    spreadRadius: 2,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: const Icon(
                Icons.star,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<CardItem> get visibleCards {
    var filtered = allCards.where((c) => c.category == categoryVM.selectedCategory);

    if (selectedRank != null) {
      filtered = filtered.where((c) => c.rank == selectedRank);
    }

    return showArchived
        ? filtered.toList()
        : filtered.where((c) => c.isOwned).toList();
  }

}



