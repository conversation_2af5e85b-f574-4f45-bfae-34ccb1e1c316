// lib/modules/card/view-Model/scanCode/RevealsScanCardViewModel.dart
import 'dart:math';
import 'package:flutter/material.dart';
import '../../model/reveal/RevealCard.dart';

class RevealsScanCardViewModel extends ChangeNotifier {
  final List<RevealCard> cards = [];

  /// Mock fetch (replace with API later). Ensures at least minCount cards.
  Future<void> fetchCards({int minCount = 5}) async {
    await Future.delayed(const Duration(milliseconds: 700));

    final mockPool = <RevealCard>[
      RevealCard(
        id: 'card_1',
        name: 'Lovely Wizard',
        category: 'Wizard',
        series: 'Fantasy',
        imagePath: 'assets/images/card/card_1.png',
      ),
      RevealCard(
        id: 'card_2',
        name: 'Stone Golem',
        category: 'Golem',
        series: 'Ancient',
        imagePath: 'assets/images/card/card_2.png',
      ),
      RevealCard(
        id: 'card_3',
        name: '<PERSON> Ranger',
        category: 'Ranger',
        series: 'Aerial',
        imagePath: 'assets/images/card/card_3.png',
      ),
      RevealCard(
        id: 'card_4',
        name: 'Flame Sprite',
        category: 'Sprite',
        series: 'Elemental',
        imagePath: 'assets/images/card/card_4.png',
      ),
      RevealCard(
        id: 'card_5',
        name: 'Shadow Assassin',
        category: 'Assassin',
        series: 'Nightfall',
        imagePath: 'assets/images/card/card_5.png',
      ),
      // add more if you want
    ];

    final rnd = Random();
    final shuffled = List<RevealCard>.from(mockPool)..shuffle(rnd);

    cards
      ..clear()
      ..addAll(shuffled.take(minCount));

    notifyListeners();
  }
}
