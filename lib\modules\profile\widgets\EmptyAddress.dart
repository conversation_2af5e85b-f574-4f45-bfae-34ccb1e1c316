import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class EmptyAddressBody extends StatelessWidget {
  const EmptyAddressBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            'assets/icons/Empty Address.svg',
            width: 120,
            height: 120,
            color: const Color(0xFF939598),
          ),
          const SizedBox(height: 16),
          Text(
            'No address added yet',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 15,
            ),
          ),
        ],
      ),
    );
  }
}
