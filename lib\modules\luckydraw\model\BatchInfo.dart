enum BatchStatus { active, closed, completed, waiting }

class BatchInfo {
  final int batchNumber;
  final int id;
  final int productSpuId;
  final int productSkuId;
  final String productSkuName;
  final String productSkuImage;
  final String drawDate;
  final String drawTime;
  final int countdownSeconds;
  final BatchStatus status;
  final int usedAmount;
  final int totalAmount;
  final int totalShares;
  final double score;
  final int sold;

  // Legacy getters for backward compatibility
  String get productId => id.toString();
  String get productName => productSkuName;
  String get imageUrl => productSkuImage;
  int get totalParticipants => usedAmount;
  int get maxParticipants => totalAmount;
  int get pointValue => totalAmount;
  String get category => 'Batch Product';

  const BatchInfo({
    required this.batchNumber,
    required this.id,
    required this.productSpuId,
    required this.productSkuId,
    required this.productSkuName,
    required this.productSkuImage,
    required this.drawDate,
    required this.drawTime,
    required this.countdownSeconds,
    required this.status,
    required this.usedAmount,
    required this.totalAmount,
    required this.totalShares,
    required this.score,
    required this.sold,
  });

  factory BatchInfo.fromJson(Map<String, dynamic> json) {
    return BatchInfo(
      batchNumber: (json['batchNumber'] as num?)?.toInt() ?? 0,
      id: (json['id'] as num?)?.toInt() ?? 0,
      productSpuId: (json['productSpuId'] as num?)?.toInt() ?? 0,
      productSkuId: (json['productSkuId'] as num?)?.toInt() ?? 0,
      productSkuName: json['productSkuName']?.toString() ?? '',
      productSkuImage: json['productSkuImage']?.toString() ?? '',
      drawDate: json['drawDate']?.toString() ?? '',
      drawTime: json['drawTime']?.toString() ?? '',
      countdownSeconds: (json['countdownSeconds'] as num?)?.toInt() ?? 0,
      status: _parseStatus(json['status']?.toString()),
      usedAmount: (json['usedAmount'] as num?)?.toInt() ?? 0,
      totalAmount: (json['totalAmount'] as num?)?.toInt() ?? 0,
      totalShares: (json['totalShares'] as num?)?.toInt() ?? 0,
      score: (json['score'] as num?)?.toDouble() ?? 0.0,
      sold: (json['sold'] as num?)?.toInt() ?? 0,
    );
  }

  static BatchStatus _parseStatus(String? value) {
    switch (value) {
      case 'active':
        return BatchStatus.active;
      case 'closed':
        return BatchStatus.closed;
      case 'completed':
        return BatchStatus.completed;
      case 'waiting':
        return BatchStatus.waiting;
      default:
        return BatchStatus.waiting;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'batchNumber': batchNumber,
      'id': id,
      'productSpuId': productSpuId,
      'productSkuId': productSkuId,
      'productSkuName': productSkuName,
      'productSkuImage': productSkuImage,
      'drawDate': drawDate,
      'drawTime': drawTime,
      'countdownSeconds': countdownSeconds,
      'status': status.name,
      'usedAmount': usedAmount,
      'totalAmount': totalAmount,
      'totalShares': totalShares,
      'score': score,
      'sold': sold,
    };
  }

  BatchInfo copyWith({
    int? batchNumber,
    int? id,
    int? productSpuId,
    int? productSkuId,
    String? productSkuName,
    String? productSkuImage,
    String? drawDate,
    String? drawTime,
    int? countdownSeconds,
    BatchStatus? status,
    int? usedAmount,
    int? totalAmount,
    int? totalShares,
    double? score,
    int? sold,
  }) {
    return BatchInfo(
      batchNumber: batchNumber ?? this.batchNumber,
      id: id ?? this.id,
      productSpuId: productSpuId ?? this.productSpuId,
      productSkuId: productSkuId ?? this.productSkuId,
      productSkuName: productSkuName ?? this.productSkuName,
      productSkuImage: productSkuImage ?? this.productSkuImage,
      drawDate: drawDate ?? this.drawDate,
      drawTime: drawTime ?? this.drawTime,
      countdownSeconds: countdownSeconds ?? this.countdownSeconds,
      status: status ?? this.status,
      usedAmount: usedAmount ?? this.usedAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      totalShares: totalShares ?? this.totalShares,
      score: score ?? this.score,
      sold: sold ?? this.sold,
    );
  }

  double get participationPercentage {
    if (totalAmount == 0) return 0.0;
    return (usedAmount / totalAmount * 100).clamp(0.0, 100.0);
  }

  bool get isFull => usedAmount >= totalAmount;

  @override
  String toString() {
    return 'BatchInfo(batchNumber: $batchNumber, productSkuName: $productSkuName, status: $status, participants: $usedAmount/$totalAmount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BatchInfo &&
        other.batchNumber == batchNumber &&
        other.id == id;
  }

  @override
  int get hashCode => batchNumber.hashCode ^ id.hashCode;
}
