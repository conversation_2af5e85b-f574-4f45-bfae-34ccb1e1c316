class RevealedCard {
  final String id;
  final String name;
  final String category;
  final String series;
  final String imagePath;

  RevealedCard({
    required this.id,
    required this.name,
    required this.category,
    required this.series,
    required this.imagePath,
  });

  // Optional: For mapping if data is passed as JSON or Map
  factory RevealedCard.fromMap(Map<String, dynamic> map) {
    return RevealedCard(
      id: map['id'],
      name: map['name'],
      category: map['category'],
      series: map['series'],
      imagePath: map['imagePath'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'series': series,
      'imagePath': imagePath,
    };
  }
}
