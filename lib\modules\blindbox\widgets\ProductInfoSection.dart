import 'package:flutter/material.dart';

class ProductInfoSection extends StatelessWidget {
  final String price;
  final int soldCount;
  final String? pointsOffer;
  final String title;
  final String? variation;
  final bool isFavorite;
  final VoidCallback onFavoritePressed;

  const ProductInfoSection({
    super.key,
    required this.price,
    required this.soldCount,
    this.pointsOffer,
    required this.title,
    this.variation,
    required this.isFavorite,
    required this.onFavoritePressed,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    String displayPrice = price.trim().startsWith('RM')
        ? price.trim()
        : 'RM $price';

    return Padding(
      padding: EdgeInsets.fromLTRB(
        getResponsivePadding(16, context),
        getResponsivePadding(12, context), // Reduced from 20
        getResponsivePadding(16, context),
        getResponsivePadding(8, context), // Reduced from 12
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Price and favorite with sold count
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                flex: 0,
                child: Text(
                  displayPrice,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(28, context),
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Spacer(),
              // Favorite button and sold count
              Row(
                children: [
                  IconButton(
                    onPressed: onFavoritePressed,
                    icon: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: isFavorite ? Colors.red : Colors.grey,
                      size: getResponsiveFontSize(24, context),
                    ),
                  ),
                  const SizedBox(width: 4), // Reduced from 8
                  Text(
                    '$soldCount sold',
                    style: TextStyle(
                      fontSize: getResponsiveFontSize(14, context),
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: getResponsiveFontSize(4, context)), // Reduced from 8
          // Points offer
          if (pointsOffer != null) ...[
            Row(
              children: [
                Text(
                  pointsOffer!,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    color: Colors.orange[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: getResponsiveFontSize(8, context),
            ), // Reduced from 16
          ],
          // Title
          Text(
            title,
            style: TextStyle(
              fontSize: getResponsiveFontSize(18, context),
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          if (variation != null) ...[
            SizedBox(
              height: getResponsiveFontSize(4, context),
            ), // Reduced from 8
            // Variation
            Text(
              'Variation: $variation',
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
