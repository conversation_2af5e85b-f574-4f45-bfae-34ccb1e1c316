class MegaPrize {
  final int id;
  final int productSpuId;
  final int productSkuId;
  final String productSkuName;
  final String productSkuImage;
  final int usedAmount;
  final int totalAmount;

  MegaPrize({
    required this.id,
    required this.productSpuId,
    required this.productSkuId,
    required this.productSkuName,
    required this.productSkuImage,
    required this.usedAmount,
    required this.totalAmount,
  });

  factory MegaPrize.fromJson(Map<String, dynamic> json) {
    return MegaPrize(
      id: json['id'] ?? 0,
      productSpuId: json['productSpuId'] ?? 0,
      productSkuId: json['productSkuId'] ?? 0,
      productSkuName: json['productSkuName'] ?? '',
      productSkuImage: json['productSkuImage'] ?? '',
      usedAmount: json['usedAmount'] ?? 0,
      totalAmount: json['totalAmount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productSpuId': productSpuId,
      'productSkuId': productSkuId,
      'productSkuName': productSkuName,
      'productSkuImage': productSkuImage,
      'usedAmount': usedAmount,
      'totalAmount': totalAmount,
    };
  }

  @override
  String toString() {
    return 'MegaPrize(id: $id, name: $productSkuName, totalAmount: $totalAmount)';
  }
}
