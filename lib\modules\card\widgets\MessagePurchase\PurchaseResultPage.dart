import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../model/purchaseCard/PurchaseCardOrder.dart';

class PurchaseResultPage extends StatelessWidget {
  final Map<String, dynamic> resultData;
  final PurchaseCardOrder order;

  const PurchaseResultPage({
    super.key,
    required this.resultData,
    required this.order,
  });

  bool get isSuccess => resultData['status'] == 'success';

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 500),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    isSuccess
                        ? 'assets/icons/checkOutCard/OrderSuccessful.svg'
                        : 'assets/icons/checkOutCard/PaymentFailed.svg',
                    height: isSmallScreen ? 140 : 200,
                    fit: BoxFit.contain,
                    colorFilter: ColorFilter.mode(
                      isSuccess ? Colors.green : Colors.red,
                      BlendMode.srcIn,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ShaderMask(
                    shaderCallback: (bounds) {
                      return LinearGradient(
                        colors: isSuccess
                            ? [Colors.green, Colors.lightGreen]
                            : [Colors.red, Colors.deepOrange],
                      ).createShader(bounds);
                    },
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        isSuccess ? 'Purchase Card Success!' : 'Purchase Failed',
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  if (!isSuccess) ...[
                    const SizedBox(height: 12),
                    const Text(
                      'Please try again\nor use a different payment method',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                  ],
                  const SizedBox(height: 32),

                  // Buttons Section
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isSuccess ? Colors.amber : Colors.red,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        onPressed: () {
                          if (isSuccess) {
                            context.pushReplacement('/card/reveal-animation', extra: order);
                          } else {
                            context.pushReplacement('/card/checkout');
                          }
                        },
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            isSuccess ? 'Reveal Card' : 'Pay Again',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      OutlinedButton(
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                          side: const BorderSide(color: Colors.black26),
                        ),
                        onPressed: () {
                          context.go('/card');
                        },
                        child: const FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            'Back to Main Page',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
