import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/CheckoutVM.dart';

class DeliveryTypeSelector extends StatelessWidget {
  const DeliveryTypeSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CheckoutVM>(
      builder: (context, viewModel, child) {
        if (!viewModel.showDeliveryOption) {
          return const SizedBox.shrink();
        }

        return Container(
          width: double.infinity,
          decoration: const BoxDecoration(color: Colors.white),
          child: Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => viewModel.setDeliveryType(DeliveryType.delivery),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    decoration: BoxDecoration(
                      color: viewModel.deliveryType == DeliveryType.delivery
                          ? const Color(0xFFFFB800)
                          : Colors.grey[100],
                      border: viewModel.deliveryType == DeliveryType.delivery
                          ? const Border(
                              bottom: BorderSide(
                                color: Color(0xFFFFB800),
                                width: 3,
                              ),
                            )
                          : null,
                    ),
                    child: Center(
                      child: Text(
                        'Delivery',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: viewModel.deliveryType == DeliveryType.delivery
                              ? Colors.black
                              : Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () => viewModel.setDeliveryType(DeliveryType.pickup),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    decoration: BoxDecoration(
                      color: viewModel.deliveryType == DeliveryType.pickup
                          ? const Color(0xFFFFB800)
                          : Colors.grey[100],
                      border: viewModel.deliveryType == DeliveryType.pickup
                          ? const Border(
                              bottom: BorderSide(
                                color: Color(0xFFFFB800),
                                width: 3,
                              ),
                            )
                          : null,
                    ),
                    child: Center(
                      child: Text(
                        'Pickup',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: viewModel.deliveryType == DeliveryType.pickup
                              ? Colors.black
                              : Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
