import 'package:flutter/material.dart';
import '../../../data/model/ShopDetailModel.dart';
import '../view-model/ShopDetailViewModel.dart';

class AddToCartSheet extends StatefulWidget {
  final ShopDetailModel product;
  final ShopDetailViewModel? viewModel; // Add this to access favorite functionality
  final String context;

  const AddToCartSheet({
    super.key, 
    required this.product,
    this.viewModel,
    required this.context,
  });

  @override
  State<AddToCartSheet> createState() => _AddToCartSheetState();
}

class _AddToCartSheetState extends State<AddToCartSheet> {
  int quantity = 1;
  ProductSku? selectedSku;
  Map<String, String> selectedProperties = {};

  @override
  void initState() {
    super.initState();
    // Select first SKU by default
    if (widget.product.productSkus.isNotEmpty) {
      selectedSku = widget.product.productSkus.first;
      // Initialize selected properties with first option for each property
      _initializeProperties();
    }
  }

  void _initializeProperties() {
    selectedProperties.clear();
    if (selectedSku?.properties.isNotEmpty == true) {
      for (var property in selectedSku!.properties) {
        selectedProperties[property.propertyKey] = property.propertyValue;
      }
    }
  }

  // Group variations method
  Map<String, List<String>> getVariationGroups() {
    final Map<String, Set<String>> groups = {};
    
    for (var sku in widget.product.productSkus) {
      if (sku.properties.isEmpty) {
        groups[''] = groups[''] ?? <String>{};
        groups['']!.add(sku.productSkuName);
      } else {
        for (var property in sku.properties) {
          groups[property.propertyKey] = groups[property.propertyKey] ?? <String>{};
          groups[property.propertyKey]!.add(property.propertyValue);
        }
      }
    }
    
    return groups.map((key, value) => MapEntry(key, value.toList()));
  }

  // Select variation method
  void selectVariation(String propertyKey, String propertyValue) {
    setState(() {
      selectedProperties[propertyKey] = propertyValue;
    });
    _updateSelectedSku();
  }

  // Find SKU based on selected properties
  void _updateSelectedSku() {
    for (var sku in widget.product.productSkus) {
      bool matches = true;
      
      for (var entry in selectedProperties.entries) {
        bool hasProperty = sku.properties.any(
          (prop) => prop.propertyKey == entry.key && prop.propertyValue == entry.value
        );
        if (!hasProperty) {
          matches = false;
          break;
        }
      }
      
      if (matches) {
        setState(() {
          selectedSku = sku;
        });
        break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final product = widget.product;
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top section with close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const SizedBox(width: 24), // Spacer for center alignment
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Close button
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: const BoxDecoration(
                      color: Colors.black,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Product image and price section with favorite heart
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product image
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey[100],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      product.productImage,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[200],
                          child: const Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: 40,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                
                // Price and stock section with favorite heart
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Price and favorite heart row
                      Row(
                        children: [
                          // Price
                          Expanded(
                            child: Text(
                              'RM ${(selectedSku?.salePrice ?? 0.0).toStringAsFixed(2)}',
                              style: const TextStyle(
                                color: Color(0xFFE53E3E),
                                fontWeight: FontWeight.bold,
                                fontSize: 24,
                              ),
                            ),
                          ),
                          // Favorite heart
                          GestureDetector(
                            onTap: () {
                              if (widget.viewModel != null) {
                                widget.viewModel!.toggleFavorite();
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                widget.viewModel?.isCollect == true 
                                    ? Icons.favorite 
                                    : Icons.favorite_border,
                                color: widget.viewModel?.isCollect == true 
                                    ? Colors.red 
                                    : Colors.grey,
                                size: 20,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      // Stock
                      Text(
                        'Stock: ${selectedSku?.stock ?? 0}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Variation sections
            ...getVariationGroups().entries.map((entry) {
              final propertyKey = entry.key;
              final propertyValues = entry.value;
              
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Property title
                  Text(
                    propertyKey.isEmpty ? 'Options' : propertyKey,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  // Options
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: propertyValues.map((value) {
                      final isSelected = selectedProperties[propertyKey] == value;
                      
                      return GestureDetector(
                        onTap: () => selectVariation(propertyKey, value),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                              color: isSelected 
                                  ? const Color.fromRGBO(255, 199, 31, 1)
                                  : Colors.grey[300]!,
                              width: isSelected ? 2 : 1,
                            ),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            value,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                              color: isSelected ? const Color.fromRGBO(255, 199, 31, 1) : Colors.black,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 20),
                ],
              );
            }).toList(),
            
            // Quantity section
            const Text(
              'Quantity',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 12),
            
            // Quantity controls
            Row(
              children: [
                // Decrease button
                GestureDetector(
                  onTap: quantity > 1 ? () => setState(() => quantity--) : null,
                  child: Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: quantity > 1 ? Color.fromRGBO(255, 199, 31, 1) : Colors.grey[50],
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Icon(
                      Icons.remove,
                      color: quantity > 1 ? Colors.white : Colors.grey[400],
                      size: 20,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Quantity display - Expanded to fill available space
                Expanded(
                  child: Container(
                    height: 36,
                    decoration: BoxDecoration(
                      color: const Color.fromRGBO(255, 199, 31, 0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: const Color.fromRGBO(255, 199, 31, 1)),
                    ),
                    child: Center(
                      child: Text(
                        '$quantity',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Increase button
                GestureDetector(
                  onTap: (selectedSku?.stock ?? 0) > quantity 
                      ? () => setState(() => quantity++) 
                      : null,
                  child: Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: (selectedSku?.stock ?? 0) > quantity 
                          ? Color.fromRGBO(255, 199, 31, 1) 
                          : Colors.grey[200],
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.add,
                      color: (selectedSku?.stock ?? 0) > quantity 
                          ? Colors.white 
                          : Colors.grey[400],
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),
            
            // Add to Cart button (changed from Buy Now)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color.fromRGBO(255, 199, 31, 1),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                onPressed: selectedSku != null && (selectedSku!.stock > 0)
                    ? () {
                        Navigator.pop(context);
                        
                        // Conditional actions based on context
                        if (widget.context == "Cart") {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Added ${selectedSku!.productSkuName} (Qty: $quantity) to cart'
                              ),
                              backgroundColor: Colors.green,
                            ),
                          );
                        } else if (widget.context == "Buy") {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Proceeding to checkout with ${selectedSku!.productSkuName} (Qty: $quantity)'
                              ),
                              backgroundColor: Colors.green,
                            ),
                          );
                        }
                      }
                    : null,
                child: Text(
                  selectedSku?.stock == 0 
                      ? 'Out of Stock' 
                      : widget.context == "Cart" 
                          ? 'Add to Cart'  // Show "Add to Cart" when context is "Cart"
                          : 'Buy Now',     // Show "Buy Now" when context is "Buy"
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            // Add bottom padding for safe area
            SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
          ],
        ),
      ),
    );
  }
}