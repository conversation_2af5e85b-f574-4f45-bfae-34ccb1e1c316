class CategoryItem {
  final String key;
  final String icon;        // Inactive icon path
  final String activeIcon;  // Active icon path
  final String desc;

  CategoryItem({
    required this.key,
    required this.icon,
    required this.activeIcon,
    required this.desc,
  });

  factory CategoryItem.fromJson(Map<String, dynamic> json) {
    return CategoryItem(
      key: json['key'],
      icon: json['icon'],
      activeIcon: json['activeIcon'],
      desc: json['desc'],
    );
  }
}
