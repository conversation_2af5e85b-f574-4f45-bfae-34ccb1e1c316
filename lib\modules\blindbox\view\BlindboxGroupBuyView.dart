import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/BlindboxGroupBuyVM.dart';
import '../widgets/CountdownTimer.dart';
import '../widgets/QRCodeSection.dart';
import '../widgets/MembersDisplay.dart';
import '../widgets/GroupBuySuccessContent.dart';

class BlindboxGroupBuyView extends StatelessWidget {
  const BlindboxGroupBuyView({super.key});

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => BlindboxGroupBuyVM(),
      child: Consumer<BlindboxGroupBuyVM>(
        builder: (context, vm, _) {
          return Scaffold(
            backgroundColor: const Color(0xFFFFF7E6), // Light cream background
            appBar: AppBar(
              backgroundColor: const Color(0xFFFFF7E6),
              elevation: 0,
              automaticallyImplyLeading: false,
              actions: [
                IconButton(
                  icon: Icon(
                    Icons.close,
                    color: Colors.black87,
                    size: getResponsiveFontSize(24, context),
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            body: Stack(
              children: [
                // Background decorative elements
                Positioned.fill(child: _buildBackgroundElements(context)),

                // Main content with white background
                Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: getResponsivePadding(16, context),
                    vertical: getResponsivePadding(8, context),
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(
                      horizontal: getResponsivePadding(24, context),
                      vertical: getResponsivePadding(16, context),
                    ),
                    child: _buildContent(context, vm),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context, BlindboxGroupBuyVM vm) {
    switch (vm.currentState) {
      case GroupBuyState.created:
        return _buildCreatedState(context, vm);
      case GroupBuyState.formed:
        return _buildFormedState(context, vm);
      case GroupBuyState.won:
        return _buildWonState(context, vm);
    }
  }

  Widget _buildCreatedState(BuildContext context, BlindboxGroupBuyVM vm) {
    return Column(
      children: [
        // Title
        Text(
          'Your Lucky Group Created',
          style: TextStyle(
            fontSize: getResponsiveFontSize(24, context),
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          'Successfully !',
          style: TextStyle(
            fontSize: getResponsiveFontSize(24, context),
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: getResponsivePadding(16, context)),

        // Minimum members needed
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              fontSize: getResponsiveFontSize(16, context),
              color: Colors.black87,
            ),
            children: [
              const TextSpan(text: 'At least '),
              TextSpan(
                text: '${vm.minimumMembers - vm.currentMembers} more members',
                style: TextStyle(
                  color: const Color(0xFFE53E3E),
                  fontWeight: FontWeight.bold,
                ),
              ),
              const TextSpan(text: ' needed'),
            ],
          ),
        ),

        SizedBox(height: getResponsivePadding(8, context)),

        // Invite message
        Text(
          'INVITE NOW BEFORE TIMES UP !',
          style: TextStyle(
            fontSize: getResponsiveFontSize(16, context),
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: getResponsivePadding(16, context)),

        // Countdown timer
        CountdownTimer(timeString: vm.formattedTime),

        SizedBox(height: getResponsivePadding(16, context)),

        // Product info
        Text(
          'Product : ${vm.productName}',
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            color: Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: getResponsivePadding(20, context)),

        // QR Code section
        QRCodeSection(qrCodeUrl: vm.qrCodeUrl, inviteLink: vm.inviteLink),

        SizedBox(height: getResponsivePadding(20, context)),

        // Members display
        MembersDisplay(
          memberAvatars: vm.memberAvatars,
          totalMembers: vm.currentMembers,
        ),

        SizedBox(height: getResponsivePadding(20, context)),

        // Status message
        Text(
          'Waiting for more friend to join and pay.',
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            color: Colors.black54,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: getResponsivePadding(20, context)),

        // Test button (for development)
        if (vm.currentMembers < vm.minimumMembers)
          ElevatedButton(
            onPressed: vm.addMember,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Add Member (Test)',
              style: TextStyle(fontSize: getResponsiveFontSize(14, context)),
            ),
          ),

        SizedBox(height: getResponsivePadding(40, context)),
      ],
    );
  }

  Widget _buildFormedState(BuildContext context, BlindboxGroupBuyVM vm) {
    return Column(
      children: [
        // Title
        Text(
          'Your Lucky Group Formed',
          style: TextStyle(
            fontSize: getResponsiveFontSize(24, context),
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          'Successfully !',
          style: TextStyle(
            fontSize: getResponsiveFontSize(24, context),
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: getResponsivePadding(16, context)),

        // Minimum reached message
        Text(
          "You've reached the minimum required.",
          style: TextStyle(
            fontSize: getResponsiveFontSize(16, context),
            color: Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          'You can still invite more to increase the excitement!',
          style: TextStyle(
            fontSize: getResponsiveFontSize(16, context),
            color: Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: getResponsivePadding(8, context)),

        // Invite message
        Text(
          'INVITE NOW BEFORE TIMES UP !',
          style: TextStyle(
            fontSize: getResponsiveFontSize(16, context),
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: getResponsivePadding(16, context)),

        // Countdown timer
        CountdownTimer(timeString: vm.formattedTime),

        SizedBox(height: getResponsivePadding(16, context)),

        // Product info
        Text(
          'Product : ${vm.productName}',
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            color: Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: getResponsivePadding(20, context)),

        // QR Code section
        QRCodeSection(qrCodeUrl: vm.qrCodeUrl, inviteLink: vm.inviteLink),

        SizedBox(height: getResponsivePadding(20, context)),

        // Members display
        MembersDisplay(
          memberAvatars: vm.memberAvatars,
          totalMembers: vm.currentMembers,
        ),

        SizedBox(height: getResponsivePadding(20, context)),

        // Status message
        Text(
          'Waiting for more friend to join and pay.',
          style: TextStyle(
            fontSize: getResponsiveFontSize(14, context),
            color: Colors.black54,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: getResponsivePadding(20, context)),

        // Reveal Now button
        Container(
          width: double.infinity,
          margin: EdgeInsets.symmetric(
            horizontal: getResponsivePadding(40, context),
          ),
          child: ElevatedButton(
            onPressed: vm.revealResult,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFCD255),
              foregroundColor: Colors.black,
              padding: EdgeInsets.symmetric(
                vertical: getResponsivePadding(16, context),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
            child: Text(
              'Reveal Now',
              style: TextStyle(
                fontSize: getResponsiveFontSize(18, context),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),

        SizedBox(height: getResponsivePadding(40, context)),
      ],
    );
  }

  Widget _buildWonState(BuildContext context, BlindboxGroupBuyVM vm) {
    return Column(
      children: [
        // Title
        Text(
          "It's your lucky day !",
          style: TextStyle(
            fontSize: getResponsiveFontSize(24, context),
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          'Congratulations, you get the product!',
          style: TextStyle(
            fontSize: getResponsiveFontSize(18, context),
            color: Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: getResponsivePadding(20, context)),

        // Success content
        GroupBuySuccessContent(
          groupId: vm.groupId,
          productName: vm.productName,
          productImageUrl: vm.productImage,
          membersInvited: vm.maxMembers,
          rewardAmount: vm.rewardAmount,
          onMyOrderPressed: () {
            // Handle my order action
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Navigating to My Orders...')),
            );
          },
          onViewWalletPressed: () {
            // Handle view wallet action
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Navigating to Wallet...')),
            );
          },
        ),

        SizedBox(height: getResponsivePadding(40, context)),
      ],
    );
  }

  Widget _buildBackgroundElements(BuildContext context) {
    return Stack(
      children: [
        // Confetti-like decorative elements
        Positioned(
          top: getResponsivePadding(60, context),
          left: getResponsivePadding(20, context),
          child: _buildConfetti(context, Colors.yellow, 8),
        ),
        Positioned(
          top: getResponsivePadding(100, context),
          right: getResponsivePadding(30, context),
          child: _buildConfetti(context, Colors.orange, 6),
        ),
        Positioned(
          bottom: getResponsivePadding(120, context),
          left: getResponsivePadding(40, context),
          child: _buildConfetti(context, Colors.yellow, 10),
        ),
        Positioned(
          bottom: getResponsivePadding(80, context),
          right: getResponsivePadding(20, context),
          child: _buildConfetti(context, Colors.orange, 7),
        ),

        // Bottom celebration illustration
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: SizedBox(
            height: getResponsivePadding(120, context),
            child: Stack(
              children: [
                // Left celebration figure
                Positioned(
                  bottom: 0,
                  left: getResponsivePadding(20, context),
                  child: _buildDetailedCelebrationFigure(
                    context,
                    Colors.orange,
                    true,
                  ),
                ),

                // Center trophy/reward
                Positioned(
                  bottom: getResponsivePadding(10, context),
                  left: 0,
                  right: 0,
                  child: Center(child: _buildLargeTrophy(context)),
                ),

                // Right celebration figure
                Positioned(
                  bottom: 0,
                  right: getResponsivePadding(20, context),
                  child: _buildDetailedCelebrationFigure(
                    context,
                    Colors.yellow,
                    false,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConfetti(BuildContext context, Color color, double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.7),
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildDetailedCelebrationFigure(
    BuildContext context,
    Color color,
    bool isLeft,
  ) {
    return SizedBox(
      width: getResponsiveFontSize(60, context),
      height: getResponsiveFontSize(80, context),
      child: Stack(
        children: [
          // Body
          Positioned(
            bottom: 0,
            left: getResponsiveFontSize(20, context),
            child: Container(
              width: getResponsiveFontSize(20, context),
              height: getResponsiveFontSize(40, context),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
          // Head
          Positioned(
            bottom: getResponsiveFontSize(35, context),
            left: getResponsiveFontSize(17, context),
            child: Container(
              width: getResponsiveFontSize(26, context),
              height: getResponsiveFontSize(26, context),
              decoration: BoxDecoration(
                color: Colors.brown[300],
                shape: BoxShape.circle,
              ),
            ),
          ),
          // Arms (celebrating)
          Positioned(
            bottom: getResponsiveFontSize(25, context),
            left: isLeft
                ? getResponsiveFontSize(5, context)
                : getResponsiveFontSize(35, context),
            child: Container(
              width: getResponsiveFontSize(15, context),
              height: getResponsiveFontSize(15, context),
              decoration: BoxDecoration(
                color: Colors.brown[300],
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLargeTrophy(BuildContext context) {
    return Container(
      width: getResponsiveFontSize(70, context),
      height: getResponsiveFontSize(70, context),
      decoration: BoxDecoration(
        color: const Color(0xFFFCD255),
        borderRadius: BorderRadius.circular(35),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Icon(
        Icons.emoji_events,
        color: Colors.orange,
        size: getResponsiveFontSize(45, context),
      ),
    );
  }
}
