# Review System Implementation

This document describes the implementation of the review system for the LuckyMall app, specifically for the main page view.

## Overview

The review system has been updated to fetch real-time data from the API endpoint `https://api.luckymall.com.my/order/comment` instead of using hardcoded mock data.

## API Endpoint

**URL:** `https://api.luckymall.com.my/order/comment`

**Method:** GET

**Query Parameters:**
- `vo.productSpuId` (optional): Filter by specific product SPU ID
- `vo.productSkuId` (optional): Filter by specific product SKU ID  
- `vo.score` (optional): Filter by rating score
- `vo.orderSource` (optional): Filter by order source (0: Store, 10: Point, 20: Blindbox)
- `vo.targetId` (optional): Filter by associated business ID
- `limit` (optional): Number of reviews per page (default: 10)
- `page` (optional): Page number for pagination (default: 1)

## API Response Structure

```json
{
  "status": 0,
  "message": "",
  "data": {
    "totalCount": 0,
    "pageSize": 0,
    "totalPage": 0,
    "currentPage": 0,
    "list": [
      {
        "orderSource": 0,
        "isAnonymous": true,
        "avatar": "",
        "nickname": "",
        "content": "",
        "score": 0,
        "product": {
          "productSpuId": 1,
          "productSkuId": 1,
          "cartId": 1,
          "quantity": 1
        },
        "pictures": [],
        "createdTime": ""
      }
    ]
  },
  "timestamp": 0
}
```

## Data Mapping

The API response is mapped to the `LuckyDrawProductReview` model as follows:

| API Field | Model Field | Description |
|-----------|-------------|-------------|
| `isAnonymous` | `reviewerName` | Shows "Anonymous User" if true, otherwise uses `nickname` |
| `avatar` | `avatarUrl` | Empty string if anonymous, otherwise uses `avatar` |
| `product.productSpuId` | `productId` | Product SPU ID as string |
| `score` | `rating` | Rating score converted to double |
| `product.productSkuId` | `variation` | **Enhanced**: Fetches actual variation properties (e.g., "White Large") using productSkuId as foreign key |
| `content` | `reviewText` | Review text content |
| `pictures` | `imageUrls` | Array of image URLs |
| `createdTime` | `date` | Formatted date string (YYYY / MM / DD) |
| `orderSource` | `orderType` | Order source type (0, 10, 20) |

**Note**: The `variation` field now automatically fetches the actual product variation properties (like "White Large", "Black Medium") instead of just showing the raw `productSkuId`. This is done by using the `productSkuId` as a foreign key to fetch product details from the `/product/point/{id}` endpoint.

## Implementation Details

### ReviewService

The `ReviewService` class handles all API calls for reviews:

- **`getLatestReviews()`**: Fetches latest reviews with optional filtering and automatic variation enrichment
- **`getProductReviews()`**: Fetches reviews for a specific product with variation properties
- **`getReviewsByOrderSource()`**: Fetches reviews by order source type with variation properties
- **`getReviewsByRating()`**: Fetches reviews with specific rating and variation properties
- **`getReviewsWithoutVariations()`**: Fast review fetching without variation enrichment (for performance-critical scenarios)

#### Variation Enrichment

The service now automatically enriches reviews with actual variation properties:

1. **Collects unique SKU IDs**: From all reviews in the response
2. **Fetches product details**: For each unique `productSkuId` using the `/product/point/{id}` endpoint
3. **Maps variation properties**: Replaces raw SKU IDs with human-readable variation names (e.g., "White Large", "Black Medium")
4. **Graceful fallback**: If variation fetching fails, reviews still work with SKU IDs

### MainpageView Updates

The `MainpageView` has been updated to:

1. **Fetch reviews from API**: Replaces hardcoded review data with dynamic API calls
2. **Handle loading states**: Shows loading skeletons while fetching reviews
3. **Handle empty states**: Displays a message when no reviews are available
4. **Auto-refresh**: Includes reviews in the pull-to-refresh functionality
5. **Error handling**: Gracefully handles API errors and falls back to empty state

### ReviewCard Integration

The `ReviewCard` widget has been updated with a `fromReview` constructor that accepts `LuckyDrawProductReview` objects, making it easy to display reviews from the API.

## Usage Examples

### Fetch Latest Reviews

```dart
final result = await reviewService.getLatestReviews(
  limit: 10,
  page: 1,
);

final reviews = List<LuckyDrawProductReview>.from(result['reviews']);
```

### Filter Reviews by Product

```dart
final productReviews = await reviewService.getProductReviews(
  productSpuId: 123,
  limit: 5,
);
```

### Filter Reviews by Order Source

```dart
final blindboxReviews = await reviewService.getReviewsByOrderSource(
  orderSource: 20, // Blindbox orders
  limit: 10,
);
```

## Error Handling

The system includes comprehensive error handling:

- **Network errors**: Catches DioException and provides meaningful error messages
- **API errors**: Checks response status and handles non-zero status codes
- **Empty responses**: Gracefully handles cases where no reviews are returned
- **Loading states**: Prevents UI from getting stuck in loading state

## Anonymous Review Handling

The system properly handles anonymous reviews:

- **Anonymous users**: Display "Anonymous User" as reviewer name
- **No avatar**: Show empty avatar for anonymous reviews
- **Content preservation**: Review content and rating are still displayed

## Future Enhancements

Potential improvements for the review system:

1. **Caching**: Implement local caching for offline viewing
2. **Pagination**: Add "load more" functionality for reviews
3. **Filtering**: Add UI for filtering reviews by rating, order source, etc.
4. **Search**: Add search functionality within reviews
5. **Analytics**: Track review engagement and helpfulness

## Testing

Basic tests have been created in `test/review_service_test.dart` to verify:

- Service instantiation
- API endpoint configuration
- Empty response handling
- Data mapping correctness
- Anonymous review handling

Run tests with:
```bash
flutter test test/review_service_test.dart
```
