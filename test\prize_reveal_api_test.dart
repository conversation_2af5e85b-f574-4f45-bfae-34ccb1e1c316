import 'package:flutter_test/flutter_test.dart';
import 'package:luckymall/modules/luckydraw/model/PrizeRevealProduct.dart';

void main() {
  group('PrizeRevealProduct Model Tests', () {
    test('should create PrizeRevealProduct from JSON', () {
      final json = {
        'id': 123,
        'productName': 'Test Product',
        'productImage': 'https://example.com/image.jpg',
        'prizeDrawTime': '2024-01-15T10:30:00Z',
        'nextId': 124,
      };

      final product = PrizeRevealProduct.fromJson(json);

      expect(product.productId, equals(123));
      expect(product.productName, equals('Test Product'));
      expect(product.imageUrl, equals('https://example.com/image.jpg'));
      expect(product.countdownSeconds, equals('2024-01-15T10:30:00Z'));
      expect(product.currentBatch, equals(123));
      expect(product.nextBatch, equals(124));
    });

    test('should handle null nextId as 0', () {
      final json = {
        'id': 123,
        'productName': 'Test Product',
        'productImage': 'https://example.com/image.jpg',
        'prizeDrawTime': '2024-01-15T10:30:00Z',
        'nextId': null,
      };

      final product = PrizeRevealProduct.fromJson(json);

      expect(product.nextBatch, equals(0));
    });

    test('should convert to JSON correctly', () {
      final product = PrizeRevealProduct(
        productId: 123,
        productName: 'Test Product',
        imageUrl: 'https://example.com/image.jpg',
        countdownSeconds: '2024-01-15T10:30:00Z',
        currentBatch: 123,
        nextBatch: 124,
      );

      final json = product.toJson();

      expect(json['id'], equals(123));
      expect(json['productName'], equals('Test Product'));
      expect(json['productImage'], equals('https://example.com/image.jpg'));
      expect(json['prizeDrawTime'], equals('2024-01-15T10:30:00Z'));
      expect(json['nextId'], equals(124));
    });

    test('should copy with new values', () {
      final original = PrizeRevealProduct(
        productId: 123,
        productName: 'Test Product',
        imageUrl: 'https://example.com/image.jpg',
        countdownSeconds: '2024-01-15T10:30:00Z',
        currentBatch: 123,
        nextBatch: 124,
      );

      final copied = original.copyWith(
        productName: 'Updated Product',
        nextBatch: 125,
      );

      expect(copied.productId, equals(123));
      expect(copied.productName, equals('Updated Product'));
      expect(copied.imageUrl, equals('https://example.com/image.jpg'));
      expect(copied.countdownSeconds, equals('2024-01-15T10:30:00Z'));
      expect(copied.currentBatch, equals(123));
      expect(copied.nextBatch, equals(125));
    });
  });
}
