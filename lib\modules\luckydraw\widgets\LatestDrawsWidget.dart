import 'package:flutter/material.dart';
import 'package:luckymall/modules/luckydraw/view/LatestDrawRecordView.dart';
import '../model/LatestDrawSummary.dart';

class LatestDrawsWidget extends StatelessWidget {
  final List<LatestDrawSummary> latestDraws;

  const LatestDrawsWidget({super.key, required this.latestDraws});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xfffffeeb9), // Match CommunitySharingWidget off-white
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.fromLTRB(
              18,
              0,
              18,
              0,
            ), // Match CommunitySharingWidget
            child: Row(
              children: [
                const Text(
                  'Latest Draws',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const LatestDrawRecordView(),
                      ),
                    );
                  },
                  child: const Text(
                    'See more',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Latest draws cards
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xfffffeeb9),
                borderRadius: BorderRadius.circular(18),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.07),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: SizedBox(
                height: 270,
                child: latestDraws.isEmpty
                    ? const Center(
                        child: Text(
                          'No latest draws available',
                          style: TextStyle(color: Colors.grey, fontSize: 14),
                        ),
                      )
                    : ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        itemCount: latestDraws.length,
                        itemBuilder: (context, index) {
                          final draw = latestDraws[index];
                          return Container(
                            width: 180,
                            margin: const EdgeInsets.only(right: 12),
                            decoration: BoxDecoration(
                              color: const Color.fromARGB(255, 253, 248, 233),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.08),
                                  spreadRadius: 0,
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Item image
                                Container(
                                  height: 130,
                                  width: 240,
                                  decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.vertical(
                                      top: Radius.circular(16),
                                    ),
                                    image: DecorationImage(
                                      image: NetworkImage(draw.imageUrl),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                // Details and winner info
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // Item name
                                        Text(
                                          draw.itemName,
                                          style: const TextStyle(
                                            fontSize: 13,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 6),
                                        // Winning ID
                                        Text(
                                          'Winning ID: ${draw.winningId}',
                                          style: const TextStyle(
                                            fontSize: 11,
                                            color: Colors.black54,
                                          ),
                                        ),
                                        const SizedBox(height: 2),
                                        // Date
                                        Text(
                                          'Date: ${draw.dateTime}',
                                          style: const TextStyle(
                                            fontSize: 10,
                                            color: Colors.grey,
                                          ),
                                        ),
                                        const Spacer(),
                                        // Winner info
                                        Row(
                                          children: [
                                            CircleAvatar(
                                              radius: 14,
                                              backgroundImage:
                                                  draw.avatarUrl.isNotEmpty
                                                  ? NetworkImage(draw.avatarUrl)
                                                  : null,
                                              child: draw.avatarUrl.isEmpty
                                                  ? const Icon(
                                                      Icons.person,
                                                      size: 16,
                                                    )
                                                  : null,
                                            ),
                                            const SizedBox(width: 10),
                                            Expanded(
                                              child: Text(
                                                draw.winnerName.isNotEmpty
                                                    ? draw.winnerName
                                                    : 'Anonymous',
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w600,
                                                  color: Colors.black87,
                                                ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
