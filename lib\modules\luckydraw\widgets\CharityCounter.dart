import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/CharityFundVM.dart';

class CharityCounter extends StatefulWidget {
  const CharityCounter({super.key});

  @override
  State<CharityCounter> createState() => _CharityCounterState();
}

class _CharityCounterState extends State<CharityCounter> {
  late CharityFundVM _charityFundVM;

  @override
  void initState() {
    super.initState();
    _charityFundVM = CharityFundVM();
    _charityFundVM.initialize();
  }

  @override
  void dispose() {
    _charityFundVM.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375; // base width 375
    double scaleH(double value) =>
        value * screenHeight / 812; // base height 812
    double scaleText(double value) => value * screenWidth / 375;

    return ChangeNotifierProvider.value(
      value: _charityFundVM,
      child: Consumer<CharityFundVM>(
        builder: (context, charityFundVM, child) {
          final isLoading = charityFundVM.isLoading;
          final hasError = charityFundVM.error != null;
          final hasData = charityFundVM.hasData;

          // Get the amount to display
          String displayAmount;
          if (isLoading) {
            displayAmount = '---';
          } else if (hasError) {
            displayAmount = 'ERR';
          } else if (hasData) {
            displayAmount = charityFundVM.formattedAmount;
          } else {
            displayAmount = '000';
          }

          final digits = displayAmount.split('');

          return Container(
            width: double.infinity,
            alignment: Alignment.center,
            margin: EdgeInsets.symmetric(
              horizontal: scaleW(16),
              vertical: scaleH(5),
            ),
            padding: EdgeInsets.symmetric(vertical: scaleH(8)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(scaleW(5)),
              border: Border.all(
                color: const Color(0xFFC20009),
                width: scaleW(4),
              ),
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Color(0xFFD90019), // Light red at the top
                  Color(0xFFFD576A), // Slightly darker red in the middle
                  Color(0xFFD90019), // Even more red at the bottom
                ],
              ),
            ),
            child: Column(
              children: [
                // Digit Row
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: scaleW(20),
                    vertical: scaleH(8),
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFFCC0000),
                    borderRadius: BorderRadius.circular(scaleW(8)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black26,
                        offset: Offset(0, scaleH(3)),
                        blurRadius: scaleW(4),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: digits.map((digit) {
                      return Container(
                        margin: EdgeInsets.symmetric(horizontal: scaleW(4)),
                        padding: EdgeInsets.all(scaleW(8)),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(scaleW(4)),
                        ),
                        child: Text(
                          digit,
                          style: TextStyle(
                            fontSize: scaleText(24),
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
                SizedBox(height: scaleH(12)),
                Text(
                  'Total Charity Fund',
                  style: TextStyle(
                    color: Colors.yellow,
                    fontSize: scaleText(16),
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        color: Colors.black38,
                        offset: Offset(0, scaleH(1)),
                        blurRadius: scaleW(2),
                      ),
                    ],
                  ),
                ),

                // Show error message if there's an error
                if (hasError)
                  Padding(
                    padding: EdgeInsets.only(top: scaleH(8)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.yellow[200],
                          size: scaleText(16),
                        ),
                        SizedBox(width: scaleW(4)),
                        Text(
                          'Failed to load data',
                          style: TextStyle(
                            color: Colors.yellow[200],
                            fontSize: scaleText(12),
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        SizedBox(width: scaleW(8)),
                        GestureDetector(
                          onTap: () => charityFundVM.refreshCharityFund(),
                          child: Icon(
                            Icons.refresh,
                            color: Colors.yellow[200],
                            size: scaleText(16),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
