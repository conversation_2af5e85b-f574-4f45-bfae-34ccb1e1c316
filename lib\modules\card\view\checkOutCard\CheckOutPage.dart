import 'package:flutter/material.dart';
import '../../model/purchaseCard/PurchaseCardOrder.dart';
import 'package:go_router/go_router.dart';

class CheckoutPage extends StatefulWidget {
  final int quantity;
  final double total;

  const CheckoutPage({
    super.key,
    required this.quantity,
    required this.total,
  });

  @override
  State<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends State<CheckoutPage> {
  String? selectedGateway;
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  
  
  final paymentGateways = [
    {'name': 'Billplz', 'iconSelected': 'assets/icons/checkOutCard/billPlz2.jpg', 'iconUnselected': 'assets/icons/checkOutCard/billPlz.jpg'},
    {'name': 'Touch \'n Go', 'iconSelected': 'assets/icons/checkOutCard/tngLogo.png', 'iconUnselected': 'assets/icons/checkOutCard/tngLogo2.png'},
    {'name': 'Stripe', 'iconSelected': 'assets/icons/checkOutCard/stripe2.jpg', 'iconUnselected': 'assets/icons/checkOutCard/stripe.jpg'},
    {'name': 'iPay88', 'iconSelected': 'assets/icons/checkOutCard/ipay882.jpg', 'iconUnselected': 'assets/icons/checkOutCard/ipay88.jpg'},
    {'name': 'Lucky Mall', 'iconSelected': 'assets/icons/checkOutCard/lmWallet2.jpg', 'iconUnselected': 'assets/icons/checkOutCard/lmWallet.jpg'},
  ];

  void _selectGateway(String gatewayName) {
    setState(() {
      selectedGateway = gatewayName;
    });
  }

  void _submitOrder() async {
    if (!_formKey.currentState!.validate() || selectedGateway == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please complete all fields correctly.')),
      );
      return;
    }

    final order = PurchaseCardOrder(
      gateway: selectedGateway!,
      name: nameController.text,
      phone: phoneController.text,
      email: emailController.text,
      quantity: widget.quantity,
      total: widget.total,
      timestamp: DateTime.now(),
    );

    try {
      await Future.delayed(const Duration(seconds: 1));
      final mockResponse = {
        'status': 'success',
        'message': 'Card purchase successful!',
      };

      if (!mounted) return;
      context.goNamed(
        'purchase_result',
        extra: {
          'result': mockResponse,
          'order': order,
        },
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Submission failed: $e')),
      );
    }
  }

  Widget buildFormRow(String label, TextEditingController controller, {String? hint, TextInputType? type, String? Function(String?)? validator}) {
     return Column(
        children: [
          SizedBox(
             height: 48,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Label on the left
                SizedBox(
                  width: 100,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      label,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ),

                // Input Field on the right
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: controller,
                    keyboardType: type,
                    validator: validator,
                    decoration: InputDecoration(
                      hintText: hint,
                      hintStyle: const TextStyle(color: Colors.grey),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(vertical: 12),
                      isCollapsed: true,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
        ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 70,
        elevation: 1,
        shadowColor: Colors.black26,
        backgroundColor: Colors.white,
        title: const Text('Checkout',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.w600, fontSize: 16)),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
          onPressed: () => {
              context.go('/card/purchase'),
          },
        ),
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
            return Container(
              height: constraints.maxHeight,
              color: const Color.fromRGBO(240, 240, 240,1),
              child :SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Total Payment
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        const Text("Total Payment",
                            style: TextStyle(fontSize: 14, color: Colors.black87)),
                        const SizedBox(height: 4),
                        Text("RM ${widget.total.toStringAsFixed(2)}",
                            style: const TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.red)),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Payment Method Section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 6,
                          offset: Offset(0, 3),
                        )
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text("Payment Methods",
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 12),
                        // Payment methods container (always 5 per row)
                        LayoutBuilder(
                          builder: (context, constraints) {
                            const spacing = 8.0;
                            final itemWidth = (constraints.maxWidth - (spacing * 4)) / 5;
                            const containerHeight = 100.0; // fixed height for all buttons

                            return Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: paymentGateways.map((gatewayData) {
                                final gateway = gatewayData as Map<String, dynamic>;
                                final isSelected = selectedGateway == gateway['name'];

                                return SizedBox(
                                  width: itemWidth,
                                  child: GestureDetector(
                                    onTap: () => _selectGateway(gateway['name']),
                                    child: AnimatedContainer(
                                      duration: const Duration(milliseconds: 300),
                                      height: containerHeight,
                                      margin: const EdgeInsets.symmetric(horizontal: 2),
                                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(6),
                                        boxShadow: isSelected
                                            ? [
                                                BoxShadow(
                                                  color: Colors.amber.withOpacity(0.5),
                                                  blurRadius: 10,
                                                  spreadRadius: 1,
                                                )
                                              ]
                                            : [
                                                BoxShadow(
                                                  color: Colors.grey.shade200,
                                                  blurRadius: 4,
                                                )
                                              ],
                                        border: Border.all(
                                          color: isSelected ? Colors.amber : Colors.grey.shade300,
                                          width: 1.0,
                                        ),
                                      ),
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          SizedBox(
                                            height: 36,
                                            child: Image.asset(
                                              isSelected
                                                  ? gateway['iconSelected']
                                                  : gateway['iconUnselected'],
                                              fit: BoxFit.contain,
                                            ),
                                          ),
                                          const SizedBox(height: 6),
                                          SizedBox(
                                            height: 32, // reserve space for 1–2 lines of text
                                            child: Center(
                                              child: Text(
                                                gateway['name'],
                                                textAlign: TextAlign.center,
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                                softWrap: true,
                                                style: TextStyle(
                                                  fontFamily: 'Roboto',
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 13,
                                                  height: 1.2,
                                                  color: isSelected ? Colors.black : Colors.grey,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            );
                          },
                        ),
                        const SizedBox(height: 10),
                        const Text(
                          "Refunds are not supported through Billplz.",
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        )
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Billing Info Section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 6,
                          offset: Offset(0, 3),
                        )
                      ],
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text("Billing Information",
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                          const SizedBox(height: 12),
                          buildFormRow(
                            "Name",
                            nameController,
                            hint: "Enter name...",
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) return 'Name is required';
                              if (!RegExp(r"^[A-Za-z\s]+$").hasMatch(value)) return 'Only letters allowed';
                              return null;
                            },
                          ),
                          buildFormRow(
                            "Mobile Number",
                            phoneController,
                            type: TextInputType.phone,
                            hint: "Enter mobile number...",
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) return 'Mobile number is required';
                              if (!RegExp(r"^01\d{8,9}$").hasMatch(value)) return 'Invalid Malaysian number';
                              return null;
                            },
                          ),
                          buildFormRow(
                            "Email",
                            emailController,
                            type: TextInputType.emailAddress,
                            hint: "Enter Email...",
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) return 'Email is required';
                              if (!RegExp(r"^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$").hasMatch(value)) return 'Invalid email';
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),

                  ),
                ],
              ),
            ),
          );
        },
      ),

      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              blurRadius: 10,
              color: Colors.black26,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: SizedBox(
            width: double.infinity,
            height: 52,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.black,
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: selectedGateway == null ? null : _submitOrder,
              child: const Text("Proceed to Pay",
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            ),
          ),
        ),
      ),
    
    );
  }
}