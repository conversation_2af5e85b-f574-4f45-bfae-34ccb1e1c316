## API Client (Dio) – Quick Guide

This module centralizes HTTP access using Dio. It exposes a singleton `ApiClient` with two configured clients:

- `authDio`: For authenticated endpoints. Automatically adds `Authorization`, `Timestamp`, and `Signature` headers using the latest values from `AuthService`.
- `publicDio`: For public endpoints. No auth headers are injected.

Base URL: `https://api.luckymall.com.my` (30s connect/receive/send timeouts).

### Where things live
- Client: `lib/res/utility/network/api_client.dart`
- Token store: `lib/router/auth_guard.dart` (`AuthService`)
- Request signature: `lib/res/utility/jwt_token_helper.dart` (`SignatureHelper`)

### Interceptor behavior (authDio)
- Normalizes requests targeting the same base host so only the path is signed: e.g. passing
  `https://api.luckymall.com.my/user/info` becomes `/user/info`.
- Automatically attaches headers when token/timestamp exist:
  - `Authorization`: `<token>` (exact token string)
  - `Timestamp`: `<unix ms>`
  - `Signature`: RSA PKCS#1 v1.5 over string: `METHOD\n/PATH\nTIMESTAMP`
    - Public key is read from JWT claims (`jti` or `publicKey`).
- If signature generation fails, `Authorization` and `Timestamp` are still sent.

The same URL normalization (absolute → path + merged query) applies to `publicDio`, but without auth headers.

### Minimal usage

Authenticated GET:
```dart
import 'package:dio/dio.dart';
import 'package:luckymall/res/utility/network/api_client.dart';

final Dio dio = ApiClient.instance.authDio;

Future<Response<dynamic>> fetchUserInfo() {
  return dio.get('/user/info'); // headers injected automatically
}
```

Authenticated POST:
```dart
Future<Response<dynamic>> updateProfile(Map<String, dynamic> body) {
  return dio.post('/user/edit', data: body);
}
```

Public request (no auth headers):
```dart
final Dio publicDio = ApiClient.instance.publicDio;

Future<Response<dynamic>> sendCode({required int country, required String mobile}) {
  return publicDio.post('/user/code', data: {'country': country, 'mobile': mobile});
}
```

Absolute URL or path both work:
```dart
// Both are equivalent; interceptor normalizes the absolute URL to path-only
dio.get('/user/info');
dio.get('https://api.luckymall.com.my/user/info');
```

### When the backend expects token in body/query
Use `publicDio` and include the token manually (auth headers are not desired in this case). See also `lib/modules/authorization/README.md`.
```dart
import 'package:luckymall/router/auth_guard.dart';

Future<Response<dynamic>> submitWithTokenInBody(Map<String, dynamic> payload) async {
  final token = await AuthService.getJwtToken();
  if (token == null || token.isEmpty) {
    throw StateError('Missing JWT token');
  }
  return publicDio.post('/some/endpoint', data: {...payload, 'token': token});
}
```

### Token lifecycle checkpoints
- After login, persist both token and timestamp:
  ```dart
  await AuthService.setJwtToken(token, timestamp);
  ```
- `authDio` reads them on each request via `AuthService.getJwtToken()` and `AuthService.getTimestamp()`.

### Troubleshooting
- 401: Token missing/expired. Ensure `AuthService.setJwtToken(token, timestamp)` has been called and storage is accessible.
- Invalid signature: Verify JWT contains a usable public key (`jti` or `publicKey`) and that the request uses only the path segment (the interceptor already enforces this).
- Absolute URLs to the same host are safe; they are normalized before signing.


