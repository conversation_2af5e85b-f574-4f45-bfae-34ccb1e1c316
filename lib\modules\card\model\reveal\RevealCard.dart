class RevealCard {
  final String id;
  final String name;
  final String category;
  final String series;
  final String imagePath;

  RevealCard({
    required this.id,
    required this.name,
    required this.category,
    required this.series,
    required this.imagePath,
  });

  factory RevealCard.fromMap(Map<String, dynamic> map) => RevealCard(
        id: map['id'],
        name: map['name'],
        category: map['category'],
        series: map['series'],
        imagePath: map['imagePath'],
      );

  Map<String, dynamic> toMap() => {
        'id': id,
        'name': name,
        'category': category,
        'series': series,
        'imagePath': imagePath,
      };
}
