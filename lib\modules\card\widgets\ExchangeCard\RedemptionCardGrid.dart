import 'package:flutter/material.dart';
import '../../model/cardPack/CardItem.dart';
import '../../view-Model/cardPack/MockCards.dart';

class RedemptionCardGrid extends StatelessWidget {
  final List<String> cardIds;
  final List<String> ownedCardIds;
  final bool Function(String) isCardSelected;
  final bool Function(String) isAutoSelected;
  final void Function(String) onCardTapped;

  const RedemptionCardGrid({
    super.key,
    required this.cardIds,
    required this.ownedCardIds,
    required this.isCardSelected,
    required this.isAutoSelected,
    required this.onCardTapped,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final crossAxisCount = screenWidth < 600
        ? 2
        : screenWidth < 900
            ? 3
            : 4;

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: cardIds.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 3 / 4.2,
      ),
      itemBuilder: (context, index) {
        final cardId = cardIds[index];
        final card = allCardCatalog.firstWhere(
          (c) => c.id == cardId,
          orElse: () => CardItem(
            id: 'unknown',
            name: 'Unknown Card', // Added name parameter
            category: 'unknown',
            series: 'unknown', // Added series parameter
            imagePath: 'assets/images/card/sample_card.png',
            rank: 'unknown',
            quantity: 0,
            isOwned: false,
          ),
        );

       final isOwned = ownedCardIds.contains(card.id);
      final selected = isCardSelected(card.id);
      final autoSelected = isAutoSelected(card.id);

      return GestureDetector(
        onTap: (isOwned && !autoSelected)
            ? () => onCardTapped(card.id)
            : null, // disable tap for auto-selected
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: selected
                  ? (autoSelected
                      ? Colors.amber.withOpacity(0.6) // lighter amber for auto
                      : Colors.amber) // bright amber for manual
                  : Colors.grey.shade300,
              width: selected ? 2.5 : 1.2,
            ),
          ),
          child: Column(
            children: [
              Expanded(
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius:
                          const BorderRadius.vertical(top: Radius.circular(12)),
                      child: ColorFiltered(
                        colorFilter: autoSelected
                            ? ColorFilter.mode(
                                Colors.amber.withOpacity(0.1),
                                BlendMode.srcATop,
                              )
                            : const ColorFilter.mode(
                                Colors.transparent, BlendMode.multiply),
                        child: Image.asset(
                          card.imagePath,
                          fit: BoxFit.cover,
                          width: double.infinity,
                        ),
                      ),
                    ),
                    if (!isOwned)
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.65),
                          borderRadius:
                              const BorderRadius.vertical(top: Radius.circular(12)),
                        ),
                      ),
                    if (selected)
                      Positioned(
                        top: 6,
                        right: 6,
                        child: Column(
                          children: [
                            CircleAvatar(
                              backgroundColor: autoSelected
                                  ? Colors.amber.withOpacity(0.6)
                                  : Colors.amber,
                              radius: 12,
                              child: Icon(
                                autoSelected ? Icons.lock : Icons.check,
                                size: 16,
                                color: Colors.white,
                              ),
                            ),
                            if (autoSelected)
                              Padding(
                                padding: const EdgeInsets.only(top: 2),
                                child: Text(
                                  "Auto",
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.amber.shade700,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(6),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius:
                      BorderRadius.vertical(bottom: Radius.circular(12)),
                ),
                child: Column(
                  children: [
                    Text(
                      card.name,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 13,
                        color: isOwned ? Colors.black : Colors.grey[400],
                      ),
                    ),
                    const SizedBox(height: 4),
                    if (isOwned && card.quantity > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '${card.quantity}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
      },
    );
  }
}
