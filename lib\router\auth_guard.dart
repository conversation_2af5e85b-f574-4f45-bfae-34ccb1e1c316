import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Enhanced authentication service with JWT token support and data persistence
class AuthService {
  static bool _isLoggedIn = false;
  static String? _jwtToken;
  static String? _timestamp;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  // Storage key
  static const String _keyIsLoggedIn = 'isLoggedIn';
  static const String _keyJwtToken = 'jwtToken';
  static const String _keyTimestamp = 'timestamp';

  // Initialize the service
  static Future<void> initialize() async {
    await _loadPersistedData();
  }

  // Getter
  static bool get isLoggedIn => _isLoggedIn;

  /// Load persisted authentication data from local storage
  static Future<void> _loadPersistedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isLoggedIn = prefs.getBool(_keyIsLoggedIn) ?? false;
      _jwtToken = await _secureStorage.read(key: _keyJwtToken);
      _timestamp = await _secureStorage.read(key: _keyTimestamp);
    } catch (e) {
      await logout();
    }
  }

  /// Persist authentication data to local storage
  static Future<void> _persistData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyIsLoggedIn, _isLoggedIn);
      if (_jwtToken != null) {
        await _secureStorage.write(key: _keyJwtToken, value: _jwtToken!);
        await _secureStorage.write(key: _keyTimestamp, value: _timestamp!);
      } else {
        await _secureStorage.delete(key: _keyJwtToken);
        await _secureStorage.delete(key: _keyTimestamp);
      }
    } catch (_) {}
  }

  /// Simple login
  static Future<void> loginSimple() async {
    _isLoggedIn = true;
    await _persistData();
  }

  /// Logout user and clear all data
  static Future<void> logout() async {
    _isLoggedIn = false;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyIsLoggedIn);
    await _secureStorage.delete(key: _keyJwtToken);
    _jwtToken = null;
  }

  /// Check if user has specific permission (extend as needed)
  static bool hasPermission(String permission) {
    return _isLoggedIn;
  }

  /// Store JWT token from API responses
  static Future<void> setJwtToken(String? token, int? timestamp) async {
    _jwtToken = token;
    _timestamp = timestamp.toString();
    await _persistData();
  }

  static Future<String?> getJwtToken() async {
    return await _secureStorage.read(key: _keyJwtToken);
  }

  static Future<int?> getTimestamp() async {
    final timestamp = await _secureStorage.read(key: _keyTimestamp);
    return timestamp != null ? int.parse(timestamp) : null;
  }

  static String? get jwtToken => _jwtToken;
}

/// Route guard that only redirects logged-in users away from auth pages
String? authGuard(BuildContext context, GoRouterState state) {
  final isLoggedIn = AuthService.isLoggedIn;
  final isLoginRoute =
      state.uri.path == '/login' ||
      state.uri.path == '/register' ||
      state.uri.path == '/forgot-password' ||
      state.uri.path == '/otp-login';

  // If logged in and on login route, redirect to main app
  if (isLoggedIn && isLoginRoute) {
    return '/blind-box/shop';
  }

  return null; // No redirect needed - allow access to all routes
}

/// Route guard specifically for profile and sensitive routes
// String? profileGuard(BuildContext context, GoRouterState state) {
//   final isLoggedIn = AuthService.isLoggedIn;

//   // Profile routes require authentication - redirect to login with return path
//   if (!isLoggedIn && state.uri.path.startsWith('/profile/')) {
//     return '/login?redirect=${Uri.encodeComponent(state.uri.path)}';
//   }

//   return null;
// }
