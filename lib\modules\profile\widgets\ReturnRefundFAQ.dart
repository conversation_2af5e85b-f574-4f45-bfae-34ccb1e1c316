import 'package:flutter/material.dart';

class ReturnRefundFAQPage extends StatelessWidget {
  const ReturnRefundFAQPage({super.key});

  final List<Map<String, String>> faqs = const [
    {
      'question': 'How do I request a return?',
      'answer': 'You can request a return from the order details page by selecting the item and choosing "Return".'
    },
    {
      'question': 'What is the return window?',
      'answer': 'Returns are accepted within 7 days of receiving your item, provided it is unused and in original packaging.'
    },
    {
      'question': 'How will I get my refund?',
      'answer': 'Refunds will be processed to your original payment method within 7–14 working days.'
    },
    {
      'question': 'Are return shipping fees covered?',
      'answer': 'Return shipping fees may apply unless the item is defective or the wrong item was received.'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        iconTheme: const IconThemeData(color: Colors.black),
        centerTitle: true,
        title: const Text(
          'Return & Refund FAQ',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: faqs.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final faq = faqs[index];
          return Card(
            elevation: 1,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ExpansionTile(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              title: Row(
                children: [
                  const Icon(Icons.undo, color: Colors.black),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      faq['question']!,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
              children: [
                Text(
                  faq['answer']!,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
