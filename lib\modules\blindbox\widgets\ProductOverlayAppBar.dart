import 'package:flutter/material.dart';

class ProductOverlayAppBar extends StatelessWidget {
  final double opacity;
  final VoidCallback onCartPressed;
  final VoidCallback onSharePressed;
  final int cartItemCount;

  const ProductOverlayAppBar({
    super.key,
    required this.opacity,
    required this.onCartPressed,
    required this.onSharePressed,
    this.cartItemCount = 0,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        height:
            getResponsiveFontSize(56, context) +
            MediaQuery.of(context).padding.top,
        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
        color: Colors.white.withValues(alpha: opacity),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              icon: Icon(
                Icons.arrow_back_ios_new_rounded,
                color: Colors.black,
                size: getResponsiveFontSize(24, context),
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            Row(
              children: [
                Stack(
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.shopping_cart_outlined,
                        color: Colors.black,
                        size: getResponsiveFontSize(24, context),
                      ),
                      onPressed: onCartPressed,
                    ),
                    if (cartItemCount > 0)
                      Positioned(
                        right: getResponsiveFontSize(8, context),
                        top: getResponsiveFontSize(8, context),
                        child: Container(
                          padding: EdgeInsets.all(
                            getResponsiveFontSize(2, context),
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: Text(
                            '$cartItemCount',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: getResponsiveFontSize(10, context),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                IconButton(
                  icon: Icon(
                    Icons.share_outlined,
                    color: Colors.black,
                    size: getResponsiveFontSize(24, context),
                  ),
                  onPressed: onSharePressed,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
