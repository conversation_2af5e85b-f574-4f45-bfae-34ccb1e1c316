import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../view-model/CheckoutVM.dart';
import '../widgets/OrderHeaderSection.dart';
import '../widgets/DeliveryTypeSelector.dart';
import '../widgets/AddressSection.dart';
import '../widgets/ProductDetailsSection.dart';
import '../widgets/PaymentDetailsSection.dart';
import '../widgets/PaymentMethodsSection.dart';
import '../widgets/BillingInfoSection.dart';
import '../widgets/RemarksSection.dart';
import '../widgets/CheckoutBottomBar.dart';
import 'package:luckymall/modules/profile/model/AddressModel.dart';

class CheckoutView extends StatefulWidget {
  final CheckoutType checkoutType;
  final Map<String, dynamic>? productData;
  final VoidCallback? onSuccess;

  const CheckoutView({
    super.key,
    required this.checkoutType,
    this.productData,
    this.onSuccess,
  });

  @override
  State<CheckoutView> createState() => _CheckoutViewState();
}

class _CheckoutViewState extends State<CheckoutView> {
  late CheckoutVM checkoutVM;

  @override
  void initState() {
    super.initState();
    checkoutVM = CheckoutVM(checkoutType: widget.checkoutType);

    // Set product data if provided
    if (widget.productData != null) {
      checkoutVM.setProductData(widget.productData!);

      // Automatically fetch checkout data from API after setting product data
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _fetchCheckoutData();
      });
    }
  }

  /// Fetch checkout data from API
  Future<void> _fetchCheckoutData() async {
    try {
      await checkoutVM.getCheckoutData();
    } catch (e) {
      // Error handling is done in the view model
    }
  }

  @override
  void dispose() {
    checkoutVM.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: checkoutVM,
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        appBar: AppBar(
          centerTitle: true,
          toolbarHeight: 70,
          elevation: 1,
          shadowColor: Colors.black26,
          backgroundColor: Colors.white,
          title: Text(
            _getAppBarTitle(widget.checkoutType),
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              // Order Header Section
              const OrderHeaderSection(),

              // Delivery Type Selector (shown for shop and luckydraw)
              const DeliveryTypeSelector(),

              // Address Section (shown when delivery is selected)
              const AddressSection(),

              const SizedBox(height: 8),

              // Product Details Section
              const ProductDetailsSection(),

              const SizedBox(height: 8),

              // Payment Details Section
              const PaymentDetailsSection(),

              const SizedBox(height: 8),

              // Billing Information Section (shown only for delivery)
              Consumer<CheckoutVM>(
                builder: (context, viewModel, child) {
                  if (viewModel.deliveryType == DeliveryType.delivery) {
                    return Column(
                      children: [
                        const BillingInfoSection(),
                        const SizedBox(height: 8),
                      ],
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              // Remarks Section
              const RemarksSection(),

              const SizedBox(height: 8),

              // Payment Methods Section (shown for shop and blindbox)
              const PaymentMethodsSection(),

              const SizedBox(height: 20),

              const CheckoutBottomBar(),
            ],
          ),
        ),
      ),
    );
  }

  String _getAppBarTitle(CheckoutType checkoutType) {
    switch (checkoutType) {
      case CheckoutType.shop:
        return 'Order Details';
      case CheckoutType.blindbox:
        return 'Order Details';
      case CheckoutType.luckydraw:
        return 'Claim Prize';
      case CheckoutType.physicalCard:
        return 'Send Physical Card';
    }
  }
}

// Helper class for easy navigation to checkout from other modules
class CheckoutHelper {
  /// Navigate to checkout view for shop products
  static void navigateToShopCheckout(
    BuildContext context, {
    required Map<String, dynamic> productData,
    VoidCallback? onSuccess,
  }) {
    context.push(
      '/checkout',
      extra: {
        'checkoutType': CheckoutType.shop,
        'productData': productData,
        'onSuccess': onSuccess,
      },
    );
  }

  /// Navigate to checkout view for blindbox products
  static void navigateToBlindboxCheckout(
    BuildContext context, {
    required Map<String, dynamic> productData,
    VoidCallback? onSuccess,
  }) {
    context.push(
      '/checkout',
      extra: {
        'checkoutType': CheckoutType.blindbox,
        'productData': productData,
        'onSuccess': onSuccess,
      },
    );
  }

  /// Navigate to checkout view for luckydraw prizes
  static void navigateToLuckydrawCheckout(
    BuildContext context, {
    required Map<String, dynamic> productData,
    VoidCallback? onSuccess,
  }) {
    context.push(
      '/checkout',
      extra: {
        'checkoutType': CheckoutType.luckydraw,
        'productData': productData,
        'onSuccess': onSuccess,
      },
    );
  }

  /// Navigate to checkout view for physical card delivery
  static void navigateToPhysicalCardCheckout(
    BuildContext context, {
    required Map<String, dynamic> productData,
    VoidCallback? onSuccess,
  }) {
    context.push(
      '/checkout',
      extra: {
        'checkoutType': CheckoutType.physicalCard,
        'productData': productData,
        'onSuccess': onSuccess,
      },
    );
  }

  /// Create sample product data for testing
  static Map<String, dynamic> createSampleProductData({
    String? title,
    double? price,
    String? imageUrl,
    int? quantity,
    double? discount,
    double? deliveryFee,
    String? variant,
  }) {
    return {
      'title': title ?? 'Sample Product',
      'price': price ?? 99.99,
      'imageUrl': imageUrl,
      'quantity': quantity ?? 1,
      'discount': discount,
      'deliveryFee': deliveryFee ?? 5.00,
      'variant': variant,
    };
  }

  /// Create sample physical card data for testing
  static Map<String, dynamic> createSamplePhysicalCardData({
    String? title,
    double? price,
    String? imageUrl,
    int? quantity,
    double? deliveryFee,
    String? variant,
  }) {
    return {
      'title': title ?? 'Physical Card',
      'price': price ?? 29.99,
      'imageUrl': imageUrl,
      'quantity': quantity ?? 1,
      'deliveryFee': deliveryFee ?? 15.00,
      'variant': variant ?? 'Standard Delivery',
    };
  }

  /// Navigate to voucher selection page
  static void navigateToVoucherSelection(
    BuildContext context, {
    required double orderAmount,
    required Function(Map<String, dynamic>?) onVoucherSelected,
  }) {
    context.push(
      '/checkout/select-voucher',
      extra: {
        'orderAmount': orderAmount,
        'onVoucherSelected': onVoucherSelected,
      },
    );
  }

  /// Navigate to address selection page
  static void navigateToAddressSelection(
    BuildContext context, {
    required Function(Addressmodel) onAddressSelected,
  }) {
    context.push(
      '/checkout/select-address',
      extra: {'onAddressSelected': onAddressSelected},
    );
  }
}
