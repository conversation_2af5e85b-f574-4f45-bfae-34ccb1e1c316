// lib/modules/card/view-model/scanCode/ScanCodeViewModel.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import 'package:luckymall/modules/card/widgets/ScanCode/dialog/AlreadyClaimedDialog.dart';
import 'package:luckymall/modules/card/widgets/ScanCode/dialog/InvalidPasskeyDialog.dart';
import '../../model/scanCode/ScanCodeModel.dart';
import '../../widgets/ScanCode/dialog/PassKeyInputDialog.dart';
import '../../widgets/ScanCode/dialog/ConfirmActionDialog.dart';
import '../../view-Model/scanCode/RevealsScanCardViewModel.dart';

class ScanCodeViewModel extends ChangeNotifier {
  final List<String> _claimedCodes = ['12345678'];

  Future<ScanCodeModel?> checkIfClaimed(String code) async {
    await Future.delayed(const Duration(milliseconds: 500));
    for (final c in _claimedCodes) {
      if (c == code) {
        return ScanCodeModel(code: code, isClaimed: true, claimedAt: DateTime.now());
      }
    }
    return null;
  }

  Future<void> showClaimedDialog(BuildContext context, ScanCodeModel model) async {
    await showDialog(context: context, builder: (_) => AlreadyClaimedDialog(model: model));
  }

  Future<void> showErrorDialog(BuildContext context, String title, String message) async {
    await showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('OK'))],
      ),
    );
  }

  Future<String?> promptPasskey(BuildContext context) async {
    return await showDialog<String>(context: context, builder: (_) => const PasskeyInputDialog());
  }

  Future<bool> validatePasskey(String code, String passkey) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return passkey == '12345678';
  }

  Future<void> showErrorPasskeyDialog(BuildContext context) async {
    await showDialog(context: context, builder: (_) => const InvalidPasskeyDialog());
  }

  Future<bool> showConfirmationDialog(BuildContext parentContext, String code, String passkey) async {
    final res = await showDialog<bool>(
      context: parentContext,
      barrierDismissible: false,
      builder: (dialogContext) => ConfirmActionDialog(
        title: "Final Confirmation",
        message: "Passkey verified!\nDo you want to claim code $code?",
        confirmText: "Confirm",
        cancelText: "Cancel",
        onCancel: () => Navigator.of(dialogContext).pop(false),
        onConfirm: () => Navigator.of(dialogContext).pop(true),
      ),
    );
    return res == true;
  }

  Future<void> handleScannedCode(BuildContext context, String code) async {
    if (code.length != 8 || !RegExp(r'^\d{8}$').hasMatch(code)) {
      await showErrorDialog(context, "Invalid Code", "Code must be 8 digits.");
      return;
    }

    final claimed = await checkIfClaimed(code);
    if (claimed != null && claimed.isClaimed) {
      await showClaimedDialog(context, claimed);
      return;
    }

    final passkey = await promptPasskey(context);
    if (passkey == null) return;

    if (passkey.length != 8 || !await validatePasskey(code, passkey)) {
      await showErrorPasskeyDialog(context);
      return;
    }

    final confirmed = await showConfirmationDialog(context, code, passkey);
    if (!confirmed) return;

    try {
      // Get Reveal VM from Provider (must be provided above this page in widget tree)
      final revealVm = Provider.of<RevealsScanCardViewModel>(context, listen: false);

      await revealVm.fetchCards(minCount: 5);

      // Navigate to animation, passing the revealVm.cards (List<RevealCard>)
      context.pushReplacement('/scan/animation', extra: revealVm.cards);
    } catch (e) {
      await showErrorDialog(context, "Error", "Failed to fetch cards. Please try again later.");
    }
  }
}
