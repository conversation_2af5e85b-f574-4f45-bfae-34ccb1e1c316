# Lucky Draw Product Details Module

This module provides functionality to fetch and display detailed information about lucky draw products using the `/product/point/{id}` API endpoint.

## API Endpoint

**GET** `/product/point/{id}`

**Parameters:**
- `id` (path, required): Product ID (integer)

**Response Structure:**
```json
{
  "status": 0,
  "message": "",
  "data": {
    "id": 0,
    "productSpuId": 0,
    "productSkuId": 0,
    "productSkuName": "",
    "productSkuImage": "",
    "productSkuPropertys": "",
    "details": "",
    "usedAmount": 0,
    "totalAmount": 0
  },
  "timestamp": 0
}
```

## Files Structure

### Models
- `LuckyDrawProductDetails.dart` - Main model for product details
- `LuckyDrawProductDetailsResponse.dart` - API response wrapper

### Services
- `LuckyDrawProductDetailsService.dart` - API service for product details

### View Models
- `LuckyDrawProductDetailsVM.dart` - Business logic and state management

### Views
- `LuckyDrawProductDetailsView.dart` - UI for displaying product details

## Usage Examples

### 1. Basic Usage in a View

```dart
import 'package:flutter/material.dart';
import '../view/LuckyDrawProductDetailsView.dart';

class MyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return LuckyDrawProductDetailsView(productId: 123);
  }
}
```

### 2. Using the Service Directly

```dart
import '../data/LuckyDrawProductDetailsService.dart';

final service = luckyDrawProductDetailsService;

// Get product details
try {
  final response = await service.getProductDetails(123);
  if (response.isSuccess && response.hasData) {
    final product = response.data!;
    print('Product: ${product.productSkuName}');
    print('Available slots: ${product.remainingSlots}');
  }
} catch (e) {
  print('Error: $e');
}
```

### 3. Using the View Model

```dart
import '../view-model/LuckyDrawProductDetailsVM.dart';

final viewModel = LuckyDrawProductDetailsVM();

// Listen to changes
viewModel.addListener(() {
  if (viewModel.hasData) {
    final product = viewModel.productDetails!;
    print('Product loaded: ${product.productSkuName}');
  }
});

// Load product details
await viewModel.loadProductDetails(123);

// Check participation status
if (viewModel.hasAvailableSlots) {
  print('Slots available: ${viewModel.remainingSlots}');
} else {
  print('Product is fully booked');
}
```

## Key Features

### Product Details Model
- **Basic Info**: ID, SPU ID, SKU ID, name, image
- **Properties**: SKU properties and detailed description
- **Participation**: Used amount vs total amount
- **Convenience Getters**: 
  - `remainingSlots` - Available participation slots
  - `participationRate` - Percentage of slots filled
  - `productName` - Alias for productSkuName
  - `imageUrl` - Alias for productSkuImage

### Service Features
- **Error Handling**: Comprehensive error handling with specific error messages
- **Retry Logic**: Built-in retry mechanism with exponential backoff
- **Validation**: Product ID validation
- **Logging**: Detailed logging for debugging

### View Model Features
- **State Management**: Loading, success, error states
- **Caching**: Avoids reloading same product
- **Business Logic**: Participation status, availability checks
- **Formatted Text**: Human-readable participation information

### UI Features
- **Responsive Design**: Adapts to different screen sizes
- **Loading States**: Shows loading indicators and error messages
- **Participation Status**: Visual progress bar and status indicators
- **Action Buttons**: Participate and share buttons (placeholders for now)
- **Refresh**: Pull-to-refresh functionality

## State Management

The module uses `ChangeNotifier` for state management with the following states:

- **idle**: Initial state, no data loaded
- **loading**: API call in progress
- **success**: Data loaded successfully
- **error**: Error occurred during loading

## Error Handling

The service handles various types of errors:
- Network timeouts
- Server errors
- Invalid responses
- Connection failures

Each error type provides specific error messages for better user experience.

## Future Enhancements

- [ ] Participation logic implementation
- [ ] Share functionality
- [ ] Image caching
- [ ] Offline support
- [ ] Analytics tracking
- [ ] Deep linking support

## Dependencies

- `flutter/foundation.dart` - For ChangeNotifier
- `provider` - For state management
- `dio` - For HTTP requests

## Testing

To test the API endpoint:

```dart
final service = luckyDrawProductDetailsService;
final isWorking = await service.testProductDetailsAPI(123);
print('API test result: $isWorking');
```

## Notes

- The `details` field structure is currently unknown and may contain HTML or formatted text
- The `productSkuPropertys` field may contain structured data that could be parsed further
- Both fields are displayed as-is in the current implementation
- The module is designed to be extensible for future enhancements
