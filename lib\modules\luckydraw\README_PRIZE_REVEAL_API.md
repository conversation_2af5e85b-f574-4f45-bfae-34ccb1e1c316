# Prize Reveal API Integration

This document describes the integration of the prize reveal API endpoint for the Lucky Draw module.

## API Endpoint

**GET** `/product/point/reveal`

**Base URL:** `https://api.luckymall.com.my`

**Response Structure:**
```json
{
  "status": 0,
  "message": "",
  "data": [
    {
      "id": 0,
      "nextId": 0,
      "productName": "",
      "productImage": "",
      "prizeDrawTime": ""
    }
  ],
  "timestamp": 0
}
```

## Implementation Details

### 1. Data Model (`PrizeRevealProduct.dart`)

The `PrizeRevealProduct` model class that represents the prize reveal data:

```dart
class PrizeRevealProduct {
  final int productId;        // Maps to API response 'id' field (period of luckydrawproduct)
  final String productName;   // Maps to API response 'productName' field
  final String imageUrl;      // Maps to API response 'productImage' field
  final String countdownSeconds; // Maps to API response 'prizeDrawTime' field
  final int currentBatch;     // Same as productId (period)
  final int? nextBatch;       // Maps to API response 'nextId' field (0 means no next batch)
  
  // Constructor and methods...
}
```

**Key Features:**
- Maps API response fields correctly (`id` → `productId`, `productName` → `productName`, etc.)
- `nextBatch` is 0 when there's no next batch (no "Join Next Batch" button)
- Removed unnecessary fields as requested: `participationDate`, `revealDate`, `status`

### 2. Service Layer (`LuckyDrawService.dart`)

The service layer that handles API communication:

```dart
class LuckyDrawService {
  Future<List<PrizeRevealProduct>> getPrizeRevealProducts() async { ... }
}
```

**Key Features:**
- Makes HTTP GET requests to the prize reveal endpoint
- Handles network errors and API errors
- Returns parsed `List<PrizeRevealProduct>` objects
- Expects `status: 0` for successful responses

### 3. Repository Layer (`LuckyDrawRepository.dart`)

The repository layer that abstracts data access:

```dart
abstract class LuckyDrawRepository {
  Future<List<PrizeRevealProduct>> fetchPrizeRevealProducts({
    String? userId,
    String? status,
  });
}

class MockLuckyDrawRepository implements LuckyDrawRepository {
  @override
  Future<List<PrizeRevealProduct>> fetchPrizeRevealProducts({
    String? userId,
    String? status,
  }) async {
    try {
      // Use the real API service instead of mock data
      return await luckyDrawService.getPrizeRevealProducts();
    } catch (e) {
      print('Repository: Error fetching prize reveal products: $e');
      // Fallback to empty list if API fails
      return [];
    }
  }
}
```

**Key Features:**
- Abstract method in base class for dependency injection
- Real API service integration in mock implementation
- Graceful error handling with fallback to empty list

### 4. View Model (`PrizeRevealVM.dart`)

The view model that manages prize reveal state:

```dart
class PrizeRevealVM extends ChangeNotifier {
  // Prize reveal products management
  List<PrizeRevealProduct> _prizeRevealProducts = [];
  List<PrizeRevealProduct> get prizeRevealProducts => _prizeRevealProducts;
  bool _isPrizeRevealLoading = true;
  bool get isPrizeRevealLoading => _isPrizeRevealLoading;

  /// Fetch prize reveal products from the API
  Future<void> fetchPrizeRevealProducts({bool notify = true}) async { ... }
  
  /// Manually refresh prize reveal products
  Future<void> refreshPrizeRevealProducts() async { ... }
}
```

**Key Features:**
- State management for prize reveal products
- Loading states and error handling
- Integration with repository layer
- Refresh methods for manual updates

### 5. View Layer (`PrizeRevealView.dart`)

The view that displays prize reveal products:

```dart
Widget _buildPrizeRevealSection(List<dynamic> allProducts, PrizeRevealVM vm) {
  final List<PrizeRevealProduct> prizeRevealProducts = vm.prizeRevealProducts;

  // Show loading state
  if (vm.isPrizeRevealLoading) {
    return Container(/* loading indicator */);
  }

  // Don't show section if no products
  if (prizeRevealProducts.isEmpty) {
    return const SizedBox.shrink();
  }

  return PrizeRevealCards(
    products: prizeRevealProducts,
    onProductTap: () { /* handle tap */ },
    onJoinNextBatch: (product) { /* handle join next batch */ },
  );
}
```

**Key Features:**
- Conditional display based on loading state and data availability
- Integration with PrizeRevealCards widget
- Proper error handling and loading states

### 6. Widget (`PrizeRevealCards.dart`)

The widget that displays individual prize reveal cards:

```dart
class PrizeRevealCards extends StatefulWidget {
  final List<PrizeRevealProduct> products;
  final VoidCallback? onProductTap;
  final Function(PrizeRevealProduct)? onJoinNextBatch;
  
  // Implementation...
}
```

**Key Features:**
- Uses proper `PrizeRevealProduct` model from models directory
- Conditional "Join Next Batch" button (only shows when `nextBatch > 0`)
- Responsive design with proper scaling
- Image loading with placeholder and error states

## Usage Examples

### 1. Basic API Call

```dart
import '../data/LuckyDrawService.dart';

final service = LuckyDrawService();

try {
  final products = await service.getPrizeRevealProducts();
  print('Fetched ${products.length} prize reveal products');
} catch (e) {
  print('Error: $e');
}
```

### 2. Using the Repository

```dart
import '../data/LuckyDrawRepository.dart';

final repository = MockLuckyDrawRepository();

try {
  final products = await repository.fetchPrizeRevealProducts();
  print('Fetched ${products.length} prize reveal products');
} catch (e) {
  print('Error: $e');
}
```

### 3. Using the View Model

```dart
import '../view-model/PrizeRevealVM.dart';

final vm = PrizeRevealVM();
await vm.fetchPrizeRevealProducts();

final products = vm.prizeRevealProducts;
final isLoading = vm.isPrizeRevealLoading;
```

## Field Mapping

| API Field | Model Field | Description |
|-----------|-------------|-------------|
| `id` | `productId` | Period of luckydrawproduct |
| `productName` | `productName` | Name of the product |
| `productImage` | `imageUrl` | URL of the product image |
| `prizeDrawTime` | `countdownSeconds` | Time until prize draw (string format) |
| `id` | `currentBatch` | Same as productId (period) |
| `nextId` | `nextBatch` | Next batch ID (0 means no next batch) |

## Notes

- The "Join Next Batch" button is only displayed when `nextBatch > 0`
- If there's no next batch (`nextId: 0`), the button is hidden
- The `prizeDrawTime` field is stored as a string and needs to be parsed for countdown functionality
- Removed fields: `participationDate`, `revealDate`, `status` as requested
- The integration follows the same pattern as other API integrations in the project
