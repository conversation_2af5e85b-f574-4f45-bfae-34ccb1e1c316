import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerCardItem extends StatelessWidget {
  const ShimmerCardItem({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final double cardWidth = (screenWidth - 48) / 2;

    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        width: cardWidth,
        height: cardWidth * 1.33,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
