import 'package:dio/dio.dart';
import '../model/OpenSoonProduct.dart';

final openSoonProductService = OpenSoonProductService();

class OpenSoonProductService {
  final Dio dio = Dio();

  /// Get opening soon products from the API
  /// Endpoint: GET /product/point/opening
  ///
  /// Returns: List of OpenSoonProduct objects
  Future<List<OpenSoonProduct>> getOpeningSoonProducts() async {
    try {
      print('OpenSoonProductService: getOpeningSoonProducts called');

      final response = await dio.get(
        'https://api.luckymall.com.my/product/point/opening',
      );

      print(
        'OpenSoonProductService: API response status: ${response.statusCode}',
      );
      print('OpenSoonProductService: API response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData['status'] == 0 && responseData['data'] != null) {
          final List<dynamic> productList = responseData['data'];
          print('OpenSoonProductService: Product list from API: $productList');

          final products = productList
              .map((json) => OpenSoonProduct.fromJson(json))
              .toList();

          print('OpenSoonProductService: Mapped products: ${products.length}');
          return products;
        } else {
          throw Exception('API Error: ${responseData['message']}');
        }
      } else {
        throw Exception(
          'Failed to load opening soon products: HTTP ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      print('OpenSoonProductService: DioException: ${e.message}');
      print('OpenSoonProductService: DioException type: ${e.type}');
      print(
        'OpenSoonProductService: DioException response: ${e.response?.data}',
      );

      String errorMessage = 'Network error';
      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = 'Connection timeout';
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Receive timeout';
      } else if (e.type == DioExceptionType.sendTimeout) {
        errorMessage = 'Send timeout';
      } else if (e.response != null) {
        errorMessage =
            'HTTP ${e.response!.statusCode}: ${e.response!.statusMessage}';
      }

      throw Exception('Failed to load opening soon products: $errorMessage');
    } catch (e) {
      print('OpenSoonProductService: Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }
}
