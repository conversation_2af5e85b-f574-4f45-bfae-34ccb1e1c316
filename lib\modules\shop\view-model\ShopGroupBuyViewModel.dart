import 'package:flutter/material.dart';
import 'dart:async';

enum GroupBuyState {
  created, // Group created, needs more members
  formed, // Minimum members reached, can reveal
  won, // User won the group buy
}

class BlindboxGroupBuyVM extends ChangeNotifier {
  // State management
  GroupBuyState _currentState = GroupBuyState.created;
  GroupBuyState get currentState => _currentState;

  // Timer management
  Timer? _timer;
  Duration _remainingTime = const Duration(hours: 24);
  Duration get remainingTime => _remainingTime;

  // Group data
  final String _productName = "<PERSON>'s 3D Visible Window Air Fryer";
  String get productName => _productName;

  final String _productImage = "https://picsum.photos/300/300";
  String get productImage => _productImage;

  int _currentMembers = 2;
  int get currentMembers => _currentMembers;

  final int _minimumMembers = 4;
  int get minimumMembers => _minimumMembers;

  final int _maxMembers = 6;
  int get maxMembers => _maxMembers;

  final String _inviteLink = "https://example.com/invite";
  String get inviteLink => _inviteLink;

  final String _qrCodeUrl =
      "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://example.com/invite";
  String get qrCodeUrl => _qrCodeUrl;

  final String _groupId = "#7j3fBto7nl";
  String get groupId => _groupId;

  final double _rewardAmount = 60.00;
  double get rewardAmount => _rewardAmount;

  // Member avatars (mock data)
  final List<String> _memberAvatars = [
    "https://i.pravatar.cc/150?img=1",
    "https://i.pravatar.cc/150?img=2",
  ];
  List<String> get memberAvatars => _memberAvatars;

  BlindboxGroupBuyVM() {
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime.inSeconds > 0) {
        _remainingTime = _remainingTime - const Duration(seconds: 1);
        notifyListeners();
      } else {
        _timer?.cancel();
        // Timer expired - could trigger some action here
      }
    });
  }

  // Simulate adding a member (for testing)
  void addMember() {
    if (_currentMembers < _maxMembers) {
      _currentMembers++;
      _memberAvatars.add("https://i.pravatar.cc/150?img=$_currentMembers");

      // Check if minimum members reached
      if (_currentMembers >= _minimumMembers &&
          _currentState == GroupBuyState.created) {
        _currentState = GroupBuyState.formed;
      }

      notifyListeners();
    }
  }

  // Simulate revealing the result
  void revealResult() {
    if (_currentState == GroupBuyState.formed) {
      _currentState = GroupBuyState.won;
      notifyListeners();
    }
  }

  // Set state for testing purposes
  void setState(GroupBuyState state) {
    _currentState = state;
    notifyListeners();
  }

  // Format timer display
  String get formattedTime {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String hours = twoDigits(_remainingTime.inHours);
    String minutes = twoDigits(_remainingTime.inMinutes.remainder(60));
    String seconds = twoDigits(_remainingTime.inSeconds.remainder(60));
    return "$hours:$minutes:$seconds";
  }

  // Check if reveal button should be enabled
  bool get canReveal => _currentState == GroupBuyState.formed;

  // Check if timer is still active
  bool get isTimerActive => _remainingTime.inSeconds > 0;

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
