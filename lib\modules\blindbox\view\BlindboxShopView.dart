import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../widgets/CategoryTab.dart';
import '../widgets/BlindboxShopCard.dart';
import '../widgets/BlindboxFilterOverlay.dart';
import '../providers/blindbox_providers.dart';
import '../data/model/BlindboxProductModel.dart';

class BlindboxShopView extends ConsumerStatefulWidget {
  const BlindboxShopView({super.key});

  @override
  ConsumerState<BlindboxShopView> createState() => _BlindboxShopViewState();
}

class _BlindboxShopViewState extends ConsumerState<BlindboxShopView> {
  // Search controller
  late TextEditingController searchController;
  late ScrollController scrollController;
  Map<String, dynamic>? currentFilters;

  // Pagination variables
  bool isLoadingMore = false;
  bool hasMoreData = true;
  int currentPage = 1;
  int pageSize = 10;
  List<BlindboxProductModel> allProducts = [];
  List<BlindboxProductModel> displayedProducts = [];

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)
    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  // Helper method to get responsive crossAxisCount for grid
  int getResponsiveCrossAxisCount(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 2; // Small devices (phones)
    } else if (screenWidth < 900) {
      return 3; // Medium devices (tablets)
    } else if (screenWidth < 1200) {
      return 4; // Large tablets
    } else {
      return 5; // Desktop/large screens
    }
  }

  // Helper method to map API category names to available SVG assets
  String? _getCategoryIconAsset(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case 'kitchen':
        return 'assets/icons/shop/Kitchen.svg';
      case 'electronic devices':
        return 'assets/icons/shop/Electronic Device.svg';
      case 'electronic accessories':
        return 'assets/icons/shop/Electronic Accessories.svg';
      case 'household':
        return 'assets/icons/shop/Household.svg';
      case 'snack':
        return 'assets/icons/shop/Snacks.svg';
      case 'ladies':
        return 'assets/icons/shop/Beauty.svg';
      case 'fashion':
        return 'assets/icons/shop/Fashion.svg';
      case 'health & beauty':
        return 'assets/icons/shop/Health.svg';
      case 'travel':
        return 'assets/icons/shop/Travel.svg';
      case 'sport':
        return 'assets/icons/shop/Sports.svg';
      case 'pets':
        return 'assets/icons/shop/Pet.svg';
      case 'stationary':
        return 'assets/icons/shop/Stationery.svg';
      default:
        return null;
    }
  }

  void _showFilterOverlay(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlindboxFilterOverlay(
        initialFilters: currentFilters,
        onFilterApplied: (filters) {
          setState(() {
            currentFilters = filters;
            // Reset pagination when filters change
            hasMoreData = true;
            currentPage = 1;
            allProducts.clear();
            displayedProducts.clear();
          });
          // Invalidate provider to get fresh filtered data
          final selectedCategoryId = ref.read(selectedCategoryIdProvider);
          ref.invalidate(blindboxProductsProvider(selectedCategoryId));
        },
      ),
    );
  }

  // Load more data when scrolling near the bottom
  Future<void> _loadMoreData() async {
    if (isLoadingMore || !hasMoreData) return;

    setState(() {
      isLoadingMore = true;
    });

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Check if we have more data to load
      if (displayedProducts.length < allProducts.length) {
        final nextBatch = allProducts
            .take(displayedProducts.length + pageSize)
            .toList();

        setState(() {
          displayedProducts = nextBatch;
          isLoadingMore = false;
        });
      } else {
        setState(() {
          hasMoreData = false;
          isLoadingMore = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoadingMore = false;
      });
    }
  }

  // Refresh data and reset pagination
  Future<void> _refreshData() async {
    setState(() {
      hasMoreData = true;
      currentPage = 1;
      allProducts.clear();
      displayedProducts.clear();
    });

    // Invalidate the main provider to refresh data
    final selectedCategoryId = ref.read(selectedCategoryIdProvider);
    ref.invalidate(blindboxProductsProvider(selectedCategoryId));

    // Wait a bit for the provider to refresh
    await Future.delayed(const Duration(milliseconds: 100));
  }

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();
    scrollController = ScrollController();
    scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200) {
      _loadMoreData();
    }
  }

  @override
  void dispose() {
    searchController.dispose();
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(blindboxCategoriesProvider);
    final selectedCategoryId = ref.watch(selectedCategoryIdProvider);

    // Use the new consolidated provider with category ID
    final productsAsync = ref.watch(
      blindboxProductsProvider(selectedCategoryId),
    );

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: const Color(0xFFFCD255),
        elevation: 0,
        centerTitle: false,
        titleSpacing: 0,
        title: Row(
          children: [
            const SizedBox(width: 24),
            CircleAvatar(
              backgroundColor: Colors.white,
              radius: getResponsiveFontSize(16, context),
              backgroundImage: const AssetImage('assets/images/logoLM.png'),
            ),
            const SizedBox(width: 8),
            const Text(
              'My Lucky Mall',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ],
        ),
        actions: [
          // IconButton(
          //   icon: Icon(
          //     Icons.qr_code_scanner,
          //     size: getResponsiveFontSize(24, context),
          //     color: Colors.black,
          //   ),
          //   onPressed: () {
          //   },
          // ),
          IconButton(
            icon: Icon(
              Icons.favorite,
              color: Colors.black,
              size: getResponsiveFontSize(24, context),
            ),
            onPressed: () {
              context.push('/shop/favourites');
            },
          ),
          IconButton(
            icon: Icon(
              Icons.shopping_cart,
              color: Colors.black,
              size: getResponsiveFontSize(24, context),
            ),
            onPressed: () {
              context.push('/shop/cart');
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            children: [
              Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color(0xFFFCD255),
                      Color(0xFFFCD255),
                      Color(0xFFFFEAAB),
                      Color(0xFFFFEAAB),
                      Color(0xFFFFFFFF),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 0.36, 0.67, 0.77, 1.0],
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: getResponsivePadding(24.0, context),
                        vertical: getResponsivePadding(10, context),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(
                              horizontal: getResponsivePadding(12, context),
                              vertical: getResponsivePadding(10, context),
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF3B300),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    'Community Sharing',
                                    style: TextStyle(
                                      fontSize: getResponsiveFontSize(
                                        16,
                                        context,
                                      ),
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                Material(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(999),
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(999),
                                    onTap: () {
                                      context.go('/blind-box');
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: getResponsivePadding(
                                          12,
                                          context,
                                        ),
                                        vertical: getResponsivePadding(
                                          6,
                                          context,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            'See more',
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontWeight: FontWeight.w600,
                                              fontSize: getResponsiveFontSize(
                                                14,
                                                context,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          Icon(
                                            Icons.chevron_right,
                                            size: getResponsiveFontSize(
                                              18,
                                              context,
                                            ),
                                            color: Colors.black,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: getResponsivePadding(16, context)),
                          Row(
                            children: [
                              Expanded(
                                child: Container(
                                  height: getResponsivePadding(44, context),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: getResponsivePadding(
                                            12,
                                            context,
                                          ),
                                        ),
                                        child: Icon(
                                          Icons.search,
                                          color: Colors.grey,
                                          size: getResponsiveFontSize(
                                            20,
                                            context,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: TextField(
                                          controller: searchController,
                                          decoration: InputDecoration(
                                            hintText: 'Search',
                                            border: InputBorder.none,
                                            isDense: true,
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                  vertical:
                                                      getResponsivePadding(
                                                        10,
                                                        context,
                                                      ),
                                                ),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        margin: EdgeInsets.only(
                                          right: getResponsivePadding(
                                            4,
                                            context,
                                          ),
                                        ),
                                        child: Material(
                                          color: Colors.red[700],
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          child: InkWell(
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                            onTap: () {
                                              context.push(
                                                '/blind-box/search',
                                                extra: {
                                                  'initialQuery':
                                                      searchController.text
                                                          .trim(),
                                                  'trigger': true,
                                                },
                                              );
                                            },
                                            child: Padding(
                                              padding: EdgeInsets.symmetric(
                                                horizontal:
                                                    getResponsivePadding(
                                                      16,
                                                      context,
                                                    ),
                                                vertical: getResponsivePadding(
                                                  8,
                                                  context,
                                                ),
                                              ),
                                              child: Text(
                                                'Search',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.w500,
                                                  fontSize:
                                                      getResponsiveFontSize(
                                                        14,
                                                        context,
                                                      ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: getResponsivePadding(12, context),
                              ),
                              Material(
                                color: Colors.amber,
                                borderRadius: BorderRadius.circular(12),
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(12),
                                  onTap: () => _showFilterOverlay(context),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 14,
                                      vertical: 8,
                                    ),
                                    child: Icon(
                                      Icons.tune,
                                      size: 20,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: getResponsivePadding(10, context)),
                    // Category Tabs from API
                    categoriesAsync.when(
                      data: (categories) {
                        return SingleChildScrollView(
                          padding: EdgeInsets.symmetric(
                            horizontal: getResponsivePadding(10, context),
                          ),
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // All tab
                              GestureDetector(
                                onTap: () {
                                  // Clear any potential interference from search/filter providers
                                  ref.invalidate(blindboxSearchResultsProvider);
                                  ref.invalidate(blindboxLoadingProvider);

                                  ref
                                          .read(
                                            selectedCategoryIdProvider.notifier,
                                          )
                                          .state =
                                      0;
                                  setState(() {
                                    hasMoreData = true;
                                    currentPage = 1;
                                    allProducts.clear();
                                    displayedProducts.clear();
                                    currentFilters = null; // Clear any filters
                                  });
                                },
                                child: CategoryTab(
                                  icon: Icons.grid_view,
                                  label: 'All',
                                  isSelected: selectedCategoryId == 0,
                                ),
                              ),
                              const SizedBox(width: 8),
                              ...categories.map(
                                (cat) => Padding(
                                  padding: const EdgeInsets.only(right: 8),
                                  child: GestureDetector(
                                    onTap: () {
                                      // Clear any potential interference from search/filter providers
                                      ref.invalidate(
                                        blindboxSearchResultsProvider,
                                      );
                                      ref.invalidate(blindboxLoadingProvider);

                                      ref
                                          .read(
                                            selectedCategoryIdProvider.notifier,
                                          )
                                          .state = cat
                                          .categoryId;
                                      setState(() {
                                        hasMoreData = true;
                                        currentPage = 1;
                                        allProducts.clear();
                                        displayedProducts.clear();
                                        currentFilters =
                                            null; // Clear any filters
                                      });
                                    },
                                    child: CategoryTab(
                                      iconAsset: _getCategoryIconAsset(
                                        cat.categoryName,
                                      ),
                                      label: cat.categoryName,
                                      isSelected:
                                          selectedCategoryId == cat.categoryId,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      loading: () => const SizedBox.shrink(),
                      error: (_, _) => const SizedBox.shrink(),
                    ),
                  ],
                ),
              ),
              // Mystery Boxes Grid
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: getResponsivePadding(16, context),
                ),
                child: productsAsync.when(
                  data: (response) {
                    final products =
                        response['products'] as List<BlindboxProductModel>;
                    final filteredProducts = currentFilters != null
                        ? _filterProducts(products, currentFilters!)
                        : products;

                    // Initialize products for pagination if not already done
                    if (allProducts.isEmpty) {
                      allProducts = List.from(filteredProducts);
                      displayedProducts = allProducts.take(pageSize).toList();
                    } else if (currentFilters != null) {
                      // Update products when filters change
                      allProducts = List.from(filteredProducts);
                      displayedProducts = allProducts.take(pageSize).toList();
                    }

                    // Check if we have more data
                    hasMoreData = displayedProducts.length < allProducts.length;

                    if (displayedProducts.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 40),
                            SvgPicture.asset(
                              'assets/icons/Empty Blind Box.svg',
                              width: 160,
                              height: 160,
                              colorFilter: ColorFilter.mode(
                                Colors.grey,
                                BlendMode.srcIn,
                              ),
                            ),
                            const SizedBox(height: 32),
                            const Text(
                              'No products found on this category',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return Column(
                      children: [
                        GridView.builder(
                          gridDelegate:
                              SliverGridDelegateWithMaxCrossAxisExtent(
                                maxCrossAxisExtent: 250,
                                mainAxisSpacing: getResponsivePadding(
                                  12,
                                  context,
                                ),
                                crossAxisSpacing: getResponsivePadding(
                                  12,
                                  context,
                                ),
                                childAspectRatio: 0.53,
                              ),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: displayedProducts.length,
                          itemBuilder: (context, index) {
                            final product = displayedProducts[index];

                            return BlindboxShopCard(
                              title: product.unboxTitle,
                              price:
                                  'RM${product.salePrice.toStringAsFixed(2)}',
                              rating:
                                  product.score, // Use actual score from model
                              soldCount: product.sold,
                              maxSavings:
                                  null, // No max savings in current model
                              imageUrl: product.unboxCover,
                              onTap: () {
                                context.push(
                                  '/blind-box/buy/${product.unboxId}',
                                  extra: {'product': _convertToMap(product)},
                                );
                              },
                            );
                          },
                        ),
                        // Load more indicator
                        if (hasMoreData)
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Center(
                              child: isLoadingMore
                                  ? const CircularProgressIndicator()
                                  : ElevatedButton(
                                      onPressed: _loadMoreData,
                                      child: const Text('Load More'),
                                    ),
                            ),
                          ),
                      ],
                    );
                  },
                  loading: () {
                    return GridView.builder(
                      gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                        maxCrossAxisExtent: 250,
                        mainAxisSpacing: getResponsivePadding(12, context),
                        crossAxisSpacing: getResponsivePadding(12, context),
                        childAspectRatio: 0.53,
                      ),
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: getResponsiveCrossAxisCount(context) * 4,
                      itemBuilder: (context, index) =>
                          const BlindboxShopCardShimmer(),
                    );
                  },
                  error: (error, stack) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.red,
                          ),
                          const SizedBox(height: 16),
                          Text('Error: ${error.toString()}'),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              // ignore: unused-result
                              ref.refresh(
                                blindboxProductsProvider(selectedCategoryId),
                              );
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<BlindboxProductModel> _filterProducts(
    List<BlindboxProductModel> products,
    Map<String, dynamic> filters,
  ) {
    List<BlindboxProductModel> filtered = List.from(products);

    // Filter by price range
    if (filters['minPrice'] != null || filters['maxPrice'] != null) {
      filtered = filtered.where((product) {
        double price = product.salePrice;
        bool minCheck =
            filters['minPrice'] == null || price >= filters['minPrice'];
        bool maxCheck =
            filters['maxPrice'] == null || price <= filters['maxPrice'];
        return minCheck && maxCheck;
      }).toList();
    }

    // Filter by category
    if (filters['category'] != null && filters['category'] != 'All') {
      // Note: You might want to add a category field to your BlindboxProductModel
      // For now, we'll skip category filtering
    }

    // Sort products
    if (filters['sortBy'] != null) {
      switch (filters['sortBy']) {
        case 'Price: Low to High':
          filtered.sort((a, b) => a.salePrice.compareTo(b.salePrice));
          break;
        case 'Price: High to Low':
          filtered.sort((a, b) => b.salePrice.compareTo(a.salePrice));
          break;
        case 'Newest':
          filtered.sort((a, b) => a.sold.compareTo(b.sold));
          break;
        case 'Popularity':
        default:
          filtered.sort((a, b) => b.sold.compareTo(a.sold));
          break;
      }
    }

    return filtered;
  }

  Map<String, dynamic> _convertToMap(BlindboxProductModel product) {
    return {
      'title': product.unboxTitle,
      'price': 'RM${product.salePrice.toStringAsFixed(2)}',
      'rating': product.score,
      'soldCount': product.sold,
      'maxSavings': null,
      'category': 'Mystery Box',
      'imageUrl': product.unboxCover,
      'imageUrls': [product.unboxCover],
      'pointsOffer': 'Use ${product.cardDeduction} card points',
      'variation': 'Random Selection',
      'description':
          'This is a mystery box containing various high-quality items. Each box is carefully curated to provide a unique unboxing experience.',
      'unboxId': product.unboxId,
      'salePrice': product.salePrice,
      'stock': 1, // Default stock for blindbox
      'cardDeduction': product.cardDeduction,
      'isCollect': product.isCollect,
    };
  }
}
