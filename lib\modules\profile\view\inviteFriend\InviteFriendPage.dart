import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/services.dart';
import '../../view-model/InviteFriendViewModel.dart';
import '../../widgets/invitedFriend/InviteQrSection.dart';
import '../../widgets/invitedFriend/SwipeUpIndicator.dart';
import '../../widgets/invitedFriend/InvitedFriendList.dart';

class InviteFriendPage extends StatefulWidget {
  const InviteFriendPage({super.key});

  @override
  State<InviteFriendPage> createState() => _InviteFriendPageState();
}

class _InviteFriendPageState extends State<InviteFriendPage> {
  final DraggableScrollableController _draggableController =
      DraggableScrollableController();
  bool _isExpanded = false;

  @override
  void dispose() {
    _draggableController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final vm = context.watch<InviteFriendViewModel>();
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = screenWidth > 600;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(isTablet ? 90 : 70),
        child: SafeArea(
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios_new,
                    color: Colors.black,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
                IconButton(
                  icon: const Icon(Icons.info_outline, color: Colors.black),
                  onPressed: () => showDialog(
                    context: context,
                    builder: (_) => AlertDialog(
                      title: const Text("Invite Friend Rules"),
                      content: const Text(
                        "Details of how inviting friends works.",
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Image.asset(
              'assets/images/backgroundFriend.png',
              fit: BoxFit.cover,
              height: screenHeight * 0.45,
              alignment: Alignment.topCenter,
            ),
          ),
          Column(
            children: [
              const SizedBox(height: 100),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: !_isExpanded
                    ? const InviteQrSection(key: ValueKey("qr"))
                    : const SizedBox.shrink(key: ValueKey("empty")),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: DraggableScrollableActuator(
                  child: NotificationListener<DraggableScrollableNotification>(
                    onNotification: (notification) {
                      final expanded = notification.extent >= 0.99;
                      if (_isExpanded != expanded) {
                        setState(() => _isExpanded = expanded);
                      }
                      return false;
                    },
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        final initialSize = isTablet ? 0.41 : 0.39;
                        final minSize = isTablet ? 0.41 : 0.39;

                        return DraggableScrollableSheet(
                          controller: _draggableController,
                          initialChildSize: initialSize,
                          minChildSize: minSize,
                          maxChildSize: 1.0,
                          builder: (context, scrollController) {
                            return Container(
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(20),
                                ),
                              ),
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  return SingleChildScrollView(
                                    controller: scrollController,
                                    child: ConstrainedBox(
                                      constraints: BoxConstraints(
                                        minHeight: constraints.maxHeight,
                                      ),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(
                                              top: 4,
                                              bottom: 2,
                                            ),
                                            child: SwipeUpIndicator(
                                              totalInvited:
                                                  vm.totalInvitedCount,
                                              isExpanded: _isExpanded,
                                              onTap: () {
                                                if (_draggableController
                                                    .isAttached) {
                                                  _draggableController
                                                      .animateTo(
                                                        _isExpanded
                                                            ? minSize
                                                            : 1.0,
                                                        duration:
                                                            const Duration(
                                                              milliseconds: 300,
                                                            ),
                                                        curve: Curves.easeOut,
                                                      );
                                                }
                                              },
                                            ),
                                          ),
                                          const SizedBox(height: 2),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 12,
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Center(
                                                  child: Text(
                                                    'Your Referral Code',
                                                    style: Theme.of(
                                                      context,
                                                    ).textTheme.titleMedium,
                                                  ),
                                                ),
                                                const SizedBox(height: 8),
                                                Row(
                                                  children: [
                                                    Expanded(
                                                      child: Container(
                                                        padding:
                                                            const EdgeInsets.symmetric(
                                                              vertical: 8,
                                                              horizontal: 16,
                                                            ),
                                                        decoration: BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                10,
                                                              ),
                                                          color: Colors
                                                              .grey
                                                              .shade100,
                                                        ),
                                                        child: Text(
                                                          vm.referralCode,
                                                          style:
                                                              const TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                              ),
                                                        ),
                                                      ),
                                                    ),
                                                    const SizedBox(width: 12),
                                                    ElevatedButton.icon(
                                                      onPressed: () {
                                                        Clipboard.setData(
                                                          ClipboardData(
                                                            text:
                                                                vm.referralCode,
                                                          ),
                                                        );
                                                        ScaffoldMessenger.of(
                                                          context,
                                                        ).showSnackBar(
                                                          const SnackBar(
                                                            content: Text(
                                                              'Referral code copied',
                                                            ),
                                                          ),
                                                        );
                                                      },
                                                      icon: const Icon(
                                                        Icons.copy,
                                                        size: 18,
                                                      ),
                                                      label: const Text("Copy"),
                                                      style: ElevatedButton.styleFrom(
                                                        minimumSize: const Size(
                                                          100,
                                                          40,
                                                        ),
                                                        backgroundColor:
                                                            Colors.amber,
                                                        foregroundColor:
                                                            Colors.black,
                                                        shape: RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                12,
                                                              ),
                                                        ),
                                                        elevation: 3,
                                                      ),
                                                    ),
                                                    const SizedBox(width: 8),
                                                    ElevatedButton.icon(
                                                      onPressed: () {
                                                        Share.share(
                                                          'Join me using my referral code: ${vm.referralCode}',
                                                        );
                                                      },
                                                      icon: const Icon(
                                                        Icons.share,
                                                        size: 18,
                                                      ),
                                                      label: const Text(
                                                        "Share",
                                                      ),
                                                      style: ElevatedButton.styleFrom(
                                                        minimumSize: const Size(
                                                          100,
                                                          40,
                                                        ),
                                                        backgroundColor:
                                                            Colors.amber,
                                                        foregroundColor:
                                                            Colors.black,
                                                        shape: RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                12,
                                                              ),
                                                        ),
                                                        elevation: 3,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(height: 24),
                                                InvitedFriendList(
                                                  scrollController:
                                                      scrollController,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
