import 'package:flutter/material.dart';
import '../../../data/model/ShopDetailModel.dart';
import '../../../data/service/ShopDetailService.dart';
import '../../shop/widget/AddToCartSheet.dart';

class ShopDetailViewModel extends ChangeNotifier {
  ShopDetailModel? product;
  bool isLoading = true;
  String? error;
  int currentImageIndex = 0;
  
  // Add these for variation selection
  ProductSku? selectedSku;
  Map<String, String> selectedProperties = {};

  // Add reviews properties
  List<ProductReview> reviews = [];
  bool isLoadingReviews = false;
  String? reviewsError;
  int currentReviewPage = 1;
  bool hasMoreReviews = true;

  Future<void> loadProductDetail(String productId) async {
    isLoading = true;
    error = null;
    notifyListeners();

    try {
      print("🔍 ViewModel: Loading product detail for ID: '$productId'");
      
      // Validate product ID before making API call
      if (productId.isEmpty) {
        throw Exception('Product ID is empty');
      }
      
      product = await shopDetailService.fetchProductDetail(productId);
      
      if (product == null) {
        error = "Product not found";
        isLoading = false;
        notifyListeners();
        return;
      }
      
      // Initialize with first SKU by default
      if (product?.productSkus.isNotEmpty == true) {
        selectedSku = product!.productSkus.first;
        _initializeSelectedProperties();
      }

      // Load reviews after product details are loaded (but don't fail if reviews fail)
      try {
        await loadProductReviews(productId);
      } catch (e) {
        print("⚠️ Failed to load reviews, but continuing: $e");
        // Don't set error for reviews failure
      }
      
    } catch (e) {
      print("🚨 ViewModel error: $e");
      error = e.toString();
    }

    isLoading = false;
    notifyListeners();
  }

  // Add method to load reviews
  Future<void> loadProductReviews(String productId, {bool loadMore = false}) async {
    if (isLoadingReviews) return;

    isLoadingReviews = true;
    reviewsError = null;
    
    if (!loadMore) {
      currentReviewPage = 1;
      reviews.clear();
    }
    
    notifyListeners();

    try {
      final response = await shopDetailService.fetchProductReviews(
        productSpuId: productId,
        page: currentReviewPage,
        limit: 10,
      );

      if (loadMore) {
        reviews.addAll(response.data.list);
      } else {
        reviews = response.data.list;
      }

      hasMoreReviews = response.data.currentPage < response.data.totalPage;
      if (hasMoreReviews) {
        currentReviewPage++;
      }
    } catch (e) {
      reviewsError = e.toString();
      print("Error loading reviews: $e");
    }

    isLoadingReviews = false;
    notifyListeners();
  }

  // Add method to load more reviews
  Future<void> loadMoreReviews() async {
    if (product != null && hasMoreReviews) {
      await loadProductReviews(product!.id.toString(), loadMore: true);
    }
  }

  // Add review helper getters
  bool get hasReviews => reviews.isNotEmpty;
  int get reviewCount => reviews.length;
  double get averageRating => hasReviews 
      ? reviews.map((r) => r.score).reduce((a, b) => a + b) / reviews.length 
      : 0.0;

  void _initializeSelectedProperties() {
    selectedProperties.clear();
    if (selectedSku?.properties.isNotEmpty == true) {
      for (var property in selectedSku!.properties) {
        selectedProperties[property.propertyKey] = property.propertyValue;
      }
    }
  }

  // Add method to select variation
  void selectVariation(String propertyKey, String propertyValue) {
    selectedProperties[propertyKey] = propertyValue;
    _updateSelectedSku();
    notifyListeners();
  }

  void _updateSelectedSku() {
    if (product?.productSkus.isEmpty == true) return;
    
    for (var sku in product!.productSkus) {
      bool matches = true;
      
      // Check if this SKU has all the selected properties
      for (var entry in selectedProperties.entries) {
        bool hasProperty = sku.properties.any(
          (prop) => prop.propertyKey == entry.key && prop.propertyValue == entry.value
        );
        if (!hasProperty) {
          matches = false;
          break;
        }
      }
      
      if (matches) {
        selectedSku = sku;
        // Remove the image changing logic - keep images static
        break;
      }
    }
  }

  void onImageChanged(int index) {
    if (index >= 0 && index < productImages.length) {
      currentImageIndex = index;
      notifyListeners();
    }
  }

  void toggleFavorite() async {
    if (product != null) {
      final newFavoriteStatus = !product!.isCollect;
      
      // Optimistically update UI
      product = product!.copyWith(isCollect: newFavoriteStatus);
      notifyListeners();
      
      // Call API to update favorite status
      try {
        bool success;
        if (newFavoriteStatus) {
          success = await shopDetailService.addToFavorites(product!.id.toString());
        } else {
          success = await shopDetailService.removeFromFavorites(product!.id.toString());
        }
        
        if (!success) {
          // Revert if API call failed
          product = product!.copyWith(isCollect: !newFavoriteStatus);
          notifyListeners();
        }
      } catch (e) {
        // Revert if API call failed
        product = product!.copyWith(isCollect: !newFavoriteStatus);
        notifyListeners();
      }
    }
  }

  void onAddToCart(BuildContext context) {
    if (product == null) return;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) => AddToCartSheet(
        product: product!,
        viewModel: this, // Pass the view model to access favorite functionality
        context: "Cart",
      ),
    );
  }

  void onBuyNow(BuildContext context) {
    if (product == null) return;

    // Check if product has multiple SKUs
    final hasMultipleSkus = product!.productSkus.length > 1;

    if (hasMultipleSkus) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
        ),
        builder: (context) => AddToCartSheet(
          product: product!,
          viewModel: this, // Also pass for Buy Now when it shows the sheet
          context: "Buy",
        ),
      );
    } else {
      // Direct to checkout
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Proceeding to checkout'),
          duration: Duration(seconds: 1),
        ),
      );
    }
  }

  void onChatNow(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening chat'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  // Add this method to group variations
  Map<String, List<String>> getVariationGroups() {
    final Map<String, Set<String>> groups = {};
    
    for (var sku in productSkus) {
      if (sku.properties.isEmpty) {
        // If no properties, group by SKU name
        groups[''] = groups[''] ?? <String>{};
        groups['']!.add(sku.productSkuName);
      } else {
        // Group by property key
        for (var property in sku.properties) {
          groups[property.propertyKey] = groups[property.propertyKey] ?? <String>{};
          groups[property.propertyKey]!.add(property.propertyValue);
        }
      }
    }
    
    // Convert Set to List for easier use
    return groups.map((key, value) => MapEntry(key, value.toList()));
  }

  // ✅ Computed properties for proper MVVM
  
  // Product images
  List<String> get productImages => product?.productImages ?? [];
  String get productImage => product?.productImage ?? '';
  
  // Price information
  double get currentPrice => selectedSku?.salePrice ?? 
      (product?.productSkus.isNotEmpty == true ? product!.productSkus.first.salePrice : 0.0);
  
  String get displayPrice => 'RM ${currentPrice.toStringAsFixed(2)}';
  
  // Product information
  String get productName => product?.productName ?? '';
  String get etails => product?.etails ?? '';
  bool get isSale => product?.isSale ?? false;
  bool get isCollect => product?.isCollect ?? false;
  int get cardDeduction => product?.cardDeduction ?? 0;
  int get sold => product?.sold ?? 0;
  
  // Product details
  List<ProductSku> get productSkus => product?.productSkus ?? [];
  List<ProductInfo> get productInfoList => product?.productInfoList ?? [];
  
  // UI states
  bool get hasMultipleImages => productImages.length > 1;
  bool get hasMultipleSkus => productSkus.length > 1;
  bool get hasCardDeduction => cardDeduction > 0;
  bool get hasProductInfo => productInfoList.isNotEmpty;
  bool get hasProductSkus => productSkus.isNotEmpty;
  
  // Display helpers
  String get soldText => '$sold sold';
  String get cardDeductionText => 'Use Cards to get RM$cardDeduction Off';
  
  // Current stock based on selected SKU
  int get currentStock => selectedSku?.stock ?? 
      (product?.productSkus.isNotEmpty == true ? product!.productSkus.first.stock : 0);
  
  // Selected SKU name
  String get selectedSkuName => selectedSku?.productSkuName ?? '';
  
  // Current display image (prioritize selected SKU image)
  String get currentDisplayImage {
    if (productImages.isNotEmpty && currentImageIndex < productImages.length) {
      return productImages[currentImageIndex];
    }
    return productImage;
  }

  // Remove or simplify the allProductImages getter:
  List<String> get allProductImages {
    // Just return the regular product images, no SKU images
    return productImages.isNotEmpty ? productImages : [productImage];
  }
}