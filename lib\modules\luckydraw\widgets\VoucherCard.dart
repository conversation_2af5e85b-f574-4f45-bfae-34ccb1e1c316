import 'package:flutter/material.dart';

class VoucherCard extends StatelessWidget {
  const VoucherCard({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375; // base width 375
    double scaleH(double value) =>
        value * screenHeight / 812; // base height 812
    double scaleText(double value) => value * screenWidth / 375;

    return Card(
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(scaleW(5)),
      ),
      child: Padding(
        padding: EdgeInsets.all(scaleW(12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: scaleW(100),
                  height: scaleH(60),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(scaleW(8)),
                    color: Colors.grey[200],
                  ),
                  child: Image.network(
                    'https://i0.wp.com/ringgitplus.wpcomstaging.com/wp-content/uploads/2019/11/5ca2d589a9789a8d034cbf51.jpg?fit=1200%2C630&ssl=1',
                    fit: BoxFit.contain,
                  ),
                ),
                SizedBox(width: scaleW(12)),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Voucher TNG RM10 Deals (E-Wallet)',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: scaleText(13),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: scaleH(4)),
                      Text(
                        'Vouchers',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: scaleText(13),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Divider(height: scaleH(24)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'RM10.00',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: scaleText(13),
                  ),
                ),
                Text(
                  'x1',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: scaleText(13),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
