import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:luckymall/modules/luckydraw/widgets/LuckyAppBar.dart';
import '../widgets/CategorySelectorWidget.dart';
import '../widgets/PointProductCard.dart';
import '../widgets/SkeletonLoader.dart';
import '../widgets/MegaPrizes.dart';
import '../widgets/PrizeRevealCards.dart';
import '../widgets/OpenSoonCard.dart';
import 'package:luckymall/modules/luckydraw/view-model/PrizeRevealVM.dart';
import 'package:luckymall/modules/luckydraw/view-model/OpenSoonVM.dart';
import 'package:luckymall/modules/luckydraw/view-model/CategoryVM.dart';
import '../model/PrizeRevealProduct.dart';

/// Prize Reveal View with conditional display logic
///
/// This view shows different content based on user's participation status:
/// - If user has participated in lucky draw and waiting for prize reveal: Shows Prize Reveal Cards first, then Mega Prizes
/// - If user hasn't participated: Shows only Mega Prizes at the top
///
/// To test the functionality:
/// 1. Initially, only Mega Prizes will be visible (hasParticipatedInLuckyDraw = false)
/// 2. Tap "Join" on any Mega Prize to simulate participation
/// 3. Prize Reveal Cards will appear above Mega Prizes
/// 4. The countdown timers will start automatically
class PrizeRevealView extends StatefulWidget {
  const PrizeRevealView({super.key});

  @override
  State<PrizeRevealView> createState() => _PrizeRevealViewState();
}

class _PrizeRevealViewState extends State<PrizeRevealView> {
  String selectedCategory = 'ALL';
  int minPointFilter = 0;
  int maxPointFilter = 1000;

  // Loading state for point products
  bool isPointProductsLoading = true;

  // Pagination state
  bool _isLoadingMore = false;
  bool _hasMoreProducts = true;
  int _currentPage = 1;
  static const int _pageSize = 10;

  // Prize reveal state - whether user has participated and waiting for reveal
  // moved to VM

  // Cache filtered products to avoid recalculation
  List<Map<String, dynamic>> _cachedFilteredProducts = [];
  String _lastFilterKey = '';

  // View model for opening soon products
  late final OpenSoonVM _openSoonVM;

  @override
  void initState() {
    super.initState();
    _openSoonVM = OpenSoonVM();
    _openSoonVM.addListener(_onOpenSoonVMChanged);
    _openSoonVM.fetchOpeningSoonProducts();
  }

  @override
  void dispose() {
    _openSoonVM.removeListener(_onOpenSoonVMChanged);
    super.dispose();
  }

  void _onOpenSoonVMChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  // Load more products when user scrolls near the bottom
  Future<void> _loadMoreProducts() async {
    if (_isLoadingMore || !_hasMoreProducts) {
      print(
        'PrizeRevealView: Skipping load more - isLoadingMore: $_isLoadingMore, hasMoreProducts: $_hasMoreProducts',
      );
      return;
    }

    print(
      'PrizeRevealView: Loading more products... Current page: $_currentPage',
    );
    setState(() {
      _isLoadingMore = true;
    });

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Get all filtered products from ViewModel
      final int minPts = minPointFilter;
      final int maxPts = maxPointFilter;

      // We need to get the products from the current VM context
      // This will be handled in the build method when we have access to the VM
      print('PrizeRevealView: Load more triggered for page $_currentPage');

      // For now, we'll just increment the page and update the cached products
      // In a real implementation, you would fetch more products from the API
      _currentPage++;

      // Update the cached products to show more
      // This is a simplified implementation - in practice you'd fetch from API
      print('PrizeRevealView: Loaded page $_currentPage');
    } catch (e) {
      print('Error loading more products: $e');
      // Reset loading state on error
      _isLoadingMore = false;
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  // Reset pagination when filters change
  void _resetPagination() {
    _currentPage = 1;
    _hasMoreProducts = true;
    _cachedFilteredProducts.clear();
  }

  // Handle filter changes and reset pagination
  void _handleFilterChange() {
    print('PrizeRevealView: Filter changed, resetting pagination');
    _resetPagination();
  }

  // Check if user has participated in lucky draw and waiting for prize reveal
  // handled in VM

  // Method to simulate participating in a lucky draw (for testing)
  // handled via VM

  // Update cached filtered products when filters change or when new data arrives
  void _updateFilteredProducts(List<dynamic> allProducts) {
    final filterKey = '$selectedCategory-$minPointFilter-$maxPointFilter';

    // Always update if we have products, regardless of filter key
    if (_lastFilterKey != filterKey || _cachedFilteredProducts.isEmpty) {
      print(
        'PrizeRevealView: Updating filtered products. ViewModel has ${allProducts.length} products',
      );
      print('PrizeRevealView: ViewModel products: $allProducts');

      // Reset pagination when filters change
      _resetPagination();

      final allFilteredProducts = allProducts
          .where((product) {
            // Note: Since we removed category and pointValue from the model,
            // we'll need to implement category and point filtering through the API
            // For now, we'll show all products and implement proper filtering later
            return true;
          })
          .map((product) {
            // Convert LuckyDrawProduct to Map format for compatibility
            if (product.runtimeType.toString().contains('LuckyDrawProduct')) {
              return <String, dynamic>{
                'id': product.id,
                'productSpuId': product.productSpuId,
                'productSkuId': product.productSkuId,
                'productSkuName': product.productSkuName,
                'productSkuImage': product.productSkuImage,
                'usedAmount': product.usedAmount,
                'totalAmount': product.totalAmount,
                'score': product.score,
                'sold': product.sold,
              };
            }
            return product as Map<String, dynamic>;
          })
          .toList();

      // Load first page of products
      final endIndex = (_currentPage * _pageSize).clamp(
        0,
        allFilteredProducts.length,
      );
      _cachedFilteredProducts = allFilteredProducts.take(endIndex).toList();

      // Update hasMoreProducts flag
      _hasMoreProducts =
          _cachedFilteredProducts.length < allFilteredProducts.length;

      print(
        'PrizeRevealView: Converted ${_cachedFilteredProducts.length} products to Map format (page $_currentPage)',
      );
      print('PrizeRevealView: Final cached products: $_cachedFilteredProducts');
      _lastFilterKey = filterKey;
    } else {
      print(
        'PrizeRevealView: Skipping update - same filter key and products already cached',
      );
    }
  }

  // Build Opening Soon Section
  Widget _buildOpeningSoonSection() {
    if (_openSoonVM.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFFBF00)),
        ),
      );
    }

    if (_openSoonVM.errorMessage != null) {
      return Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red.shade200),
        ),
        child: Column(
          children: [
            Icon(Icons.error_outline, color: Colors.red.shade400, size: 24),
            const SizedBox(height: 8),
            Text(
              _openSoonVM.errorMessage!,
              style: TextStyle(color: Colors.red.shade700),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => _openSoonVM.refresh(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // Always show the OpenSoonCard container, but handle empty state inside
    return OpenSoonCard(
      products: _openSoonVM.products.isEmpty
          ? []
          : _openSoonVM.productsByProgress,
      onProductTap: _openSoonVM.products.isEmpty
          ? null
          : () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Row(
                    children: [
                      Icon(Icons.schedule, color: Colors.white, size: 20),
                      SizedBox(width: 8),
                      Text(
                        'This product will be available soon!',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  backgroundColor: const Color(0xFFFFBF00),
                  duration: const Duration(seconds: 2),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  margin: const EdgeInsets.all(16),
                ),
              );
            },
    );
  }

  // Map prize reveal products to actual product data indices
  static const Map<String, int> _prizeProductMapping = {
    'gaming_mouse_razer_001': 1, // Maps to Gaming Mouse Razer DeathAdder V3
    'vacuum_s8000_002':
        5, // Maps to TNG eWallet RM50 Voucher (using as placeholder)
    'smartphone_kingpinix_003': 0, // Maps to Smartphone KingPinix 128GB
  };

  // Get full product data with updated batch number
  // This ensures we pass complete product data (including reviews, ratings, etc.)
  // but only change the batch number to avoid stacking/overlapping issues
  Map<String, dynamic> _getProductDataWithBatch(
    String prizeProductId,
    int batchNumber,
    List<dynamic> allProducts,
  ) {
    final productIndex = _prizeProductMapping[prizeProductId];
    if (productIndex != null && productIndex < allProducts.length) {
      // Create a deep copy of the product data to avoid modifying original
      final originalProduct = Map<String, dynamic>.from(
        allProducts[productIndex] is Map
            ? allProducts[productIndex]
            : {
                'id': allProducts[productIndex].id,
                'productSpuId': allProducts[productIndex].productSpuId,
                'productSkuId': allProducts[productIndex].productSkuId,
                'productSkuName': allProducts[productIndex].productSkuName,
                'productSkuImage': allProducts[productIndex].productSkuImage,
                'usedAmount': allProducts[productIndex].usedAmount,
                'totalAmount': allProducts[productIndex].totalAmount,
                'score': allProducts[productIndex].score,
                'sold': allProducts[productIndex].sold,
              },
      );

      // Add batch information to the product data
      originalProduct['batchNumber'] = batchNumber;
      originalProduct['prizeProductId'] = prizeProductId;

      // Log for debugging (can be removed in production)
      print(
        'Mapped product ${prizeProductId} to batch ${batchNumber}: ${originalProduct['productSkuName']}',
      );

      return originalProduct;
    }

    // Fallback: return first product with batch info if mapping fails
    if (allProducts.isNotEmpty) {
      final fallbackProduct = allProducts[0] is Map
          ? Map<String, dynamic>.from(allProducts[0])
          : {
              'id': allProducts[0].id,
              'productSpuId': allProducts[0].productSpuId,
              'productSkuId': allProducts[0].productSkuId,
              'productSkuName': allProducts[0].productSkuName,
              'productSkuImage': allProducts[0].productSkuImage,
              'usedAmount': allProducts[0].usedAmount,
              'totalAmount': allProducts[0].totalAmount,
              'score': allProducts[0].score,
              'sold': allProducts[0].sold,
            };
      fallbackProduct['batchNumber'] = batchNumber;
      fallbackProduct['prizeProductId'] = prizeProductId;
      return fallbackProduct;
    }

    // Return empty product if no products available
    return {
      'batchNumber': batchNumber,
      'prizeProductId': prizeProductId,
      'productSkuName': 'Unknown Product',
    };
  }

  // Build Prize Reveal Section
  Widget _buildPrizeRevealSection(List<dynamic> allProducts, PrizeRevealVM vm) {
    // Use real data from the view model
    final List<PrizeRevealProduct> prizeRevealProducts = vm.prizeRevealProducts;

    // Show loading state
    if (vm.isPrizeRevealLoading) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        height: 220,
        child: const Center(
          child: CircularProgressIndicator(color: Color(0xFFFFBF00)),
        ),
      );
    }

    // Show "No Prize Reveal for now" message if no products
    if (prizeRevealProducts.isEmpty) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        child: Stack(
          children: [
            // Main yellow container
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: const Color(0xFFFFBF00),
                border: Border.all(color: const Color(0xFFFFD556), width: 5),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Icon(
                              Icons.card_giftcard_outlined,
                              size: 48,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No Prize Reveal for now',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey[700],
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Check back later for exciting prize reveals!',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // "Prize Reveal" banner (overlapping)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFD700), // Dark yellow
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        offset: const Offset(0, 2),
                        blurRadius: 4,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: const Text(
                    'Prize Reveal',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return PrizeRevealCards(
      products: prizeRevealProducts,
      onProductTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.timer, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Text(
                  'Prize reveal countdown in progress!',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
              ],
            ),
            backgroundColor: const Color(0xFFFFBF00),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      },
      onJoinNextBatch: (product) {
        // Navigate to product details with next batch number
        final nextBatch = product.currentBatch + 1;

        // Get complete product data with the new batch number
        final completeProductData = _getProductDataWithBatch(
          product.productId.toString(),
          nextBatch,
          allProducts,
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.group_add, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Joining next batch (#$nextBatch) for ${product.productName}',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: const Color(0xFFFFBF00),
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );

        // Navigate to product details page with complete product data including new batch number
        // This ensures no stacking/overlapping issues as we pass all the original data
        context.push('/lucky-draw/product-detail', extra: completeProductData);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [ChangeNotifierProvider(create: (context) => CategoryVM())],
      child: Scaffold(appBar: const LuckyAppBar(), body: _buildBody()),
    );
  }

  Widget _buildBody() {
    return ChangeNotifierProvider(
      create: (_) => PrizeRevealVM()..initialize(),
      child: Consumer<PrizeRevealVM>(
        builder: (context, vm, _) {
          isPointProductsLoading = vm.isPointProductsLoading;

          // Debug: Print ViewModel data
          print('PrizeRevealView: Consumer builder called');
          print(
            'PrizeRevealView: ViewModel has ${vm.allProducts.length} products',
          );
          print('PrizeRevealView: ViewModel products: ${vm.allProducts}');
          print(
            'PrizeRevealView: ViewModel loading state: ${vm.isPointProductsLoading}',
          );

          // Update filtered products when ViewModel data changes
          _updateFilteredProducts(vm.allProducts);

          // Force update if we have products but cached is empty
          if (vm.allProducts.isNotEmpty && _cachedFilteredProducts.isEmpty) {
            print(
              'PrizeRevealView: Force updating filtered products due to mismatch',
            );
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                _updateFilteredProducts(vm.allProducts);
                setState(() {});
              }
            });
          }

          return Scaffold(
            backgroundColor: Colors.white,
            body: RefreshIndicator(
              onRefresh: () async {
                await vm.refreshAll();
                // Reset pagination when refreshing
                _resetPagination();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Row(
                        children: [
                          Icon(Icons.refresh, color: Colors.white, size: 20),
                          SizedBox(width: 8),
                          Text(
                            'Content refreshed successfully!',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      backgroundColor: const Color(0xFFFFBF00),
                      duration: const Duration(seconds: 2),
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      margin: const EdgeInsets.all(16),
                    ),
                  );
                }
                return Future.value();
              },
              color: const Color(0xFFFFBF00),
              backgroundColor: Colors.white,
              strokeWidth: 3.0,
              displacement: 50.0,
              child: NotificationListener<ScrollNotification>(
                onNotification: (ScrollNotification scrollInfo) {
                  if (scrollInfo is ScrollEndNotification) {
                    // Check if we're near the bottom
                    if (scrollInfo.metrics.pixels >=
                        scrollInfo.metrics.maxScrollExtent - 300) {
                      print(
                        'PrizeRevealView: Scroll notification - near bottom, triggering pagination',
                      );
                      _loadMoreProducts();
                    }
                  }
                  return false;
                },
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Search Bar and gradient area
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [Color(0xFFFFD54F), Color(0xFFFFFFFF)],
                          ),
                        ),
                        child: Column(
                          children: [
                            // Search Bar at the very top - matching shop UI style
                            Container(
                              padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
                              child: GestureDetector(
                                onTap: () {
                                  context.push(
                                    '/lucky-draw/search',
                                    extra: vm.allProducts,
                                  );
                                },
                                child: Container(
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      const Padding(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 12,
                                        ),
                                        child: Icon(
                                          Icons.search,
                                          color: Colors.grey,
                                          size: 20,
                                        ),
                                      ),
                                      const Expanded(
                                        child: Text(
                                          'Search Products',
                                          style: TextStyle(
                                            color: Colors.grey,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ),
                                      Container(
                                        height: 32,
                                        margin: const EdgeInsets.symmetric(
                                          vertical: 2,
                                          horizontal: 4,
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 18,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.red[700],
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: const Center(
                                          child: Text(
                                            'Search',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 30),
                          ],
                        ),
                      ),

                      // Prize Reveal Section - always show for all users
                      _buildPrizeRevealSection(vm.allProducts, vm),
                      const SizedBox(height: 10),

                      // Mega Prizes Widget - always show, but position depends on prize reveal visibility
                      vm.isMegaPrizesLoading
                          ? const MegaPrizesShimmer()
                          : Column(
                              children: [
                                MegaPrizes(
                                  prizes: vm.megaPrizes,
                                  onPrizeJoin: () {
                                    // This callback is now handled internally by MegaPrizes widget
                                    // It will navigate to product details and show appropriate messages
                                    vm.simulateParticipate();
                                  },
                                ),
                              ],
                            ),
                      const SizedBox(height: 12),

                      // Opening Soon Section
                      isPointProductsLoading
                          ? const OpenSoonCardShimmer()
                          : _buildOpeningSoonSection(),

                      const SizedBox(height: 20),

                      const SizedBox(height: 20),

                      // Divider
                      Divider(
                        color: Color(0xFFffbf00),
                        thickness: 3,
                        indent: 20,
                        endIndent: 20,
                      ),
                      const SizedBox(height: 30),

                      Center(
                        child: Text(
                          'Point Products',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Category Selector Widget
                      CategorySelectorWidget(
                        initialCategory: selectedCategory,
                        onCategoryChanged: (category) {
                          setState(() {
                            selectedCategory = category;
                          });
                          vm.setCategory(category);
                          // Reset pagination when category changes
                          _handleFilterChange();
                          print('Selected category: $category');
                        },
                        onPointRangeChanged: (min, max) {
                          setState(() {
                            minPointFilter = min;
                            maxPointFilter = max;
                          });
                          vm.setPointRange(min, max);
                          // Reset pagination when point range changes
                          _handleFilterChange();
                          print('Point range changed: $min - $max');
                        },
                      ),

                      const SizedBox(height: 16),

                      // Filter Results Info
                      if (!isPointProductsLoading)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  'Showing ${_cachedFilteredProducts.isNotEmpty ? _cachedFilteredProducts.length : vm.allProducts.length} product${(_cachedFilteredProducts.isNotEmpty ? _cachedFilteredProducts.length : vm.allProducts.length) != 1 ? 's' : ''}',
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black54,
                                  ),
                                ),
                              ),
                              Row(
                                children: [
                                  if (_hasMoreProducts)
                                    Text(
                                      'Page $_currentPage',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  if (_hasMoreProducts && !_isLoadingMore)
                                    TextButton(
                                      onPressed: _loadMoreProducts,
                                      child: Text(
                                        'Load More',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: const Color(0xFFFFBF00),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ),
                        ),

                      const SizedBox(height: 16),

                      // Point Products Grid
                      Column(
                        children: [
                          // Debug info and refresh button
                          if (_cachedFilteredProducts.isEmpty)
                            Container(
                              padding: const EdgeInsets.all(16),
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.orange[100],
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.orange),
                              ),
                              child: Column(
                                children: [
                                  Text(
                                    'No products found. Products count: ${vm.allProducts.length}',
                                    style: TextStyle(color: Colors.orange[800]),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Cached products count: ${_cachedFilteredProducts.length}',
                                    style: TextStyle(color: Colors.orange[800]),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Last filter key: $_lastFilterKey',
                                    style: TextStyle(color: Colors.orange[800]),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Current page: $_currentPage, Page size: $_pageSize',
                                    style: TextStyle(
                                      color: Colors.orange[700],
                                      fontSize: 12,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Has more products: $_hasMoreProducts',
                                    style: TextStyle(
                                      color: Colors.orange[700],
                                      fontSize: 12,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  ElevatedButton(
                                    onPressed: () async {
                                      print(
                                        'PrizeRevealView: Manual refresh button pressed',
                                      );
                                      // Reset pagination before refreshing
                                      _resetPagination();
                                      await vm.refreshAll();
                                      if (mounted) {
                                        _updateFilteredProducts(vm.allProducts);
                                        setState(() {});
                                      }
                                    },
                                    child: const Text('Refresh All'),
                                  ),
                                  const SizedBox(height: 8),
                                  ElevatedButton(
                                    onPressed: () {
                                      print(
                                        'PrizeRevealView: Force update filtered products',
                                      );
                                      _updateFilteredProducts(vm.allProducts);
                                      setState(() {});
                                    },
                                    child: const Text('Force Update'),
                                  ),
                                  const SizedBox(height: 8),
                                  ElevatedButton(
                                    onPressed: _loadMoreProducts,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue[100],
                                      foregroundColor: Colors.blue[800],
                                    ),
                                    child: const Text('Load More'),
                                  ),
                                ],
                              ),
                            ),

                          // Products Grid
                          isPointProductsLoading
                              ? const PointProductGridSkeleton()
                              : Builder(
                                  builder: (context) {
                                    // Use cached products if available, otherwise fallback to ViewModel products
                                    final productsToShow =
                                        _cachedFilteredProducts.isNotEmpty
                                        ? _cachedFilteredProducts
                                        : vm.allProducts
                                              .map(
                                                (product) => <String, dynamic>{
                                                  'id': product.id,
                                                  'productSpuId':
                                                      product.productSpuId,
                                                  'productSkuId':
                                                      product.productSkuId,
                                                  'productSkuName':
                                                      product.productSkuName,
                                                  'productSkuImage':
                                                      product.productSkuImage,
                                                  'usedAmount':
                                                      product.usedAmount,
                                                  'totalAmount':
                                                      product.totalAmount,
                                                  'score': product.score,
                                                  'sold': product.sold,
                                                },
                                              )
                                              .toList();

                                    print(
                                      'PrizeRevealView: Rendering PointProductGrid with ${productsToShow.length} products',
                                    );
                                    print(
                                      'PrizeRevealView: Products data: $productsToShow',
                                    );
                                    return PointProductGrid(
                                      products: productsToShow,
                                      isLoadingMore: _isLoadingMore,
                                      hasMoreProducts: _hasMoreProducts,
                                      onProductTap: (product) {
                                        // For now, we'll use a simple navigation - in a real app, you'd pass product data
                                        context.push(
                                          '/lucky-draw/product-detail',
                                          extra: product,
                                        );
                                      },
                                    );
                                  },
                                ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
