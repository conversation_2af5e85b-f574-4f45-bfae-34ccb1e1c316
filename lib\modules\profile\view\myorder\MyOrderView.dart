import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import '../../view-model/MyOrderVM.dart';
import '../../widgets/OrderCard.dart';
import '../../widgets/CancelOrderModal.dart';
import '../../model/MyOrderModel.dart';
import '../../../../router/navigation_helper.dart';
import 'package:go_router/go_router.dart';

class MyOrderView extends StatefulWidget {
  const MyOrderView({super.key});

  @override
  State<MyOrderView> createState() => _MyOrderViewState();
}

class _MyOrderViewState extends State<MyOrderView>
    with TickerProviderStateMixin {
  late MyOrderVM _orderVM;
  late TabController _tabController;

  // List of all statuses including null for "All"
  final List<int?> _tabStatuses = [
    null, // All
    0, // To Pay
    10, // To Ship
    20, // To Receive
    100, // Completed
    -1, // Cancelled
  ];

  @override
  void initState() {
    super.initState();
    _orderVM = MyOrderVM();
    _tabController = TabController(length: _tabStatuses.length, vsync: this);
    _tabController.addListener(_onTabChanged);

    // Initialize with the first tab status
    _orderVM.selectStatus(_tabStatuses[0]);
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _orderVM.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    // Update selected status when tab changes (either during or after animation)
    final status = _tabStatuses[_tabController.index];
    _orderVM.selectStatus(status);
  }

  // Helper method to get orders for a specific status
  List<MyOrderModel> _getOrdersForStatus(MyOrderVM vm, int? status) {
    if (status == null) {
      // Return all orders
      return List.from(vm.allOrders)..sort((a, b) {
        if (a.createdTime == null || b.createdTime == null) return 0;
        return b.createdTime!.compareTo(a.createdTime!);
      });
    } else {
      // Return orders filtered by status
      return vm.allOrders.where((order) => order.state == status).toList()
        ..sort((a, b) {
          if (a.createdTime == null || b.createdTime == null) return 0;
          return b.createdTime!.compareTo(a.createdTime!);
        });
    }
  }

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  void _handleOrderTap(BuildContext context, MyOrderModel order) {
    // Navigate based on order status
    switch (order.state) {
      case 0: // To Pay
        NavigationHelper.goToCheckout(context, order);
        break;
      case 10: // To Ship
        NavigationHelper.goToToShip(context, order);
        break;
      case 20: // To Receive
        NavigationHelper.goToReceive(context, order);
        break;
      case -1: // Cancelled
        NavigationHelper.goToCancelledOrderDetails(context, order);
        break;
      case 100: // Completed
        NavigationHelper.goToCompletedOrderDetails(context, order);
        break;
      default:
        // TODO: Navigate to order detail view for other statuses
        debugPrint('Navigate to order detail: ${order.id}');
        break;
    }
  }

  Future<void> _handleCancelOrder(
    BuildContext context,
    MyOrderVM vm,
    MyOrderModel order,
  ) async {
    final result = await CancelOrderModal.show(context: context, order: order);

    if (result == true && context.mounted) {
      final success = await vm.cancelOrder(order.id!);
      if (success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Order cancelled successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(vm.errorMessage ?? 'Failed to cancel order'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handlePayNow(BuildContext context, MyOrderVM vm, MyOrderModel order) {
    vm.handlePayment(order.id!);
    // Navigate to checkout page
    NavigationHelper.goToCheckout(context, order);
  }

  void _handleBuyAgain(BuildContext context, MyOrderVM vm, MyOrderModel order) {
    vm.handleBuyAgain(order);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Added ${order.orderItems?.first.productSkuName ?? "product"} to cart',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleHelpSupport(
    BuildContext context,
    MyOrderVM vm,
    MyOrderModel order,
  ) {
    vm.handleHelpSupport(order.id!);
    // TODO: Navigate to help/support page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Redirecting to customer support'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _handleRate(BuildContext context, MyOrderVM vm, MyOrderModel order) {
    vm.handleRate(order.id!);
    // TODO: Navigate to rating page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening rating page'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _handleShare(BuildContext context, MyOrderVM vm, MyOrderModel order) {
    vm.handleShare(order);
    // Navigate to SharePostView
    NavigationHelper.goToSharePost(
      context,
      orderType: _getOrderTypeDisplayName(order),
      orderId: order.id.toString(),
    );
  }

  Future<void> _handleReturnRefund(
    BuildContext context,
    MyOrderVM vm,
    MyOrderModel order,
  ) async {
    // Navigate to RefundView
    NavigationHelper.goToRefund(context, order);
  }

  String _getOrderTypeDisplayName(MyOrderModel order) {
    switch (order.source) {
      case 0:
        return 'Mall Order';
      case 1:
        return 'Card Mailing';
      case 10:
        return 'Points Mall Order';
      case 30:
        return 'Group Purchase Order';
      default:
        return 'Order';
    }
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(getResponsivePadding(32, context)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: getResponsivePadding(64, context),
              color: Colors.grey.shade400,
            ),
            SizedBox(height: getResponsivePadding(16, context)),
            Text(
              'No Orders Found',
              style: TextStyle(
                fontSize: getResponsiveFontSize(18, context),
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
            SizedBox(height: getResponsivePadding(8, context)),
            Text(
              'You haven\'t placed any orders yet.\nStart shopping to see your orders here!',
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: Colors.grey.shade500,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String error) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(getResponsivePadding(32, context)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: getResponsivePadding(64, context),
              color: Colors.red.shade400,
            ),
            SizedBox(height: getResponsivePadding(16, context)),
            Text(
              'Error Loading Orders',
              style: TextStyle(
                fontSize: getResponsiveFontSize(18, context),
                fontWeight: FontWeight.w600,
                color: Colors.red.shade600,
              ),
            ),
            SizedBox(height: getResponsivePadding(8, context)),
            Text(
              error,
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: Colors.grey.shade600,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: getResponsivePadding(24, context)),
            ElevatedButton(
              onPressed: () {
                final vm = Provider.of<MyOrderVM>(context, listen: false);
                vm.refreshOrders();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFCD255),
                foregroundColor: Colors.black,
                padding: EdgeInsets.symmetric(
                  horizontal: getResponsivePadding(24, context),
                  vertical: getResponsivePadding(12, context),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Retry',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: getResponsivePadding(8, context)),
      itemCount: 5, // Show 5 shimmer cards
      itemBuilder: (context, index) {
        return _buildShimmerOrderCard(context);
      },
    );
  }

  Widget _buildShimmerOrderCard(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(16, context),
        vertical: getResponsivePadding(8, context),
      ),
      elevation: 1,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200, width: 1),
      ),
      child: Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Padding(
          padding: EdgeInsets.all(getResponsivePadding(16, context)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header badges shimmer
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: getResponsivePadding(80, context),
                        height: getResponsivePadding(24, context),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      SizedBox(width: getResponsivePadding(8, context)),
                      Container(
                        width: getResponsivePadding(60, context),
                        height: getResponsivePadding(24, context),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    width: getResponsivePadding(50, context),
                    height: getResponsivePadding(24, context),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ],
              ),

              SizedBox(height: getResponsivePadding(12, context)),

              // Product section shimmer
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product image shimmer
                  Container(
                    width: getResponsivePadding(60, context),
                    height: getResponsivePadding(60, context),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),

                  SizedBox(width: getResponsivePadding(12, context)),

                  // Product details shimmer
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: double.infinity,
                          height: getResponsivePadding(16, context),
                          color: Colors.white,
                        ),
                        SizedBox(height: getResponsivePadding(4, context)),
                        Container(
                          width: getResponsivePadding(120, context),
                          height: getResponsivePadding(14, context),
                          color: Colors.white,
                        ),
                        SizedBox(height: getResponsivePadding(4, context)),
                        Container(
                          width: getResponsivePadding(100, context),
                          height: getResponsivePadding(12, context),
                          color: Colors.white,
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              SizedBox(height: getResponsivePadding(12, context)),

              // Total price shimmer
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: getResponsivePadding(40, context),
                    height: getResponsivePadding(16, context),
                    color: Colors.white,
                  ),
                  Container(
                    width: getResponsivePadding(80, context),
                    height: getResponsivePadding(18, context),
                    color: Colors.white,
                  ),
                ],
              ),

              SizedBox(height: getResponsivePadding(16, context)),

              // Action buttons shimmer
              Row(
                children: [
                  Expanded(
                    child: Container(
                      height: getResponsivePadding(36, context),
                      margin: EdgeInsets.only(
                        right: getResponsivePadding(8, context),
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                      height: getResponsivePadding(36, context),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _orderVM,
      child: DefaultTabController(
        length: _tabStatuses.length,
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            centerTitle: true,
            backgroundColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 0,
            title: Text(
              'My Orders',
              style: TextStyle(
                fontSize: getResponsiveFontSize(18, context),
                fontWeight: FontWeight.w700,
                color: Colors.black,
              ),
            ),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black87),
              onPressed: () => context.pop(),
            ),
            bottom: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              indicatorColor: const Color(0xFFFFBF00),
              indicatorWeight: 3,
              labelColor: Colors.black,
              unselectedLabelColor: Colors.grey.shade600,
              labelStyle: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                fontWeight: FontWeight.w500,
              ),
              labelPadding: EdgeInsets.symmetric(
                horizontal: getResponsivePadding(8, context),
              ),
              tabs: [
                SizedBox(
                  width: getResponsivePadding(80, context),
                  child: const Tab(text: 'All'),
                ),
                SizedBox(
                  width: getResponsivePadding(80, context),
                  child: const Tab(text: 'To Pay'),
                ),
                SizedBox(
                  width: getResponsivePadding(80, context),
                  child: const Tab(text: 'To Ship'),
                ),
                SizedBox(
                  width: getResponsivePadding(80, context),
                  child: const Tab(text: 'To Receive'),
                ),
                SizedBox(
                  width: getResponsivePadding(80, context),
                  child: const Tab(text: 'Completed'),
                ),
                SizedBox(
                  width: getResponsivePadding(80, context),
                  child: const Tab(text: 'Cancelled'),
                ),
              ],
            ),
          ),
          body: Consumer<MyOrderVM>(
            builder: (context, vm, _) {
              return SafeArea(
                child: TabBarView(
                  controller: _tabController,
                  children: _tabStatuses.map((status) {
                    final statusOrders = _getOrdersForStatus(vm, status);

                    return vm.isLoading && vm.allOrders.isEmpty
                        ? _buildLoadingState(context)
                        : vm.errorMessage != null && vm.allOrders.isEmpty
                            ? _buildErrorState(context, vm.errorMessage!)
                            : statusOrders.isEmpty
                                ? _buildEmptyState(context)
                                : RefreshIndicator(
                                    color: const Color(0xFFFCD255),
                                    onRefresh: vm.refreshOrders,
                                    child: ListView.builder(
                                      padding: EdgeInsets.symmetric(
                                        vertical: getResponsivePadding(8, context),
                                      ),
                                      itemCount: statusOrders.length,
                                      itemBuilder: (context, index) {
                                        final order = statusOrders[index];
                                        return OrderCard(
                                          order: order,
                                          onTap: () => _handleOrderTap(context, order),
                                          onCancel: _canCancelOrder(order)
                                              ? () => _handleCancelOrder(context, vm, order)
                                              : null,
                                          onPay: order.state == 0
                                              ? () => _handlePayNow(context, vm, order)
                                              : null,
                                          onBuyAgain: () =>
                                              _handleBuyAgain(context, vm, order),
                                          onHelpSupport: () =>
                                              _handleHelpSupport(context, vm, order),
                                          onRate: _canRateOrder(order)
                                              ? () => _handleRate(context, vm, order)
                                              : null,
                                          onShare: _canShareOrder(order)
                                              ? () => _handleShare(context, vm, order)
                                              : null,
                                          onReturnRefund: order.state == 100
                                              ? () => _handleReturnRefund(context, vm, order)
                                              : null,
                                        );
                                      },
                                    ),
                                  );
                  }).toList(),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  bool _canCancelOrder(MyOrderModel order) {
    // Can cancel if order is in "To Pay" status
    return order.state == 0;
  }

  bool _canRateOrder(MyOrderModel order) {
    // Can rate if order is completed and not yet rated
    return order.state == 100 && order.isAppraise == true;
  }

  bool _canShareOrder(MyOrderModel order) {
    // Can share if order is completed
    return order.state == 100;
  }
}
