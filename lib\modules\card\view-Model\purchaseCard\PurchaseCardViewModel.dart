import 'package:flutter/material.dart';
import '../../model/purchaseCard/PurchaseCardModel.dart';
import '../cardPack/CardCategoryViewModel.dart';
import '../../services/SeriesService.dart';
import '../../model/SeriesContent.dart';

class PurchaseCardViewModel extends ChangeNotifier {
  final CardCategoryViewModel cardCategoryViewModel;
  final SeriesService seriesService;

  PurchaseCardModel? _card;
  bool _isLoading = true;
  SeriesContent? _currentSeriesContent;

  PurchaseCardModel? get card => _card;
  bool get isLoading => _isLoading;

  PurchaseCardViewModel({
    required this.cardCategoryViewModel,
    required this.seriesService,
  });

  // Fetch initial card data (defaults to fantasy series)
  Future<void> fetchPurchaseCardData({String initialSeries = 'fantasy'}) async {
    _isLoading = true;
    notifyListeners();

    _currentSeriesContent = await seriesService.getSeriesContent(initialSeries);

    final String initialCategory =
        _currentSeriesContent!.categories.isNotEmpty
            ? _currentSeriesContent!.categories.first.key
            : '';

    _card = _buildCardFromSeries(
      _currentSeriesContent!,
      category: initialCategory,
    );

    _isLoading = false;
    notifyListeners();
  }

  // Update card when series or category changes
  Future<void> updateSeriesAndCategory(String series, String category) async {
    _isLoading = true;
    notifyListeners();

    _currentSeriesContent = await seriesService.getSeriesContent(series);

    // fall back to first available category if empty
    final String catKey = (category.isNotEmpty)
        ? category
        : (_currentSeriesContent!.categories.isNotEmpty
            ? _currentSeriesContent!.categories.first.key
            : '');

    _card = _buildCardFromSeries(
      _currentSeriesContent!,
      category: catKey,
    );

    _isLoading = false;
    notifyListeners();
  }

  // Centralized card builder with series-specific quantity rules only
  PurchaseCardModel _buildCardFromSeries(
    SeriesContent seriesContent, {
    required String category,
  }) {
    final sName = seriesContent.name.toLowerCase();

    // ✅ global minimum is 5
    const int minQ = 5;

    // ✅ fantasy step=1, zodiac step=5
    final int stepQ = (sName == 'zodiac') ? 5 : 1;

    return PurchaseCardModel(
      seriesName: seriesContent.name,
      imageUrls: seriesContent.imageUrls,
      category: category,
      description: seriesContent.description, // unchanged
      minQuantity: minQ,
      stepQuantity: stepQ,
    );
  }
}
