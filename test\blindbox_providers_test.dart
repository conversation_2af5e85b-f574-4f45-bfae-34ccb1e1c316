import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luckymall/modules/blindbox/providers/blindbox_providers.dart';

void main() {
  group('Blindbox Providers Tests', () {
    testWidgets('selectedCategoryIdProvider should default to 0', (
      tester,
    ) async {
      await tester.pumpWidget(
        ProviderScope(
          child: Consumer(
            builder: (context, ref, child) {
              final categoryId = ref.watch(selectedCategoryIdProvider);
              expect(categoryId, equals(0));
              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('selectedCategoryIdProvider should update when changed', (
      tester,
    ) async {
      await tester.pumpWidget(
        ProviderScope(
          child: Consumer(
            builder: (context, ref, child) {
              final categoryId = ref.watch(selectedCategoryIdProvider);

              // Change the category
              ref.read(selectedCategoryIdProvider.notifier).state = 5;

              expect(categoryId, equals(0)); // Initial value
              return const SizedBox();
            },
          ),
        ),
      );

      await tester.pump();

      // Verify the change
      await tester.pumpWidget(
        ProviderScope(
          child: Consumer(
            builder: (context, ref, child) {
              final categoryId = ref.watch(selectedCategoryIdProvider);
              expect(categoryId, equals(5));
              return const SizedBox();
            },
          ),
        ),
      );
    });
  });
}
