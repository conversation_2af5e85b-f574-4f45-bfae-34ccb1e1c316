import 'package:flutter/material.dart';

class SwipeUpIndicator extends StatelessWidget {
  final int totalInvited;
  final VoidCallback? onTap;
  final bool isExpanded;

  const SwipeUpIndicator({
    super.key,
    this.totalInvited = 0,
    this.onTap,
    this.isExpanded = false,
  });

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.width > 600;
    final fontSize = isTablet ? 18.0 : 16.0;
    final iconSize = isTablet ? 28.0 : 24.0;

    return AnimatedSlide(
      duration: const Duration(milliseconds: 300),
      offset: isExpanded ? const Offset(0, -0.05) : Offset.zero,
      child: AnimatedOpacity(
        duration: const Duration(milliseconds: 300),
        opacity: 1.0,
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            key: ValueKey(isExpanded),
            height: 80,
            decoration: BoxDecoration(
              color: const Color(0xFFFFF9C4),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 6,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            padding: const EdgeInsets.fromLTRB(20, 10, 20, 12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(bottom: 6),
                  decoration: BoxDecoration(
                    color: Colors.black26,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                RichText(
                  text: TextSpan(
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    children: [
                      const TextSpan(text: "You have invited "),
                      TextSpan(
                        text: "$totalInvited",
                        style: TextStyle(color: Colors.red[700]),
                      ),
                      const TextSpan(text: " people"),
                    ],
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      isExpanded
                          ? "Swipe down to view the QR code"
                          : "Swipe up to see the list!",
                      style: TextStyle(
                        fontSize: fontSize - 2,
                        fontWeight: FontWeight.w500,
                        color: Colors.black54,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Icon(
                      isExpanded
                          ? Icons.keyboard_arrow_down
                          : Icons.keyboard_arrow_up,
                      size: iconSize,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
