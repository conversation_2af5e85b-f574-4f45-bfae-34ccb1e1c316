import 'package:flutter/material.dart';

class ProductPriceSection extends StatelessWidget {
  final String price;
  final int soldCount;
  final String? pointsOffer;
  final bool isFavorite;
  final VoidCallback? onFavoritePressed;

  const ProductPriceSection({
    super.key,
    required this.price,
    required this.soldCount,
    this.pointsOffer,
    this.isFavorite = false,
    this.onFavoritePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Price and favorite row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                price,
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              Row(
                children: [
                  GestureDetector(
                    onTap: onFavoritePressed,
                    child: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: isFavorite ? Colors.red : Colors.grey[600],
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '$soldCount Sold',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Points offer badge
          if (pointsOffer != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFFFFC107), // Yellow color
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                pointsOffer!,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
