import 'package:flutter/material.dart';
import '../data/LuckyDrawRepository.dart';
import '../model/LuckyDrawProduct.dart';
import '../model/BatchInfo.dart';

class GroupPurchaseVM extends ChangeNotifier {
  final LuckyDrawRepository repository;

  GroupPurchaseVM({LuckyDrawRepository? repository})
    : repository = repository ?? MockLuckyDrawRepository();

  bool isLoading = true;
  bool isRefreshing = false;
  String? errorMessage;

  List<LuckyDrawProduct> groupProducts = [];
  List<BatchInfo> currentBatches = [];
  String selectedCategory = 'ALL';
  int minPointFilter = 0;
  int maxPointFilter = 1000;

  // Filtered products based on current filters
  List<LuckyDrawProduct> get filteredProducts {
    return groupProducts.where((product) {
      // Note: Since we removed category and pointValue from the model,
      // we'll need to implement category and point filtering through the API
      // For now, we'll show all products and implement proper filtering later
      return true;
    }).toList();
  }

  Future<void> initialize() async {
    await Future.wait([fetchGroupProducts(), fetchCurrentBatches()]);
  }

  Future<void> fetchGroupProducts({bool refresh = false}) async {
    if (refresh) {
      isRefreshing = true;
    } else {
      isLoading = true;
    }

    errorMessage = null;
    notifyListeners();

    try {
      groupProducts = await repository.fetchGroupPurchaseProducts(
        category: selectedCategory == 'ALL' ? null : selectedCategory,
      );
    } catch (e) {
      errorMessage = 'Failed to load group purchase products: ${e.toString()}';
      if (refresh || groupProducts.isEmpty) {
        groupProducts = [];
      }
    } finally {
      if (refresh) {
        isRefreshing = false;
      } else {
        isLoading = false;
      }
      notifyListeners();
    }
  }

  Future<void> fetchCurrentBatches() async {
    try {
      currentBatches = await repository.fetchCurrentBatches();
      notifyListeners();
    } catch (e) {
      // Don't show error for batches as it's supplementary data
      currentBatches = [];
      notifyListeners();
    }
  }

  Future<void> refreshAll() async {
    await Future.wait([
      fetchGroupProducts(refresh: true),
      fetchCurrentBatches(),
    ]);
  }

  Future<void> participateInGroupPurchase({
    required String productId,
    required int pointsToSpend,
    required int shares,
  }) async {
    try {
      await repository.participateInLuckyDraw(
        productId: productId,
        pointsToSpend: pointsToSpend,
        shares: shares,
      );

      // Refresh data after participation
      await refreshAll();
    } catch (e) {
      errorMessage = 'Failed to join group purchase: ${e.toString()}';
      notifyListeners();
    }
  }

  void setCategory(String category) {
    if (selectedCategory != category) {
      selectedCategory = category;
      notifyListeners();
      // Optionally fetch new data for the category
      fetchGroupProducts();
    }
  }

  void setPointRange(int min, int max) {
    if (minPointFilter != min || maxPointFilter != max) {
      minPointFilter = min;
      maxPointFilter = max;
      notifyListeners();
    }
  }

  void clearFilters() {
    selectedCategory = 'ALL';
    minPointFilter = 0;
    maxPointFilter = 1000;
    notifyListeners();
    fetchGroupProducts();
  }

  // Get batch info for a specific product
  BatchInfo? getBatchInfoForProduct(String productId) {
    try {
      return currentBatches.firstWhere((batch) => batch.productId == productId);
    } catch (e) {
      return null;
    }
  }

  // Check if a product has an active batch
  bool hasActiveBatch(String productId) {
    final batch = getBatchInfoForProduct(productId);
    return batch != null && batch.status == BatchStatus.active;
  }

  // Get countdown seconds for a product
  int getCountdownSeconds(String productId) {
    final batch = getBatchInfoForProduct(productId);
    return batch?.countdownSeconds ?? 0;
  }

  void clearError() {
    errorMessage = null;
    notifyListeners();
  }
}
