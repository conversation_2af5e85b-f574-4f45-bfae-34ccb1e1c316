import 'package:flutter/material.dart';
import '../../model/historyCard/HistoryCardItem.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';

class HistoryCardItemRow extends StatelessWidget {
  final HistoryCardItem item;

  const HistoryCardItemRow({super.key, required this.item});

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.grey;
      case 'cancel':
        return Colors.redAccent;
      case 'shipping':
        return Colors.green;
      case 'pickup':
        return Colors.amber;
      default:
        return Colors.black45;
    }
  }

  @override
  Widget build(BuildContext context) {
    final scale = MediaQuery.of(context).size.width / 390.0;
    final dateStr = DateFormat('dd MMM yyyy, hh:mm a').format(item.timestamp);
    final isPositive = item.amount >= 0;
    final amountColor = isPositive ? Colors.green : Colors.red;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 5 * scale, vertical: 6 * scale),
      child: InkWell(
        borderRadius: BorderRadius.circular(12 * scale),
        onTap: () {
          context.pushNamed(
            'historyCardDetail',
            extra: item,
          );
        },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12 * scale),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          padding: EdgeInsets.symmetric(horizontal: 14 * scale, vertical: 12 * scale),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left - Date + Detail + Type
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(dateStr,
                        style: TextStyle(
                          fontSize: 12 * scale,
                          color: Colors.grey[600],
                        )),
                    SizedBox(height: 4 * scale),
                    Text(item.detail,
                        style: TextStyle(
                          fontSize: 14 * scale,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        )),
                    SizedBox(height: 2 * scale),
                    Text(item.type,
                        style: TextStyle(
                          fontSize: 13 * scale,
                          color: Colors.black54,
                        )),
                  ],
                ),
              ),
              SizedBox(width: 10 * scale),
              // Right - Status + Amount
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10 * scale, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(item.status),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      item.status.toUpperCase(),
                      style: TextStyle(color: Colors.white, fontSize: 11 * scale),
                    ),
                  ),
                  SizedBox(height: 8 * scale),
                  Text(
                    "${isPositive ? '+' : '-'}RM${item.amount.abs().toStringAsFixed(2)}",
                    style: TextStyle(
                      fontSize: 14 * scale,
                      color: amountColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
