import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/CheckoutVM.dart';

class PaymentDetailsSection extends StatelessWidget {
  const PaymentDetailsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CheckoutVM>(
      builder: (context, viewModel, child) {
        final productData = viewModel.productData;
        if (productData == null) {
          return const SizedBox.shrink();
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(color: Colors.white),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Payment Details',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 12),

              // Product Price Subtotal
              _buildPriceRow(
                'Product Price Subtotal',
                productData['price'] ?? 0.0,
              ),

              // New User Discount (if applicable)
              if (productData['discount'] != null &&
                  productData['discount'] > 0)
                _buildPriceRow(
                  'New User Discount -20%',
                  -(productData['discount'] ?? 0.0),
                  isDiscount: true,
                ),

              // Voucher Discount (if voucher is selected)
              if (viewModel.selectedVoucher != null &&
                  viewModel.voucherDiscount > 0)
                _buildPriceRow(
                  'Voucher Discount',
                  -viewModel.voucherDiscount,
                  isDiscount: true,
                ),

              // Delivery Fees (if delivery is selected)
              if (viewModel.deliveryType == DeliveryType.delivery)
                _buildPriceRow(
                  'Delivery Fees',
                  productData['deliveryFee'] ?? 0.0,
                ),

              const SizedBox(height: 12),
              const Divider(height: 1, color: Colors.grey),
              const SizedBox(height: 12),

              // Total Payment
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Total Payment',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  Text(
                    'RM${_calculateTotal(productData, viewModel).toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPriceRow(
    String label,
    double amount, {
    bool isDiscount = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.black, fontSize: 14),
          ),
          Text(
            isDiscount
                ? '-RM${amount.abs().toStringAsFixed(2)}'
                : 'RM${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isDiscount ? Colors.red : Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  double _calculateTotal(
    Map<String, dynamic> productData,
    CheckoutVM viewModel,
  ) {
    double total = productData['price'] ?? 0.0;

    // Subtract product discount
    if (productData['discount'] != null) {
      total -= productData['discount'];
    }

    // Subtract voucher discount
    if (viewModel.selectedVoucher != null) {
      total -= viewModel.voucherDiscount;
    }

    // Add delivery fee if delivery is selected
    if (viewModel.deliveryType == DeliveryType.delivery &&
        productData['deliveryFee'] != null) {
      total += productData['deliveryFee'];
    }

    return total;
  }
}
