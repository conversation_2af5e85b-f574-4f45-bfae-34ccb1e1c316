import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class MySharingPostVM {
  static double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  static double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  static double getResponsiveImageHeight(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 320;
    } else if (screenWidth < 900) {
      return 400;
    } else {
      return 500;
    }
  }

  static double getResponsiveAvatarRadius(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 20;
    } else if (screenWidth < 900) {
      return 25;
    } else {
      return 30;
    }
  }

  static double getResponsiveProductImageHeight(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 80;
    } else if (screenWidth < 900) {
      return 100;
    } else {
      return 120;
    }
  }

  // Helper method to extract images from post data (matching blindbox PostView)
  static List<String> extractImages(Map<String, dynamic> post) {
    final List<String> images = [];

    if (post['images'] != null) {
      final dynamicImages = post['images'] as List<dynamic>;

      for (int i = 0; i < dynamicImages.length; i++) {
        final item = dynamicImages[i];

        if (item != null && item is String && item.isNotEmpty) {
          images.add(item);
        }
      }
    }

    return images;
  }

  // Sample images for demonstration when post data doesn't have images
  static List<String> getSampleImages() {
    return [
      'https://down-my.img.susercontent.com/file/sg-11134201-7rd4j-lvmr60yl4xybd5',
      'https://preview.redd.it/skintific-water-gel-ceramide-toner-first-impression-v0-r8u75rmvvs3c1.jpeg?auto=webp&s=16435fbc1a0c6db0a41b8547ffa5a129a6881130',
    ];
  }

  // Sample product data for demonstration (matching blindbox PostVM structure)
  static Map<String, dynamic> getSampleProduct() {
    return {
      'id': '1',
      'name': 'Mystery Box Elite',
      'title': 'Mystery Box Elite',
      'price': 'RM 45.00',
      'imageUrl':
          'https://oss.luckymymall.com/3238aef0019fb92e208adb997431f2ac2337a7baab70f472d0a49a314b29fde9.jpg',
      'imageUrls': [
        'https://oss.luckymymall.com/3238aef0019fb92e208adb997431f2ac2337a7baab70f472d0a49a314b29fde9.jpg',
      ],
      'soldCount': 156,
      'rating': 4.8,
      'pointsOffer': 'Earn 45 points',
      'variation': 'Random Selection',
      'description':
          'This is a premium mystery box containing various high-quality items. Each box is carefully curated to provide a unique unboxing experience. Perfect for gifts or personal enjoyment.',
      'isAvailable': true,
      'isGroupBuy': false,
    };
  }

  // Handle product tap action (matching blindbox PostVM functionality)
  static void onProductTap(BuildContext context, Map<String, dynamic> product) {
    if (!product['isAvailable']) {
      // Show sold out message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${product['title']} is currently sold out'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    } else {
      // Navigate to product details using go_router like blindbox post view
      context.push(
        '/blind-box/buy',
        extra: {
          'product': product,
          'isGroupBuy': product['isGroupBuy'] ?? false,
        },
      );
    }
  }

  // Handle post deletion
  static void deletePost(BuildContext context, Map<String, dynamic> post) {
    // TODO: Implement actual API call to delete post
    // For now, show success message and navigate back
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Post "${post['title']}" deleted successfully'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );

    // Navigate back to the sharing page
    Navigator.of(context).pop();
  }
}
