import 'package:flutter/material.dart';

class BottomActionBar extends StatelessWidget {
  final VoidCallback? onChatPressed;
  final VoidCallback? onBuyNowPressed;
  final String buyButtonText;

  const BottomActionBar({
    super.key,
    this.onChatPressed,
    this.onBuyNowPressed,
    this.buyButtonText = 'Buy Now',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(color: Colors.white),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            // Chat button
            GestureDetector(
              onTap: onChatPressed,
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                ),
                child: Icon(
                  Icons.chat_bubble_outline,
                  color: Colors.grey[700],
                  size: 24,
                ),
              ),
            ),
            const SizedBox(width: 16),
            // Buy Now button
            Expanded(
              child: GestureDetector(
                onTap: onBuyNowPressed,
                child: Container(
                  height: 48,
                  decoration: BoxDecoration(
                    color: const Color(
                      0xFFFFC107,
                    ), // Yellow color matching the theme
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Text(
                      buyButtonText,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
