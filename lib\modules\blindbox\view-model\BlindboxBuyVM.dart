import 'package:flutter/material.dart';
import '../widgets/ProductReviewsSection.dart';

class BlindboxBuyVM extends ChangeNotifier {
  bool isFavorite = false;
  int currentImageIndex = 0;
  bool isReviewsLoading = true;
  final Set<String> _helpfulLoadingReviews = {};

  final List<String> imageUrls = [];
  List<ProductReview> reviews = [
    const ProductReview(
      id: '1',
      userName: 'Ashley Lim',
      rating: 5.0,
      comment:
          'Dapat lebih 10 jenis snack, semua dalam keadaan yang baik. Ada yang saya tak pernah cuba pun! Packaging pun comel. Memang berbaloil untuk harga ni. Confirm repeat order! 😊',
      date: '2025/02/15',
      variation: 'Random Selection',
      helpfulCount: 22,
    ),
    const ProductReview(
      id: '2',
      userName: 'Zhang Wei',
      rating: 4.5,
      comment:
          'Great mystery box experience! The items were well-packaged and exceeded my expectations. Will definitely order again.',
      date: '2025/02/10',
      variation: 'Random Selection',
      helpfulCount: 18,
    ),
    const ProductReview(
      id: '3',
      userName: '<PERSON><PERSON> Ma',
      rating: 5.0,
      comment:
          'Amazing value for money! Each item was carefully selected and the surprise factor made it even more exciting.',
      date: '2025/02/08',
      variation: 'Random Selection',
      helpfulCount: 25,
    ),
  ];

  BlindboxBuyVM() {
    _loadReviews();
  }

  bool isReviewHelpfulLoading(String reviewId) =>
      _helpfulLoadingReviews.contains(reviewId);

  Future<void> _loadReviews() async {
    // Simulate loading reviews from API
    await Future.delayed(const Duration(seconds: 1));
    isReviewsLoading = false;
    notifyListeners();
  }

  Future<void> refreshReviews() async {
    isReviewsLoading = true;
    notifyListeners();
    await _loadReviews();
  }

  void addReview(ProductReview review) {
    reviews.insert(0, review); // Add new review at the beginning
    notifyListeners();
  }

  Future<void> markReviewHelpful(String reviewId, BuildContext context) async {
    if (_helpfulLoadingReviews.contains(reviewId))
      return; // Prevent double-clicking

    _helpfulLoadingReviews.add(reviewId);
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      final index = reviews.indexWhere((review) => review.id == reviewId);
      if (index != -1) {
        // Create a new review with updated helpful count
        final review = reviews[index];
        final updatedReview = ProductReview(
          id: review.id,
          userName: review.userName,
          rating: review.rating,
          comment: review.comment,
          date: review.date,
          variation: review.variation,
          helpfulCount: review.helpfulCount + 1,
        );
        reviews[index] = updatedReview;

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Review marked as helpful!'),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } finally {
      _helpfulLoadingReviews.remove(reviewId);
      notifyListeners();
    }
  }

  void setFavorite(bool value) {
    isFavorite = value;
    notifyListeners();
  }

  void toggleFavorite() {
    isFavorite = !isFavorite;
    notifyListeners();
  }

  void setCurrentImageIndex(int index) {
    currentImageIndex = index;
    notifyListeners();
  }
}
