import 'package:flutter/material.dart';

class LuckydrawFullReview {
  final String userName;
  final double rating;
  final String date; // yyyy / mm / dd
  final String reviewText;
  final int helpfulCount;
  final String variation; // e.g., "Black", "White"
  final bool hasMedia;
  final List<String>? imageUrls;

  const LuckydrawFullReview({
    required this.userName,
    required this.rating,
    required this.date,
    required this.reviewText,
    required this.helpfulCount,
    required this.variation,
    this.hasMedia = false,
    this.imageUrls,
  });
}

enum ReviewTab { all, withMedia, stars, variation }

class LuckydrawReviewVM extends ChangeNotifier {
  ReviewTab selectedTab = ReviewTab.all;
  int? selectedStars; // 1..5, null = All
  String? selectedVariation; // null = All

  final List<LuckydrawFullReview> _allReviews = const [
    LuckydrawFullReview(
      userName: 'Zhang Wei',
      rating: 5,
      date: '2025 / 02 / 15',
      reviewText:
          'Amazing product! The quality is outstanding and it works perfectly. Highly recommend this to everyone.',
      helpfulCount: 22,
      variation: 'Black',
      hasMedia: false,
    ),
    LuckydrawFullReview(
      userName: 'Jacky Ma',
      rating: 5,
      date: '2025 / 02 / 22',
      reviewText:
          'Excellent service and fast delivery. The product exceeded my expectations. Will definitely participate again!',
      helpfulCount: 23,
      variation: 'White',
      hasMedia: true,
      imageUrls: [
        'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
      ],
    ),
    LuckydrawFullReview(
      userName: 'Sarah Chen',
      rating: 4,
      date: '2025 / 02 / 10',
      reviewText:
          'Great product overall. Good value for the points spent. Would participate in more draws.',
      helpfulCount: 18,
      variation: 'Black',
      hasMedia: true,
      imageUrls: [
        'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
      ],
    ),
    LuckydrawFullReview(
      userName: 'Mike Johnson',
      rating: 5,
      date: '2025 / 02 / 05',
      reviewText:
          'Perfect condition upon arrival. The raffle system is fair and transparent. Very satisfied!',
      helpfulCount: 15,
      variation: 'White',
      hasMedia: false,
    ),
    LuckydrawFullReview(
      userName: 'Lisa Wong',
      rating: 4,
      date: '2025 / 01 / 28',
      reviewText:
          'Good quality product. The points system is easy to understand and use.',
      helpfulCount: 12,
      variation: 'Black',
      hasMedia: false,
    ),
  ];

  List<LuckydrawFullReview> get allReviews => List.unmodifiable(_allReviews);

  void setTab(ReviewTab tab) {
    selectedTab = tab;
    notifyListeners();
  }

  void setStars(int? stars) {
    selectedStars = stars;
    notifyListeners();
  }

  void setVariation(String? variation) {
    selectedVariation = variation;
    notifyListeners();
  }

  int get allCount => _allReviews.length;
  int get withMediaCount =>
      _allReviews.where((review) => review.hasMedia).length;

  List<LuckydrawFullReview> get filteredReviews {
    Iterable<LuckydrawFullReview> result = _allReviews;

    if (selectedTab == ReviewTab.withMedia) {
      result = result.where((r) => r.hasMedia);
    }

    if (selectedStars != null) {
      result = result.where((r) => r.rating.floor() == selectedStars);
    }

    if (selectedVariation != null && selectedVariation!.isNotEmpty) {
      result = result.where((r) => r.variation == selectedVariation);
    }

    return result.toList();
  }
}
