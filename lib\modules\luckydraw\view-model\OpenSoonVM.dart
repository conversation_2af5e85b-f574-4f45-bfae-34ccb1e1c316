import 'package:flutter/material.dart';
import '../data/LuckyDrawRepository.dart';
import '../model/OpenSoonProduct.dart';

class OpenSoonVM extends ChangeNotifier {
  final LuckyDrawRepository repository;

  OpenSoonVM({LuckyDrawRepository? repository})
    : repository = repository ?? MockLuckyDrawRepository();

  bool isLoading = false;
  String? errorMessage;
  List<OpenSoonProduct> products = [];

  Future<void> fetchOpeningSoonProducts({
    String? category,
    int? minPoints,
    int? limit,
  }) async {
    isLoading = true;
    errorMessage = null;
    notifyListeners();

    try {
      print('OpenSoonVM: Fetching opening soon products...');
      final result = await repository.fetchOpeningSoonProducts(
        category: category,
        minPoints: minPoints,
        limit: limit,
      );

      products = result;
      print('OpenSoonVM: Fetched ${products.length} opening soon products');
    } catch (e) {
      errorMessage = 'Failed to load opening soon products: ${e.toString()}';
      print('OpenSoonVM: Error fetching opening soon products: $e');
      products = [];
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refresh() async {
    await fetchOpeningSoonProducts();
  }

  // Get products that are nearly ready to open (progress > 90%)
  List<OpenSoonProduct> get nearlyReadyProducts {
    return products.where((product) {
      final progress = product.calculatedProgressPercentage;
      return progress >= 90.0;
    }).toList();
  }

  // Get products sorted by progress (highest first)
  List<OpenSoonProduct> get productsByProgress {
    final sorted = List<OpenSoonProduct>.from(products);
    sorted.sort(
      (a, b) => b.calculatedProgressPercentage.compareTo(
        a.calculatedProgressPercentage,
      ),
    );
    return sorted;
  }

  // Get products sorted by priority (if needed for future use)
  List<OpenSoonProduct> get productsByScore {
    final sorted = List<OpenSoonProduct>.from(products);
    sorted.sort((a, b) => b.score.compareTo(a.score));
    return sorted;
  }
}
