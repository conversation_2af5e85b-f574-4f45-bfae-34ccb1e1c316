import 'package:flutter/material.dart';

class CategoryDescriptionBox extends StatelessWidget {
  final String category;
  final String description;

  const CategoryDescriptionBox({
    super.key,
    required this.category,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity, // full width
      color: Colors.black26, // background color
      padding: const EdgeInsets.symmetric(
        vertical: 12,
        horizontal: 16, // inner spacing, not outer
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (category.isNotEmpty) ...[
            Text(
              category,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 6),
          ],
          Text(
            description,
            style: const TextStyle(fontSize: 14),
            textAlign: TextAlign.justify,
          ),
        ],
      ),
    );
  }
}
