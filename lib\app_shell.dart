import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'modules/nav/tabItem.dart';
import 'component/navigation/bottomNavBar.dart';

class AppShell extends StatefulWidget {
  final Widget child;

  const AppShell({super.key, required this.child});

  @override
  State<AppShell> createState() => _AppShellState();
}

class _AppShellState extends State<AppShell> {
  TabItem _getCurrentTab(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;

    if (location.startsWith('/shop')) return TabItem.shop;
    if (location.startsWith('/card')) return TabItem.card;
    if (location.startsWith('/lucky-draw')) return TabItem.luckyDraw;
    if (location.startsWith('/blind-box')) return TabItem.blindBox;
    if (location.startsWith('/profile')) return TabItem.me;

    return TabItem.shop; // default
  }

  void _onTabSelected(TabItem selectedTab) {
    // Navigate to the selected tab
    switch (selectedTab) {
      case TabItem.shop:
        context.go('/shop');
        break;
      case TabItem.card:
        context.go('/card');
        break;
      case TabItem.blindBox:
        context.go('/blind-box/shop');
        break;
      case TabItem.luckyDraw:
        context.go('/lucky-draw');
        break;
      case TabItem.me:
        context.go('/profile');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentTab = _getCurrentTab(context);

    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavBar(
        currentTab: currentTab,
        onSelectTab: _onTabSelected,
      ),
    );
  }
}
