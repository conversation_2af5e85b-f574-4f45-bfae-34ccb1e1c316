import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/CheckoutVM.dart';

class BillingInfoSection extends StatelessWidget {
  const BillingInfoSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CheckoutVM>(
      builder: (context, viewModel, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(6),
            boxShadow: const [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 6,
                offset: Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Billing Information',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),

              _buildFormRow(
                'Name',
                viewModel.nameController,
                hint: 'Enter name...',
                isRequired: true,
              ),

              _buildFormRow(
                'Mobile Number',
                viewModel.phoneController,
                hint: 'Enter mobile number...',
                type: TextInputType.phone,
                isRequired: true,
              ),

              _buildFormRow(
                'Email',
                viewModel.emailController,
                hint: 'Enter email...',
                type: TextInputType.emailAddress,
                isRequired: true,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFormRow(
    String label,
    TextEditingController controller, {
    String? hint,
    TextInputType? type,
    bool isRequired = false,
    String? errorText,
  }) {
    return Column(
      children: [
        SizedBox(
          height: 48,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Label on the left
              SizedBox(
                width: 100,
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        label,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                    if (isRequired)
                      const Text(
                        ' *',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.red,
                        ),
                      ),
                  ],
                ),
              ),

              // Input Field on the right
              const SizedBox(width: 16),
              Expanded(
                child: TextField(
                  controller: controller,
                  keyboardType: type,
                  decoration: InputDecoration(
                    hintText: hint,
                    hintStyle: const TextStyle(color: Colors.grey),
                    alignLabelWithHint: true,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 12),
                    isCollapsed: true,
                  ),
                  textAlign: TextAlign.right,
                  onChanged: (_) {
                    // Trigger validation on change
                    controller.notifyListeners();
                  },
                ),
              ),
            ],
          ),
        ),
        Container(
          height: 1,
          color: errorText != null ? Colors.red : Colors.grey.shade300,
          margin: const EdgeInsets.only(bottom: 8),
        ),
        // Show error text if any
        if (errorText != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              errorText,
              style: const TextStyle(fontSize: 12, color: Colors.red),
            ),
          ),
      ],
    );
  }
}
