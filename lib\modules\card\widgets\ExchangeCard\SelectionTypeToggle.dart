import 'package:flutter/material.dart';
import '../../model/exchangeCard/ExchangeCardModel.dart';

class SelectionTypeToggle extends StatelessWidget {
  final SelectionType? selectedType;
  final Function(SelectionType?) onSelected;

  const SelectionTypeToggle({
    super.key,
    required this.selectedType,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    final types = [
      {
        "label": "All",
        "value": null,
        "icon": Icons.all_inclusive,
        "gradient": [Colors.amber.shade400, Colors.orange.shade600],
      },
      {
        "label": "Auto",
        "value": SelectionType.auto,
        "icon": Icons.flash_on,
        "gradient": [Colors.amber.shade300, Colors.teal.shade700],
      },
      {
        "label": "Self",
        "value": SelectionType.selfSelect,
        "icon": Icons.touch_app,
        "gradient": [Colors.amber.shade300, Colors.deepPurple.shade700],
      },
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final bool isTablet = constraints.maxWidth > 600;
          final bool isTinyScreen = constraints.maxWidth < 340;

          // Adaptive height
          final double totalHeight = isTinyScreen ? 40 : (isTablet ? 50 : 44);
          final double itemWidth = (constraints.maxWidth - 8) / types.length;
          final double borderRadius = totalHeight / 2;
          final double fontSize = totalHeight * 0.28;
          final double iconSize = totalHeight * 0.4;

          int selectedIndex =
              types.indexWhere((t) => t["value"] == selectedType);
          if (selectedIndex == -1) selectedIndex = 0;

          return SizedBox(
            height: totalHeight + 6, // Extra bottom breathing room
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: const Color.fromARGB(66, 0, 0, 0),
                borderRadius: BorderRadius.circular(borderRadius),
                border: Border.all(color: Colors.amber.shade300, width: 1.2),
              ),
              child: Stack(
                children: [
                  // Sliding highlight
                  AnimatedPositioned(
                    duration: const Duration(milliseconds: 250),
                    curve: Curves.easeOut,
                    left: selectedIndex * itemWidth,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 250),
                      width: itemWidth,
                      height: totalHeight - 8 + 4, // match container height better
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: types[selectedIndex]["gradient"] as List<Color>,
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(borderRadius - 2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.amberAccent.shade400,
                            blurRadius: 12,
                            spreadRadius: 1,
                          )
                        ],
                      ),
                    ),
                  ),

                  // Tabs
                  Row(
                    children: types.map((type) {
                      final isSelected = type["value"] == selectedType;
                      return Expanded(
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent, // Easier tapping
                          onTap: () => onSelected(type["value"] as SelectionType?),
                          child: Center(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  type["icon"] as IconData,
                                  size: iconSize,
                                  color: isSelected
                                      ? Colors.white
                                      : Colors.grey[850],
                                ),
                                SizedBox(width: totalHeight * 0.12),
                                Text(
                                  type["label"] as String,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: fontSize,
                                    letterSpacing: 0.5,
                                    color: isSelected
                                        ? Colors.white
                                        : Colors.grey[850],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
