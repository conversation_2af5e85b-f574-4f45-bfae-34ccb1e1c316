import 'package:flutter/material.dart';
import '../../model/exchangeCard/ExchangeCardModel.dart';

class RankFilterBar extends StatelessWidget {
  final CardRank? selectedRank;
  final Function(CardRank?) onSelected;

  const RankFilterBar({
    super.key,
    required this.selectedRank,
    required this.onSelected,
  });

  List<Widget> _buildStars(int count, Color color) {
    return List.generate(count, (_) => Icon(Icons.star, color: color, size: 18));
  }

  @override
  Widget build(BuildContext context) {
    final ranks = [
      {"label": "All", "value": null, "color": Colors.amber, "stars": 0},
      {"label": "R", "value": CardRank.r, "color": Colors.lightBlueAccent, "stars": 1},
      {"label": "SR", "value": CardRank.sr, "color": Colors.yellow[600], "stars": 2},
      {"label": "SSR", "value": CardRank.ssr, "color": Colors.orangeAccent, "stars": 3},
      {"label": "SSSR", "value": CardRank.sssr, "color": Colors.deepOrange, "stars": 4},
      {"label": "UR", "value": CardRank.ur, "color": Colors.redAccent, "stars": 5},
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: ranks.map((rank) {
          final isSelected = rank["value"] == selectedRank;
          final baseColor = rank["color"] as Color?;
          final starCount = rank["stars"] as int;

          return GestureDetector(
            onTap: () => onSelected(rank["value"] as CardRank?),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
              decoration: BoxDecoration(
                gradient: isSelected
                    ? LinearGradient(colors: [baseColor!, baseColor.withOpacity(0.7)])
                    : LinearGradient(colors: [Colors.grey[300]!, Colors.grey[200]!]),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected ? Colors.yellowAccent : Colors.grey[400]!,
                  width: isSelected ? 2 : 1,
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: baseColor!.withOpacity(0.5),
                          blurRadius: 8,
                          spreadRadius: 1,
                        )
                      ]
                    : [],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (starCount == 0)
                    Icon(Icons.all_inclusive,
                        color: isSelected ? Colors.white : Colors.grey[700], size: 20)
                  else
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: _buildStars(
                        starCount,
                        isSelected ? Colors.white : Colors.grey[700]!,
                      ),
                    ),
                  const SizedBox(height: 4),
                  Text(
                    rank["label"] as String,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.white : Colors.grey[800],
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
