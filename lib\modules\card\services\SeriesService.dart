import '../model/SeriesContent.dart';


class SeriesService {
  // Simulate API call
  Future<SeriesContent> getSeriesContent(String seriesId) async {
    // TODO: Replace with actual API call
    await Future.delayed(const Duration(seconds: 1));
    
    // Simulated API response
    final Map<String, dynamic> response = {
      'fantasy': {
        'seriesId': 'fantasy',
        'name': 'Fantasy',
        'description': 'Card Event - Knights, Wizards, Elves, Blacksmiths, and Hidden Cards appear randomly',
        'sheetImage': 'assets/images/card/backgroundCard/cardWallpaper02.png',
        'imageUrls': [
          'assets/images/card/imageSlider/example1.jpg',
          'assets/images/card/imageSlider/example2.jpeg',
          'assets/images/card/imageSlider/example3.png',
        ],
        'categories': [
          {
            'key': 'royal',
            'icon': 'assets/icons/cardCategory/King01.svg',
            'activeIcon': 'assets/icons/cardCategory/King02.svg',
            'desc': 'Royal desc...'
          },
          {
            'key': 'knight',
            'icon': 'assets/icons/cardCategory/Knight01.svg',
            'activeIcon': 'assets/icons/cardCategory/Knight02.svg',
            'desc': 'Knight desc...'
          },
          {
            'key': 'wizard',
            'icon': 'assets/icons/cardCategory/Wizard01.svg',
            'activeIcon': 'assets/icons/cardCategory/Wizard02.svg',
            'desc': 'wizard desc...'
          },
          {
            'key': 'smith',
            'icon': 'assets/icons/cardCategory/Smith01.svg',
            'activeIcon': 'assets/icons/cardCategory/Smith02.svg',
            'desc': 'smith desc...'
          },
          {
            'key': 'elf',
            'icon': 'assets/icons/cardCategory/Elf01.svg',
            'activeIcon': 'assets/icons/cardCategory/Elf02.svg',
            'desc': 'elf desc...'
          },
          // ...other categories
        ],
      },
      'zodiac': {
        'seriesId': 'zodiac',
        'name': 'Zodiac',
        'description': 'Zodiac Series - Collect all 12 zodiac signs with special powers and abilities',
        'imageUrls': [
          'assets/images/card/zodiac/slider1.png',
          'assets/images/card/zodiac/slider2.png',
          'assets/images/card/zodiac/slider3.png',
        ],
        'sheetImage': 'assets/images/card/fantasy/sheet_image.png',
        'categories': [
          {
            'key': 'aries',
            'icon': 'assets/images/card/zodiac/aries.png',
            'activeIcon': 'assets/images/card/zodiac/aries_active.png',
            'desc': 'Aries desc...'
          },
          {
            'key': 'taurus',
            'icon': 'assets/images/card/zodiac/taurus.png',
            'activeIcon': 'assets/images/card/zodiac/taurus_active.png',
            'desc': 'Taurus desc...'
          },
          {
            'key': 'gemini',
            'icon': 'assets/images/card/zodiac/gemini.png',
            'activeIcon': 'assets/images/card/zodiac/gemini_active.png',
            'desc': 'Gemini desc...'
          },
          {
            'key': 'cancer',
            'icon': 'assets/images/card/zodiac/cancer.png',
            'activeIcon': 'assets/images/card/zodiac/cancer_active.png',
            'desc': 'Cancer desc...'
          },
          {
            'key': 'leo',
            'icon': 'assets/images/card/zodiac/leo.png',
            'activeIcon': 'assets/images/card/zodiac/leo_active.png',
            'desc': 'Leo desc...'
          },
          {
            'key': 'virgo',
            'icon': 'assets/images/card/zodiac/virgo.png',
            'activeIcon': 'assets/images/card/zodiac/virgo_active.png',
            'desc': 'Virgo desc...'
          },
          {
            'key': 'libra',
            'icon': 'assets/images/card/zodiac/libra.png',
            'activeIcon': 'assets/images/card/zodiac/libra_active.png',
            'desc': 'Libra desc...'
          },
          {
            'key': 'scorpio',
            'icon': 'assets/images/card/zodiac/scorpio.png',
            'activeIcon': 'assets/images/card/zodiac/scorpio_active.png',
            'desc': 'Scorpio desc...'
          },
          {
            'key': 'sagittarius',
            'icon': 'assets/images/card/zodiac/sagittarius.png',
            'activeIcon': 'assets/images/card/zodiac/sagittarius_active.png',
            'desc': 'Sagittarius desc...'
          },
          {
            'key': 'capricorn',
            'icon': 'assets/images/card/zodiac/capricorn.png',
            'activeIcon': 'assets/images/card/zodiac/capricorn_active.png',
            'desc': 'Capricorn desc...'
          },
          {
            'key': 'aquarius',
            'icon': 'assets/images/card/zodiac/aquarius.png',
            'activeIcon': 'assets/images/card/zodiac/aquarius_active.png',
            'desc': 'Aquarius desc...'
          },
          {
            'key': 'pisces',
            'icon': 'assets/images/card/zodiac/pisces.png',
            'activeIcon': 'assets/images/card/zodiac/pisces_active.png',
            'desc': 'Pisces desc...'
          },
        ],
      },
    };

    if (!response.containsKey(seriesId)) {
      throw Exception('Series not found');
    }

    return SeriesContent.fromJson(response[seriesId]!);
  }
}